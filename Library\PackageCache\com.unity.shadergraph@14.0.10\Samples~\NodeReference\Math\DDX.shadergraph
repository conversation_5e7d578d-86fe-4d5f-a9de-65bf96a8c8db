{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "a6a2c084b83c48dd8da3ccd3c87a8680",
    "m_Properties": [],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "fe4c3cc527364c06b551d113c4e0fb2f"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "60b69faeaa0b4a2bb8158bfebe048214"
        },
        {
            "m_Id": "45f91cf7fd434f8a91c5ef6ff7fd6043"
        },
        {
            "m_Id": "e05ec86b78a747e982591b586d73fa7f"
        },
        {
            "m_Id": "d1e9246b2c0c441786c33e492c72e7d8"
        },
        {
            "m_Id": "c52ad94f45004847a9a858af379b2cdb"
        },
        {
            "m_Id": "502f9eb8b4d54c549f374a170bf3c6c5"
        },
        {
            "m_Id": "d5750300ed654bab86d8bc4c2a237d5a"
        },
        {
            "m_Id": "7d60ed2d45894499bfd63a62a3480828"
        },
        {
            "m_Id": "1d29654110be4cb0bb650a6622f5d732"
        },
        {
            "m_Id": "657f1cf219a94cd7a2310cb268b68f65"
        },
        {
            "m_Id": "c8f8e7f6846d46b196fb24d3efa0cf0c"
        },
        {
            "m_Id": "fa9b5083b8cb4867903b8de46b69524e"
        },
        {
            "m_Id": "b04da535db4345c6b59a648f238e45ab"
        },
        {
            "m_Id": "13847eb85bb8481fbf66c1867330e6d8"
        },
        {
            "m_Id": "3b3bba5316e544108df7527779485bdc"
        },
        {
            "m_Id": "2b5f1072e2ec4b608f3f61ca4f5a4dfb"
        },
        {
            "m_Id": "a661068defb54766a8bdf42d0083d160"
        },
        {
            "m_Id": "2b5a5c33c50742c1b741d1d17c3e8d6a"
        }
    ],
    "m_GroupDatas": [
        {
            "m_Id": "6251f5292d5b4c228867ceded03d9afe"
        },
        {
            "m_Id": "9fc22f37c54f4de082a0c46f2840b0e3"
        }
    ],
    "m_StickyNoteDatas": [
        {
            "m_Id": "aa110b4d7c0b482eaed378ff91dbd031"
        },
        {
            "m_Id": "f6e28907f1984a54bb766b660adddbd6"
        },
        {
            "m_Id": "3792d264843e49b2a45b266a0c9f30e5"
        },
        {
            "m_Id": "858faf9f38fb4cbbb5b37f9bd006bc9b"
        },
        {
            "m_Id": "14a4a7ad6ec74a649a3185d23e7ee435"
        },
        {
            "m_Id": "8501b48a23014d6e88ec70a5010bc739"
        },
        {
            "m_Id": "6492b0bc4d244dc28fcf9c9ed271bb7a"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "13847eb85bb8481fbf66c1867330e6d8"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "fa9b5083b8cb4867903b8de46b69524e"
                },
                "m_SlotId": 10
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "1d29654110be4cb0bb650a6622f5d732"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "657f1cf219a94cd7a2310cb268b68f65"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "2b5f1072e2ec4b608f3f61ca4f5a4dfb"
                },
                "m_SlotId": 4
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "fa9b5083b8cb4867903b8de46b69524e"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "3b3bba5316e544108df7527779485bdc"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "fa9b5083b8cb4867903b8de46b69524e"
                },
                "m_SlotId": 11
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "502f9eb8b4d54c549f374a170bf3c6c5"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "7d60ed2d45894499bfd63a62a3480828"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "502f9eb8b4d54c549f374a170bf3c6c5"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "d5750300ed654bab86d8bc4c2a237d5a"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "657f1cf219a94cd7a2310cb268b68f65"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "c8f8e7f6846d46b196fb24d3efa0cf0c"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "7d60ed2d45894499bfd63a62a3480828"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1d29654110be4cb0bb650a6622f5d732"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "b04da535db4345c6b59a648f238e45ab"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "13847eb85bb8481fbf66c1867330e6d8"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "b04da535db4345c6b59a648f238e45ab"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "3b3bba5316e544108df7527779485bdc"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "d5750300ed654bab86d8bc4c2a237d5a"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1d29654110be4cb0bb650a6622f5d732"
                },
                "m_SlotId": 1
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": [
            {
                "m_Id": "60b69faeaa0b4a2bb8158bfebe048214"
            },
            {
                "m_Id": "45f91cf7fd434f8a91c5ef6ff7fd6043"
            },
            {
                "m_Id": "e05ec86b78a747e982591b586d73fa7f"
            }
        ]
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 200.0
        },
        "m_Blocks": [
            {
                "m_Id": "d1e9246b2c0c441786c33e492c72e7d8"
            },
            {
                "m_Id": "a661068defb54766a8bdf42d0083d160"
            },
            {
                "m_Id": "2b5a5c33c50742c1b741d1d17c3e8d6a"
            }
        ]
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Shader Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": ""
    },
    "m_ActiveTargets": [
        {
            "m_Id": "e8a1ef8522294f28bc9b276c58745a33"
        },
        {
            "m_Id": "9f28618838ba45e3a33e14db214db778"
        },
        {
            "m_Id": "ee1e4c88cb064cf88766bfda4fb0106b"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "008696905a014fb69cedf45236fd35cf",
    "m_Id": 2,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "03e21359f2304ae892036fa830a2e2be",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 1.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "065d0411d9714a44a01d7d2b4d6523d1",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "0c36a0cb6f0540d19e52e321f3ce5947"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "10f710a58c294f9788655a8c3de28f39",
    "m_Id": 5,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DDXNode",
    "m_ObjectId": "13847eb85bb8481fbf66c1867330e6d8",
    "m_Group": {
        "m_Id": "9fc22f37c54f4de082a0c46f2840b0e3"
    },
    "m_Name": "DDX",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -668.000244140625,
            "y": 150.00001525878907,
            "width": 131.50006103515626,
            "height": 94.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "3d9d62d5241c41328d69c2730a93cc74"
        },
        {
            "m_Id": "d8825b9b6af64fef975051efd3c2793a"
        }
    ],
    "synonyms": [
        "derivative"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "14a4a7ad6ec74a649a3185d23e7ee435",
    "m_Title": "",
    "m_Content": "This illustrates how to create Face Normals using DDX and DDY.  This is useful for meshes where you've altered the vertex normals - like you might for foliage - but you still need to use the real normal for some things.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1946.5001220703125,
        "y": 407.0000305175781,
        "width": 200.0,
        "height": 118.50003051757813
    },
    "m_Group": {
        "m_Id": "6251f5292d5b4c228867ceded03d9afe"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "162162b5d3fa403393ac21e67c0d6529",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "1c535656aafb4092bed08ebede94bd55",
    "m_Id": 0,
    "m_DisplayName": "Alpha",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Alpha",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CrossProductNode",
    "m_ObjectId": "1d29654110be4cb0bb650a6622f5d732",
    "m_Group": {
        "m_Id": "6251f5292d5b4c228867ceded03d9afe"
    },
    "m_Name": "Cross Product",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1529.000244140625,
            "y": 81.99996948242188,
            "width": 129.5,
            "height": 118.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "93355243d2cb4a958865044033e8ebd7"
        },
        {
            "m_Id": "03e21359f2304ae892036fa830a2e2be"
        },
        {
            "m_Id": "a915288d689b4ea1ae429469c9a34c38"
        }
    ],
    "synonyms": [
        "perpendicular"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "1e6fe2c5e7834a709519e5d31d54cbe1",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "2407a1d6d7ae458ca24c16bf4a612a56",
    "m_Id": 4,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "2505d721f0514a0e9afa9633771a0692",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "2b5a5c33c50742c1b741d1d17c3e8d6a",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Alpha",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "1c535656aafb4092bed08ebede94bd55"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Alpha"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PolarCoordinatesNode",
    "m_ObjectId": "2b5f1072e2ec4b608f3f61ca4f5a4dfb",
    "m_Group": {
        "m_Id": "9fc22f37c54f4de082a0c46f2840b0e3"
    },
    "m_Name": "Polar Coordinates",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -780.5000610351563,
            "y": 35.00000762939453,
            "width": 147.5,
            "height": 94.0000228881836
        }
    },
    "m_Slots": [
        {
            "m_Id": "c21b44a7e2214e52bd220a95086b315e"
        },
        {
            "m_Id": "6a196a87cf594dbe81446497864eae14"
        },
        {
            "m_Id": "b3ed18f7483a44ec8f8458a26a382f7e"
        },
        {
            "m_Id": "b26729d29d724d9f810c582549eba7d1"
        },
        {
            "m_Id": "2407a1d6d7ae458ca24c16bf4a612a56"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalUnlitSubTarget",
    "m_ObjectId": "316c1535eded4ac9bf05c74ad5c2fb3d"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "3792d264843e49b2a45b266a0c9f30e5",
    "m_Title": "",
    "m_Content": "Doing a cross product between those two slope vectors creates a surface normal.  Notice that it's faceted.  This is because we're not using the smoothed vertex normals, but instead, we're generating the normals purely from the shape of the geometry.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1488.0001220703125,
        "y": 367.5000305175781,
        "width": 200.0,
        "height": 125.00003051757813
    },
    "m_Group": {
        "m_Id": "6251f5292d5b4c228867ceded03d9afe"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DDYNode",
    "m_ObjectId": "3b3bba5316e544108df7527779485bdc",
    "m_Group": {
        "m_Id": "9fc22f37c54f4de082a0c46f2840b0e3"
    },
    "m_Name": "DDY",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -668.000244140625,
            "y": 244.0000457763672,
            "width": 131.50006103515626,
            "height": 93.99998474121094
        }
    },
    "m_Slots": [
        {
            "m_Id": "6725036b153b4e1da44da6ce9e6fade7"
        },
        {
            "m_Id": "065d0411d9714a44a01d7d2b4d6523d1"
        }
    ],
    "synonyms": [
        "derivative"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "3d9d62d5241c41328d69c2730a93cc74",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "45f91cf7fd434f8a91c5ef6ff7fd6043",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Normal",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "8187c346069145e3bfbf5e8d27025cf9"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Normal"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "4aa0437ecd0f41aeaed574bd467e4665",
    "m_Id": 11,
    "m_DisplayName": "DDY",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "DDY",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.PositionNode",
    "m_ObjectId": "502f9eb8b4d54c549f374a170bf3c6c5",
    "m_Group": {
        "m_Id": "6251f5292d5b4c228867ceded03d9afe"
    },
    "m_Name": "Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1944.000244140625,
            "y": 91.0000228881836,
            "width": 206.0,
            "height": 130.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "65ee3446f8ca4b74a7e93f35ccc30c0e"
        }
    ],
    "synonyms": [
        "location"
    ],
    "m_Precision": 1,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2,
    "m_PositionSource": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "50986e24efe54575ac069b245ac57fdc",
    "m_Id": 0,
    "m_DisplayName": "Emission",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Emission",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 1,
    "m_DefaultColor": {
        "r": 0.0,
        "g": 0.0,
        "b": 0.0,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "54f18cfc71e14a4984aa0f2a344d84eb",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "5a4dabe85ede4ee49dc52fa8a855fc32",
    "m_Id": 7,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInUnlitSubTarget",
    "m_ObjectId": "5fe42c351be04b7789ad62f15f001d6f"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "60b69faeaa0b4a2bb8158bfebe048214",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "efdf4183f23b467ca4bd049122142568"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Position"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "6251f5292d5b4c228867ceded03d9afe",
    "m_Title": "Generating Face Normals",
    "m_Position": {
        "x": -1971.500244140625,
        "y": -7.499958038330078
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "63fb1cfc046546db992e7a697ed1b372",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "6492b0bc4d244dc28fcf9c9ed271bb7a",
    "m_Title": "",
    "m_Content": "By default, a texture sampler uses DDX and DDY internally to compute the proper mip level.  Here we're doing it manually to fix a discontinuity seam.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -789.5000610351563,
        "y": 356.5000305175781,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "9fc22f37c54f4de082a0c46f2840b0e3"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalizeNode",
    "m_ObjectId": "657f1cf219a94cd7a2310cb268b68f65",
    "m_Group": {
        "m_Id": "6251f5292d5b4c228867ceded03d9afe"
    },
    "m_Name": "Normalize",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1399.500244140625,
            "y": 81.99996948242188,
            "width": 208.0,
            "height": 278.0000305175781
        }
    },
    "m_Slots": [
        {
            "m_Id": "8f8236f0e3eb4ed7866febf3dbe4ecbf"
        },
        {
            "m_Id": "1e6fe2c5e7834a709519e5d31d54cbe1"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "65ee3446f8ca4b74a7e93f35ccc30c0e",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "6725036b153b4e1da44da6ce9e6fade7",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "6a196a87cf594dbe81446497864eae14",
    "m_Id": 1,
    "m_DisplayName": "Center",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Center",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.5,
        "y": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "6a5f47ae933c41e99f226b6e60ba57ce",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "72e0804c093e4400897d5cc22e1f066e",
    "m_Id": 0,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "79fc7be331f747389a542a45468d6b83",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DDYNode",
    "m_ObjectId": "7d60ed2d45894499bfd63a62a3480828",
    "m_Group": {
        "m_Id": "6251f5292d5b4c228867ceded03d9afe"
    },
    "m_Name": "DDY",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1701.0001220703125,
            "y": 35.0,
            "width": 131.5,
            "height": 94.00001525878906
        }
    },
    "m_Slots": [
        {
            "m_Id": "85cc5228dd8546338a8aefc6c0946192"
        },
        {
            "m_Id": "162162b5d3fa403393ac21e67c0d6529"
        }
    ],
    "synonyms": [
        "derivative"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "8187c346069145e3bfbf5e8d27025cf9",
    "m_Id": 0,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "843e28aca1ea4059a9038add136eaa51",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "8501b48a23014d6e88ec70a5010bc739",
    "m_Title": "",
    "m_Content": "When using polar coordinates, you get an ugly seam where the UVs wrap around and meet up. This can be fixed by manually computing the texture mip map derivatives using DDX and DDY.  You need to set the Sample Texture 2D node to \"Gradient\" mode in the Graph Inspector first.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -444.5000305175781,
        "y": 450.0000305175781,
        "width": 200.00001525878907,
        "height": 160.00003051757813
    },
    "m_Group": {
        "m_Id": "9fc22f37c54f4de082a0c46f2840b0e3"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "858faf9f38fb4cbbb5b37f9bd006bc9b",
    "m_Title": "",
    "m_Content": "The results are in World space, so we have to transform to Tangent space if we want to plug the result into the Master Stack.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1166.0001220703125,
        "y": 425.0000305175781,
        "width": 200.00006103515626,
        "height": 100.00003051757813
    },
    "m_Group": {
        "m_Id": "6251f5292d5b4c228867ceded03d9afe"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "85cc5228dd8546338a8aefc6c0946192",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "8baa8a4e83434c26836a078408b015b9",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "8d23bdc81d6841b49626e72f5bc59ab1",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "8f8236f0e3eb4ed7866febf3dbe4ecbf",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "93355243d2cb4a958865044033e8ebd7",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "936aa06f91bc4bd5b672384221ae7c7a",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"583384ba064432b41891bec94e35c8af\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "9ea975d2954b4edc86fb5d0d133d5965",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDTarget",
    "m_ObjectId": "9f28618838ba45e3a33e14db214db778",
    "m_ActiveSubTarget": {
        "m_Id": "0c36a0cb6f0540d19e52e321f3ce5947"
    },
    "m_Datas": [
        {
            "m_Id": "dd748b3793824a8fb308932007f01b4c"
        },
        {
            "m_Id": "f3526e0cf4ce4d72bcd7f5d8c68fe538"
        },
        {
            "m_Id": "6a5f47ae933c41e99f226b6e60ba57ce"
        }
    ],
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false,
    "m_SupportLineRendering": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "9fc22f37c54f4de082a0c46f2840b0e3",
    "m_Title": "Manually Create Texture UV Derivatives",
    "m_Position": {
        "x": -877.0001220703125,
        "y": -7.5
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TangentMaterialSlot",
    "m_ObjectId": "a1b8b00a8937468789ac1216bf31e6e3",
    "m_Id": 0,
    "m_DisplayName": "Tangent",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tangent",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "a661068defb54766a8bdf42d0083d160",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Emission",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "50986e24efe54575ac069b245ac57fdc"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Emission"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "a915288d689b4ea1ae429469c9a34c38",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "a9617e2b910c43c5933b5416d6bdae9a",
    "m_Id": 0,
    "m_DisplayName": "Base Color",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BaseColor",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.5,
        "y": 0.5,
        "z": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.5,
        "g": 0.5,
        "b": 0.5,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "aa110b4d7c0b482eaed378ff91dbd031",
    "m_Title": "DDX Node",
    "m_Content": "The DDX Node allows you to find the difference (the partial derivative) between data in the current pixel and data in the next pixel over to the right in screen space.\n\nThis node only works in the fragment stage.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1013.0000610351563,
        "y": -199.50001525878907,
        "width": 200.0,
        "height": 160.39132690429688
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "aeaf671c7d734f1c89bf90766b52b216",
    "m_Id": 6,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVNode",
    "m_ObjectId": "b04da535db4345c6b59a648f238e45ab",
    "m_Group": {
        "m_Id": "9fc22f37c54f4de082a0c46f2840b0e3"
    },
    "m_Name": "UV",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -852.0001220703125,
            "y": 183.99998474121095,
            "width": 145.0,
            "height": 128.5000457763672
        }
    },
    "m_Slots": [
        {
            "m_Id": "da5c27b1e76d4e8dbe99b28e46ed5fe0"
        }
    ],
    "synonyms": [
        "texcoords",
        "coords",
        "coordinates"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_OutputChannel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "b1970dfa6a8140cda00d540da5bf8741",
    "m_Id": 10,
    "m_DisplayName": "DDX",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "DDX",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b26729d29d724d9f810c582549eba7d1",
    "m_Id": 3,
    "m_DisplayName": "Length Scale",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "LengthScale",
    "m_StageCapability": 3,
    "m_Value": 4.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b3ed18f7483a44ec8f8458a26a382f7e",
    "m_Id": 2,
    "m_DisplayName": "Radial Scale",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "RadialScale",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "c21b44a7e2214e52bd220a95086b315e",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DDXNode",
    "m_ObjectId": "c52ad94f45004847a9a858af379b2cdb",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "DDX",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1149.000244140625,
            "y": -191.50003051757813,
            "width": 127.5001220703125,
            "height": 94.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "f06f2c41a83a430882d6db8d80b5fc14"
        },
        {
            "m_Id": "dbf1a7fc554d4a36bd40fce78b342b88"
        }
    ],
    "synonyms": [
        "derivative"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.ShaderGraph.TransformNode",
    "m_ObjectId": "c8f8e7f6846d46b196fb24d3efa0cf0c",
    "m_Group": {
        "m_Id": "6251f5292d5b4c228867ceded03d9afe"
    },
    "m_Name": "Transform",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1170.000244140625,
            "y": 81.99996948242188,
            "width": 212.50006103515626,
            "height": 340.00006103515627
        }
    },
    "m_Slots": [
        {
            "m_Id": "63fb1cfc046546db992e7a697ed1b372"
        },
        {
            "m_Id": "2505d721f0514a0e9afa9633771a0692"
        }
    ],
    "synonyms": [
        "world",
        "tangent",
        "object",
        "view",
        "screen",
        "convert"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Conversion": {
        "from": 2,
        "to": 3
    },
    "m_ConversionType": 2,
    "m_Normalize": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "d1e9246b2c0c441786c33e492c72e7d8",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.BaseColor",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "a9617e2b910c43c5933b5416d6bdae9a"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.BaseColor"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DDXNode",
    "m_ObjectId": "d5750300ed654bab86d8bc4c2a237d5a",
    "m_Group": {
        "m_Id": "6251f5292d5b4c228867ceded03d9afe"
    },
    "m_Name": "DDX",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1701.0001220703125,
            "y": 127.50001525878906,
            "width": 131.5,
            "height": 94.00001525878906
        }
    },
    "m_Slots": [
        {
            "m_Id": "843e28aca1ea4059a9038add136eaa51"
        },
        {
            "m_Id": "9ea975d2954b4edc86fb5d0d133d5965"
        }
    ],
    "synonyms": [
        "derivative"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "d8825b9b6af64fef975051efd3c2793a",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "da5c27b1e76d4e8dbe99b28e46ed5fe0",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "dbf1a7fc554d4a36bd40fce78b342b88",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "dd748b3793824a8fb308932007f01b4c",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "e05ec86b78a747e982591b586d73fa7f",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Tangent",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "a1b8b00a8937468789ac1216bf31e6e3"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Tangent"
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInTarget",
    "m_ObjectId": "e8a1ef8522294f28bc9b276c58745a33",
    "m_ActiveSubTarget": {
        "m_Id": "5fe42c351be04b7789ad62f15f001d6f"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZWriteControl": 0,
    "m_ZTestMode": 4,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CustomEditorGUI": ""
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "eca9a1892f5f44619758a8a8f8688630"
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget",
    "m_ObjectId": "ee1e4c88cb064cf88766bfda4fb0106b",
    "m_Datas": [],
    "m_ActiveSubTarget": {
        "m_Id": "316c1535eded4ac9bf05c74ad5c2fb3d"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZTestMode": 4,
    "m_ZWriteControl": 0,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CastShadows": true,
    "m_ReceiveShadows": true,
    "m_SupportsLODCrossFade": false,
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "efdf4183f23b467ca4bd049122142568",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "f06f2c41a83a430882d6db8d80b5fc14",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "f3526e0cf4ce4d72bcd7f5d8c68fe538",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "f6e28907f1984a54bb766b660adddbd6",
    "m_Title": "",
    "m_Content": "DDX and DDY find the difference between the position of the current pixel and the one next to it - horizontally (for DDX), and vertically (for DDY). ",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1774.0001220703125,
        "y": 244.50003051757813,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "6251f5292d5b4c228867ceded03d9afe"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "f80ad007e46a4a359cc2926449012a11",
    "m_Id": 4,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleTexture2DNode",
    "m_ObjectId": "fa9b5083b8cb4867903b8de46b69524e",
    "m_Group": {
        "m_Id": "9fc22f37c54f4de082a0c46f2840b0e3"
    },
    "m_Name": "Sample Texture 2D",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -448.0001525878906,
            "y": 50.999996185302737,
            "width": 207.99996948242188,
            "height": 386.00006103515627
        }
    },
    "m_Slots": [
        {
            "m_Id": "72e0804c093e4400897d5cc22e1f066e"
        },
        {
            "m_Id": "f80ad007e46a4a359cc2926449012a11"
        },
        {
            "m_Id": "10f710a58c294f9788655a8c3de28f39"
        },
        {
            "m_Id": "aeaf671c7d734f1c89bf90766b52b216"
        },
        {
            "m_Id": "5a4dabe85ede4ee49dc52fa8a855fc32"
        },
        {
            "m_Id": "936aa06f91bc4bd5b672384221ae7c7a"
        },
        {
            "m_Id": "008696905a014fb69cedf45236fd35cf"
        },
        {
            "m_Id": "79fc7be331f747389a542a45468d6b83"
        },
        {
            "m_Id": "b1970dfa6a8140cda00d540da5bf8741"
        },
        {
            "m_Id": "4aa0437ecd0f41aeaed574bd467e4665"
        }
    ],
    "synonyms": [
        "tex2d"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_TextureType": 0,
    "m_NormalMapSpace": 0,
    "m_EnableGlobalMipBias": true,
    "m_MipSamplingMode": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "fe4c3cc527364c06b551d113c4e0fb2f",
    "m_Name": "",
    "m_ChildObjectList": []
}

