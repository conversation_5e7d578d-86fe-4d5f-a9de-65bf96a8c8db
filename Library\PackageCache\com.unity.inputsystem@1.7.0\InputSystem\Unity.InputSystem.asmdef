{"name": "Unity.InputSystem", "references": ["Unity.ugui"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": true, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.xr.oculus", "expression": "1.0.3", "define": "DISABLE_BUILTIN_INPUT_SYSTEM_OCULUS"}, {"name": "com.unity.xr.googlevr", "expression": "1.0.0", "define": "DISABLE_BUILTIN_INPUT_SYSTEM_GOOGLEVR"}, {"name": "com.unity.xr.openvr", "expression": "1.0.0", "define": "DISABLE_BUILTIN_INPUT_SYSTEM_OPENVR"}, {"name": "com.unity.xr.windowsmr", "expression": "2.0.3", "define": "DISABLE_BUILTIN_INPUT_SYSTEM_WINDOWSMR"}, {"name": "com.unity.modules.vr", "expression": "1.0.0", "define": "UNITY_INPUT_SYSTEM_ENABLE_VR"}, {"name": "com.unity.modules.xr", "expression": "1.0.0", "define": "UNITY_INPUT_SYSTEM_ENABLE_XR"}, {"name": "com.unity.modules.physics", "expression": "1.0.0", "define": "UNITY_INPUT_SYSTEM_ENABLE_PHYSICS"}, {"name": "com.unity.modules.physics2d", "expression": "1.0.0", "define": "UNITY_INPUT_SYSTEM_ENABLE_PHYSICS2D"}, {"name": "com.unity.ugui", "expression": "1.0.0", "define": "UNITY_INPUT_SYSTEM_ENABLE_UI"}, {"name": "Unity", "expression": "[2021.3.11,2022.1)", "define": "HAS_SET_LOCAL_POSITION_AND_ROTATION"}, {"name": "Unity", "expression": "[2022.1.19,2022.2)", "define": "HAS_SET_LOCAL_POSITION_AND_ROTATION"}, {"name": "Unity", "expression": "2022.2", "define": "HAS_SET_LOCAL_POSITION_AND_ROTATION"}], "noEngineReferences": false}