* [Universal Render Pipeline](index.md)
* [Requirements](requirements.md)
  * [Building for Closed platforms](Building-For-Consoles.md)
* [What's new in URP](whats-new/urp-whats-new.md)
* [Features]()
  * [Feature Comparison with the Built-in Render Pipeline](universalrp-builtin-feature-comparison.md)
* [Getting started](InstallingAndConfiguringURP.md)
  * [Create a project with URP](creating-a-new-project-with-urp.md)
  * [Install URP into an existing Project](InstallURPIntoAProject.md)
  * [Package samples](package-samples.md)
    * [URP Package Samples](package-sample-urp-package-samples.md)
  * [Scene Templates](scene-templates.md)
  * [Quality Settings in URP](birp-onboarding/quality-settings-location.md)
  * [Change Quality settings with code](quality/quality-settings-through-code.md)
  * [Understand performance](understand-performance.md)
  * [Configure for better performance](configure-for-better-performance.md)
* [Render Pipeline Concepts](urp-concepts.md)
  * [The URP Asset](universalrp-asset.md)
  * [URP Global Settings](urp-global-settings.md)
  * [Universal Renderer](urp-universal-renderer.md)
  * [Deferred Rendering Path](rendering/deferred-rendering-path.md)
  * [Forward+ Rendering Path](rendering/forward-plus-rendering-path.md)
  * [Graphics settings window reference in URP](urp-global-settings.md)
  * [Pre-built effects (Renderer Features)](urp-renderer-feature.md)
    * [How to add a Renderer Feature](urp-renderer-feature-how-to-add.md)
    * [Render Objects Renderer Feature](renderer-features/renderer-feature-render-objects-landing.md)	
      * [Example: How to create a custom rendering effect using the Render Objects Renderer Feature](renderer-features/how-to-custom-effect-render-objects.md)
      * [Render Objects Renderer Feature reference](renderer-features/renderer-feature-render-objects.md)
    * [Decal Renderer Feature](renderer-feature-decal-landing.md)
	    * [Decal Renderer Feature](renderer-feature-decal.md)
	    * [Decal Shader Graph](decal-shader.md)
    * [Screen Space Ambient Occlusion (SSAO) Renderer Feature](post-processing-ssao.md)
    * [Screen Space Shadows Renderer Feature](renderer-feature-screen-space-shadows.md)
    * [Full Screen Pass Renderer Feature](renderer-features/renderer-feature-full-screen-pass-landing.md)
      * [How to create a custom post-processing effect](post-processing/post-processing-custom-effect-low-code.md)
      * [Full Screen Pass Renderer Feature reference](renderer-features/renderer-feature-full-screen-pass.md)
* [Upgrade guides](upgrade-guides.md)
  * [Render Pipeline Converter](features/rp-converter.md)
  * [Upgrading to URP 14](upgrade-guide-2022-2.md)
  * [Upgrading to URP 13](upgrade-guide-2022-1.md)
  * [Upgrading to URP 12.0.x](upgrade-guide-2021-2.md)
  * [Upgrading to URP 11.0.x](upgrade-guide-11-0-x.md)
  * [Upgrading to URP 10.1.x](upgrade-guide-10-1-x.md)
  * [Upgrading to URP 10.0.x](upgrade-guide-10-0-x.md)
  * [Upgrading to URP 9.0.x](upgrade-guide-9-0-x.md)
  * [Upgrading to URP 8.2.0](upgrade-guide-8-2-0.md)
  * [Upgrading to URP 8.1.0](upgrade-guide-8-1-0.md)
  * [Upgrading to URP 8.0.0](upgrade-guide-8-0-0.md)
  * [Upgrading to URP 7.4.0](upgrade-guide-7-4-0.md)
  * [Upgrading to URP 7.3.0](upgrade-guide-7-3-0.md)
  * [Upgrading to URP 7.2.0](upgrade-guide-7-2-0.md)
  * [Upgrading from LWRP to URP](upgrade-lwrp-to-urp.md)
* [Rendering](rendering-in-universalrp.md)
  * [Rendering Layers](features/rendering-layers.md)
* [Lighting](lighting.md)
  * [Light component reference](light-component.md)
  * [Lighting Mode](urp-lighting-mode.md)
  * [The Universal Additional Light Data component](universal-additional-light-data.md)
  * [Shadows in the Universal Render Pipeline](Shadows-in-URP.md)
  * [Reflection probes](lighting/reflection-probes.md)
  * [Lens Flare asset](shared/lens-flare/lens-flare-asset.md)
* [Cameras](cameras.md)
  * [The Universal Additional Camera Data component](universal-additional-camera-data.md)
  * [Render Type](camera-types-and-render-type.md)
  * [Working with multiple cameras](cameras-multiple.md)
    * [Camera Stacking](camera-stacking.md)
    * [Rendering from multiple Cameras to the same render target](rendering-to-the-same-render-target.md)
    * [Rendering to a Render Texture](rendering-to-a-render-texture.md)
  * [Clearing, rendering order and overdraw](cameras-advanced.md)
  * [Anti-aliasing](anti-aliasing.md)
  * [Camera component reference](camera-component-reference.md)

* [Post-processing](integration-with-post-processing.md)
  * [How to configure](integration-with-post-processing.md#post-proc-how-to)
  * [HDR Output](post-processing/hdr-output.md)
  * [Volumes](Volumes.md)
    * [Volume Profile](VolumeProfile.md)
    * [Volume Overrides](VolumeOverrides.md)
  * [Effect List](EffectList.md)
    * [Ambient Occlusion](post-processing-ssao.md)
    * [Bloom](post-processing-bloom.md)
    * [Channel Mixer](Post-Processing-Channel-Mixer.md)
    * [Chromatic Aberration](post-processing-chromatic-aberration.md)
    * [Color Adjustments](Post-Processing-Color-Adjustments.md)
    * [Color Curves](Post-Processing-Color-Curves.md)
    * [Depth of Field](post-processing-depth-of-field.md)
    * [Film Grain](Post-Processing-Film-Grain.md)
    * [Lens Distortion](Post-Processing-Lens-Distortion.md)
    * [Lift, Gamma, and Gain](Post-Processing-Lift-Gamma-Gain.md)
    * [Motion Blur](Post-Processing-Motion-Blur.md)
    * [Panini Projection](Post-Processing-Panini-Projection.md)
    * [Shadows Midtones Highlights](Post-Processing-Shadows-Midtones-Highlights.md)
    * [Split Toning](Post-Processing-Split-Toning.md)
    * [Tonemapping](post-processing-tonemapping.md)
    * [Vignette](post-processing-vignette.md)
    * [White Balance](Post-Processing-White-Balance.md)
    * [Lens Flare](shared/lens-flare/lens-flare-component.md)
  * [Custom Post-processing](post-processing/custom-post-processing.md)
    * [How to create a custom post-processing effect](post-processing/post-processing-custom-effect-low-code.md)

* [Shaders and Materials](shaders-in-universalrp.md)
  * [Shading Models](shading-model.md)
  * [Material Variants](materialvariant-URP.md)
  * [Complex Lit](shader-complex-lit.md)
  * [Lit](lit-shader.md)
  * [Simple Lit](simple-lit-shader.md)
  * [Baked Lit](baked-lit-shader.md)
  * [Unlit](unlit-shader.md)
  * [Terrain Lit](shader-terrain-lit.md)
  * [Particles Lit](particles-lit-shader.md)
  * [Particles Simple Lit](particles-simple-lit-shader.md)
  * [Particles Unlit](particles-unlit-shader.md)
  * [Decal](decal-shader.md)
  * [Upgrading shaders from Built-in](upgrading-your-shaders.md)
    * [Upgrade custom shaders for URP compatibility](urp-shaders/birp-urp-custom-shader-upgrade-guide.md)
  * [Shader stripping](shader-stripping.md)
  * [Writing custom shaders](writing-custom-shaders-urp.md)
    * [Creating a sample scene](writing-shaders-urp-basic-prerequisites.md)
    * [URP basic unlit shader](writing-shaders-urp-basic-unlit-structure.md)
    * [URP unlit shader with color input](writing-shaders-urp-unlit-color.md)
    * [Drawing a texture](writing-shaders-urp-unlit-texture.md)
    * [Visualizing normal vectors](writing-shaders-urp-unlit-normals.md)
    * [Reconstruct the world space positions](writing-shaders-urp-reconstruct-world-position.md)
  * [URP ShaderLab Pass tags](urp-shaders/urp-shaderlab-pass-tags.md)
* [Custom rendering and post-processing](customizing-urp.md)
  * [Custom render passes](renderer-features/custom-rendering-passes.md)
    * [Custom render pass workflow in URP](renderer-features/custom-rendering-pass-workflow-in-urp.md)
    * [Scriptable Render Passes](renderer-features/scriptable-render-passes.md)
      * [Scriptable Render Passes](renderer-features/intro-to-scriptable-render-passes.md)
      * [Write a Scriptable Render Pass](renderer-features/write-a-scriptable-render-pass.md)
      * [Inject a pass via scripting](customize/inject-render-pass-via-script.md)    
    * [Scriptable Renderer Features](renderer-features/scriptable-renderer-features/scriptable-renderer-features-landing.md)
      * [Introduction to Scriptable Renderer Features](renderer-features/scriptable-renderer-features/intro-to-scriptable-renderer-features.md)
      * [Inject a custom render pass using a Scriptable Renderer Feature](renderer-features/scriptable-renderer-features/inject-a-pass-using-a-scriptable-renderer-feature.md)
      * [Apply a Scriptable Renderer Feature to a specific camera type](renderer-features/scriptable-renderer-features/apply-scriptable-feature-to-specific-camera.md)
      * [Example of a complete Scriptable Renderer Feature](renderer-features/create-custom-renderer-feature.md)  
    * [Using textures](working-with-textures.md)
      * [URP blit best practices](customize/blit-overview.md)
      * [Perform a full screen blit in URP](renderer-features/how-to-fullscreen-blit.md)
      * [Blit input and output textures](customize/blit-to-rthandle.md)
      * [Blit multiple RTHandle textures](customize/blit-multiple-rthandles.md)
  * [Injection points reference](customize/custom-pass-injection-points.md)
  * [Scriptable Renderer Feature and Scriptable Render Pass API reference](renderer-features/scriptable-renderer-features/scriptable-renderer-feature-reference.md)
* [Optimization](urp-optimization.md)
  * [Rendering Debugger](features/rendering-debugger.md)
  * [Optimize for better performance](optimize-for-better-performance.md)
  * [Update Quality Setting Presets for URP](birp-onboarding/quality-presets.md)
* [2D graphic features](2d-index.md)
  * [Introduction to Lights 2D](Lights-2D-intro.md)
    * [Requirements and setup](Setup.md)
    * [Configuring the 2D Renderer Asset](2DRendererData_overview.md)
      * [HDR emulation scale](HDREmulationScale.md)
      * [Light Blend Styles](LightBlendStyles.md)
    * [Preparing and upgrading](PrepShader.md)
    * [Normal map and mask Textures](SecondaryTextures.md)
    * [Common Lights 2D properties](2DLightProperties.md)
      * [Lights 2D types and specific properties](LightTypes.md)
    * [2D Shadows](2DShadows.md)
    * [2D Renderer and Shader Graph](ShaderGraph.md)
  * [2D Pixel Perfect](2d-pixelperfect.md)
    * [Cinemachine Pixel Perfect extension](pixel-cinemachine.md)

* [Frequently asked questions (FAQ)](faq.md)
* [Known issues](known-issues.md)
