using UnityEngine;
using UnityEditor;
using VRoidFaceCustomization;
using System.Collections.Generic;
using System.Text;
using System.Linq;

namespace VRoidFaceCustomization.Editor
{
    /// <summary>
    /// 骨骼状态记录器
    /// 记录和对比DynamicClothBones的变化
    /// </summary>
    public class VRM10BoneStateRecorder : EditorWindow
    {
        private VRM10ClothBinder clothBinder;
        private List<BoneStateSnapshot> snapshots = new List<BoneStateSnapshot>();
        private Vector2 scrollPosition;
        
        [System.Serializable]
        public class BoneStateSnapshot
        {
            public string snapshotName;
            public string timestamp;
            public int totalBoneCount;
            public int dynamicBoneCount;
            public List<string> dynamicBoneNames = new List<string>();
        }
        
        [MenuItem("Tools/VRM 1.0/Bone State Recorder")]
        public static void ShowWindow()
        {
            GetWindow<VRM10BoneStateRecorder>("骨骼状态记录器");
        }
        
        private void OnGUI()
        {
            EditorGUILayout.LabelField("骨骼状态记录器", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("记录DynamicClothBones节点的变化情况", MessageType.Info);
            EditorGUILayout.Space();
            
            // 设置
            clothBinder = (VRM10ClothBinder)EditorGUILayout.ObjectField(
                "服装绑定器", clothBinder, typeof(VRM10ClothBinder), true);
            
            EditorGUILayout.Space();
            
            // 快速记录按钮
            EditorGUILayout.LabelField("测试A：验证问题", EditorStyles.boldLabel);
            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("1.初始状态"))
            {
                RecordState("1.初始状态");
            }

            if (GUILayout.Button("2.直接穿戴(有问题)"))
            {
                RecordState("2.直接穿戴原版(有问题)");
            }

            if (GUILayout.Button("3.脱下后"))
            {
                RecordState("3.脱下服装后");
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.LabelField("测试B：验证修复", EditorStyles.boldLabel);
            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("4.提取工具后"))
            {
                RecordState("4.运行提取工具后");
            }

            if (GUILayout.Button("5.穿戴原版(正常)"))
            {
                RecordState("5.提取后穿戴原版(正常)");
            }

            if (GUILayout.Button("6.穿戴提取版"))
            {
                RecordState("6.穿戴提取版服装");
            }

            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();

            // 诊断按钮
            EditorGUILayout.LabelField("诊断工具", EditorStyles.boldLabel);
            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("检查DynamicClothBones"))
            {
                CheckDynamicBonesNode();
            }

            if (GUILayout.Button("检查VRoid设置"))
            {
                CheckVRoidSettings();
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space();

            // 操作按钮
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("清空记录"))
            {
                snapshots.Clear();
            }
            
            if (GUILayout.Button("导出记录"))
            {
                ExportRecords();
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            // 显示记录
            if (snapshots.Count > 0)
            {
                EditorGUILayout.LabelField($"已记录 {snapshots.Count} 个状态", EditorStyles.boldLabel);
                
                scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(200));
                
                for (int i = 0; i < snapshots.Count; i++)
                {
                    var snapshot = snapshots[i];
                    
                    EditorGUILayout.BeginVertical("box");
                    
                    EditorGUILayout.LabelField($"{i + 1}. {snapshot.snapshotName}", EditorStyles.boldLabel);
                    EditorGUILayout.LabelField($"时间: {snapshot.timestamp}");
                    EditorGUILayout.LabelField($"总骨骼数: {snapshot.totalBoneCount}");
                    EditorGUILayout.LabelField($"动态骨骼数: {snapshot.dynamicBoneCount}");
                    
                    // 对比按钮
                    if (i > 0)
                    {
                        if (GUILayout.Button($"与上一状态对比"))
                        {
                            CompareSnapshots(snapshots[i - 1], snapshot);
                        }
                    }
                    
                    EditorGUILayout.EndVertical();
                }
                
                EditorGUILayout.EndScrollView();
                
                // 全局对比
                if (snapshots.Count >= 2)
                {
                    if (GUILayout.Button("生成完整变化报告"))
                    {
                        GenerateChangeReport();
                    }
                }
            }
        }
        
        private void RecordState(string snapshotName)
        {
            if (clothBinder == null)
            {
                EditorUtility.DisplayDialog("错误", "请先设置服装绑定器", "确定");
                return;
            }
            
            var snapshot = new BoneStateSnapshot
            {
                snapshotName = snapshotName,
                timestamp = System.DateTime.Now.ToString("HH:mm:ss")
            };
            
            // 获取所有骨骼
            var allBones = clothBinder.transform.GetComponentsInChildren<Transform>();
            snapshot.totalBoneCount = allBones.Length;
            
            // 获取动态骨骼
            var dynamicParent = clothBinder.transform.Find("DynamicClothBones");
            if (dynamicParent != null)
            {
                var dynamicBones = dynamicParent.GetComponentsInChildren<Transform>();
                snapshot.dynamicBoneCount = dynamicBones.Length - 1; // 减去父节点本身
                
                foreach (var bone in dynamicBones)
                {
                    if (bone != dynamicParent) // 排除父节点
                    {
                        snapshot.dynamicBoneNames.Add(bone.name);
                    }
                }
            }
            else
            {
                snapshot.dynamicBoneCount = 0;
            }
            
            snapshots.Add(snapshot);
            
            Debug.Log($"[BoneStateRecorder] 记录状态: {snapshotName}");
            Debug.Log($"  总骨骼数: {snapshot.totalBoneCount}");
            Debug.Log($"  动态骨骼数: {snapshot.dynamicBoneCount}");
            if (snapshot.dynamicBoneCount > 0)
            {
                Debug.Log($"  动态骨骼列表: {string.Join(", ", snapshot.dynamicBoneNames.Take(5))}...");
            }
            
            Repaint();
        }
        
        private void CompareSnapshots(BoneStateSnapshot before, BoneStateSnapshot after)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"=== 骨骼状态对比 ===");
            sb.AppendLine($"前: {before.snapshotName} ({before.timestamp})");
            sb.AppendLine($"后: {after.snapshotName} ({after.timestamp})");
            sb.AppendLine();
            
            // 总骨骼数变化
            int totalChange = after.totalBoneCount - before.totalBoneCount;
            sb.AppendLine($"总骨骼数变化: {before.totalBoneCount} → {after.totalBoneCount} ({totalChange:+0;-0;0})");
            
            // 动态骨骼数变化
            int dynamicChange = after.dynamicBoneCount - before.dynamicBoneCount;
            sb.AppendLine($"动态骨骼数变化: {before.dynamicBoneCount} → {after.dynamicBoneCount} ({dynamicChange:+0;-0;0})");
            sb.AppendLine();
            
            // 新增的动态骨骼
            var addedBones = after.dynamicBoneNames.Except(before.dynamicBoneNames).ToList();
            if (addedBones.Count > 0)
            {
                sb.AppendLine($"新增动态骨骼 ({addedBones.Count}):");
                foreach (var bone in addedBones.Take(10)) // 只显示前10个
                {
                    sb.AppendLine($"  + {bone}");
                }
                if (addedBones.Count > 10)
                {
                    sb.AppendLine($"  ... 还有 {addedBones.Count - 10} 个");
                }
                sb.AppendLine();
            }
            
            // 移除的动态骨骼
            var removedBones = before.dynamicBoneNames.Except(after.dynamicBoneNames).ToList();
            if (removedBones.Count > 0)
            {
                sb.AppendLine($"移除动态骨骼 ({removedBones.Count}):");
                foreach (var bone in removedBones.Take(10))
                {
                    sb.AppendLine($"  - {bone}");
                }
                if (removedBones.Count > 10)
                {
                    sb.AppendLine($"  ... 还有 {removedBones.Count - 10} 个");
                }
            }
            
            Debug.Log(sb.ToString());
            
            EditorUtility.DisplayDialog("对比结果", 
                $"对比完成！详细结果已输出到控制台。\n\n" +
                $"总骨骼变化: {totalChange:+0;-0;0}\n" +
                $"动态骨骼变化: {dynamicChange:+0;-0;0}\n" +
                $"新增: {addedBones.Count}, 移除: {removedBones.Count}", "确定");
        }
        
        private void GenerateChangeReport()
        {
            if (snapshots.Count < 2) return;
            
            var sb = new StringBuilder();
            sb.AppendLine("=== 完整骨骼状态变化报告 ===");
            sb.AppendLine($"记录时间范围: {snapshots[0].timestamp} - {snapshots[snapshots.Count - 1].timestamp}");
            sb.AppendLine($"总记录数: {snapshots.Count}");
            sb.AppendLine();
            
            for (int i = 1; i < snapshots.Count; i++)
            {
                var prev = snapshots[i - 1];
                var curr = snapshots[i];
                
                sb.AppendLine($"{i}. {prev.snapshotName} → {curr.snapshotName}");
                sb.AppendLine($"   总骨骼: {prev.totalBoneCount} → {curr.totalBoneCount} ({curr.totalBoneCount - prev.totalBoneCount:+0;-0;0})");
                sb.AppendLine($"   动态骨骼: {prev.dynamicBoneCount} → {curr.dynamicBoneCount} ({curr.dynamicBoneCount - prev.dynamicBoneCount:+0;-0;0})");
                sb.AppendLine();
            }
            
            Debug.Log(sb.ToString());
        }
        
        private void ExportRecords()
        {
            if (snapshots.Count == 0)
            {
                EditorUtility.DisplayDialog("提示", "没有记录可以导出", "确定");
                return;
            }
            
            string fileName = $"BoneStateRecords_{System.DateTime.Now:yyyyMMdd_HHmmss}.txt";
            string path = EditorUtility.SaveFilePanel("导出骨骼状态记录", "", fileName, "txt");
            
            if (!string.IsNullOrEmpty(path))
            {
                var sb = new StringBuilder();
                sb.AppendLine("骨骼状态记录导出");
                sb.AppendLine($"导出时间: {System.DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                sb.AppendLine($"角色: {clothBinder?.name ?? "未设置"}");
                sb.AppendLine(new string('=', 50));
                sb.AppendLine();
                
                foreach (var snapshot in snapshots)
                {
                    sb.AppendLine($"状态: {snapshot.snapshotName}");
                    sb.AppendLine($"时间: {snapshot.timestamp}");
                    sb.AppendLine($"总骨骼数: {snapshot.totalBoneCount}");
                    sb.AppendLine($"动态骨骼数: {snapshot.dynamicBoneCount}");
                    
                    if (snapshot.dynamicBoneCount > 0)
                    {
                        sb.AppendLine("动态骨骼列表:");
                        foreach (var boneName in snapshot.dynamicBoneNames)
                        {
                            sb.AppendLine($"  - {boneName}");
                        }
                    }
                    
                    sb.AppendLine();
                }
                
                System.IO.File.WriteAllText(path, sb.ToString());
                EditorUtility.DisplayDialog("成功", $"记录已导出到: {path}", "确定");
            }
        }

        private void CheckDynamicBonesNode()
        {
            if (clothBinder == null)
            {
                EditorUtility.DisplayDialog("错误", "请先设置服装绑定器", "确定");
                return;
            }

            var sb = new StringBuilder();
            sb.AppendLine("=== DynamicClothBones 节点检查 ===");

            var dynamicParent = clothBinder.transform.Find("DynamicClothBones");
            if (dynamicParent != null)
            {
                sb.AppendLine("✅ DynamicClothBones 节点存在");
                sb.AppendLine($"位置: {dynamicParent.position}");
                sb.AppendLine($"旋转: {dynamicParent.rotation.eulerAngles}");
                sb.AppendLine($"缩放: {dynamicParent.localScale}");
                sb.AppendLine($"子节点数量: {dynamicParent.childCount}");

                if (dynamicParent.childCount > 0)
                {
                    sb.AppendLine("子骨骼列表:");
                    for (int i = 0; i < dynamicParent.childCount && i < 10; i++)
                    {
                        var child = dynamicParent.GetChild(i);
                        sb.AppendLine($"  {i + 1}. {child.name}");
                    }
                    if (dynamicParent.childCount > 10)
                    {
                        sb.AppendLine($"  ... 还有 {dynamicParent.childCount - 10} 个");
                    }
                }
            }
            else
            {
                sb.AppendLine("❌ DynamicClothBones 节点不存在");
                sb.AppendLine("这可能说明:");
                sb.AppendLine("1. 预初始化功能未启用");
                sb.AppendLine("2. 还没有穿戴过需要动态骨骼的服装");
                sb.AppendLine("3. VRoid模式未启用");
            }

            Debug.Log(sb.ToString());

            string message = dynamicParent != null ?
                $"DynamicClothBones节点存在\n子节点数: {dynamicParent.childCount}" :
                "DynamicClothBones节点不存在";

            EditorUtility.DisplayDialog("检查结果", message, "确定");
        }

        private void CheckVRoidSettings()
        {
            if (clothBinder == null)
            {
                EditorUtility.DisplayDialog("错误", "请先设置服装绑定器", "确定");
                return;
            }

            var sb = new StringBuilder();
            sb.AppendLine("=== VRoid设置检查 ===");

            // 使用反射获取私有字段
            var enableVRoidMode = GetPrivateField<bool>(clothBinder, "enableVRoidMode");
            var preInitializeBones = GetPrivateField<bool>(clothBinder, "preInitializeBones");
            var forceSyncBinding = GetPrivateField<bool>(clothBinder, "forceSyncBinding");
            var stabilizationFrames = GetPrivateField<int>(clothBinder, "stabilizationFrames");

            sb.AppendLine($"Enable VRoid Mode: {enableVRoidMode}");
            sb.AppendLine($"Pre Initialize Bones: {preInitializeBones}");
            sb.AppendLine($"Force Sync Binding: {forceSyncBinding}");
            sb.AppendLine($"Stabilization Frames: {stabilizationFrames}");

            // 检查设置是否正确
            bool settingsCorrect = enableVRoidMode && preInitializeBones && forceSyncBinding;

            if (settingsCorrect)
            {
                sb.AppendLine("\n✅ VRoid设置正确");
            }
            else
            {
                sb.AppendLine("\n❌ VRoid设置有问题，建议:");
                if (!enableVRoidMode) sb.AppendLine("- 启用 Enable VRoid Mode");
                if (!preInitializeBones) sb.AppendLine("- 启用 Pre Initialize Bones");
                if (!forceSyncBinding) sb.AppendLine("- 启用 Force Sync Binding");
            }

            Debug.Log(sb.ToString());

            string message = settingsCorrect ? "VRoid设置正确" : "VRoid设置需要调整，请查看控制台";
            EditorUtility.DisplayDialog("设置检查", message, "确定");
        }

        private T GetPrivateField<T>(object obj, string fieldName)
        {
            var field = obj.GetType().GetField(fieldName,
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            return field != null ? (T)field.GetValue(obj) : default(T);
        }
    }
}
