<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" editor-extension-mode="False">
    <Style src="project://database/Assets/Samples/Core%20RP%20Library/Common/Scripts/Resources/SamplesSelectionUSS.uss?fileID=7433441132597879392&amp;guid=dace8ee3f59c99149ad4c1db64b635fe&amp;type=3#SamplesSelectionUSS" />
    <ui:ScrollView horizontal-scroller-visibility="Hidden">
        <ui:Button text="Open in Window" name="OpenInWindowButton" style="margin-top: 15px; margin-bottom: 0; width: 100%; align-self: center;" />
        <ui:VisualElement name="RequiredSettingsBox" style="flex-grow: 1; top: auto; left: auto; bottom: auto; right: auto; margin-left: 0; margin-right: 0; margin-top: 15px; margin-bottom: 15px; padding-left: 5px; padding-right: 5px; padding-top: 5px; padding-bottom: 5px; align-self: flex-start; border-left-color: rgba(0, 0, 0, 0.25); border-right-color: rgba(0, 0, 0, 0.25); border-top-color: rgba(0, 0, 0, 0.25); border-bottom-color: rgba(0, 0, 0, 0.25); flex-direction: column; flex-wrap: nowrap; border-left-width: 1px; border-right-width: 1px; border-top-width: 1px; border-bottom-width: 1px; border-top-left-radius: 5px; border-bottom-left-radius: 5px; border-top-right-radius: 5px; border-bottom-right-radius: 5px; background-color: rgba(255, 255, 255, 0.1); justify-content: space-around; width: 100%;">
            <ui:VisualElement style="flex-grow: 1; flex-direction: row; margin-bottom: 5px;">
                <ui:VisualElement class="warningIcon" />
                <ui:Label text="The following settings are required for the samples to display properly:" style="white-space: normal; justify-content: center; align-self: center; width: auto; margin-right: 39px;" />
            </ui:VisualElement>
            <ui:VisualElement name="RequiredSettingsList" style="flex-grow: 1; background-color: rgba(0, 0, 0, 0);">
                <ui:Button text="Button" name="PreviewButton" class="requiredSettingButton" style="border-left-color: rgba(0, 0, 0, 0.25); border-right-color: rgba(0, 0, 0, 0.25); border-top-color: rgba(0, 0, 0, 0.25); border-bottom-color: rgba(0, 0, 0, 0.25); margin-left: 0; padding-left: 12px;" />
            </ui:VisualElement>
        </ui:VisualElement>
        <ui:Label tabindex="-1" text="Samples Selection" display-tooltip-when-elided="true" view-data-key="Headline" binding-path="headline" name="headline" style="-unity-font-style: bold; color: rgb(184, 226, 229); -unity-text-align: middle-center; font-size: 22px; white-space: normal; height: auto; width: auto; margin-bottom: 15px; margin-top: 15px; justify-content: center; align-self: center; flex-wrap: wrap; flex-direction: column; align-items: auto; margin-left: 0; margin-right: 0; padding-left: 0; padding-right: 0;" />
        <ui:VisualElement name="intro" style="flex-grow: 1; background-color: rgba(0, 0, 0, 0); flex-direction: column; flex-wrap: nowrap; padding-top: 0; padding-bottom: 0; padding-left: 0; padding-right: 0; height: auto; align-self: stretch; justify-content: center; align-items: stretch; width: 100%; margin-left: 0; margin-right: 0; margin-top: 15px; margin-bottom: 15px;" />
        <ui:VisualElement name="SamplesSelection" style="flex-grow: 1; background-color: rgba(0, 0, 0, 0.16); margin-right: 0; -unity-background-scale-mode: scale-to-fit; width: 100%; margin-left: 0; align-self: flex-start; align-items: stretch; justify-content: space-around; padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 9px; -unity-text-align: upper-left; white-space: normal; margin-top: 15px; margin-bottom: 15px; border-top-left-radius: 5px; border-top-right-radius: 5px; border-bottom-right-radius: 5px; border-bottom-left-radius: 5px; border-left-color: rgba(0, 0, 0, 0.25); border-right-color: rgba(0, 0, 0, 0.25); border-top-color: rgba(0, 0, 0, 0.25); border-bottom-color: rgba(0, 0, 0, 0.25); border-left-width: 1px; border-right-width: 1px; border-top-width: 1px; border-bottom-width: 1px;">
            <ui:VisualElement name="selectionButtons" style="flex-grow: 1; background-color: rgba(0, 0, 0, 0); flex-direction: row; justify-content: flex-start; margin-left: 0; margin-right: 0; margin-top: 0; align-self: auto; width: 100%; align-items: auto;">
                <ui:Label text="Samples" style="-unity-font-style: bold; align-self: center; margin-left: 0; margin-right: 0; padding-right: 0; padding-left: 0; align-items: stretch; justify-content: flex-start;" />
                <ui:DropdownField index="-1" choices="System.Collections.Generic.List`1[System.String]" name="SampleDropDown" tooltip="Select the Sample to Instantiate" style="opacity: 1; display: flex; visibility: visible; overflow: hidden; height: 18px; width: 50%; justify-content: space-evenly; align-items: flex-end; flex-direction: row; align-self: center; -unity-text-align: middle-center; padding-left: 0; padding-right: 0; padding-top: 0; padding-bottom: 0; margin-left: 5px; margin-right: 8px; margin-top: 0; margin-bottom: 0; text-overflow: ellipsis; min-height: auto; max-width: none; min-width: auto;" />
                <ui:Button text="&lt;&lt;" name="switchBack" style="height: 18px; margin-left: 0; margin-right: 5px; margin-bottom: 0; padding-left: 0; padding-bottom: 0; padding-right: 0; padding-top: 0; min-height: auto; width: auto; justify-content: flex-start; align-self: center; align-items: stretch; margin-top: 0; border-left-width: 0; border-right-width: 0; border-top-width: 0; border-bottom-width: 0; -unity-text-align: middle-center; white-space: nowrap; max-width: none; max-height: none; min-width: 20px; font-size: 10px; -unity-font-style: bold;" />
                <ui:Button text="&gt;&gt;" name="switchForward" style="height: 18px; margin-left: 0; margin-right: 0; margin-bottom: 0; padding-left: 0; padding-bottom: 0; padding-right: 0; padding-top: 0; min-height: auto; width: auto; justify-content: flex-start; align-self: center; align-items: stretch; margin-top: 0; border-left-width: 0; border-right-width: 0; border-top-width: 0; border-bottom-width: 0; -unity-text-align: middle-center; white-space: nowrap; font-size: 10px; -unity-font-style: bold; min-width: 20px;" />
            </ui:VisualElement>
            <ui:VisualElement name="sampleInfosContainer" style="flex-grow: 1; background-color: rgba(0, 0, 0, 0); min-width: auto; min-height: auto; height: auto; padding-left: 0; padding-bottom: 0; padding-top: 0; padding-right: 0; justify-content: flex-start; width: 100%; margin-left: 0; margin-right: 0; margin-top: 20px; margin-bottom: 20px; max-height: none; align-self: center;" />
            <ui:Button text="Select Object" display-tooltip-when-elided="true" name="SelectSampleBtn" style="width: 100%; align-self: center; padding-left: 0; margin-left: 0; margin-right: 0; margin-top: 0; margin-bottom: 0; padding-right: 0; padding-top: 0; padding-bottom: 0; height: 18px; white-space: nowrap; justify-content: flex-start;" />
        </ui:VisualElement>
    </ui:ScrollView>
</ui:UXML>
