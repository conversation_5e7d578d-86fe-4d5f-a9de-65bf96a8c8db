# Using textures

How to access and use textures in a custom render pass in the Universal Render Pipeline (URP).

|Page|Description|
|-|-|
|[URP blit best practices](customize/blit-overview.md)|Understand the different ways to perform a blit operation in URP and best practices to follow when writing custom render passes.|
|[Perform a full screen blit in URP](renderer-features/how-to-fullscreen-blit.md)|An example of creating a custom render pass and a custom Scriptable Renderer Feature that performs a full screen blit.|
