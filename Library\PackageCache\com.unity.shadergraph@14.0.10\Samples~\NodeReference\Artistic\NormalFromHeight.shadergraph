{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "9e2b377197404134829913680ce1b064",
    "m_Properties": [],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "56a981b43cfe48ee96166436ac630a36"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "8ec7e4838547489f970e5079e0ebd822"
        },
        {
            "m_Id": "efe684c211f643ecafe76c84f7954fc0"
        },
        {
            "m_Id": "b9d67cec765e40fabbbfc1dc5f8c1501"
        },
        {
            "m_Id": "bac01bdcd339421893564bc375b78d0f"
        },
        {
            "m_Id": "78d1c20f36ef4e769c469cc196944950"
        },
        {
            "m_Id": "2e04885db1f5457fb983c3bf64bc39d8"
        },
        {
            "m_Id": "732fdd8db6c04c9aa373740110bb0ccd"
        },
        {
            "m_Id": "5e5e7c52da8e4719b164bb6d08177ed5"
        },
        {
            "m_Id": "4f877614e27e497ba56764374e2e1fab"
        },
        {
            "m_Id": "02e844237146493195834d9bd1161585"
        },
        {
            "m_Id": "68aa514b3bdc4b38863abf96899ca473"
        },
        {
            "m_Id": "60b7beee71ab46c9bcd2c3282cc733e5"
        }
    ],
    "m_GroupDatas": [
        {
            "m_Id": "58f6489f500a4d20829e82773e2317b9"
        },
        {
            "m_Id": "42a27170986e4f2987dc7db7cdb4ec1e"
        }
    ],
    "m_StickyNoteDatas": [
        {
            "m_Id": "9a05e16a73b945ebb5c00a995f1dc4f2"
        },
        {
            "m_Id": "6c3a10a1f38149a79ff574d910b8c0ca"
        },
        {
            "m_Id": "d2204c9490d24ce588940d7f887a4254"
        },
        {
            "m_Id": "2cce202e636b495db33b487056186a78"
        },
        {
            "m_Id": "2a11bcce31734929b371990bfc10fcaf"
        },
        {
            "m_Id": "b1de756f9c4a455da1744502f135f62d"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "2e04885db1f5457fb983c3bf64bc39d8"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "732fdd8db6c04c9aa373740110bb0ccd"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "4f877614e27e497ba56764374e2e1fab"
                },
                "m_SlotId": 4
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "5e5e7c52da8e4719b164bb6d08177ed5"
                },
                "m_SlotId": 0
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": [
            {
                "m_Id": "8ec7e4838547489f970e5079e0ebd822"
            },
            {
                "m_Id": "efe684c211f643ecafe76c84f7954fc0"
            },
            {
                "m_Id": "b9d67cec765e40fabbbfc1dc5f8c1501"
            }
        ]
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 200.0
        },
        "m_Blocks": [
            {
                "m_Id": "bac01bdcd339421893564bc375b78d0f"
            },
            {
                "m_Id": "68aa514b3bdc4b38863abf96899ca473"
            },
            {
                "m_Id": "60b7beee71ab46c9bcd2c3282cc733e5"
            }
        ]
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Shader Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": ""
    },
    "m_ActiveTargets": [
        {
            "m_Id": "287ab03b43e74506bf2d4287921b9f38"
        },
        {
            "m_Id": "01729169bf3d4187b6984aa0cfad8f81"
        },
        {
            "m_Id": "9a614b34302c4f7a936468deec844196"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDTarget",
    "m_ObjectId": "01729169bf3d4187b6984aa0cfad8f81",
    "m_ActiveSubTarget": {
        "m_Id": "ff62627e244a4196932a9d2dd9d9c326"
    },
    "m_Datas": [
        {
            "m_Id": "76a779a5694b498f85b020f5fd5c9a6c"
        },
        {
            "m_Id": "2141ce4f309a4e21b9206ec7a7de9596"
        },
        {
            "m_Id": "b13a2a74c8504d3dbf262ac25583229c"
        }
    ],
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false,
    "m_SupportLineRendering": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalFromTextureNode",
    "m_ObjectId": "02e844237146493195834d9bd1161585",
    "m_Group": {
        "m_Id": "42a27170986e4f2987dc7db7cdb4ec1e"
    },
    "m_Name": "Normal From Texture",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -378.0000305175781,
            "y": 153.50003051757813,
            "width": 208.00009155273438,
            "height": 245.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "ac928ab00404493c8135ba3a53353f2f"
        },
        {
            "m_Id": "288184510bb34d959d466b0b46c8bb3b"
        },
        {
            "m_Id": "9b440db2b5944bedb17579f7f6551053"
        },
        {
            "m_Id": "ad9559b232154cecb6568aa2ac6e1cab"
        },
        {
            "m_Id": "f64c890a1f7c410f9949502785c8f36d"
        },
        {
            "m_Id": "fe251470ae224630a7153a6b0873ee86"
        }
    ],
    "synonyms": [
        "convert to normal",
        "bump map"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "20c704e0a1b44944ad6aeaa95bc97ae2",
    "m_Id": 2,
    "m_DisplayName": "Strength",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Strength",
    "m_StageCapability": 3,
    "m_Value": 0.009999999776482582,
    "m_DefaultValue": 0.009999999776482582,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "2141ce4f309a4e21b9206ec7a7de9596",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInTarget",
    "m_ObjectId": "287ab03b43e74506bf2d4287921b9f38",
    "m_ActiveSubTarget": {
        "m_Id": "a6c0a3a0253344a1a2d656637729d4f3"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZWriteControl": 0,
    "m_ZTestMode": 4,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CustomEditorGUI": ""
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "288184510bb34d959d466b0b46c8bb3b",
    "m_Id": 1,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "2a11bcce31734929b371990bfc10fcaf",
    "m_Title": "",
    "m_Content": "Here we have the same height map being used with both the Normal From Height and Normal From Texture nodes so we can compare the results.\n\nAn attempt has been made to match the intensities between them so we can directly compare the quality.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -817.0000610351563,
        "y": 304.0000305175781,
        "width": 200.0,
        "height": 160.0
    },
    "m_Group": {
        "m_Id": "42a27170986e4f2987dc7db7cdb4ec1e"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "2bdc4959745a4894a9a9cdad372039d1",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "2cce202e636b495db33b487056186a78",
    "m_Title": "",
    "m_Content": "The Normal From Texture node produces much smoother results, but it requires 3 or more texture samples to achieve this - so it is slower.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -372.0000305175781,
        "y": 403.0000305175781,
        "width": 200.00001525878907,
        "height": 100.00003051757813
    },
    "m_Group": {
        "m_Id": "42a27170986e4f2987dc7db7cdb4ec1e"
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.GradientNoiseNode",
    "m_ObjectId": "2e04885db1f5457fb983c3bf64bc39d8",
    "m_Group": {
        "m_Id": "58f6489f500a4d20829e82773e2317b9"
    },
    "m_Name": "Gradient Noise",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1385.0001220703125,
            "y": 85.50003051757813,
            "width": 208.0,
            "height": 336.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "4652869499a54564a98b313e165cc33c"
        },
        {
            "m_Id": "cf25932262504153a2947e40d611993d"
        },
        {
            "m_Id": "acab925c4bf0459f953978c406e251b4"
        }
    ],
    "synonyms": [
        "perlin noise"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_HashType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "42a27170986e4f2987dc7db7cdb4ec1e",
    "m_Title": "Comparison",
    "m_Position": {
        "x": -842.0000610351563,
        "y": 27.50006103515625
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "4652869499a54564a98b313e165cc33c",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TangentMaterialSlot",
    "m_ObjectId": "4f47361671d2429fb5de9dddef3f55dc",
    "m_Id": 0,
    "m_DisplayName": "Tangent",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tangent",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "4f4c4afa92574d29bbaf24f66d6078c2",
    "m_Id": 2,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleTexture2DNode",
    "m_ObjectId": "4f877614e27e497ba56764374e2e1fab",
    "m_Group": {
        "m_Id": "42a27170986e4f2987dc7db7cdb4ec1e"
    },
    "m_Name": "Sample Texture 2D",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -741.0000610351563,
            "y": 86.00003814697266,
            "width": 155.0,
            "height": 154.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "66ddb5d87a3f4e349ec0d03750e4796d"
        },
        {
            "m_Id": "e7db6673dc554dcba93be5c99409cbbd"
        },
        {
            "m_Id": "5cf28d31894f4f0cacd146529966e363"
        },
        {
            "m_Id": "da6621c8edff4b2b991fbf3674ded4f6"
        },
        {
            "m_Id": "b85b82ed63b0440e852bb744ef034418"
        },
        {
            "m_Id": "e52490985bdf4eac9e40f941df0c5992"
        },
        {
            "m_Id": "4f4c4afa92574d29bbaf24f66d6078c2"
        },
        {
            "m_Id": "edf9554837b44c32b502ae5af6aa0668"
        }
    ],
    "synonyms": [
        "tex2d"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_TextureType": 0,
    "m_NormalMapSpace": 0,
    "m_EnableGlobalMipBias": true,
    "m_MipSamplingMode": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "56a981b43cfe48ee96166436ac630a36",
    "m_Name": "",
    "m_ChildObjectList": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "56b04eb32e3c443aa71fa1587c4aa91e",
    "m_Id": 0,
    "m_DisplayName": "Emission",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Emission",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 1,
    "m_DefaultColor": {
        "r": 0.0,
        "g": 0.0,
        "b": 0.0,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "58f6489f500a4d20829e82773e2317b9",
    "m_Title": "The Basics",
    "m_Position": {
        "x": -1410.0001220703125,
        "y": 27.000057220458986
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "5cf28d31894f4f0cacd146529966e363",
    "m_Id": 5,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalFromHeightNode",
    "m_ObjectId": "5e5e7c52da8e4719b164bb6d08177ed5",
    "m_Group": {
        "m_Id": "42a27170986e4f2987dc7db7cdb4ec1e"
    },
    "m_Name": "Normal From Height",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -586.0000610351563,
            "y": 86.00003814697266,
            "width": 208.00003051757813,
            "height": 312.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "b9f0be913208485e8a9143379cc6f386"
        },
        {
            "m_Id": "b855761cb4ec4063be7025514170065b"
        },
        {
            "m_Id": "85207f2258254e43b99cd036302f29ab"
        }
    ],
    "synonyms": [
        "convert to normal",
        "bump map"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_OutputSpace": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "60b7beee71ab46c9bcd2c3282cc733e5",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Alpha",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "c974bffe07c94e9db3261cfb2631fd2e"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Alpha"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "668091ec98494e3293d5b55f2e531535",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "66ddb5d87a3f4e349ec0d03750e4796d",
    "m_Id": 0,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "68aa514b3bdc4b38863abf96899ca473",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Emission",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "56b04eb32e3c443aa71fa1587c4aa91e"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Emission"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "6c3a10a1f38149a79ff574d910b8c0ca",
    "m_Title": "",
    "m_Content": "Here we're converting the procedural noise output of a Gradient Noise node to a normal.\n\nIf you zoom in, you can see that the normal is blocky - because the Normal From Height node produces a cheap approximation.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1084.5001220703125,
        "y": 427.5000305175781,
        "width": 200.00006103515626,
        "height": 133.00003051757813
    },
    "m_Group": {
        "m_Id": "58f6489f500a4d20829e82773e2317b9"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalFromHeightNode",
    "m_ObjectId": "732fdd8db6c04c9aa373740110bb0ccd",
    "m_Group": {
        "m_Id": "58f6489f500a4d20829e82773e2317b9"
    },
    "m_Name": "Normal From Height",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1088.5001220703125,
            "y": 85.50003051757813,
            "width": 208.00006103515626,
            "height": 336.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "2bdc4959745a4894a9a9cdad372039d1"
        },
        {
            "m_Id": "cf3349d62e924faa89b16f22fd67fff5"
        },
        {
            "m_Id": "76c1223a4ec84e8f9952b87709ce6c1b"
        }
    ],
    "synonyms": [
        "convert to normal",
        "bump map"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_OutputSpace": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "76a779a5694b498f85b020f5fd5c9a6c",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "76c1223a4ec84e8f9952b87709ce6c1b",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalFromHeightNode",
    "m_ObjectId": "78d1c20f36ef4e769c469cc196944950",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Normal From Height",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -963.0,
            "y": -154.99998474121095,
            "width": 165.5,
            "height": 152.49998474121095
        }
    },
    "m_Slots": [
        {
            "m_Id": "cb8a2458a1f34ad6b4c75658605cbeec"
        },
        {
            "m_Id": "20c704e0a1b44944ad6aeaa95bc97ae2"
        },
        {
            "m_Id": "ebca40fd228f482aa26f4854529eb5a0"
        }
    ],
    "synonyms": [
        "convert to normal",
        "bump map"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_OutputSpace": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "794420b496cd4a18acb2d24d8c0b128b",
    "m_Id": 0,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "85207f2258254e43b99cd036302f29ab",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "8ec7e4838547489f970e5079e0ebd822",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "668091ec98494e3293d5b55f2e531535"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Position"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "943cd274212e4ea58c3f02c772b45418",
    "m_Id": 0,
    "m_DisplayName": "Base Color",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BaseColor",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.5,
        "y": 0.5,
        "z": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.5,
        "g": 0.5,
        "b": 0.5,
        "a": 1.0
    }
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalUnlitSubTarget",
    "m_ObjectId": "94990e0d7cd44643aaec91b0c2886d95"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "9a05e16a73b945ebb5c00a995f1dc4f2",
    "m_Title": "Normal From Height Node",
    "m_Content": "The Normal From Height Node generates an approximate normal using an incoming height value. It's the cheapest method of bump mapping because it works with just a height input instead of using 3 or more texture samples like the Normal From Texture node.\n\nHowever, results can be very low quality and have a pixelated appearance because there is no filtering.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -781.5000610351563,
        "y": -149.00001525878907,
        "width": 295.0000305175781,
        "height": 154.00001525878907
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget",
    "m_ObjectId": "9a614b34302c4f7a936468deec844196",
    "m_Datas": [],
    "m_ActiveSubTarget": {
        "m_Id": "94990e0d7cd44643aaec91b0c2886d95"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZTestMode": 4,
    "m_ZWriteControl": 0,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CastShadows": true,
    "m_ReceiveShadows": true,
    "m_SupportsLODCrossFade": false,
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "9b440db2b5944bedb17579f7f6551053",
    "m_Id": 2,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInUnlitSubTarget",
    "m_ObjectId": "a6c0a3a0253344a1a2d656637729d4f3"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "ac928ab00404493c8135ba3a53353f2f",
    "m_Id": 0,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"046e8c3035707834da517db833a8c498\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "acab925c4bf0459f953978c406e251b4",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ad9559b232154cecb6568aa2ac6e1cab",
    "m_Id": 3,
    "m_DisplayName": "Offset",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Offset",
    "m_StageCapability": 3,
    "m_Value": 0.5,
    "m_DefaultValue": 0.5,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "b13a2a74c8504d3dbf262ac25583229c",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "b1de756f9c4a455da1744502f135f62d",
    "m_Title": "",
    "m_Content": "If your input height map is already fairly noisy and your camera doesn't get too close to the surface, using the Normal From Height node may be a good option.\n\nIf your height map is fairly smooth and you need the camera to get close to the surface, use the Normal From Texture Node instead.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -643.5000610351563,
        "y": 558.0000610351563,
        "width": 299.5000305175781,
        "height": 127.0
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b855761cb4ec4063be7025514170065b",
    "m_Id": 2,
    "m_DisplayName": "Strength",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Strength",
    "m_StageCapability": 3,
    "m_Value": 0.029999999329447748,
    "m_DefaultValue": 0.009999999776482582,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b85b82ed63b0440e852bb744ef034418",
    "m_Id": 7,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "b9d67cec765e40fabbbfc1dc5f8c1501",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Tangent",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "4f47361671d2429fb5de9dddef3f55dc"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Tangent"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b9f0be913208485e8a9143379cc6f386",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "bac01bdcd339421893564bc375b78d0f",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.BaseColor",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "943cd274212e4ea58c3f02c772b45418"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.BaseColor"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "c974bffe07c94e9db3261cfb2631fd2e",
    "m_Id": 0,
    "m_DisplayName": "Alpha",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Alpha",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "cb8a2458a1f34ad6b4c75658605cbeec",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "cf25932262504153a2947e40d611993d",
    "m_Id": 1,
    "m_DisplayName": "Scale",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Scale",
    "m_StageCapability": 3,
    "m_Value": 10.0,
    "m_DefaultValue": 10.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "cf3349d62e924faa89b16f22fd67fff5",
    "m_Id": 2,
    "m_DisplayName": "Strength",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Strength",
    "m_StageCapability": 3,
    "m_Value": 0.029999999329447748,
    "m_DefaultValue": 0.009999999776482582,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "d2204c9490d24ce588940d7f887a4254",
    "m_Title": "",
    "m_Content": "The Normal From Height results are quite a bit blockier - but should be cheaper to calculate since we're only doing one texture sample.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -581.0000610351563,
        "y": 404.0000305175781,
        "width": 200.00003051757813,
        "height": 100.00003051757813
    },
    "m_Group": {
        "m_Id": "42a27170986e4f2987dc7db7cdb4ec1e"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "da6621c8edff4b2b991fbf3674ded4f6",
    "m_Id": 6,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "e52490985bdf4eac9e40f941df0c5992",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"046e8c3035707834da517db833a8c498\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e7db6673dc554dcba93be5c99409cbbd",
    "m_Id": 4,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "ebca40fd228f482aa26f4854529eb5a0",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "edf9554837b44c32b502ae5af6aa0668",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "efe684c211f643ecafe76c84f7954fc0",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Normal",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "794420b496cd4a18acb2d24d8c0b128b"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Normal"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "f64c890a1f7c410f9949502785c8f36d",
    "m_Id": 4,
    "m_DisplayName": "Strength",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Strength",
    "m_StageCapability": 3,
    "m_Value": 3.0,
    "m_DefaultValue": 8.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "fe251470ae224630a7153a6b0873ee86",
    "m_Id": 5,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "ff62627e244a4196932a9d2dd9d9c326"
}

