%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 36baaa8bdcb9d8b49b9199833965d2c3, type: 3}
  m_Name: BlenderSettings
  m_EditorClassIdentifier: 
  m_CustomBlends:
  - m_From: '**ANY CAMERA**'
    m_To: TURN ME ON CM_1
    m_Blend:
      m_Style: 1
      m_Time: 0.5
      m_CustomCurve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  - m_From: '**ANY CAMERA**'
    m_To: THEN TURN ME ON CM_2
    m_Blend:
      m_Style: 0
      m_Time: 2
      m_CustomCurve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  - m_From: '**ANY CAMERA**'
    m_To: WILL YOU TURN ME ON? CM_3
    m_Blend:
      m_Style: 1
      m_Time: 0.5
      m_CustomCurve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  - m_From: '**ANY CAMERA**'
    m_To: OK TURN ME ON TOO CM_4
    m_Blend:
      m_Style: 1
      m_Time: 5
      m_CustomCurve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
