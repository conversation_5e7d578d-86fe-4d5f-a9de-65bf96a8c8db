%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8075041343399620118
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2d08ce26990eb1a4a9177b860541e702, type: 3}
  m_Name: Exposure
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 4
  meteringMode:
    m_OverrideState: 0
    m_Value: 2
  luminanceSource:
    m_OverrideState: 0
    m_Value: 1
  fixedExposure:
    m_OverrideState: 1
    m_Value: 14
  compensation:
    m_OverrideState: 0
    m_Value: 0
  limitMin:
    m_OverrideState: 1
    m_Value: 0
  limitMax:
    m_OverrideState: 1
    m_Value: 14
  curveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -10
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 20
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  limitMinCurveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -12
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 18
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  limitMaxCurveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -8
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 22
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  adaptationMode:
    m_OverrideState: 0
    m_Value: 1
  adaptationSpeedDarkToLight:
    m_OverrideState: 0
    m_Value: 3
  adaptationSpeedLightToDark:
    m_OverrideState: 0
    m_Value: 1
  weightTextureMask:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  histogramPercentages:
    m_OverrideState: 0
    m_Value: {x: 40, y: 90}
  histogramUseCurveRemapping:
    m_OverrideState: 0
    m_Value: 0
  targetMidGray:
    m_OverrideState: 0
    m_Value: 0
  centerAroundExposureTarget:
    m_OverrideState: 0
    m_Value: 0
  proceduralCenter:
    m_OverrideState: 0
    m_Value: {x: 0.5, y: 0.5}
  proceduralRadii:
    m_OverrideState: 0
    m_Value: {x: 0.3, y: 0.3}
  maskMinIntensity:
    m_OverrideState: 0
    m_Value: -30
  maskMaxIntensity:
    m_OverrideState: 0
    m_Value: 30
  proceduralSoftness:
    m_OverrideState: 0
    m_Value: 0.5
--- !u!114 &-8067166546897044546
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e88178bb13f64a54f90d6cd6ef7aa9a1, type: 3}
  m_Name: CloudLayer
  m_EditorClassIdentifier: 
  active: 1
  opacity:
    m_OverrideState: 1
    m_Value: 1
  upperHemisphereOnly:
    m_OverrideState: 1
    m_Value: 1
  layers:
    m_OverrideState: 1
    m_Value: 0
  resolution:
    m_OverrideState: 1
    m_Value: 4096
  shadowMultiplier:
    m_OverrideState: 0
    m_Value: 1
  shadowTint:
    m_OverrideState: 0
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  shadowResolution:
    m_OverrideState: 0
    m_Value: 256
  shadowSize:
    m_OverrideState: 0
    m_Value: 500
  layerA:
    cloudMap:
      m_OverrideState: 0
      m_Value: {fileID: 2800000, guid: 57a33fc2476a01644865bfde5f06e2f4, type: 3}
    opacityR:
      m_OverrideState: 1
      m_Value: 0.015
    opacityG:
      m_OverrideState: 1
      m_Value: 0.02
    opacityB:
      m_OverrideState: 1
      m_Value: 0.104
    opacityA:
      m_OverrideState: 1
      m_Value: 0
    altitude:
      m_OverrideState: 0
      m_Value: 2000
    rotation:
      m_OverrideState: 1
      m_Value: 153
    tint:
      m_OverrideState: 0
      m_Value: {r: 1, g: 1, b: 1, a: 1}
    exposure:
      m_OverrideState: 0
      m_Value: 0
    distortionMode:
      m_OverrideState: 0
      m_Value: 0
    scrollOrientation:
      m_OverrideState: 1
      m_Value:
        mode: 1
        customValue: 360
        additiveValue: 0
        multiplyValue: 1
    scrollSpeed:
      m_OverrideState: 1
      m_Value:
        mode: 1
        customValue: 0
        additiveValue: 0
        multiplyValue: 1
    flowmap:
      m_OverrideState: 0
      m_Value: {fileID: 0}
    lighting:
      m_OverrideState: 1
      m_Value: 1
    steps:
      m_OverrideState: 1
      m_Value: 6
    thickness:
      m_OverrideState: 1
      m_Value: 1
    ambientProbeDimmer:
      m_OverrideState: 0
      m_Value: 1
    castShadows:
      m_OverrideState: 1
      m_Value: 0
  layerB:
    cloudMap:
      m_OverrideState: 0
      m_Value: {fileID: 2800000, guid: 57a33fc2476a01644865bfde5f06e2f4, type: 3}
    opacityR:
      m_OverrideState: 0
      m_Value: 1
    opacityG:
      m_OverrideState: 0
      m_Value: 0
    opacityB:
      m_OverrideState: 0
      m_Value: 0
    opacityA:
      m_OverrideState: 0
      m_Value: 0
    altitude:
      m_OverrideState: 0
      m_Value: 2000
    rotation:
      m_OverrideState: 0
      m_Value: 0
    tint:
      m_OverrideState: 0
      m_Value: {r: 1, g: 1, b: 1, a: 1}
    exposure:
      m_OverrideState: 0
      m_Value: 0
    distortionMode:
      m_OverrideState: 0
      m_Value: 0
    scrollOrientation:
      m_OverrideState: 0
      m_Value:
        mode: 1
        customValue: 0
        additiveValue: 0
        multiplyValue: 1
    scrollSpeed:
      m_OverrideState: 0
      m_Value:
        mode: 1
        customValue: 100
        additiveValue: 0
        multiplyValue: 1
    flowmap:
      m_OverrideState: 0
      m_Value: {fileID: 0}
    lighting:
      m_OverrideState: 0
      m_Value: 1
    steps:
      m_OverrideState: 0
      m_Value: 6
    thickness:
      m_OverrideState: 0
      m_Value: 0.5
    ambientProbeDimmer:
      m_OverrideState: 0
      m_Value: 1
    castShadows:
      m_OverrideState: 0
      m_Value: 0
--- !u!114 &-6975776770395274358
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 32b6af8f7ad32324cb6941c3290e5895, type: 3}
  m_Name: MicroShadowing
  m_EditorClassIdentifier: 
  active: 1
  enable:
    m_OverrideState: 1
    m_Value: 1
  opacity:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &-5853923668859720466
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7ddcec8a8eb2d684d833ac8f5d26aebd, type: 3}
  m_Name: HDShadowSettings
  m_EditorClassIdentifier: 
  active: 1
  interCascadeBorders: 1
  maxShadowDistance:
    m_OverrideState: 1
    m_Value: 100
  directionalTransmissionMultiplier:
    m_OverrideState: 0
    m_Value: 1
  cascadeShadowSplitCount:
    m_OverrideState: 1
    m_Value: 3
  cascadeShadowSplit0:
    m_OverrideState: 1
    m_Value: 0.1
  cascadeShadowSplit1:
    m_OverrideState: 1
    m_Value: 0.25
  cascadeShadowSplit2:
    m_OverrideState: 0
    m_Value: 0.3
  cascadeShadowBorder0:
    m_OverrideState: 1
    m_Value: 0.16333997
  cascadeShadowBorder1:
    m_OverrideState: 1
    m_Value: 0.23941854
  cascadeShadowBorder2:
    m_OverrideState: 0
    m_Value: 0
  cascadeShadowBorder3:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &-3552054655843743451
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4b709909182ba0943abef2c49ed59205, type: 3}
  m_Name: PaniniProjection
  m_EditorClassIdentifier: 
  active: 1
  distance:
    m_OverrideState: 1
    m_Value: 0.01
  cropToFit:
    m_OverrideState: 0
    m_Value: 1
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: ExteriorVolumeProfile
  m_EditorClassIdentifier: 
  components:
  - {fileID: -8075041343399620118}
  - {fileID: 5580338249648076752}
  - {fileID: 6508213727735904763}
  - {fileID: 7879906963843830384}
  - {fileID: -8067166546897044546}
  - {fileID: 3884085445312726101}
  - {fileID: 1469905032442258176}
  - {fileID: 4624770114631576748}
  - {fileID: 254486641261711594}
  - {fileID: -6975776770395274358}
  - {fileID: 1918416040923684496}
  - {fileID: -5853923668859720466}
  - {fileID: 4302018370519120706}
  - {fileID: -3552054655843743451}
--- !u!114 &254486641261711594
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4b8bcdf71d7fafa419fca1ed162f5fc9, type: 3}
  m_Name: ColorAdjustments
  m_EditorClassIdentifier: 
  active: 1
  postExposure:
    m_OverrideState: 1
    m_Value: 0
  contrast:
    m_OverrideState: 1
    m_Value: 10
  colorFilter:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  hueShift:
    m_OverrideState: 0
    m_Value: 0
  saturation:
    m_OverrideState: 1
    m_Value: 10
--- !u!114 &1469905032442258176
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 24f077503be6ae942a1e1245dbd53ea9, type: 3}
  m_Name: Bloom
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 1
    m_Value: 1
  threshold:
    m_OverrideState: 1
    m_Value: 0
  intensity:
    m_OverrideState: 1
    m_Value: 0.1
  scatter:
    m_OverrideState: 0
    m_Value: 0.7
  tint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  dirtTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  dirtIntensity:
    m_OverrideState: 0
    m_Value: 0
  anamorphic:
    m_OverrideState: 0
    m_Value: 1
  m_Resolution:
    m_OverrideState: 1
    m_Value: 2
  m_HighQualityPrefiltering:
    m_OverrideState: 1
    m_Value: 0
  m_HighQualityFiltering:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &1918416040923684496
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9008a067f4d626c4d8bc4bc48f04bb89, type: 3}
  m_Name: ScreenSpaceAmbientOcclusion
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 1
    m_Value: 3
  rayTracing:
    m_OverrideState: 0
    m_Value: 0
  intensity:
    m_OverrideState: 1
    m_Value: 1
  directLightingStrength:
    m_OverrideState: 0
    m_Value: 0
  radius:
    m_OverrideState: 0
    m_Value: 2
  spatialBilateralAggressiveness:
    m_OverrideState: 0
    m_Value: 0.15
  temporalAccumulation:
    m_OverrideState: 0
    m_Value: 1
  ghostingReduction:
    m_OverrideState: 0
    m_Value: 0.5
  blurSharpness:
    m_OverrideState: 0
    m_Value: 0.1
  layerMask:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Bits: **********
  specularOcclusion:
    m_OverrideState: 0
    m_Value: 0.5
  occluderMotionRejection:
    m_OverrideState: 0
    m_Value: 1
  receiverMotionRejection:
    m_OverrideState: 0
    m_Value: 1
  m_StepCount:
    m_OverrideState: 1
    m_Value: 16
  m_FullResolution:
    m_OverrideState: 1
    m_Value: 1
  m_MaximumRadiusInPixels:
    m_OverrideState: 1
    m_Value: 30
  m_BilateralUpsample:
    m_OverrideState: 1
    m_Value: 1
  m_DirectionCount:
    m_OverrideState: 1
    m_Value: 4
  m_RayLength:
    m_OverrideState: 1
    m_Value: 20
  m_SampleCount:
    m_OverrideState: 1
    m_Value: 8
  m_Denoise:
    m_OverrideState: 1
    m_Value: 1
  m_DenoiserRadius:
    m_OverrideState: 1
    m_Value: 0.65
--- !u!114 &3884085445312726101
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f086a068d4c5889438831b3ae9afc11c, type: 3}
  m_Name: Tonemapping
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 2
  useFullACES:
    m_OverrideState: 1
    m_Value: 1
  toeStrength:
    m_OverrideState: 0
    m_Value: 0
  toeLength:
    m_OverrideState: 0
    m_Value: 0.5
  shoulderStrength:
    m_OverrideState: 0
    m_Value: 0
  shoulderLength:
    m_OverrideState: 0
    m_Value: 0.5
  shoulderAngle:
    m_OverrideState: 0
    m_Value: 0
  gamma:
    m_OverrideState: 0
    m_Value: 1
  lutTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  lutContribution:
    m_OverrideState: 0
    m_Value: 1
  neutralHDRRangeReductionMode:
    m_OverrideState: 0
    m_Value: 2
  acesPreset:
    m_OverrideState: 0
    m_Value: 3
  fallbackMode:
    m_OverrideState: 0
    m_Value: 1
  hueShiftAmount:
    m_OverrideState: 0
    m_Value: 0
  detectPaperWhite:
    m_OverrideState: 0
    m_Value: 0
  paperWhite:
    m_OverrideState: 0
    m_Value: 300
  detectBrightnessLimits:
    m_OverrideState: 0
    m_Value: 1
  minNits:
    m_OverrideState: 0
    m_Value: 0.005
  maxNits:
    m_OverrideState: 0
    m_Value: 1000
--- !u!114 &4302018370519120706
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5e17fad69ea181b4483974138b566975, type: 3}
  m_Name: ScreenSpaceRefraction
  m_EditorClassIdentifier: 
  active: 1
  screenFadeDistance:
    m_OverrideState: 1
    m_Value: 0.001
--- !u!114 &4624770114631576748
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2c1be1b6c95cd2e41b27903b9270817f, type: 3}
  m_Name: Vignette
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 0
  color:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  center:
    m_OverrideState: 1
    m_Value: {x: 0.5, y: 0.5}
  intensity:
    m_OverrideState: 1
    m_Value: 0.4
  smoothness:
    m_OverrideState: 0
    m_Value: 0.2
  roundness:
    m_OverrideState: 0
    m_Value: 1
  rounded:
    m_OverrideState: 0
    m_Value: 0
  mask:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  opacity:
    m_OverrideState: 0
    m_Value: 1
--- !u!114 &5580338249648076752
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0d7593b3a9277ac4696b20006c21dde2, type: 3}
  m_Name: VisualEnvironment
  m_EditorClassIdentifier: 
  active: 1
  skyType:
    m_OverrideState: 1
    m_Value: 4
  cloudType:
    m_OverrideState: 1
    m_Value: 1
  skyAmbientMode:
    m_OverrideState: 1
    m_Value: 1
  windOrientation:
    m_OverrideState: 0
    m_Value: 0
  windSpeed:
    m_OverrideState: 1
    m_Value: 0
  fogType:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &6508213727735904763
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d877ec3e844f2ca46830012e8e79319b, type: 3}
  m_Name: PhysicallyBasedSky
  m_EditorClassIdentifier: 
  active: 1
  rotation:
    m_OverrideState: 0
    m_Value: 0
  skyIntensityMode:
    m_OverrideState: 1
    m_Value: 0
  exposure:
    m_OverrideState: 1
    m_Value: 1
  multiplier:
    m_OverrideState: 0
    m_Value: 1
  upperHemisphereLuxValue:
    m_OverrideState: 0
    m_Value: 1
  upperHemisphereLuxColor:
    m_OverrideState: 0
    m_Value: {x: 0, y: 0, z: 0}
  desiredLuxValue:
    m_OverrideState: 0
    m_Value: 20000
  updateMode:
    m_OverrideState: 1
    m_Value: 0
  updatePeriod:
    m_OverrideState: 0
    m_Value: 0
  includeSunInBaking:
    m_OverrideState: 0
    m_Value: 0
  type:
    m_OverrideState: 1
    m_Value: 1
  sphericalMode:
    m_OverrideState: 0
    m_Value: 1
  seaLevel:
    m_OverrideState: 0
    m_Value: 0
  planetaryRadius:
    m_OverrideState: 0
    m_Value: 6378100
  planetCenterPosition:
    m_OverrideState: 0
    m_Value: {x: 0, y: -6378100, z: 0}
  airDensityR:
    m_OverrideState: 0
    m_Value: 0.04534
  airDensityG:
    m_OverrideState: 0
    m_Value: 0.10237241
  airDensityB:
    m_OverrideState: 0
    m_Value: 0.23264056
  airTint:
    m_OverrideState: 0
    m_Value: {r: 0.9, g: 0.9, b: 1, a: 1}
  airMaximumAltitude:
    m_OverrideState: 0
    m_Value: 55261.973
  aerosolDensity:
    m_OverrideState: 0
    m_Value: 0.059
  aerosolTint:
    m_OverrideState: 0
    m_Value: {r: 0.9, g: 0.9, b: 0.9, a: 1}
  aerosolMaximumAltitude:
    m_OverrideState: 0
    m_Value: 8289.296
  aerosolAnisotropy:
    m_OverrideState: 1
    m_Value: 0.8
  groundTint:
    m_OverrideState: 1
    m_Value: {r: 0.5660378, g: 0.5660378, b: 0.5660378, a: 1}
  groundColorTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  groundEmissionTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  groundEmissionMultiplier:
    m_OverrideState: 0
    m_Value: 1
  planetRotation:
    m_OverrideState: 0
    m_Value: {x: 0, y: 0, z: 0}
  spaceEmissionTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  spaceEmissionMultiplier:
    m_OverrideState: 0
    m_Value: 1
  spaceRotation:
    m_OverrideState: 0
    m_Value: {x: 0, y: 0, z: 0}
  colorSaturation:
    m_OverrideState: 0
    m_Value: 1
  alphaSaturation:
    m_OverrideState: 0
    m_Value: 1
  alphaMultiplier:
    m_OverrideState: 0
    m_Value: 1
  horizonTint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  zenithTint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  horizonZenithShift:
    m_OverrideState: 0
    m_Value: 0
  m_SkyVersion: 2
  m_ObsoleteEarthPreset:
    m_OverrideState: 0
    m_Value: 1
--- !u!114 &7879906963843830384
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 953beb541740ddc499d005ee80c9ff29, type: 3}
  m_Name: Fog
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 1
    m_Value: 1
  enabled:
    m_OverrideState: 1
    m_Value: 1
  colorMode:
    m_OverrideState: 0
    m_Value: 1
  color:
    m_OverrideState: 0
    m_Value: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  tint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  maxFogDistance:
    m_OverrideState: 0
    m_Value: 5000
  mipFogMaxMip:
    m_OverrideState: 1
    m_Value: 0.75
  mipFogNear:
    m_OverrideState: 0
    m_Value: 0
  mipFogFar:
    m_OverrideState: 1
    m_Value: 100
  baseHeight:
    m_OverrideState: 1
    m_Value: 2
  maximumHeight:
    m_OverrideState: 1
    m_Value: 100
  meanFreePath:
    m_OverrideState: 1
    m_Value: 400
  enableVolumetricFog:
    m_OverrideState: 1
    m_Value: 1
  albedo:
    m_OverrideState: 1
    m_Value: {r: 0.81761, g: 0.9246877, b: 1, a: 1}
  globalLightProbeDimmer:
    m_OverrideState: 0
    m_Value: 1
  depthExtent:
    m_OverrideState: 0
    m_Value: 64
  denoisingMode:
    m_OverrideState: 0
    m_Value: 2
  anisotropy:
    m_OverrideState: 1
    m_Value: 0
  sliceDistributionUniformity:
    m_OverrideState: 0
    m_Value: 0.75
  m_FogControlMode:
    m_OverrideState: 1
    m_Value: 0
  screenResolutionPercentage:
    m_OverrideState: 0
    m_Value: 12.5
  volumeSliceCount:
    m_OverrideState: 0
    m_Value: 64
  m_VolumetricFogBudget:
    m_OverrideState: 1
    m_Value: 0.5
  m_ResolutionDepthRatio:
    m_OverrideState: 1
    m_Value: 0.5
  directionalLightsOnly:
    m_OverrideState: 0
    m_Value: 0
