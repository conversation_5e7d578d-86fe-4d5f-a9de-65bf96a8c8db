using System.Reflection;
using UnityEngine;

namespace UnityEditor.ShaderGraph
{
    [Title("Artistic", "Filter", "Dither")]
    class DitherNode : CodeFunctionNode
    {
        public DitherNode()
        {
            name = "Dither";
            synonyms = new string[] { "blue noise", "half tone" };
            UpdateNodeAfterDeserialization();
        }

        protected override MethodInfo GetFunctionToConvert()
        {
            return GetType().GetMethod("Unity_Dither", BindingFlags.Static | BindingFlags.NonPublic);
        }

        static string Unity_Dither(
            [Slot(0, Binding.None)] DynamicDimensionVector In,
            [Slot(1, Binding.ScreenPosition)] Vector2 ScreenPosition,
            [Slot(2, Binding.None)] out DynamicDimensionVector Out)
        {
            return
@"
{
    $precision2 uv = ScreenPosition.xy * _ScreenParams.xy;
    $precision DITHER_THRESHOLDS[16] =
    {
        1.0 / 17.0,  9.0 / 17.0,  3.0 / 17.0, 11.0 / 17.0,
        13.0 / 17.0,  5.0 / 17.0, 15.0 / 17.0,  7.0 / 17.0,
        4.0 / 17.0, 12.0 / 17.0,  2.0 / 17.0, 10.0 / 17.0,
        16.0 / 17.0,  8.0 / 17.0, 14.0 / 17.0,  6.0 / 17.0
    };
    uint index = (uint(uv.x) % 4) * 4 + uint(uv.y) % 4;
    Out = In - DITHER_THRESHOLDS[index];
}";
        }
    }
}
