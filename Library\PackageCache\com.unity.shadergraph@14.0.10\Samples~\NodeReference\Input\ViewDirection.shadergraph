{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "85bfb06210dd471bbd8269b5c70d605e",
    "m_Properties": [
        {
            "m_Id": "abd0c7a1c344469e9c8cf8ac8823d2df"
        },
        {
            "m_Id": "009b99837e1a4ffb92ac1b290148e6f7"
        }
    ],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "e40d238ad26243c3b8658facfd122aa6"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "256528849f3b4b60aeb128c6cc61b5d3"
        },
        {
            "m_Id": "4357dc94eeae42fdb8127bd345eb4684"
        },
        {
            "m_Id": "08f1a591b88f4e298f38a3a06e636675"
        },
        {
            "m_Id": "29b9f1e5a70e4df58bef8083041e8c43"
        },
        {
            "m_Id": "1764121c7b494329b91744739c9cb926"
        },
        {
            "m_Id": "3754a4970366400282ed96821fedf43a"
        },
        {
            "m_Id": "a2bc300e0d41436d9c429b829142ba76"
        },
        {
            "m_Id": "54052261ae4c487e85baabdbb5e2ec75"
        },
        {
            "m_Id": "138c294ae2e543a1940f96ee2dd7539e"
        },
        {
            "m_Id": "d31bad90162946d09cec22215af3f737"
        },
        {
            "m_Id": "e8b4ab3b3316488097e55471d134a918"
        },
        {
            "m_Id": "3354edb3c048425f9ca419369de37d6f"
        },
        {
            "m_Id": "dc2a36e1ea704c56a8c90d36146378c2"
        },
        {
            "m_Id": "125b6b455dd64ad19a1fb9fb65c99aa8"
        },
        {
            "m_Id": "b7a474f6b91c4a2a9c46c57da4edc60f"
        },
        {
            "m_Id": "0f6e7d5dae064723ab7a79525af53c9d"
        }
    ],
    "m_GroupDatas": [
        {
            "m_Id": "139c589797e542bdbbb9e44bd2755931"
        },
        {
            "m_Id": "7ef8ac2de3a54424922789d150698ad0"
        }
    ],
    "m_StickyNoteDatas": [
        {
            "m_Id": "386b15c1dc1648baafb431bddfacec09"
        },
        {
            "m_Id": "b8a6d3f0b4c84905b9e6a482e4fb722d"
        },
        {
            "m_Id": "86955fa0fc3847678635b41c7eada382"
        },
        {
            "m_Id": "d77f029b49c642cb93ed4f6c7c27e65f"
        },
        {
            "m_Id": "ec684f36441041c7973af8b279363de0"
        },
        {
            "m_Id": "d3b20b59934145c6a709313de0129b0e"
        },
        {
            "m_Id": "ced46a388db94855858e5d9dec9fce6f"
        },
        {
            "m_Id": "f5dcf059ae434a2cb27dc88799b1a731"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "138c294ae2e543a1940f96ee2dd7539e"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "3354edb3c048425f9ca419369de37d6f"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "1764121c7b494329b91744739c9cb926"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "3754a4970366400282ed96821fedf43a"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "3354edb3c048425f9ca419369de37d6f"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "dc2a36e1ea704c56a8c90d36146378c2"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "a2bc300e0d41436d9c429b829142ba76"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "3754a4970366400282ed96821fedf43a"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "d31bad90162946d09cec22215af3f737"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "138c294ae2e543a1940f96ee2dd7539e"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "dc2a36e1ea704c56a8c90d36146378c2"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "125b6b455dd64ad19a1fb9fb65c99aa8"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "e8b4ab3b3316488097e55471d134a918"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "138c294ae2e543a1940f96ee2dd7539e"
                },
                "m_SlotId": 0
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": [
            {
                "m_Id": "256528849f3b4b60aeb128c6cc61b5d3"
            },
            {
                "m_Id": "4357dc94eeae42fdb8127bd345eb4684"
            },
            {
                "m_Id": "08f1a591b88f4e298f38a3a06e636675"
            }
        ]
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 200.0
        },
        "m_Blocks": [
            {
                "m_Id": "29b9f1e5a70e4df58bef8083041e8c43"
            },
            {
                "m_Id": "b7a474f6b91c4a2a9c46c57da4edc60f"
            },
            {
                "m_Id": "0f6e7d5dae064723ab7a79525af53c9d"
            }
        ]
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Shader Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": ""
    },
    "m_ActiveTargets": [
        {
            "m_Id": "3a95811082ec4aa09acd1d382fdc864d"
        },
        {
            "m_Id": "70284f50b7d54e2eb7f1473408dee8d6"
        },
        {
            "m_Id": "70612408391f4688b8fb956853837bdf"
        }
    ]
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "009b99837e1a4ffb92ac1b290148e6f7",
    "m_Guid": {
        "m_GuidSerialized": "7062c1d0-4d34-4713-a1ba-d82d847e773f"
    },
    "m_Name": "Mask Length",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "Mask Length",
    "m_DefaultReferenceName": "_Mask_Length",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": 0.0,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "053966ed3bdb49debeb3145a7c71bbb6",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "08f1a591b88f4e298f38a3a06e636675",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Tangent",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "c6046aa3999b4df1941340bfe394a13f"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Tangent"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "0bc209d7dde24006b2f879abf0766999",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "0e5e99bc57c04facb21ef9ecc23dadc6",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "0f6e7d5dae064723ab7a79525af53c9d",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Alpha",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "7931ddd363be4afe8ecba63d0de405d8"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Alpha"
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalUnlitSubTarget",
    "m_ObjectId": "0fa9908ad6574e76a9fe9cf0a8df7576"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PowerNode",
    "m_ObjectId": "125b6b455dd64ad19a1fb9fb65c99aa8",
    "m_Group": {
        "m_Id": "7ef8ac2de3a54424922789d150698ad0"
    },
    "m_Name": "Power",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -739.0000610351563,
            "y": 427.5000305175781,
            "width": 208.00006103515626,
            "height": 302.0000305175781
        }
    },
    "m_Slots": [
        {
            "m_Id": "c350b5f56ff545c0bc5e633e39b04497"
        },
        {
            "m_Id": "64c2d71a967f4344aa8066210e046559"
        },
        {
            "m_Id": "d146042b9ea14109bc26432a1b084e50"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DotProductNode",
    "m_ObjectId": "138c294ae2e543a1940f96ee2dd7539e",
    "m_Group": {
        "m_Id": "7ef8ac2de3a54424922789d150698ad0"
    },
    "m_Name": "Dot Product",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1327.0,
            "y": 427.5000305175781,
            "width": 207.9998779296875,
            "height": 302.0000305175781
        }
    },
    "m_Slots": [
        {
            "m_Id": "50cd89c2de944bbd893e450521ba0f88"
        },
        {
            "m_Id": "ddfb5ee618ef4264af962f71b4e19b68"
        },
        {
            "m_Id": "053966ed3bdb49debeb3145a7c71bbb6"
        }
    ],
    "synonyms": [
        "scalar product"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "139c589797e542bdbbb9e44bd2755931",
    "m_Title": "Camera Facing",
    "m_Position": {
        "x": -2109.500244140625,
        "y": 298.00006103515627
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.ViewDirectionNode",
    "m_ObjectId": "1764121c7b494329b91744739c9cb926",
    "m_Group": {
        "m_Id": "139c589797e542bdbbb9e44bd2755931"
    },
    "m_Name": "View Direction",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -2084.500244140625,
            "y": 356.5000305175781,
            "width": 206.000244140625,
            "height": 130.50003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "a61f96cfbb7b4c39b72b61c6e8c7259a"
        }
    ],
    "synonyms": [
        "eye direction"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "23478e0a7d7a4a9596e844ffb869f8a7"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "24cd877f518c45aea05210820718e938",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "256528849f3b4b60aeb128c6cc61b5d3",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "632259da20534eb68acafe232cefff67"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Position"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "29b9f1e5a70e4df58bef8083041e8c43",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.BaseColor",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "cdbb46f907e14f27a7545940ee4852d5"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.BaseColor"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SaturateNode",
    "m_ObjectId": "3354edb3c048425f9ca419369de37d6f",
    "m_Group": {
        "m_Id": "7ef8ac2de3a54424922789d150698ad0"
    },
    "m_Name": "Saturate",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1119.0001220703125,
            "y": 427.5000305175781,
            "width": 127.5001220703125,
            "height": 93.99996948242188
        }
    },
    "m_Slots": [
        {
            "m_Id": "f374a56ca9994a318fb99cf8965674e4"
        },
        {
            "m_Id": "9528f22ccebb408cbc10224517b93646"
        }
    ],
    "synonyms": [
        "clamp"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DotProductNode",
    "m_ObjectId": "3754a4970366400282ed96821fedf43a",
    "m_Group": {
        "m_Id": "139c589797e542bdbbb9e44bd2755931"
    },
    "m_Name": "Dot Product",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1843.5,
            "y": 408.5000305175781,
            "width": 208.0,
            "height": 302.0000305175781
        }
    },
    "m_Slots": [
        {
            "m_Id": "6f3b6282934b405da247cee68d4a9b11"
        },
        {
            "m_Id": "fbfe7c389c114d3eb982d246fff60312"
        },
        {
            "m_Id": "e1d65796981f4ab7bed1cd13c7f21cdb"
        }
    ],
    "synonyms": [
        "scalar product"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "386b15c1dc1648baafb431bddfacec09",
    "m_Title": "View Direction Node",
    "m_Content": "The View Direction Node brings in a unit length vector that points in the direction of the camera.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1306.0001220703125,
        "y": -68.50000762939453,
        "width": 213.1502685546875,
        "height": 82.658203125
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInTarget",
    "m_ObjectId": "3a95811082ec4aa09acd1d382fdc864d",
    "m_ActiveSubTarget": {
        "m_Id": "9640d0caa7ab49d49602a15f0f6cf19a"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZWriteControl": 0,
    "m_ZTestMode": 4,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CustomEditorGUI": ""
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "4357dc94eeae42fdb8127bd345eb4684",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Normal",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "5ba9ff0c80f44f1684d1b2bdeb998f6b"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Normal"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "44c1288091aa4a41a30f56c4a2d9aff7",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "50cd89c2de944bbd893e450521ba0f88",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.ViewDirectionNode",
    "m_ObjectId": "54052261ae4c487e85baabdbb5e2ec75",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "View Direction",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1522.5001220703125,
            "y": -70.00001525878906,
            "width": 208.0,
            "height": 314.50006103515627
        }
    },
    "m_Slots": [
        {
            "m_Id": "24cd877f518c45aea05210820718e938"
        }
    ],
    "synonyms": [
        "eye direction"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "5ba9ff0c80f44f1684d1b2bdeb998f6b",
    "m_Id": 0,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "60d83e9204744dfa9109b3d6df146d77",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "622ee3d03331483f9d1a64ea4d51e578",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "632259da20534eb68acafe232cefff67",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "64c2d71a967f4344aa8066210e046559",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 2.0,
        "y": 2.0,
        "z": 2.0,
        "w": 2.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "6f3b6282934b405da247cee68d4a9b11",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDTarget",
    "m_ObjectId": "70284f50b7d54e2eb7f1473408dee8d6",
    "m_ActiveSubTarget": {
        "m_Id": "23478e0a7d7a4a9596e844ffb869f8a7"
    },
    "m_Datas": [
        {
            "m_Id": "9f3ca3768c524798b95f3bd98940a4a4"
        },
        {
            "m_Id": "622ee3d03331483f9d1a64ea4d51e578"
        },
        {
            "m_Id": "f73754bea63e4eee9eaae60f05a37f2a"
        }
    ],
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false,
    "m_SupportLineRendering": false
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget",
    "m_ObjectId": "70612408391f4688b8fb956853837bdf",
    "m_Datas": [],
    "m_ActiveSubTarget": {
        "m_Id": "0fa9908ad6574e76a9fe9cf0a8df7576"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZTestMode": 4,
    "m_ZWriteControl": 0,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CastShadows": true,
    "m_ReceiveShadows": true,
    "m_SupportsLODCrossFade": false,
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "7931ddd363be4afe8ecba63d0de405d8",
    "m_Id": 0,
    "m_DisplayName": "Alpha",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Alpha",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "7ef8ac2de3a54424922789d150698ad0",
    "m_Title": "Fresnel Effect",
    "m_Position": {
        "x": -1601.5,
        "y": 295.50006103515627
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "86955fa0fc3847678635b41c7eada382",
    "m_Title": "",
    "m_Content": "By doing a dot product between the View Direction and the Normal Vector, you can create a mask that's white when the surface is facing the camera and black when it's facing away.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -2078.000244140625,
        "y": 625.5000610351563,
        "width": 200.0001220703125,
        "height": 106.5
    },
    "m_Group": {
        "m_Id": "139c589797e542bdbbb9e44bd2755931"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "9528f22ccebb408cbc10224517b93646",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInUnlitSubTarget",
    "m_ObjectId": "9640d0caa7ab49d49602a15f0f6cf19a"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "97c4f93bb8674d5892cb999f36ab3371",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "9a4601fad6bf4116aff1e460d0929d8f",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "9f3ca3768c524798b95f3bd98940a4a4",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "a1fee6609a0a4579a8adc2e9b049f7d0",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalVectorNode",
    "m_ObjectId": "a2bc300e0d41436d9c429b829142ba76",
    "m_Group": {
        "m_Id": "139c589797e542bdbbb9e44bd2755931"
    },
    "m_Name": "Normal Vector",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -2084.500244140625,
            "y": 487.00006103515627,
            "width": 206.000244140625,
            "height": 130.49993896484376
        }
    },
    "m_Slots": [
        {
            "m_Id": "97c4f93bb8674d5892cb999f36ab3371"
        }
    ],
    "synonyms": [
        "surface direction"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "a5d2d3a6cbe949ff8add47c844e1fcb6"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "a61f96cfbb7b4c39b72b61c6e8c7259a",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "abd0c7a1c344469e9c8cf8ac8823d2df",
    "m_Guid": {
        "m_GuidSerialized": "430c58cb-aa46-4b18-8dc0-1eaf3425540b"
    },
    "m_Name": "Mask Start Distance",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "Mask Start Distance",
    "m_DefaultReferenceName": "_Mask_Start_Distance",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": 0.0,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "b7a474f6b91c4a2a9c46c57da4edc60f",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Emission",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "ec75ec60f1594dc88f6063c521c5f092"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Emission"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "b8a6d3f0b4c84905b9e6a482e4fb722d",
    "m_Title": "",
    "m_Content": "The difference between the View Vector and the View Direction:\n\nThe View Vector is a line that extends all the way from the current fragment or vertex to the camera - so it can be used to get the DISTANCE to the camera.\n\nThe View Direction is a normalized vector (so it's only 1 unit long). It's best used to find the ANGLE of the camera and other vectors.\n\nBoth vectors point in the same direction.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1091.5001220703125,
        "y": -69.00000762939453,
        "width": 200.00006103515626,
        "height": 234.50001525878907
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "c350b5f56ff545c0bc5e633e39b04497",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TangentMaterialSlot",
    "m_ObjectId": "c6046aa3999b4df1941340bfe394a13f",
    "m_Id": 0,
    "m_DisplayName": "Tangent",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tangent",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "c8205c711dd44193b4bb2fa0f1d5f8dd",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "cdbb46f907e14f27a7545940ee4852d5",
    "m_Id": 0,
    "m_DisplayName": "Base Color",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BaseColor",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.5,
        "y": 0.5,
        "z": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.5,
        "g": 0.5,
        "b": 0.5,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "ced46a388db94855858e5d9dec9fce6f",
    "m_Title": "",
    "m_Content": "Raising the result to a power controls the falloff curve.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -705.0000610351563,
        "y": 767.0000610351563,
        "width": 200.00003051757813,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "7ef8ac2de3a54424922789d150698ad0"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "d146042b9ea14109bc26432a1b084e50",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalVectorNode",
    "m_ObjectId": "d31bad90162946d09cec22215af3f737",
    "m_Group": {
        "m_Id": "7ef8ac2de3a54424922789d150698ad0"
    },
    "m_Name": "Normal Vector",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1576.5001220703125,
            "y": 493.0000915527344,
            "width": 206.0,
            "height": 130.49996948242188
        }
    },
    "m_Slots": [
        {
            "m_Id": "60d83e9204744dfa9109b3d6df146d77"
        }
    ],
    "synonyms": [
        "surface direction"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "d3b20b59934145c6a709313de0129b0e",
    "m_Title": "",
    "m_Content": "Most use-cases call for the mask to be inverted so we do that with the One Minus node.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -958.0000610351563,
        "y": 744.5000610351563,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "7ef8ac2de3a54424922789d150698ad0"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "d77f029b49c642cb93ed4f6c7c27e65f",
    "m_Title": "",
    "m_Content": "This is the math that the Fresnel Effect node does to generate the mask.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -704.0000610351563,
        "y": 354.0000305175781,
        "width": 200.00003051757813,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "7ef8ac2de3a54424922789d150698ad0"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.OneMinusNode",
    "m_ObjectId": "dc2a36e1ea704c56a8c90d36146378c2",
    "m_Group": {
        "m_Id": "7ef8ac2de3a54424922789d150698ad0"
    },
    "m_Name": "One Minus",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -991.5,
            "y": 427.5000305175781,
            "width": 208.0,
            "height": 278.0000305175781
        }
    },
    "m_Slots": [
        {
            "m_Id": "9a4601fad6bf4116aff1e460d0929d8f"
        },
        {
            "m_Id": "44c1288091aa4a41a30f56c4a2d9aff7"
        }
    ],
    "synonyms": [
        "complement",
        "invert",
        "opposite"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "ddfb5ee618ef4264af962f71b4e19b68",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 1.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e1d65796981f4ab7bed1cd13c7f21cdb",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "e40d238ad26243c3b8658facfd122aa6",
    "m_Name": "",
    "m_ChildObjectList": [
        {
            "m_Id": "abd0c7a1c344469e9c8cf8ac8823d2df"
        },
        {
            "m_Id": "009b99837e1a4ffb92ac1b290148e6f7"
        }
    ]
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.ViewDirectionNode",
    "m_ObjectId": "e8b4ab3b3316488097e55471d134a918",
    "m_Group": {
        "m_Id": "7ef8ac2de3a54424922789d150698ad0"
    },
    "m_Name": "View Direction",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1576.5001220703125,
            "y": 362.50006103515627,
            "width": 206.0,
            "height": 130.50003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "0bc209d7dde24006b2f879abf0766999"
        }
    ],
    "synonyms": [
        "eye direction"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "ec684f36441041c7973af8b279363de0",
    "m_Title": "",
    "m_Content": "The dot product is white where the normal and view vector are parallel and black where they're perpendicular.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1294.0001220703125,
        "y": 764.0000610351563,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "7ef8ac2de3a54424922789d150698ad0"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "ec75ec60f1594dc88f6063c521c5f092",
    "m_Id": 0,
    "m_DisplayName": "Emission",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Emission",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 1,
    "m_DefaultColor": {
        "r": 0.0,
        "g": 0.0,
        "b": 0.0,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "f374a56ca9994a318fb99cf8965674e4",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "f5dcf059ae434a2cb27dc88799b1a731",
    "m_Title": "",
    "m_Content": "Building on the Camera Facing example to the left, we can also create an edge glow effect using the View Direction node. This is what the Fresnel Effect node does under the hood.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1194.0001220703125,
        "y": 341.0000305175781,
        "width": 337.50006103515627,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "7ef8ac2de3a54424922789d150698ad0"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "f73754bea63e4eee9eaae60f05a37f2a",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "fbfe7c389c114d3eb982d246fff60312",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 1.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

