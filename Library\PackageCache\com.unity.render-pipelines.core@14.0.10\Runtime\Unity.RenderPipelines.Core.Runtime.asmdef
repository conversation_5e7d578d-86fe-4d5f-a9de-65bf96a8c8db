{"name": "Unity.RenderPipelines.Core.Runtime", "references": ["GUID:75469ad4d38634e559750d17036d5f7c"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": true, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.modules.vr", "expression": "1.0.0", "define": "ENABLE_VR_MODULE"}, {"name": "com.unity.modules.xr", "expression": "1.0.0", "define": "ENABLE_XR_MODULE"}, {"name": "com.unity.inputsystem", "expression": "0.0.0", "define": "ENABLE_INPUT_SYSTEM_PACKAGE"}, {"name": "com.unity.rendering.hybrid", "expression": "0.6.0-preview.0", "define": "HYBRID_0_6_0_OR_NEWER"}, {"name": "com.unity.entities.graphics", "expression": "0.6.0-preview.0", "define": "HYBRID_0_6_0_OR_NEWER"}, {"name": "com.unity.modules.nvidia", "expression": "1.0.0", "define": "ENABLE_NVIDIA_MODULE"}], "noEngineReferences": false}