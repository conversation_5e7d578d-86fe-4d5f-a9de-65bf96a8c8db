%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0.44657868, g: 0.49641263, b: 0.57481706, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_TemporalCoherenceThreshold: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 1
  m_LightmapEditorSettings:
    serializedVersion: 10
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 0
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 500
    m_PVRBounces: 2
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVRFilteringMode: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ShowResolutionOverlay: 1
  m_LightingDataAsset: {fileID: 0}
  m_UseShadowmask: 1
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &62958715
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 62958717}
  - component: {fileID: 62958716}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &62958716
Light:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 62958715}
  m_Enabled: 1
  serializedVersion: 8
  m_Type: 1
  m_Color: {r: 1, g: 0.95686275, b: 0.8392157, a: 1}
  m_Intensity: 1
  m_Range: 10
  m_SpotAngle: 30
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_Lightmapping: 4
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
--- !u!4 &62958717
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 62958715}
  m_LocalRotation: {x: 0.40821788, y: -0.23456968, z: 0.10938163, w: 0.8754261}
  m_LocalPosition: {x: 3.3263245, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 50, y: -30, z: 0}
--- !u!1 &399017372
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 1888939156634140, guid: dacddffc9983cdf44bb21aadfd301d81,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 399017374}
  - component: {fileID: 399017373}
  - component: {fileID: 399017375}
  m_Layer: 0
  m_Name: CM Dolly camera in cart
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &399017373
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 114679459329679542, guid: dacddffc9983cdf44bb21aadfd301d81,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 399017372}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45e653bab7fb20e499bda25e1b646fea, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ExcludedPropertiesInInspector:
  - m_Script
  m_LockStageInInspector: 
  m_StreamingVersion: 20170927
  m_Priority: 10
  m_LookAt: {fileID: 480199373}
  m_Follow: {fileID: 1629969563}
  m_Lens:
    FieldOfView: 40
    OrthographicSize: 10
    NearClipPlane: 0.1
    FarClipPlane: 5000
    Dutch: 0
    LensShift: {x: 0, y: 0}
  m_Transitions:
    m_BlendHint: 0
    m_InheritPosition: 0
    m_OnCameraLive:
      m_PersistentCalls:
        m_Calls: []
      m_TypeName: Cinemachine.CinemachineBrain+VcamEvent, Cinemachine, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_LegacyBlendHint: 0
  m_ComponentOwner: {fileID: 1035685395}
--- !u!4 &399017374
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 4962682443765646, guid: dacddffc9983cdf44bb21aadfd301d81,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 399017372}
  m_LocalRotation: {x: 0.10564118, y: 0.7670904, z: -0.13089494, w: 0.61909515}
  m_LocalPosition: {x: -19.545486, y: 10.445385, z: 1.3382607}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1035685395}
  m_Father: {fileID: 0}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &399017375
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 399017372}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4700f9f03ad19f94baf0367cb7a9c988, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Width: 3.68
  m_Damping: 2
  m_MinFOV: 2
  m_MaxFOV: 60
--- !u!1 &410947112
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 410947114}
  - component: {fileID: 410947113}
  m_Layer: 0
  m_Name: GameManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &410947113
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 410947112}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2cfb0b06d5a4af5429ea7f2b67fd8579, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  vcam: {fileID: 0}
--- !u!4 &410947114
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 410947112}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -19.25}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &436401187
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 436401188}
  m_Layer: 0
  m_Name: Environment
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &436401188
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 436401187}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 905855938}
  m_Father: {fileID: 0}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &480199373 stripped
Transform:
  m_PrefabParentObject: {fileID: 4433166873231232, guid: f9dee39d229a4bb4087224760ba2d338,
    type: 2}
  m_PrefabInternal: {fileID: 1251549206}
--- !u!1 &758404294
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 1120736325280268, guid: dacddffc9983cdf44bb21aadfd301d81,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 758404296}
  - component: {fileID: 758404295}
  m_Layer: 0
  m_Name: DollyTrack
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &758404295
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 114863931766110406, guid: dacddffc9983cdf44bb21aadfd301d81,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 758404294}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2d37e5385efd7064cb1d54c94960acae, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Resolution: 20
  m_Appearance:
    pathColor: {r: 0, g: 1, b: 0, a: 1}
    inactivePathColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
    width: 0.2
  m_Looped: 1
  m_Waypoints:
  - position: {x: 0, y: 0, z: -9.136103}
    tangent: {x: 4.7018776, y: 0.30654335, z: 0}
    roll: 0
  - position: {x: 14.513111, y: -2.2013016, z: 5}
    tangent: {x: 0, y: 0, z: 10.140342}
    roll: 0
  - position: {x: 0.3156376, y: 1.0794573, z: 20.542486}
    tangent: {x: -3.8074021, y: 0, z: 0}
    roll: 0
  - position: {x: -15.788214, y: -1.942997, z: 5}
    tangent: {x: 0, y: 0, z: -12.03297}
    roll: 0
--- !u!4 &758404296
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 4684324391922834, guid: dacddffc9983cdf44bb21aadfd301d81,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 758404294}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -4.9569983, y: 12.1, z: -9.655998}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1498267123}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &884881798
Prefab:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 436401188}
    m_Modifications:
    - target: {fileID: 4753686679862902, guid: b6a68029251c6c54ea270e3724819861, type: 2}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4753686679862902, guid: b6a68029251c6c54ea270e3724819861, type: 2}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4753686679862902, guid: b6a68029251c6c54ea270e3724819861, type: 2}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4753686679862902, guid: b6a68029251c6c54ea270e3724819861, type: 2}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4753686679862902, guid: b6a68029251c6c54ea270e3724819861, type: 2}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4753686679862902, guid: b6a68029251c6c54ea270e3724819861, type: 2}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4753686679862902, guid: b6a68029251c6c54ea270e3724819861, type: 2}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4753686679862902, guid: b6a68029251c6c54ea270e3724819861, type: 2}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 100100000, guid: b6a68029251c6c54ea270e3724819861, type: 2}
  m_IsPrefabParent: 0
--- !u!4 &905855938 stripped
Transform:
  m_PrefabParentObject: {fileID: 4753686679862902, guid: b6a68029251c6c54ea270e3724819861,
    type: 2}
  m_PrefabInternal: {fileID: 884881798}
--- !u!1 &1035685394
GameObject:
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 1346676014627556, guid: dacddffc9983cdf44bb21aadfd301d81,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1035685395}
  - component: {fileID: 1035685398}
  - component: {fileID: 1035685397}
  - component: {fileID: 1035685396}
  m_Layer: 0
  m_Name: cm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1035685395
Transform:
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 4553480210941062, guid: dacddffc9983cdf44bb21aadfd301d81,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1035685394}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 399017374}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1035685396
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 114887472121485400, guid: dacddffc9983cdf44bb21aadfd301d81,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1035685394}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 36d1163fa822e8b418a0a603ec078d5c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1035685397
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 114394121848141998, guid: dacddffc9983cdf44bb21aadfd301d81,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1035685394}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4044717213e31446939f7bd49c896ea, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TrackedObjectOffset: {x: 0, y: 0, z: 0}
  m_LookaheadTime: 0.15
  m_LookaheadSmoothing: 20
  m_LookaheadIgnoreY: 0
  m_HorizontalDamping: 0.2
  m_VerticalDamping: 1
  m_ScreenX: 0.5042067
  m_ScreenY: 0.3310714
  m_DeadZoneWidth: 0.1
  m_DeadZoneHeight: 0.1
  m_SoftZoneWidth: 0.8
  m_SoftZoneHeight: 0.8
  m_BiasX: 0
  m_BiasY: 0
--- !u!114 &1035685398
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 114014728496124226, guid: dacddffc9983cdf44bb21aadfd301d81,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1035685394}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac0b09e7857660247b1477e93731de29, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1001 &1251549206
Prefab:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4921245023048312, guid: f9dee39d229a4bb4087224760ba2d338, type: 2}
      propertyPath: m_LocalPosition.x
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 4921245023048312, guid: f9dee39d229a4bb4087224760ba2d338, type: 2}
      propertyPath: m_LocalPosition.y
      value: 1.1
      objectReference: {fileID: 0}
    - target: {fileID: 4921245023048312, guid: f9dee39d229a4bb4087224760ba2d338, type: 2}
      propertyPath: m_LocalPosition.z
      value: -4
      objectReference: {fileID: 0}
    - target: {fileID: 4921245023048312, guid: f9dee39d229a4bb4087224760ba2d338, type: 2}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4921245023048312, guid: f9dee39d229a4bb4087224760ba2d338, type: 2}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4921245023048312, guid: f9dee39d229a4bb4087224760ba2d338, type: 2}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4921245023048312, guid: f9dee39d229a4bb4087224760ba2d338, type: 2}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4921245023048312, guid: f9dee39d229a4bb4087224760ba2d338, type: 2}
      propertyPath: m_RootOrder
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 95487103652067774, guid: f9dee39d229a4bb4087224760ba2d338,
        type: 2}
      propertyPath: m_UpdateMode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 136696895249700382, guid: f9dee39d229a4bb4087224760ba2d338,
        type: 2}
      propertyPath: m_Height
      value: 1.72
      objectReference: {fileID: 0}
    - target: {fileID: 114640801015031044, guid: f9dee39d229a4bb4087224760ba2d338,
        type: 2}
      propertyPath: worldDirection
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114640801015031044, guid: f9dee39d229a4bb4087224760ba2d338,
        type: 2}
      propertyPath: useCharacterForward
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 100100000, guid: f9dee39d229a4bb4087224760ba2d338, type: 2}
  m_IsPrefabParent: 0
--- !u!1 &1498267122
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1498267123}
  m_Layer: 0
  m_Name: DollyTrackWithCart
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1498267123
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1498267122}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 758404296}
  - {fileID: 1629969563}
  m_Father: {fileID: 0}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1629969562
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 1170264707004384, guid: dacddffc9983cdf44bb21aadfd301d81,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1629969563}
  - component: {fileID: 1629969564}
  m_Layer: 0
  m_Name: DollyCart
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1629969563
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 4703474791163362, guid: dacddffc9983cdf44bb21aadfd301d81,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1629969562}
  m_LocalRotation: {x: -0.009768188, y: 0.97754914, z: -0.046517186, w: -0.20527649}
  m_LocalPosition: {x: -19.545486, y: 10.445385, z: 1.3382607}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1498267123}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1629969564
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 114740907135044088, guid: dacddffc9983cdf44bb21aadfd301d81,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1629969562}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99a9c787e5d1bbf48a389834c4a9641c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Path: {fileID: 758404295}
  m_UpdateMethod: 1
  m_PositionUnits: 1
  m_Speed: 1
  m_Position: 64.45
--- !u!1 &1787124812
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1787124816}
  - component: {fileID: 1787124815}
  - component: {fileID: 1787124814}
  - component: {fileID: 1787124813}
  - component: {fileID: 1787124817}
  - component: {fileID: 1787124818}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &1787124813
AudioListener:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1787124812}
  m_Enabled: 1
--- !u!124 &1787124814
Behaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1787124812}
  m_Enabled: 1
--- !u!20 &1787124815
Camera:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1787124812}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.1
  far clip plane: 5000
  field of view: 7.9704194
  orthographic: 0
  orthographic size: 10
  m_Depth: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &1787124816
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1787124812}
  m_LocalRotation: {x: 0.10564118, y: 0.7670904, z: -0.13089494, w: 0.61909515}
  m_LocalPosition: {x: -19.545486, y: 10.445385, z: 1.3382607}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1787124817
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1787124812}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 72ece51f2901e7445ab60da3685d6b5f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ShowDebugText: 0
  m_ShowCameraFrustum: 1
  m_IgnoreTimeScale: 0
  m_WorldUpOverride: {fileID: 0}
  m_UpdateMethod: 2
  m_DefaultBlend:
    m_Style: 1
    m_Time: 2
    m_CustomCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  m_CustomBlends: {fileID: 0}
  m_CameraCutEvent:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: Cinemachine.CinemachineBrain+BrainEvent, Cinemachine, Version=0.0.0.0,
      Culture=neutral, PublicKeyToken=null
  m_CameraActivatedEvent:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: Cinemachine.CinemachineBrain+VcamEvent, Cinemachine, Version=0.0.0.0,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &1787124818
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1787124812}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 803b00b1c55094e499a99684ddbd9b57, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Title: Dolly Cart
  m_Description: 'Turn this window off by removing the script on Main Camera


    The Dolly Cart is set to move at a continuous speed around the dolly track.  The
    camera Aim targets the player while the Body follows the position of the cart


    Note the Look Ahead on the camera''s Aim Composer and the Follow Zoom extension
    which keep the character the same size onscreen no matter where he is in the level'
