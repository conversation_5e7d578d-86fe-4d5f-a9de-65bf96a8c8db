using UnityEngine;
using UnityEditor;
using System.Linq;
using System.Text.RegularExpressions;

namespace VRoidFaceCustomization.Tools
{
    /// <summary>
    /// 快速控制台过滤器
    /// 提供快速的控制台日志过滤功能
    /// </summary>
    public class QuickConsoleFilter : EditorWindow
    {
        [MenuItem("Tools/VRoid Face Customization/Quick Console Filter")]
        public static void ShowWindow()
        {
            var window = GetWindow<QuickConsoleFilter>("快速日志过滤");
            window.minSize = new Vector2(400, 300);
        }
        
        private string inputText = "";
        private string outputText = "";
        private Vector2 scrollPos1, scrollPos2;
        
        private void OnGUI()
        {
            EditorGUILayout.LabelField("快速控制台日志过滤器", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            // 快速按钮
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("从剪贴板粘贴"))
            {
                inputText = EditorGUIUtility.systemCopyBuffer;
            }
            if (GUILayout.Button("快速过滤"))
            {
                QuickFilter();
            }
            if (GUILayout.Button("复制结果"))
            {
                EditorGUIUtility.systemCopyBuffer = outputText;
                ShowNotification(new GUIContent("已复制"));
            }
            if (GUILayout.Button("清空"))
            {
                inputText = "";
                outputText = "";
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            // 输入区域
            EditorGUILayout.LabelField("原始日志:");
            scrollPos1 = EditorGUILayout.BeginScrollView(scrollPos1, GUILayout.Height(120));
            inputText = EditorGUILayout.TextArea(inputText, GUILayout.ExpandHeight(true));
            EditorGUILayout.EndScrollView();
            
            EditorGUILayout.Space();
            
            // 输出区域
            EditorGUILayout.LabelField("过滤后的日志:");
            scrollPos2 = EditorGUILayout.BeginScrollView(scrollPos2, GUILayout.Height(120));
            var style = new GUIStyle(EditorStyles.textArea) { richText = true };
            EditorGUILayout.TextArea(outputText, style, GUILayout.ExpandHeight(true));
            EditorGUILayout.EndScrollView();
            
            // 预设过滤按钮
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("预设过滤:", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("只看我们的日志"))
            {
                FilterOurLogs();
            }
            if (GUILayout.Button("只看错误"))
            {
                FilterErrors();
            }
            if (GUILayout.Button("只看成功"))
            {
                FilterSuccess();
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("移除堆栈"))
            {
                RemoveStackTrace();
            }
            if (GUILayout.Button("只看关键步骤"))
            {
                FilterKeySteps();
            }
            if (GUILayout.Button("统计消息"))
            {
                CountMessages();
            }
            EditorGUILayout.EndHorizontal();
        }
        
        private void QuickFilter()
        {
            if (string.IsNullOrEmpty(inputText))
            {
                outputText = "请输入日志内容";
                return;
            }
            
            var lines = inputText.Split('\n')
                .Where(line => !string.IsNullOrWhiteSpace(line))
                .Select(line => line.Trim())
                .Where(line => !IsStackTrace(line))
                .Where(line => !IsUnityInternal(line))
                .Select(line => CleanLine(line))
                .Where(line => !string.IsNullOrEmpty(line))
                .ToList();
            
            outputText = string.Join("\n", lines);
        }
        
        private void FilterOurLogs()
        {
            if (string.IsNullOrEmpty(inputText)) return;
            
            var keywords = new[] { "ThirdPersonSceneLoader", "VRMModelManager", "SceneTransitionManager", "VRM10UnifiedManager" };
            
            var lines = inputText.Split('\n')
                .Where(line => keywords.Any(keyword => line.Contains(keyword)))
                .Select(line => ExtractMainMessage(line))
                .Where(line => !string.IsNullOrEmpty(line))
                .ToList();
            
            outputText = string.Join("\n", lines);
        }
        
        private void FilterErrors()
        {
            if (string.IsNullOrEmpty(inputText)) return;
            
            var lines = inputText.Split('\n')
                .Where(line => line.Contains("❌") || line.Contains("失败") || line.Contains("错误") || line.Contains("Error"))
                .Select(line => $"<color=red>{ExtractMainMessage(line)}</color>")
                .ToList();
            
            outputText = string.Join("\n", lines);
        }
        
        private void FilterSuccess()
        {
            if (string.IsNullOrEmpty(inputText)) return;
            
            var lines = inputText.Split('\n')
                .Where(line => line.Contains("✅") || line.Contains("成功") || line.Contains("完成"))
                .Select(line => $"<color=green>{ExtractMainMessage(line)}</color>")
                .ToList();
            
            outputText = string.Join("\n", lines);
        }
        
        private void RemoveStackTrace()
        {
            if (string.IsNullOrEmpty(inputText)) return;
            
            var lines = inputText.Split('\n')
                .Where(line => !IsStackTrace(line))
                .Where(line => !IsUnityInternal(line))
                .Select(line => CleanLine(line))
                .Where(line => !string.IsNullOrEmpty(line))
                .ToList();
            
            outputText = string.Join("\n", lines);
        }
        
        private void FilterKeySteps()
        {
            if (string.IsNullOrEmpty(inputText)) return;
            
            var keySteps = new[] { "🎮", "📂", "🏗️", "✅", "❌", "🎭", "📦", "🆕", "🎉" };
            
            var lines = inputText.Split('\n')
                .Where(line => keySteps.Any(step => line.Contains(step)))
                .Select(line => ExtractMainMessage(line))
                .Where(line => !string.IsNullOrEmpty(line))
                .ToList();
            
            outputText = string.Join("\n", lines);
        }
        
        private void CountMessages()
        {
            if (string.IsNullOrEmpty(inputText)) return;
            
            var lines = inputText.Split('\n')
                .Where(line => !string.IsNullOrWhiteSpace(line))
                .Select(line => ExtractMainMessage(line))
                .Where(line => !string.IsNullOrEmpty(line))
                .GroupBy(line => line)
                .OrderByDescending(g => g.Count())
                .Select(g => $"{g.Key} <color=yellow>(×{g.Count()})</color>")
                .ToList();
            
            outputText = string.Join("\n", lines);
        }
        
        private bool IsStackTrace(string line)
        {
            return line.Contains(" (at Assets/") ||
                   line.Contains("UnityEngine.MonoBehaviour:StartCoroutine") ||
                   line.Contains("UnityEngine.Debug:Log") ||
                   line.Contains("SetupCoroutine:InvokeMoveNext");
        }
        
        private bool IsUnityInternal(string line)
        {
            return line.Contains("UnityEngine.") && !line.Contains("[") ||
                   line.Contains("UnityEditor.") ||
                   line.Contains("System.Collections.");
        }
        
        private string CleanLine(string line)
        {
            // 移除时间戳
            line = Regex.Replace(line, @"\d{2}:\d{2}:\d{2}\.\d{3}", "");
            
            // 移除文件路径信息
            line = Regex.Replace(line, @"\(at Assets/[^)]+\)", "");
            
            return line.Trim();
        }
        
        private string ExtractMainMessage(string line)
        {
            // 提取主要消息内容
            if (line.Contains("] "))
            {
                int index = line.LastIndexOf("] ");
                if (index >= 0 && index < line.Length - 2)
                {
                    return line.Substring(index + 2).Trim();
                }
            }
            
            return CleanLine(line);
        }
    }
}
