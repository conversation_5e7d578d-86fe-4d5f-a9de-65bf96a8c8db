ResizableElement
{
    position:absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    flex-direction: row;
    align-items: stretch;
}

ResizableElement #right
{
    width: 10px;
    flex-direction: column;
    align-items: stretch;
}

ResizableElement #left
{
    width: 10px;
    flex-direction: column;
    align-items: stretch;
}
ResizableElement #middle
{
    flex:1 0 auto;
    flex-direction: column;
    align-items: stretch;
}


ResizableElement #left #top-left-resize
{
    height: 10px;
    cursor: resize-up-left;
}

ResizableElement #left #left-resize
{
    flex:1 0 auto;
    cursor: resize-horizontal;
}

ResizableElement #left #bottom-left-resize
{
    height: 10px;
    cursor: resize-up-right;
}

ResizableElement #middle #top-resize
{
    height: 10px;
    cursor: resize-vertical;
}

ResizableElement #middle #middle-center
{
    flex:1 0 auto;
}

ResizableElement #middle #bottom-resize
{
    height: 10px;
    cursor: resize-vertical;
}

ResizableElement #right #top-right-resize
{
    height: 10px;
    cursor: resize-up-right;
}

ResizableElement #right #right-resize
{
    flex:1 0 auto;
    cursor: resize-horizontal;
}

ResizableElement #right #bottom-right-resize
{
    height: 10px;
    cursor: resize-up-left;
}
