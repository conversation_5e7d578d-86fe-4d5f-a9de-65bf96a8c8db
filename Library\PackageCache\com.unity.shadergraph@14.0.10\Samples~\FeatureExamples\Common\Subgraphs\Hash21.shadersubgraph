{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "06f9e921e934457d847e89724f38b4aa",
    "m_Properties": [
        {
            "m_Id": "918e51e0e4ce4634b8f74780255dd320"
        }
    ],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "0179a444c5304e668af24642a0ab024e"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "7cd81ecf6c3a4fc7af5d969b135c5117"
        },
        {
            "m_Id": "dfe0e1c50fb44c23a9cf47c3cb978b13"
        },
        {
            "m_Id": "f329ddd3ccbd40c895e296e64420e5eb"
        }
    ],
    "m_GroupDatas": [],
    "m_StickyNoteDatas": [
        {
            "m_Id": "ca3839df5ebc49a79b043b35c282704a"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "dfe0e1c50fb44c23a9cf47c3cb978b13"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "f329ddd3ccbd40c895e296e64420e5eb"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "f329ddd3ccbd40c895e296e64420e5eb"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "7cd81ecf6c3a4fc7af5d969b135c5117"
                },
                "m_SlotId": 1
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Sub Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": "7cd81ecf6c3a4fc7af5d969b135c5117"
    },
    "m_SubDatas": [],
    "m_ActiveTargets": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "0179a444c5304e668af24642a0ab024e",
    "m_Name": "",
    "m_ChildObjectList": [
        {
            "m_Id": "918e51e0e4ce4634b8f74780255dd320"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "75e5f8553dd24cdc85961e8cdfec1910",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphOutputNode",
    "m_ObjectId": "7cd81ecf6c3a4fc7af5d969b135c5117",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Output",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -428.0,
            "y": 400.66668701171877,
            "width": 85.33328247070313,
            "height": 76.66665649414063
        }
    },
    "m_Slots": [
        {
            "m_Id": "b30c1b64b56b4f97a05c85df5427ed6a"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "IsFirstSlotValid": true
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector2ShaderProperty",
    "m_ObjectId": "918e51e0e4ce4634b8f74780255dd320",
    "m_Guid": {
        "m_GuidSerialized": "d2586364-9f56-48b7-b332-ebcdb7102620"
    },
    "m_Name": "In",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "In",
    "m_DefaultReferenceName": "_In",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "92a3515d4a2240c68de8f422aad3552d",
    "m_Id": 0,
    "m_DisplayName": "p",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "p",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "9a2ef1284aac4c46b47ad782a811da4e",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b30c1b64b56b4f97a05c85df5427ed6a",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "ca3839df5ebc49a79b043b35c282704a",
    "m_Title": "",
    "m_Content": "Generates a random output value for every unique input value.\n\nThis one receives a Vec2 and outputs a Float.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -642.0000610351563,
        "y": 300.5000305175781,
        "width": 200.00003051757813,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "dfe0e1c50fb44c23a9cf47c3cb978b13",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -746.0,
            "y": 443.3333435058594,
            "width": 86.0,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "9a2ef1284aac4c46b47ad782a811da4e"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "918e51e0e4ce4634b8f74780255dd320"
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.CustomFunctionNode",
    "m_ObjectId": "f329ddd3ccbd40c895e296e64420e5eb",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Hash21Tchou (Custom Function)",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -660.0000610351563,
            "y": 400.5000305175781,
            "width": 230.50006103515626,
            "height": 94.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "92a3515d4a2240c68de8f422aad3552d"
        },
        {
            "m_Id": "75e5f8553dd24cdc85961e8cdfec1910"
        }
    ],
    "synonyms": [
        "code",
        "HLSL"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SourceType": 1,
    "m_FunctionName": "Hash21Tchou",
    "m_FunctionSource": "",
    "m_FunctionBody": "uint r;\r\nuint2 v = (uint2) (int2) round(p);\r\nv.y ^= 1103515245U;\r\nv.x += v.y;\r\nv.x *= v.y;                        \nv.x ^= v.x >> 5u;             \nv.x *= 0x27d4eb2du;     \nr = v.x;\r\nOut = r * (1.0 / float(0xffffffff));"
}

