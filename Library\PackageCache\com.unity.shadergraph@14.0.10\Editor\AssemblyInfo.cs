using System.Runtime.CompilerServices;

[assembly: InternalsVisibleTo("Unity.ShaderGraph.Editor.Tests")]
[assembly: InternalsVisibleTo("Unity.RenderPipelines.Universal.Editor")]
[assembly: InternalsVisibleTo("Unity.ShaderGraph.GraphicsTests")]
[assembly: InternalsVisibleTo("Unity.ShaderGraph.Editor.GraphicsTests")]
[assembly: InternalsVisibleTo("Unity.RenderPipelines.HighDefinition.Editor")]
[assembly: InternalsVisibleTo("Unity.VisualEffectGraph.Editor")]
[assembly: InternalsVisibleTo("Unity.Industrial.Materials.AVRD.Editor")]
[assembly: InternalsVisibleTo("Unity.VisualEffectGraph.EditorTests")]
[assembly: InternalsVisibleTo("Unity.XR.Quantum.Editor.ShaderGraph")]
