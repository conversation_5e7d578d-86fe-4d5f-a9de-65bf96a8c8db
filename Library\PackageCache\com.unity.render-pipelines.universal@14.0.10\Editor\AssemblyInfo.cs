using System.Runtime.CompilerServices;

[assembly: InternalsVisibleTo("DynamicProxyGenAssembly2")] // permit using internal interfaces with Moq
[assembly: InternalsVisibleTo("UniversalGraphicsTests")]
[assembly: InternalsVisibleTo("Unity.RenderPipelines.Universal.Editor.Tests")]
[assembly: InternalsVisibleTo("Unity.Testing.SRP.Universal-Upgrade.Editor")]
[assembly: InternalsVisibleTo("Unity.GraphicTests.Performance.Universal.Editor")]
[assembly: InternalsVisibleTo("Unity.VisualEffectGraph.EditorTests")]
