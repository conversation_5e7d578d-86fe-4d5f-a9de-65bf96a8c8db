{"name": "Unity.Burst.Editor.Tests", "references": ["Unity.Burst", "Unity.Mathematics", "Unity.Burst.Tests.UnitTests", "SeparateAssembly", "Unity.Burst.Editor"], "optionalUnityReferences": ["TestAssemblies"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": true, "overrideReferences": true, "precompiledReferences": ["nunit.framework.dll"], "autoReferenced": false, "defineConstraints": ["UNITY_INCLUDE_TESTS"], "versionDefines": [{"name": "Unity", "expression": "", "define": "FOO"}], "noEngineReferences": false}