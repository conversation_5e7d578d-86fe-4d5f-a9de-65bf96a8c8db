# Custom rendering and post-processing 

Customize and extend the rendering process in the Universal Render Pipeline (URP). URP uses Renderer Features to implement certain effects. URP includes a selection of pre-built Renderer Features and the ability to create customized Renderer Features known as Scriptable Renderer Features.

| Page | Description |
|-|-|
|[Custom render passes](renderer-features/custom-rendering-passes.md)|Create a custom render pass in a C# script and inject it into the URP frame rendering loop.|
|[Injection points reference](customize/custom-pass-injection-points.md)|The injection points you can use to inject render passes into the frame rendering loop.|
|[Scriptable Renderer Feature and Scriptable Render Pass API reference](renderer-features/scriptable-renderer-features/scriptable-renderer-feature-reference.md)|Common methods you can use to write Scriptable Renderer Passes and Scriptable Renderer Features.|

## Additional resources

- [Pre-built effects (Renderer Features)](urp-renderer-feature.md)
- [How to create a custom post-processing effect](post-processing/post-processing-custom-effect-low-code.md)
