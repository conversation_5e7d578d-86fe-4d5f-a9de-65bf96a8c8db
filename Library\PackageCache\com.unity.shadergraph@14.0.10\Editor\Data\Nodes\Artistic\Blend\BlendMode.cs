namespace UnityEditor.ShaderGraph
{
    enum BlendMode
    {
        Burn,
        <PERSON>en,
        Difference,
        <PERSON>,
        Divide,
        Exclusion,
        HardLight,
        HardMix,
        Lighten,
        LinearBurn,
        LinearDodge,
        LinearLight,
        LinearLightAddSub,
        Multiply,
        Negation,
        Overlay,
        PinLight,
        Screen,
        SoftLight,
        Subtract,
        VividLight,
        Overwrite
    }
}
