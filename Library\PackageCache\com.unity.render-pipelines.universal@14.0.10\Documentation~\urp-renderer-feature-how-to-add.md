# How to add a Renderer Feature to a Renderer

To add a Renderer Feature to a Renderer:

1. In the __Project__ window, select a Renderer.

    ![Select a Renderer.](Images/add-renderer-feature/renderer-feature-select-renderer.png)

    The Inspector window shows the Renderer properties.

    ![Inspector window shows the Renderer properties.](Images/add-renderer-feature/renderer-feature-inspector-no-rend-features.png)

2. In the Inspector window, select __Add Renderer Feature__. In the list, select a Renderer Feature.

    ![Select __Add Renderer Feature__, then select a Renderer Feature.](Images/add-renderer-feature/renderer-feature-select-renderer-feature.png)

    Unity adds the selected Renderer Feature to the Renderer.

    ![New Renderer Feature added.](Images/add-renderer-feature/renderer-feature-created.png)

Unity shows Renderer Features as child items of the Renderer in the Project Window:

![Renderer Feature as child item of the Renderer in the Project Window](Images/add-renderer-feature/renderer-feature-project-window.png)
