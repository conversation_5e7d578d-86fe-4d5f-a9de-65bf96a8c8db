# Creating a sample scene

To follow the examples in this section:

1. Install URP into an existing Unity project, or create a new project using the [__Universal Project Template__](https://docs.unity3d.com/Packages/com.unity.render-pipelines.universal@8.0/manual/creating-a-new-project-with-urp.html).

2. In the sample Scene, create a GameObject to test the shaders on; for example, a capsule.

    ![Sample GameObject](Images/shader-examples/urp-template-sample-object.png)

3. Create a new Material and assign it to the capsule.

4. Create a new Shader asset and assign it to the Material of the capsule. When following an example, open the shader asset to edit the Unity shader source file. Replace the code in the source file with the code in the example.

To start writing URP shaders, continue to section [URP unlit basic shader](writing-shaders-urp-basic-unlit-structure.md).
