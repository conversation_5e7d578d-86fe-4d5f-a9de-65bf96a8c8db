# Node Library

## Description

The **Node Library** contains documentation for all the individual [Nodes](Node.md) in Shader Graph; including descriptions, ports, parameters, shader code and example images. The [Nodes](Node.md) are organised in the same categories as found in the [Create Node Menu](Create-Node-Menu.md) for convenience.

## Graph Nodes

| | |
|:--|:--|
| [Artistic](Artistic-Nodes.md)| [Channel](Channel-Nodes.md)|
| [Input](Input-Nodes.md) | [Math](Math-Nodes.md) |
| [Procedural](Procedural-Nodes.md) | [Utility](Utility-Nodes.md)|
| [UV](UV-Nodes.md) | |


## Block Nodes

| | |
|:-|:-|
| [Built In](Built-In-Blocks.md) |  |
| Universal | High Definition |
