{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "f0884f927810451c8932a84be541011d",
    "m_Properties": [],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "44d87f4aca09413d91f37b5b6ae610e0"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "e625235660d54ce3927040376e0c97b5"
        },
        {
            "m_Id": "9e1940bb2885439f8bb94ccef33a7fb9"
        },
        {
            "m_Id": "f451e43f57b3411489d0a68044593919"
        },
        {
            "m_Id": "18cbfac04a6941ff8c490a28185928ee"
        },
        {
            "m_Id": "ea3cd1e82250401cb5c9b5c9adfa49ad"
        },
        {
            "m_Id": "7aa3a8fe03124907b54d26e5a93da5b4"
        },
        {
            "m_Id": "a6b8275941cc4af88b92e6da8019536e"
        },
        {
            "m_Id": "7fc3592345f24a0b9dd5bc5783b5aa6b"
        },
        {
            "m_Id": "c46ba95bb8d34e3e868a628476bb3825"
        },
        {
            "m_Id": "347fe38f85a849b286694958595aea8d"
        },
        {
            "m_Id": "dc076e6dade44c3fa17355d405602560"
        },
        {
            "m_Id": "6fc717080c68404baa6abd344d13e7b4"
        },
        {
            "m_Id": "b4b768b290c34d2bab0cdeb4dbd8cd4a"
        },
        {
            "m_Id": "a8d718227e3c4fca9711d279a90ca5c7"
        },
        {
            "m_Id": "5bce790aa7d84268a37b8624a5e5bcc5"
        },
        {
            "m_Id": "d1c8dc7db4f240deb4bfc4af8c16da3d"
        },
        {
            "m_Id": "1e514d7b20de4649a9d1c476f5c8eb3f"
        }
    ],
    "m_GroupDatas": [
        {
            "m_Id": "1b36861b9c89441383dc516d4a021e6d"
        },
        {
            "m_Id": "cecb12d106d54edb81c372ca338a2041"
        }
    ],
    "m_StickyNoteDatas": [
        {
            "m_Id": "0aa5be59e7504e5ba35c0df6de802278"
        },
        {
            "m_Id": "e777ca52a62c4f11a65a40c1ed6a001e"
        },
        {
            "m_Id": "423f1fe4988240fcae56a3bbc7d9d95b"
        },
        {
            "m_Id": "be40dd10e01e492bb8f4912e5c2b477f"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "347fe38f85a849b286694958595aea8d"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "5bce790aa7d84268a37b8624a5e5bcc5"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "5bce790aa7d84268a37b8624a5e5bcc5"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "dc076e6dade44c3fa17355d405602560"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "7aa3a8fe03124907b54d26e5a93da5b4"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "7fc3592345f24a0b9dd5bc5783b5aa6b"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "7aa3a8fe03124907b54d26e5a93da5b4"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "a6b8275941cc4af88b92e6da8019536e"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "7fc3592345f24a0b9dd5bc5783b5aa6b"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "a6b8275941cc4af88b92e6da8019536e"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "c46ba95bb8d34e3e868a628476bb3825"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "347fe38f85a849b286694958595aea8d"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "dc076e6dade44c3fa17355d405602560"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "6fc717080c68404baa6abd344d13e7b4"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "dc076e6dade44c3fa17355d405602560"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "b4b768b290c34d2bab0cdeb4dbd8cd4a"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "dc076e6dade44c3fa17355d405602560"
                },
                "m_SlotId": 3
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "a8d718227e3c4fca9711d279a90ca5c7"
                },
                "m_SlotId": 0
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": [
            {
                "m_Id": "e625235660d54ce3927040376e0c97b5"
            },
            {
                "m_Id": "9e1940bb2885439f8bb94ccef33a7fb9"
            },
            {
                "m_Id": "f451e43f57b3411489d0a68044593919"
            }
        ]
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 200.0
        },
        "m_Blocks": [
            {
                "m_Id": "18cbfac04a6941ff8c490a28185928ee"
            },
            {
                "m_Id": "d1c8dc7db4f240deb4bfc4af8c16da3d"
            },
            {
                "m_Id": "1e514d7b20de4649a9d1c476f5c8eb3f"
            }
        ]
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Shader Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": ""
    },
    "m_ActiveTargets": [
        {
            "m_Id": "d6c1732b92a44042aea65506096262d2"
        },
        {
            "m_Id": "0fad20a24feb421d86b63f37862db9fa"
        },
        {
            "m_Id": "2e6ba64d3c354498b668bd0d55003c48"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "0aa5be59e7504e5ba35c0df6de802278",
    "m_Title": "Sign Node",
    "m_Content": "The Sign Node gives the sign (negative or positive) of the input. It returns 1 if the input is positive and -1 if the input is negative. If the number is zero, it returns 0.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1082.5001220703125,
        "y": -85.50000762939453,
        "width": 200.00006103515626,
        "height": 100.00000762939453
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDTarget",
    "m_ObjectId": "0fad20a24feb421d86b63f37862db9fa",
    "m_ActiveSubTarget": {
        "m_Id": "8d91eed9ca574d7a846cbd354f31da71"
    },
    "m_Datas": [
        {
            "m_Id": "d842d0b357c94c77bb5b06e525ca39e6"
        },
        {
            "m_Id": "eec503e49a304e80813f02ae3a7e7793"
        },
        {
            "m_Id": "cf3f3748da5a475cbf24d1f65cb72581"
        }
    ],
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false,
    "m_SupportLineRendering": false
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalUnlitSubTarget",
    "m_ObjectId": "1091133922e1422b90a489e8d23340a6"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "18cbfac04a6941ff8c490a28185928ee",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.BaseColor",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "e620fadba331486fbc9fa17707323f54"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.BaseColor"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "1b36861b9c89441383dc516d4a021e6d",
    "m_Title": "Under The Hood",
    "m_Position": {
        "x": -1802.5001220703125,
        "y": 45.0000114440918
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "1cf8dfd21da04e25a7be6bff33ec8138",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "1e514d7b20de4649a9d1c476f5c8eb3f",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Alpha",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "84e25a536cce41c0b7034ef911456256"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Alpha"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TangentMaterialSlot",
    "m_ObjectId": "28a74eac0b04467485adab27c09d8e9b",
    "m_Id": 0,
    "m_DisplayName": "Tangent",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tangent",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "2d586535604144848b3ea7a43921fd91",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "2d668a74c6f34963abd51d37a9992b54",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 2.0,
        "y": 2.0,
        "z": 2.0,
        "w": 2.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget",
    "m_ObjectId": "2e6ba64d3c354498b668bd0d55003c48",
    "m_Datas": [],
    "m_ActiveSubTarget": {
        "m_Id": "1091133922e1422b90a489e8d23340a6"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZTestMode": 4,
    "m_ZWriteControl": 0,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CastShadows": true,
    "m_ReceiveShadows": true,
    "m_SupportsLODCrossFade": false,
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "2ee3cc2492a44db89fbfc03e0b7c2c32",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SignNode",
    "m_ObjectId": "347fe38f85a849b286694958595aea8d",
    "m_Group": {
        "m_Id": "cecb12d106d54edb81c372ca338a2041"
    },
    "m_Name": "Sign",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1041.5001220703125,
            "y": 101.00003051757813,
            "width": 208.00006103515626,
            "height": 277.9999694824219
        }
    },
    "m_Slots": [
        {
            "m_Id": "863460c8ff3e41d1a4c7be6ac9031acb"
        },
        {
            "m_Id": "634795d8166f4a0eace6ca084e113d1b"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "367b8236caf6475cbd33804228cb10f2",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInUnlitSubTarget",
    "m_ObjectId": "3d08ba479c0e402d82c02f635d1dce9d"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "423f1fe4988240fcae56a3bbc7d9d95b",
    "m_Title": "",
    "m_Content": "The Sign node divides the input value by the absolute value of the input resulting in either 1 or -1.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1662.0001220703125,
        "y": 311.0000305175781,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "1b36861b9c89441383dc516d4a021e6d"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "44d87f4aca09413d91f37b5b6ae610e0",
    "m_Name": "",
    "m_ChildObjectList": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "4a37e39398324784bb000032441bb87b",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "582113be54024b008c802a9c028e9e94",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SaturateNode",
    "m_ObjectId": "5bce790aa7d84268a37b8624a5e5bcc5",
    "m_Group": {
        "m_Id": "cecb12d106d54edb81c372ca338a2041"
    },
    "m_Name": "Saturate",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -869.5000610351563,
            "y": 101.00003051757813,
            "width": 131.5,
            "height": 94.00001525878906
        }
    },
    "m_Slots": [
        {
            "m_Id": "2d586535604144848b3ea7a43921fd91"
        },
        {
            "m_Id": "fa95a05a15a54767887e9001b89a2c12"
        }
    ],
    "synonyms": [
        "clamp"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "634795d8166f4a0eace6ca084e113d1b",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "670a53da3f6c4a51929dbbe01475de8c",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PreviewNode",
    "m_ObjectId": "6fc717080c68404baa6abd344d13e7b4",
    "m_Group": {
        "m_Id": "cecb12d106d54edb81c372ca338a2041"
    },
    "m_Name": "Preview",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -545.5000610351563,
            "y": 101.00003051757813,
            "width": 208.00003051757813,
            "height": 277.9999694824219
        }
    },
    "m_Slots": [
        {
            "m_Id": "c44f75e542be4b1d9aad9a8686698954"
        },
        {
            "m_Id": "e7a1ba5b66f34b329275304975709bd6"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Width": 208.0,
    "m_Height": 208.0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "7393fd611b6d4f47b12fe9082da7f111",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "743971e05d424b9eb4f2a452bf393afa",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1Node",
    "m_ObjectId": "7aa3a8fe03124907b54d26e5a93da5b4",
    "m_Group": {
        "m_Id": "1b36861b9c89441383dc516d4a021e6d"
    },
    "m_Name": "Float",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1777.5001220703125,
            "y": 132.00001525878907,
            "width": 125.5,
            "height": 77.00001525878906
        }
    },
    "m_Slots": [
        {
            "m_Id": "caf7f54522824253a3a6894f30daa470"
        },
        {
            "m_Id": "9fc7ce30920e46708cc355e43b45025c"
        }
    ],
    "synonyms": [
        "Vector 1",
        "1",
        "v1",
        "vec1",
        "scalar"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": 0.0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "7b3eeb545b354d29992eec318f7eab13",
    "m_Id": 0,
    "m_DisplayName": "Emission",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Emission",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 1,
    "m_DefaultColor": {
        "r": 0.0,
        "g": 0.0,
        "b": 0.0,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.AbsoluteNode",
    "m_ObjectId": "7fc3592345f24a0b9dd5bc5783b5aa6b",
    "m_Group": {
        "m_Id": "1b36861b9c89441383dc516d4a021e6d"
    },
    "m_Name": "Absolute",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1627.5001220703125,
            "y": 209.00003051757813,
            "width": 127.5,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "1cf8dfd21da04e25a7be6bff33ec8138"
        },
        {
            "m_Id": "582113be54024b008c802a9c028e9e94"
        }
    ],
    "synonyms": [
        "positive"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "82f311ab62224d59af1763698c911b42",
    "m_Id": 2,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "83a9c05bc9c4485c8f029c77ca3e4356",
    "m_Id": 1,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "84e25a536cce41c0b7034ef911456256",
    "m_Id": 0,
    "m_DisplayName": "Alpha",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Alpha",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "863460c8ff3e41d1a4c7be6ac9031acb",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "8a827a48f41b4df4b39ea732aece2b72",
    "m_Id": 0,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "8c93d87d32434b7d8ed3e3f0741732a6",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "8d91eed9ca574d7a846cbd354f31da71"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "9e1940bb2885439f8bb94ccef33a7fb9",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Normal",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "8a827a48f41b4df4b39ea732aece2b72"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Normal"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "9fc7ce30920e46708cc355e43b45025c",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "a3d3f801419d4ba89a1661fd77c9c9f9",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DivideNode",
    "m_ObjectId": "a6b8275941cc4af88b92e6da8019536e",
    "m_Group": {
        "m_Id": "1b36861b9c89441383dc516d4a021e6d"
    },
    "m_Name": "Divide",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1476.5001220703125,
            "y": 132.00001525878907,
            "width": 126.0,
            "height": 118.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "743971e05d424b9eb4f2a452bf393afa"
        },
        {
            "m_Id": "2d668a74c6f34963abd51d37a9992b54"
        },
        {
            "m_Id": "a3d3f801419d4ba89a1661fd77c9c9f9"
        }
    ],
    "synonyms": [
        "division",
        "divided by"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PreviewNode",
    "m_ObjectId": "a8d718227e3c4fca9711d279a90ca5c7",
    "m_Group": {
        "m_Id": "cecb12d106d54edb81c372ca338a2041"
    },
    "m_Name": "Preview",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -545.5,
            "y": 657.0000610351563,
            "width": 207.99996948242188,
            "height": 278.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "a9c3f62b3cfd43d9acb036a4310e85de"
        },
        {
            "m_Id": "df29222382e64101b3e8dcea1f2f6960"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Width": 208.0,
    "m_Height": 208.0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "a9c3f62b3cfd43d9acb036a4310e85de",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PreviewNode",
    "m_ObjectId": "b4b768b290c34d2bab0cdeb4dbd8cd4a",
    "m_Group": {
        "m_Id": "cecb12d106d54edb81c372ca338a2041"
    },
    "m_Name": "Preview",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -545.5000610351563,
            "y": 379.0000305175781,
            "width": 208.00003051757813,
            "height": 278.0000305175781
        }
    },
    "m_Slots": [
        {
            "m_Id": "ce3c732f9b7a42fbb0fb7e4a803b8a16"
        },
        {
            "m_Id": "2ee3cc2492a44db89fbfc03e0b7c2c32"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Width": 208.0,
    "m_Height": 208.0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "be40dd10e01e492bb8f4912e5c2b477f",
    "m_Title": "",
    "m_Content": "You can use the Sign Node with the Normal Vector node to generate masks to tell you if a surface is facing the left or right, top or bottom, and front or back.\n\nThis is useful if you want to do something different on one side of your model from the other.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1028.5001220703125,
        "y": 227.00003051757813,
        "width": 200.00006103515626,
        "height": 160.0
    },
    "m_Group": {
        "m_Id": "cecb12d106d54edb81c372ca338a2041"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "c44f75e542be4b1d9aad9a8686698954",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalVectorNode",
    "m_ObjectId": "c46ba95bb8d34e3e868a628476bb3825",
    "m_Group": {
        "m_Id": "cecb12d106d54edb81c372ca338a2041"
    },
    "m_Name": "Normal Vector",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1282.5001220703125,
            "y": 101.00003051757813,
            "width": 208.0,
            "height": 314.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "670a53da3f6c4a51929dbbe01475de8c"
        }
    ],
    "synonyms": [
        "surface direction"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "caf7f54522824253a3a6894f30daa470",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 12.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ccf879a1179e47d782f582aba046abb9",
    "m_Id": 3,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "ce3c732f9b7a42fbb0fb7e4a803b8a16",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "cecb12d106d54edb81c372ca338a2041",
    "m_Title": "Directional Masks",
    "m_Position": {
        "x": 10.0,
        "y": 10.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "cf3f3748da5a475cbf24d1f65cb72581",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "d1363e022a614862a6849b88e31dc43f",
    "m_Id": 4,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "d1c8dc7db4f240deb4bfc4af8c16da3d",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Emission",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "7b3eeb545b354d29992eec318f7eab13"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Emission"
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInTarget",
    "m_ObjectId": "d6c1732b92a44042aea65506096262d2",
    "m_ActiveSubTarget": {
        "m_Id": "3d08ba479c0e402d82c02f635d1dce9d"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZWriteControl": 0,
    "m_ZTestMode": 4,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CustomEditorGUI": ""
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "d842d0b357c94c77bb5b06e525ca39e6",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SplitNode",
    "m_ObjectId": "dc076e6dade44c3fa17355d405602560",
    "m_Group": {
        "m_Id": "cecb12d106d54edb81c372ca338a2041"
    },
    "m_Name": "Split",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -738.0000610351563,
            "y": 101.00000762939453,
            "width": 118.99993896484375,
            "height": 125.00003814697266
        }
    },
    "m_Slots": [
        {
            "m_Id": "7393fd611b6d4f47b12fe9082da7f111"
        },
        {
            "m_Id": "83a9c05bc9c4485c8f029c77ca3e4356"
        },
        {
            "m_Id": "82f311ab62224d59af1763698c911b42"
        },
        {
            "m_Id": "ccf879a1179e47d782f582aba046abb9"
        },
        {
            "m_Id": "d1363e022a614862a6849b88e31dc43f"
        }
    ],
    "synonyms": [
        "separate"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "df29222382e64101b3e8dcea1f2f6960",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "e620fadba331486fbc9fa17707323f54",
    "m_Id": 0,
    "m_DisplayName": "Base Color",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BaseColor",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.5,
        "y": 0.5,
        "z": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.5,
        "g": 0.5,
        "b": 0.5,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "e625235660d54ce3927040376e0c97b5",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "367b8236caf6475cbd33804228cb10f2"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Position"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "e777ca52a62c4f11a65a40c1ed6a001e",
    "m_Title": "",
    "m_Content": "Input value",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1761.0001220703125,
        "y": 103.50001525878906,
        "width": 80.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "1b36861b9c89441383dc516d4a021e6d"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "e7a1ba5b66f34b329275304975709bd6",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SignNode",
    "m_ObjectId": "ea3cd1e82250401cb5c9b5c9adfa49ad",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Sign",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1215.0001220703125,
            "y": -90.99999237060547,
            "width": 127.5,
            "height": 93.99998474121094
        }
    },
    "m_Slots": [
        {
            "m_Id": "4a37e39398324784bb000032441bb87b"
        },
        {
            "m_Id": "8c93d87d32434b7d8ed3e3f0741732a6"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "eec503e49a304e80813f02ae3a7e7793",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "f451e43f57b3411489d0a68044593919",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Tangent",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "28a74eac0b04467485adab27c09d8e9b"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Tangent"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "fa95a05a15a54767887e9001b89a2c12",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

