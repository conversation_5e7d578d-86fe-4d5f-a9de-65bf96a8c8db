{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "2387cdd9f7c24caabd3c446c180dcc52",
    "m_Properties": [],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "5e5da317a75f4a4ea9fb4d05440fdd51"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "84cef664682e40f1a0b946632837d439"
        },
        {
            "m_Id": "87b3372f87f343ea924aeac5a6dcc5bf"
        },
        {
            "m_Id": "d9bcb957e33044ae97b7a25cfefdb60a"
        },
        {
            "m_Id": "34557bb8d7e94496afb986016cbeca08"
        },
        {
            "m_Id": "2845e12eb5eb43329dd215d58045a115"
        },
        {
            "m_Id": "5b7d684b62c3468cbfd07d8db84cb86d"
        },
        {
            "m_Id": "e555198ae9fa4c01bb815f75ca069763"
        },
        {
            "m_Id": "adca4e85ee574dd38b95f215a1e255a5"
        },
        {
            "m_Id": "ed76d158ae9a45b6ae9de4b626cb56d7"
        },
        {
            "m_Id": "c37141bf60ae451d800d304cc4797878"
        },
        {
            "m_Id": "7c9368bcec404a36b744fd1a0946231d"
        }
    ],
    "m_GroupDatas": [
        {
            "m_Id": "0c13c24a35ee473982af6b8b25a11135"
        }
    ],
    "m_StickyNoteDatas": [
        {
            "m_Id": "331d5e76fe6b40909f48870c278add04"
        },
        {
            "m_Id": "74986e6b26ea4a1581b895f4a3036c9a"
        },
        {
            "m_Id": "1fb9f81a17a842ee8b092456b902d0be"
        },
        {
            "m_Id": "136d6ba1093a4a688aa5534dc555d355"
        },
        {
            "m_Id": "b7488da65ed546908dde623c06e0bd14"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "5b7d684b62c3468cbfd07d8db84cb86d"
                },
                "m_SlotId": 4
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "adca4e85ee574dd38b95f215a1e255a5"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "e555198ae9fa4c01bb815f75ca069763"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "5b7d684b62c3468cbfd07d8db84cb86d"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "ed76d158ae9a45b6ae9de4b626cb56d7"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "5b7d684b62c3468cbfd07d8db84cb86d"
                },
                "m_SlotId": 0
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": [
            {
                "m_Id": "84cef664682e40f1a0b946632837d439"
            },
            {
                "m_Id": "87b3372f87f343ea924aeac5a6dcc5bf"
            },
            {
                "m_Id": "d9bcb957e33044ae97b7a25cfefdb60a"
            }
        ]
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 200.0
        },
        "m_Blocks": [
            {
                "m_Id": "34557bb8d7e94496afb986016cbeca08"
            },
            {
                "m_Id": "c37141bf60ae451d800d304cc4797878"
            },
            {
                "m_Id": "7c9368bcec404a36b744fd1a0946231d"
            }
        ]
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Shader Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": ""
    },
    "m_ActiveTargets": [
        {
            "m_Id": "e6bb596de71c4ce19f5f06efebd6b5ff"
        },
        {
            "m_Id": "18439004a96448c5b4ffd8d246337805"
        },
        {
            "m_Id": "f7e260f6635f4fadb06732126227f06d"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "0c13c24a35ee473982af6b8b25a11135",
    "m_Title": "Creating a Refraction Effect",
    "m_Position": {
        "x": -1372.4998779296875,
        "y": 123.50003814697266
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0d574f4aba9d44c19fb4c83f8049fb9d",
    "m_Id": 4,
    "m_DisplayName": "LOD",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "LOD",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "0f9011644c8a4dd0a6734f239fe23841",
    "m_Id": 2,
    "m_DisplayName": "Dir",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Dir",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "136d6ba1093a4a688aa5534dc555d355",
    "m_Title": "Indices of Refraction",
    "m_Content": "Refraction indices for some common materials:\n\nAir: \t\t1.000293\nIce:\t\t1.31\nWater: \t1.333\nGlass:\t1.52\nDiamond:\t2.42",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -482.5000305175781,
        "y": 271.0000305175781,
        "width": 279.5,
        "height": 137.0
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "13809205dbbe40f7a294c80b2c1e5d95",
    "m_Id": 1,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": -1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDTarget",
    "m_ObjectId": "18439004a96448c5b4ffd8d246337805",
    "m_ActiveSubTarget": {
        "m_Id": "41f49840991d48b2bf90da9289d23b5d"
    },
    "m_Datas": [
        {
            "m_Id": "b75b3623ba2e4ce3a04583d55e1219aa"
        },
        {
            "m_Id": "4793aa79e81e41f8865b836a6279fd3c"
        },
        {
            "m_Id": "8e50b421642140abb67e94dc3df2c129"
        }
    ],
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false,
    "m_SupportLineRendering": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "19543d8a22dc45659669e38584d99e1f",
    "m_Id": 0,
    "m_DisplayName": "Incident",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Incident",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "1a9f9da5e6b0437ab22b9a007791ba7a",
    "m_Id": 3,
    "m_DisplayName": "IORMedium",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "IORMedium",
    "m_StageCapability": 3,
    "m_Value": 1.5,
    "m_DefaultValue": 1.5,
    "m_Labels": []
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalUnlitSubTarget",
    "m_ObjectId": "1d921e6822284408949dbb938f203908"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "1fb9f81a17a842ee8b092456b902d0be",
    "m_Title": "",
    "m_Content": "We use the refraction vector to sample a cubemap texture.  The result is what the environment would look like when seen through our transparent glass object.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -775.5000610351563,
        "y": 463.5000305175781,
        "width": 200.0,
        "height": 100.00003051757813
    },
    "m_Group": {
        "m_Id": "0c13c24a35ee473982af6b8b25a11135"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.RefractNode",
    "m_ObjectId": "2845e12eb5eb43329dd215d58045a115",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Refract",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -934.9999389648438,
            "y": -119.99999237060547,
            "width": 212.5,
            "height": 200.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "19543d8a22dc45659669e38584d99e1f"
        },
        {
            "m_Id": "43aee704d4ee4699ad3431ef5bfd9823"
        },
        {
            "m_Id": "6929fb921de74c1b88760eb6f2df26f6"
        },
        {
            "m_Id": "887584aeb031478e94fda43fabe6d2c0"
        },
        {
            "m_Id": "6d3e83f6dee24072ac426ca4a7f6c879"
        },
        {
            "m_Id": "75f125c08f2f44bc8f92e33f81039f86"
        }
    ],
    "synonyms": [
        "refract",
        "warp",
        "bend",
        "distort"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_RefractMode": 1
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "2df0762bb4e648ca8e575b012414c865",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "2f792de092884e3fbbc9f1f996062a81",
    "m_Id": 3,
    "m_DisplayName": "Near Plane",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Near Plane",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "331d5e76fe6b40909f48870c278add04",
    "m_Title": "Refract Node",
    "m_Content": "Creates a refraction vector from an input vector, a Normal, and the indices of refraction for the source and target materials.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -684.0000610351563,
        "y": -66.00000762939453,
        "width": 200.00003051757813,
        "height": 100.00000762939453
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "34557bb8d7e94496afb986016cbeca08",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.BaseColor",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "47a4c262be2f4b90a526acd0ab1dd4f5"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.BaseColor"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "383cc7f27c32460db0a60b791aa27fe0",
    "m_Id": 7,
    "m_DisplayName": "Height",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Height",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "419226d738e84164887ee38b9a9e042b",
    "m_Id": 0,
    "m_DisplayName": "Alpha",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Alpha",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "41f49840991d48b2bf90da9289d23b5d"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "4395319855a04b8cb0bc25f3c2306a33",
    "m_Id": 0,
    "m_DisplayName": "Emission",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Emission",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 1,
    "m_DefaultColor": {
        "r": 0.0,
        "g": 0.0,
        "b": 0.0,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "43aee704d4ee4699ad3431ef5bfd9823",
    "m_Id": 1,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": -1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "4793aa79e81e41f8865b836a6279fd3c",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "47a4c262be2f4b90a526acd0ab1dd4f5",
    "m_Id": 0,
    "m_DisplayName": "Base Color",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BaseColor",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.5,
        "y": 0.5,
        "z": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.5,
        "g": 0.5,
        "b": 0.5,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "49c02aa6cca243abae65734aec8e43f9",
    "m_Id": 5,
    "m_DisplayName": "Z Buffer Sign",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Z Buffer Sign",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "50cceb347024489491755be403a001e7",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.RefractNode",
    "m_ObjectId": "5b7d684b62c3468cbfd07d8db84cb86d",
    "m_Group": {
        "m_Id": "0c13c24a35ee473982af6b8b25a11135"
    },
    "m_Name": "Refract",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1041.0,
            "y": 182.00003051757813,
            "width": 212.00006103515626,
            "height": 200.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "82d69041b74e4b34a5672aa2713ef036"
        },
        {
            "m_Id": "13809205dbbe40f7a294c80b2c1e5d95"
        },
        {
            "m_Id": "84e959134fea4e90b7727d1fe1fa3f7f"
        },
        {
            "m_Id": "1a9f9da5e6b0437ab22b9a007791ba7a"
        },
        {
            "m_Id": "a92418725f294195b9c91c794cf74814"
        },
        {
            "m_Id": "ba3ebfc04e714cc8b05b24ba471294a7"
        }
    ],
    "synonyms": [
        "refract",
        "warp",
        "bend",
        "distort"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_RefractMode": 1
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "5e5da317a75f4a4ea9fb4d05440fdd51",
    "m_Name": "",
    "m_ChildObjectList": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "5eb6b4e0481c46e9b8b3df01b4362456",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "6929fb921de74c1b88760eb6f2df26f6",
    "m_Id": 2,
    "m_DisplayName": "IORSource",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "IORSource",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "6d3e83f6dee24072ac426ca4a7f6c879",
    "m_Id": 4,
    "m_DisplayName": "Refracted",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Refracted",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "6fd5e3f671ca4cd18a0cff61cf2b18d7",
    "m_Id": 4,
    "m_DisplayName": "Far Plane",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Far Plane",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "70278426dc904d1ea015dcb8cccf25a2",
    "m_Id": 0,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "74986e6b26ea4a1581b895f4a3036c9a",
    "m_Title": "",
    "m_Content": "The Refraction node creates a refraction vector, that bends the camera vector to imitate what happens to light when it passes from one type of material to another - like from air into glass or from air into water.\n\nThe IORSource value of 1 is the index of refraction for air (the material where the Incident vector starts) and the IORMedium value of 1.5 is the index of refraction for glass (the material into which the Incident vector enters).",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1035.0001220703125,
        "y": 385.0000305175781,
        "width": 200.00006103515626,
        "height": 223.35311889648438
    },
    "m_Group": {
        "m_Id": "0c13c24a35ee473982af6b8b25a11135"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "75f125c08f2f44bc8f92e33f81039f86",
    "m_Id": 5,
    "m_DisplayName": "Intensity",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Intensity",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "7c9368bcec404a36b744fd1a0946231d",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Alpha",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "419226d738e84164887ee38b9a9e042b"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Alpha"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "7fe673d817f14a51bc6ea3dd7020f268",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "82d69041b74e4b34a5672aa2713ef036",
    "m_Id": 0,
    "m_DisplayName": "Incident",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Incident",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInUnlitSubTarget",
    "m_ObjectId": "83452527c633480e996d3523e18a5268"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "84cef664682e40f1a0b946632837d439",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "7fe673d817f14a51bc6ea3dd7020f268"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Position"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "84e959134fea4e90b7727d1fe1fa3f7f",
    "m_Id": 2,
    "m_DisplayName": "IORSource",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "IORSource",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "87b3372f87f343ea924aeac5a6dcc5bf",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Normal",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "70278426dc904d1ea015dcb8cccf25a2"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Normal"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "887584aeb031478e94fda43fabe6d2c0",
    "m_Id": 3,
    "m_DisplayName": "IORMedium",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "IORMedium",
    "m_StageCapability": 3,
    "m_Value": 1.5,
    "m_DefaultValue": 1.5,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "8e50b421642140abb67e94dc3df2c129",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a3094970bc89457897edc6cabd9a4c37",
    "m_Id": 6,
    "m_DisplayName": "Width",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Width",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a4e9cedb725841019a3468f29378b703",
    "m_Id": 2,
    "m_DisplayName": "Orthographic",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Orthographic",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "a92418725f294195b9c91c794cf74814",
    "m_Id": 4,
    "m_DisplayName": "Refracted",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Refracted",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleRawCubemapNode",
    "m_ObjectId": "adca4e85ee574dd38b95f215a1e255a5",
    "m_Group": {
        "m_Id": "0c13c24a35ee473982af6b8b25a11135"
    },
    "m_Name": "Sample Cubemap",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -779.5,
            "y": 182.00003051757813,
            "width": 208.00006103515626,
            "height": 277.9999694824219
        }
    },
    "m_Slots": [
        {
            "m_Id": "e6b152f430a34c8aa34d6eed368e14e1"
        },
        {
            "m_Id": "ebd0e637ea7a4642930cded3ce7a5389"
        },
        {
            "m_Id": "0f9011644c8a4dd0a6734f239fe23841"
        },
        {
            "m_Id": "5eb6b4e0481c46e9b8b3df01b4362456"
        },
        {
            "m_Id": "0d574f4aba9d44c19fb4c83f8049fb9d"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "b7488da65ed546908dde623c06e0bd14",
    "m_Title": "",
    "m_Content": "IORSource is the index of refraction of the material the vector starts out in.  Most commonly, the Source is air.\n\nIORMedium is the index of refraction of the material into which the vector is passing. Most commonly water or glass.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1274.0001220703125,
        "y": -16.500001907348634,
        "width": 255.50006103515626,
        "height": 112.50001525878906
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "b75b3623ba2e4ce3a04583d55e1219aa",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ba3ebfc04e714cc8b05b24ba471294a7",
    "m_Id": 5,
    "m_DisplayName": "Intensity",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Intensity",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "c37141bf60ae451d800d304cc4797878",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Emission",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "4395319855a04b8cb0bc25f3c2306a33"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Emission"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "d9bcb957e33044ae97b7a25cfefdb60a",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Tangent",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "dafa403b62c94d0f99ca1c6414caa9c1"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Tangent"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TangentMaterialSlot",
    "m_ObjectId": "dafa403b62c94d0f99ca1c6414caa9c1",
    "m_Id": 0,
    "m_DisplayName": "Tangent",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tangent",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalVectorNode",
    "m_ObjectId": "e555198ae9fa4c01bb815f75ca069763",
    "m_Group": {
        "m_Id": "0c13c24a35ee473982af6b8b25a11135"
    },
    "m_Name": "Normal Vector",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1347.5,
            "y": 259.0000305175781,
            "width": 206.0001220703125,
            "height": 130.49996948242188
        }
    },
    "m_Slots": [
        {
            "m_Id": "2df0762bb4e648ca8e575b012414c865"
        }
    ],
    "synonyms": [
        "surface direction"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "e6b152f430a34c8aa34d6eed368e14e1",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInTarget",
    "m_ObjectId": "e6bb596de71c4ce19f5f06efebd6b5ff",
    "m_ActiveSubTarget": {
        "m_Id": "83452527c633480e996d3523e18a5268"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZWriteControl": 0,
    "m_ZTestMode": 4,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CustomEditorGUI": ""
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CubemapInputMaterialSlot",
    "m_ObjectId": "ebd0e637ea7a4642930cded3ce7a5389",
    "m_Id": 1,
    "m_DisplayName": "Cube",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cube",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Cubemap": {
        "m_SerializedCubemap": "{\"cubemap\":{\"fileID\":8900000,\"guid\":\"bfa21e80e1c1ccb4093926638b074ee4\",\"type\":3}}",
        "m_Guid": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CameraNode",
    "m_ObjectId": "ed76d158ae9a45b6ae9de4b626cb56d7",
    "m_Group": {
        "m_Id": "0c13c24a35ee473982af6b8b25a11135"
    },
    "m_Name": "Camera",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1243.5,
            "y": 182.00003051757813,
            "width": 102.0001220703125,
            "height": 77.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "50cceb347024489491755be403a001e7"
        },
        {
            "m_Id": "fe6f62b6e6fd4f59adf5d4b8f16d3ddd"
        },
        {
            "m_Id": "a4e9cedb725841019a3468f29378b703"
        },
        {
            "m_Id": "2f792de092884e3fbbc9f1f996062a81"
        },
        {
            "m_Id": "6fd5e3f671ca4cd18a0cff61cf2b18d7"
        },
        {
            "m_Id": "49c02aa6cca243abae65734aec8e43f9"
        },
        {
            "m_Id": "a3094970bc89457897edc6cabd9a4c37"
        },
        {
            "m_Id": "383cc7f27c32460db0a60b791aa27fe0"
        }
    ],
    "synonyms": [
        "position",
        "direction",
        "orthographic",
        "near plane",
        "far plane",
        "width",
        "height"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget",
    "m_ObjectId": "f7e260f6635f4fadb06732126227f06d",
    "m_Datas": [],
    "m_ActiveSubTarget": {
        "m_Id": "1d921e6822284408949dbb938f203908"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZTestMode": 4,
    "m_ZWriteControl": 0,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CastShadows": true,
    "m_ReceiveShadows": true,
    "m_SupportsLODCrossFade": false,
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "fe6f62b6e6fd4f59adf5d4b8f16d3ddd",
    "m_Id": 1,
    "m_DisplayName": "Direction",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Direction",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

