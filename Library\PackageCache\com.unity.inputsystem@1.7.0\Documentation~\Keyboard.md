---
uid: input-system-keyboard
---
# Keyboard support

The [`Keyboard`](../api/UnityEngine.InputSystem.Keyboard.html) class defines a [Device](Devices.md) with a set of key Controls defined by the [`Key`](../api/UnityEngine.InputSystem.Key.html) enumeration.

The location of individual keys is agnostic to keyboard layout. This means that, for example, the `A` key is always the key to the right of the `Caps Lock` key, regardless of where the currently active keyboard layout places the key that generates an `a` character, or whether or not the layout doesn't have a key assigned to that character.

To query which (if any) character is generated by a given key, use the key Control's [`displayName`](../api/UnityEngine.InputSystem.InputControl.html#UnityEngine_InputSystem_InputControl_displayName) property. The value of this property changes automatically when the OS changes the keyboard layout.

You can look up keys based on the character they produce using [Control paths](Controls.md#control-paths). For example,  you can query the key that produces the producing the `a` character from [`Keyboard`](../api/UnityEngine.InputSystem.Keyboard.html) using [`Keyboard.current["#(a)"]`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_Item_UnityEngine_InputSystem_Key_).

>__Note__
>* Keyboards usually have hardware limitations on both the number of simultaneous keypresses they report, and the combinations of keys they support. This means that certain simultaneous keypresses may not register correctly. For example, a given keyboard might report a simultaneous press of the "QWERT" keys correctly, but might not report "QWERA" correctly.
>* At the moment, the new Input System doesn't support on-screen keyboards. For now, please Unity's existing API in `UnityEngine.TouchScreenKeyboard`.
>* At the moment, Unity platform backends generally do not support distinguishing between multiple keyboards. While the Input System supports having many [`Keyboard`](../api/UnityEngine.InputSystem.Keyboard.html) devices at any point, platform backends generally only report a single keyboard and route input from all attached keyboards to the one keyboard device.

## Controls

To retrieve a key from [`Keyboard`](../api/UnityEngine.InputSystem.Keyboard.html), you can use one of these methods:

* Use the key's accessor property, such [`Keyboard.spaceKey`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_spaceKey).
* Use [`Keyboard`](../api/UnityEngine.InputSystem.Keyboard.html)'s indexer and the [`Key`](../api/UnityEngine.InputSystem.Key.html) enumeration (for example, `keyboard[Key.Space]`).

The [scripting API reference for the `Keyboard` class](../api/UnityEngine.InputSystem.Keyboard.html) lists all the properties for the individual key Controls.

Two special Controls, [`anyKey`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_anyKey) and [`imeSelected`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_imeSelected), don't directly map to individual keys. [`anyKey`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_anyKey) is a [synthetic](Controls.md#synthetic-controls) button Control which reports whether any key on the keyboard is pressed, and [`imeSelected`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_imeSelected) reports whether or not [IME](#ime) text processing is enabled.

In addition, [`Keyboard`](../api/UnityEngine.InputSystem.Keyboard.html)'s indexer and the [`Key`](../api/UnityEngine.InputSystem.Key.html) has three [synthetic](Controls.md#synthetic-controls) controls that represent combinations of modifier keys:

|Control|Description|
|-------|-----------|
|[`shiftKey`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_shiftKey)|A button that is pressed if [`leftShiftKey`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_leftShiftKey) and/or [`rightShiftKey`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_rightShiftKey) is pressed.|
|[`ctrlKey`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_ctrlKey)|A button that is pressed if [`leftCtrlKey`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_leftCtrlKey) and/or [`rightCtrlKey`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_rightCtrlKey) is pressed.|
|[`altKey`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_altKey)|A button that is pressed if [`leftAltKey`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_leftAltKey) and/or [`rightAltKey`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_rightAltKey) is pressed.|

## Text input

As a best practice, you shouldn't manually translate text input from key presses by trying to string together the characters corresponding to the keys. Instead, to listen to text input, hook into [`Keyboard.onTextInput`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_onTextInput). This delivers character-by-character input as reported by the platform, including input from on-screen keyboards.

Note that the text input API doesn't allocate GC memory because it doesn't deliver fully composed strings.

### IME

Some writing systems, such as some East-Asian scripts, are too complex to represent all characters as individual keys on a keyboard. For these layouts, operating systems implement IMEs (Input Method Editors) to allow composing input strings by other methods, for instance by typing several keys to produce a single character. Unity's UI frameworks for text input support IME without any additional configuration. If you want to build your own UI for text input, the [`Keyboard`](../api/UnityEngine.InputSystem.Keyboard.html) class allows you to work with input from IMEs using the following APIs:

* [`imeSelected`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_imeSelected) is a virtual input Control that reports whether IME text processing is enabled.

* [`SetIMEEnabled()`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_SetIMEEnabled_System_Boolean_) is a method which lets you turn IME processing on or off. Typically, IME processing is useful when the user wants to edit text, but not so much when the user is using the keyboard to control a game.

* [`SetIMECursorPosition()`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_SetIMECursorPosition_UnityEngine_Vector2_). IMEs might open system windows that let users interactively edit the text they want to input. Typically, these open next to the text editing UI. You can use the [`SetIMECursorPosition()`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_SetIMECursorPosition_UnityEngine_Vector2_) method to tell the OS where that is.

* [`onIMECompositionChange`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_onIMECompositionChange) is an event you can subscribe to in order to receive all updates to the IME composition string. The composition string is the text input the user is currently editing using an IME. Typically, any UI dealing with text input displays this text (with some visual indication of it being actively edited, usually an underline) at the current text input cursor position.

## Keyboard layouts

You can query the name of the current keyboard layout using [`Keyboard.keyboardLayout`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_keyboardLayout). Layout names are platform-specific.

There is no support for setting keyboard layouts from your application.

To monitor keyboard layout changes, hook into [`InputSystem.onDeviceChange`](../api/UnityEngine.InputSystem.InputSystem.html#UnityEngine_InputSystem_InputSystem_onDeviceChange) and check for [`InputDeviceChange.ConfigurationChanged`](../api/UnityEngine.InputSystem.InputDeviceChange.html) on a [`Keyboard`](../api/UnityEngine.InputSystem.Keyboard.html) device.

To find the key control that corresponds to a specific display character sequence, call [`Keyboard.FindKeyOnCurrentKeyboardLayout`](../api/UnityEngine.InputSystem.Keyboard.html#UnityEngine_InputSystem_Keyboard_FindKeyOnCurrentKeyboardLayout_).

```CSharp
// Find key that generates a 'q' character according to the current keyboard layout.
Keyboard.current.FindKeyOnCurrentKeyboardLayout("q");
```
