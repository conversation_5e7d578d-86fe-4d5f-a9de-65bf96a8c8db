
/* ******************************* */
/* override for personal skin them */
/* ******************************* */
#environmentContainer > EnvironmentElement
{
    border-color: #999999;
}

#separator
{
    border-color: #999999;
}

.list-environment-overlay > ToolbarButton
{
    background-color: #CBCBCB;
}

.list-environment-overlay > ToolbarButton:hover
{
    background-color: #e8e8e8;
}

#inspector-header
{
    background-color: #CBCBCB;
    border-color: #999999;
}

#separator-line
{
    background-color: #CBCBCB;
}

Image.unity-list-view__item:selected
{
    border-color: #3A72B0;
}

#environmentContainer
{
    border-color: #999999;
}

#debugContainer
{
    border-color: #999999;
}

#debugToolbar
{
    border-color: #999999;
}

MultipleSourcePopupField > MultipleDifferentValue
{
    background-color: #DFDFDF;
}

MultipleSourcePopupField > MultipleDifferentValue:hover
{
    background-color: #e8e8e8;
}

#sunToBrightestButton:hover
{
    background-color: #e8e8e8;
}
