{"name": "ExampleActions", "maps": [{"name": "gameplay", "id": "d55be63c-61eb-47ef-92dd-eef1248d601e", "actions": [{"name": "move", "type": "Value", "id": "8387a17d-aedd-4411-9931-6a855a8299fb", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "jump", "type": "<PERSON><PERSON>", "id": "7fc1eb3c-b4d1-4aba-9a58-f36cac532d06", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "crouch", "type": "<PERSON><PERSON>", "id": "851b3e53-f21f-4ba6-94fa-9f0b4db1ccb4", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "use", "type": "<PERSON><PERSON>", "id": "b5f08480-c03b-4654-8475-9c94e8ccccaf", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "", "id": "2c541328-ed00-4524-817c-97599bac7de5", "path": "<Gamepad>{Player1}/leftStick", "interactions": "", "processors": "", "groups": "", "action": "move", "isComposite": false, "isPartOfComposite": false}, {"name": "2D Vector", "id": "1bff55cb-bc9f-4a07-921b-c363f330e099", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "move", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "a463e3bc-75b0-4b56-a790-05e2b7fd264c", "path": "<Keyboard>/upArrow", "interactions": "", "processors": "", "groups": "", "action": "move", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "875ee5b9-f51e-4e41-bad6-930e14120008", "path": "<Keyboard>/downArrow", "interactions": "", "processors": "", "groups": "", "action": "move", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "63c4eca5-962b-429e-8988-41dbdd085179", "path": "<Keyboard>/leftArrow", "interactions": "", "processors": "", "groups": "", "action": "move", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "bfd5fbe5-e471-45c2-bcf9-905f21b37819", "path": "<Keyboard>/rightArrow", "interactions": "", "processors": "", "groups": "", "action": "move", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "c48db537-b211-412a-a86a-d31614bebb5d", "path": "<Gamepad>{Player1}/buttonSouth", "interactions": "", "processors": "", "groups": "", "action": "jump", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "9a105e9c-c84a-4269-852a-29220b37c2a4", "path": "<Keyboard>/space", "interactions": "", "processors": "", "groups": "", "action": "jump", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "636da59d-2ea1-4682-b67d-a40f48fa02a5", "path": "<Gamepad>{Player1}/buttonWest", "interactions": "", "processors": "", "groups": "", "action": "crouch", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "69f2763d-08bc-4aac-9c35-f4f5f22e2928", "path": "<Keyboard>/z", "interactions": "", "processors": "", "groups": "", "action": "crouch", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "41041dd1-570a-487c-856e-d58cfa06509a", "path": "<Gamepad>{Player1}/buttonNorth", "interactions": "", "processors": "", "groups": "", "action": "use", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "4ac0be70-ea16-4ea3-98d4-0cf19fab4aec", "path": "<Keyboard>/x", "interactions": "", "processors": "", "groups": "", "action": "use", "isComposite": false, "isPartOfComposite": false}]}, {"name": "menus", "id": "4d6bdb0c-7386-48fd-b39c-65304c2d209d", "actions": [{"name": "New action", "type": "<PERSON><PERSON>", "id": "e4dd2c60-a6d7-4ccd-8c2f-151ad3cf28a0", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "", "id": "3599143f-75d5-4647-8633-83410cf6d8f8", "path": "", "interactions": "", "processors": "", "groups": "", "action": "New action", "isComposite": false, "isPartOfComposite": false}]}], "controlSchemes": []}