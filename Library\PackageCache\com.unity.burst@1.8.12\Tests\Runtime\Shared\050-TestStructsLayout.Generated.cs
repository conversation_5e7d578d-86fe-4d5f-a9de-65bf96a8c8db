// -----------------------------------------------------------
// This file was generated automatically from 050-TestStructsLayout.cs
// DO NOT EDIT THIS FILE MANUALLY
// -----------------------------------------------------------
using Unity.Collections.LowLevel.Unsafe;

namespace Burst.Compiler.IL.Tests
{
    partial class TestStructsLayout
    {
        [TestCompiler]
        public static int Test_CheckHoleInner_Size()
        {
            return UnsafeUtility.SizeOf<CheckHoleInner>();
        }

        [TestCompiler]
        public static unsafe int Test_CheckHoleInner_FieldOffset_m_Ptr()
        {
            var value = new CheckHoleInner();
            var addressStart = &value;
            var addressField = &value.m_Ptr;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        // Commented out until upstream IL2CPP bug is fixed
#if BURST_TESTS_ONLY
        [TestCompiler(OverrideOn32BitNative = 20)]
        public static int Test_CheckHoleOuter_Size()
        {
            return UnsafeUtility.SizeOf<CheckHoleOuter>();
        }
#endif

        [TestCompiler]
        public static unsafe int Test_CheckHoleOuter_FieldOffset_a()
        {
            var value = new CheckHoleOuter();
            var addressStart = &value;
            var addressField = &value.a;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_CheckHoleOuter_FieldOffset_b()
        {
            var value = new CheckHoleOuter();
            var addressStart = &value;
            var addressField = &value.b;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        // Commented out until upstream IL2CPP bug is fixed
#if BURST_TESTS_ONLY
        [TestCompiler(OverrideOn32BitNative = 12)]
        public static unsafe int Test_CheckHoleOuter_FieldOffset_c()
        {
            var value = new CheckHoleOuter();
            var addressStart = &value;
            var addressField = &value.c;
            return (int)((byte*)addressField - (byte*)addressStart);
        }
#endif

        [TestCompiler]
        public static int Test_ExplicitStructWithoutSize2_Size()
        {
            return UnsafeUtility.SizeOf<ExplicitStructWithoutSize2>();
        }

        [TestCompiler]
        public static unsafe int Test_ExplicitStructWithoutSize2_FieldOffset_a()
        {
            var value = new ExplicitStructWithoutSize2();
            var addressStart = &value;
            var addressField = &value.a;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_ExplicitStructWithoutSize2_FieldOffset_b()
        {
            var value = new ExplicitStructWithoutSize2();
            var addressStart = &value;
            var addressField = &value.b;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_ExplicitStructWithoutSize2_FieldOffset_c()
        {
            var value = new ExplicitStructWithoutSize2();
            var addressStart = &value;
            var addressField = &value.c;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_ExplicitStructWithoutSize_Size()
        {
            return UnsafeUtility.SizeOf<ExplicitStructWithoutSize>();
        }

        [TestCompiler]
        public static unsafe int Test_ExplicitStructWithoutSize_FieldOffset_a()
        {
            var value = new ExplicitStructWithoutSize();
            var addressStart = &value;
            var addressField = &value.a;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_ExplicitStructWithoutSize_FieldOffset_b()
        {
            var value = new ExplicitStructWithoutSize();
            var addressStart = &value;
            var addressField = &value.b;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_ExplicitStructWithoutSize_FieldOffset_c()
        {
            var value = new ExplicitStructWithoutSize();
            var addressStart = &value;
            var addressField = &value.c;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_SequentialStructWithSize3_Size()
        {
            return UnsafeUtility.SizeOf<SequentialStructWithSize3>();
        }

        [TestCompiler]
        public static unsafe int Test_SequentialStructWithSize3_FieldOffset_a()
        {
            var value = new SequentialStructWithSize3();
            var addressStart = &value;
            var addressField = &value.a;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_SequentialStructWithSize3_FieldOffset_b()
        {
            var value = new SequentialStructWithSize3();
            var addressStart = &value;
            var addressField = &value.b;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_SequentialStructWithSize3_FieldOffset_c()
        {
            var value = new SequentialStructWithSize3();
            var addressStart = &value;
            var addressField = &value.c;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_SequentialStructWithoutSize_Size()
        {
            return UnsafeUtility.SizeOf<SequentialStructWithoutSize>();
        }

        [TestCompiler]
        public static unsafe int Test_SequentialStructWithoutSize_FieldOffset_a()
        {
            var value = new SequentialStructWithoutSize();
            var addressStart = &value;
            var addressField = &value.a;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_SequentialStructWithoutSize_FieldOffset_b()
        {
            var value = new SequentialStructWithoutSize();
            var addressStart = &value;
            var addressField = &value.b;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_SequentialStructWithoutSize_FieldOffset_c()
        {
            var value = new SequentialStructWithoutSize();
            var addressStart = &value;
            var addressField = &value.c;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_SequentialStructEmptyNoAttributes_Size()
        {
            return UnsafeUtility.SizeOf<SequentialStructEmptyNoAttributes>();
        }

        [TestCompiler]
        public static int Test_ExplicitStructWithEmptySequentialFields_Size()
        {
            return UnsafeUtility.SizeOf<ExplicitStructWithEmptySequentialFields>();
        }

        [TestCompiler]
        public static unsafe int Test_ExplicitStructWithEmptySequentialFields_FieldOffset_FieldA()
        {
            var value = new ExplicitStructWithEmptySequentialFields();
            var addressStart = &value;
            var addressField = &value.FieldA;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_ExplicitStructWithEmptySequentialFields_FieldOffset_FieldB()
        {
            var value = new ExplicitStructWithEmptySequentialFields();
            var addressStart = &value;
            var addressField = &value.FieldB;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_ExplicitStrictWithEmptyAndNonEmptySequentialFields_Size()
        {
            return UnsafeUtility.SizeOf<ExplicitStrictWithEmptyAndNonEmptySequentialFields>();
        }

        [TestCompiler]
        public static unsafe int Test_ExplicitStrictWithEmptyAndNonEmptySequentialFields_FieldOffset_FieldA()
        {
            var value = new ExplicitStrictWithEmptyAndNonEmptySequentialFields();
            var addressStart = &value;
            var addressField = &value.FieldA;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_ExplicitStrictWithEmptyAndNonEmptySequentialFields_FieldOffset_FieldB()
        {
            var value = new ExplicitStrictWithEmptyAndNonEmptySequentialFields();
            var addressStart = &value;
            var addressField = &value.FieldB;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_StructWithPack8_Size()
        {
            return UnsafeUtility.SizeOf<StructWithPack8>();
        }

        [TestCompiler]
        public static unsafe int Test_StructWithPack8_FieldOffset_FieldA()
        {
            var value = new StructWithPack8();
            var addressStart = &value;
            var addressField = &value.FieldA;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_StructWithPack8_FieldOffset_FieldB()
        {
            var value = new StructWithPack8();
            var addressStart = &value;
            var addressField = &value.FieldB;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_StructPack2WithBytesAndInt_Size()
        {
            return UnsafeUtility.SizeOf<StructPack2WithBytesAndInt>();
        }

        [TestCompiler]
        public static unsafe int Test_StructPack2WithBytesAndInt_FieldOffset_FieldA()
        {
            var value = new StructPack2WithBytesAndInt();
            var addressStart = &value;
            var addressField = &value.FieldA;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_StructPack2WithBytesAndInt_FieldOffset_FieldB()
        {
            var value = new StructPack2WithBytesAndInt();
            var addressStart = &value;
            var addressField = &value.FieldB;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_StructPack2WithBytesAndInt_FieldOffset_FieldC()
        {
            var value = new StructPack2WithBytesAndInt();
            var addressStart = &value;
            var addressField = &value.FieldC;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_StructPack2WithBytesAndInts_Size()
        {
            return UnsafeUtility.SizeOf<StructPack2WithBytesAndInts>();
        }

        [TestCompiler]
        public static unsafe int Test_StructPack2WithBytesAndInts_FieldOffset_FieldA()
        {
            var value = new StructPack2WithBytesAndInts();
            var addressStart = &value;
            var addressField = &value.FieldA;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_StructPack2WithBytesAndInts_FieldOffset_FieldB()
        {
            var value = new StructPack2WithBytesAndInts();
            var addressStart = &value;
            var addressField = &value.FieldB;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_StructPack2WithBytesAndInts_FieldOffset_FieldC()
        {
            var value = new StructPack2WithBytesAndInts();
            var addressStart = &value;
            var addressField = &value.FieldC;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_StructPack2WithBytesAndInts_FieldOffset_FieldD()
        {
            var value = new StructPack2WithBytesAndInts();
            var addressStart = &value;
            var addressField = &value.FieldD;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_StructPack1WithBytesAndInt_Size()
        {
            return UnsafeUtility.SizeOf<StructPack1WithBytesAndInt>();
        }

        [TestCompiler]
        public static unsafe int Test_StructPack1WithBytesAndInt_FieldOffset_FieldA()
        {
            var value = new StructPack1WithBytesAndInt();
            var addressStart = &value;
            var addressField = &value.FieldA;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_StructPack1WithBytesAndInt_FieldOffset_FieldB()
        {
            var value = new StructPack1WithBytesAndInt();
            var addressStart = &value;
            var addressField = &value.FieldB;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_StructPack1WithBytesAndInt_FieldOffset_FieldC()
        {
            var value = new StructPack1WithBytesAndInt();
            var addressStart = &value;
            var addressField = &value.FieldC;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_StructPack1WithByteAndInt_Size()
        {
            return UnsafeUtility.SizeOf<StructPack1WithByteAndInt>();
        }

        [TestCompiler]
        public static unsafe int Test_StructPack1WithByteAndInt_FieldOffset_FieldA()
        {
            var value = new StructPack1WithByteAndInt();
            var addressStart = &value;
            var addressField = &value.FieldA;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_StructPack1WithByteAndInt_FieldOffset_FieldB()
        {
            var value = new StructPack1WithByteAndInt();
            var addressStart = &value;
            var addressField = &value.FieldB;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_StructPack1WithByteAndIntWrapper_Size()
        {
            return UnsafeUtility.SizeOf<StructPack1WithByteAndIntWrapper>();
        }

        [TestCompiler]
        public static unsafe int Test_StructPack1WithByteAndIntWrapper_FieldOffset_FieldA()
        {
            var value = new StructPack1WithByteAndIntWrapper();
            var addressStart = &value;
            var addressField = &value.FieldA;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_StructPack1WithByteAndIntWrapper_FieldOffset_FieldB()
        {
            var value = new StructPack1WithByteAndIntWrapper();
            var addressStart = &value;
            var addressField = &value.FieldB;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_StructPack1WithByteAndIntWrapper2_Size()
        {
            return UnsafeUtility.SizeOf<StructPack1WithByteAndIntWrapper2>();
        }

        [TestCompiler]
        public static unsafe int Test_StructPack1WithByteAndIntWrapper2_FieldOffset_FieldA()
        {
            var value = new StructPack1WithByteAndIntWrapper2();
            var addressStart = &value;
            var addressField = &value.FieldA;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_StructPack1WithByteAndIntWrapper2_FieldOffset_FieldB()
        {
            var value = new StructPack1WithByteAndIntWrapper2();
            var addressStart = &value;
            var addressField = &value.FieldB;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_StructWithSizeAndPack_Size()
        {
            return UnsafeUtility.SizeOf<StructWithSizeAndPack>();
        }

        [TestCompiler]
        public static unsafe int Test_StructWithSizeAndPack_FieldOffset_FieldA()
        {
            var value = new StructWithSizeAndPack();
            var addressStart = &value;
            var addressField = &value.FieldA;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_StructWithSizeAndPack_FieldOffset_FieldB()
        {
            var value = new StructWithSizeAndPack();
            var addressStart = &value;
            var addressField = &value.FieldB;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_StructWithSizeAndPackWrapper_Size()
        {
            return UnsafeUtility.SizeOf<StructWithSizeAndPackWrapper>();
        }

        [TestCompiler]
        public static unsafe int Test_StructWithSizeAndPackWrapper_FieldOffset_FieldA()
        {
            var value = new StructWithSizeAndPackWrapper();
            var addressStart = &value;
            var addressField = &value.FieldA;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_StructWithSizeAndPackWrapper_FieldOffset_FieldB()
        {
            var value = new StructWithSizeAndPackWrapper();
            var addressStart = &value;
            var addressField = &value.FieldB;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_StructWithSizeAndPack4_Size()
        {
            return UnsafeUtility.SizeOf<StructWithSizeAndPack4>();
        }

        [TestCompiler]
        public static unsafe int Test_StructWithSizeAndPack4_FieldOffset_FieldA()
        {
            var value = new StructWithSizeAndPack4();
            var addressStart = &value;
            var addressField = &value.FieldA;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_StructWithSizeAndPack4_FieldOffset_FieldB()
        {
            var value = new StructWithSizeAndPack4();
            var addressStart = &value;
            var addressField = &value.FieldB;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        // Commented out until upstream IL2CPP bug is fixed
#if BURST_TESTS_ONLY
        [TestCompiler]
        public static int Test_StructWithSizeAndPack4Wrapper_Size()
        {
            return UnsafeUtility.SizeOf<StructWithSizeAndPack4Wrapper>();
        }
#endif

        [TestCompiler]
        public static unsafe int Test_StructWithSizeAndPack4Wrapper_FieldOffset_FieldA()
        {
            var value = new StructWithSizeAndPack4Wrapper();
            var addressStart = &value;
            var addressField = &value.FieldA;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        // Commented out until upstream IL2CPP bug is fixed
#if BURST_TESTS_ONLY
        [TestCompiler]
        public static unsafe int Test_StructWithSizeAndPack4Wrapper_FieldOffset_FieldB()
        {
            var value = new StructWithSizeAndPack4Wrapper();
            var addressStart = &value;
            var addressField = &value.FieldB;
            return (int)((byte*)addressField - (byte*)addressStart);
        }
#endif

        [TestCompiler]
        public static int Test_StructExplicitPack1WithByteAndInt_Size()
        {
            return UnsafeUtility.SizeOf<StructExplicitPack1WithByteAndInt>();
        }

        [TestCompiler]
        public static unsafe int Test_StructExplicitPack1WithByteAndInt_FieldOffset_FieldA()
        {
            var value = new StructExplicitPack1WithByteAndInt();
            var addressStart = &value;
            var addressField = &value.FieldA;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_StructExplicitPack1WithByteAndInt_FieldOffset_FieldB()
        {
            var value = new StructExplicitPack1WithByteAndInt();
            var addressStart = &value;
            var addressField = &value.FieldB;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_StructExplicitPack1WithByteAndIntWrapper_Size()
        {
            return UnsafeUtility.SizeOf<StructExplicitPack1WithByteAndIntWrapper>();
        }

        [TestCompiler]
        public static unsafe int Test_StructExplicitPack1WithByteAndIntWrapper_FieldOffset_FieldA()
        {
            var value = new StructExplicitPack1WithByteAndIntWrapper();
            var addressStart = &value;
            var addressField = &value.FieldA;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_StructExplicitPack1WithByteAndIntWrapper_FieldOffset_FieldB()
        {
            var value = new StructExplicitPack1WithByteAndIntWrapper();
            var addressStart = &value;
            var addressField = &value.FieldB;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_StructExplicitPack1WithByteAndIntWrapper2_Size()
        {
            return UnsafeUtility.SizeOf<StructExplicitPack1WithByteAndIntWrapper2>();
        }

        [TestCompiler]
        public static unsafe int Test_StructExplicitPack1WithByteAndIntWrapper2_FieldOffset_FieldA()
        {
            var value = new StructExplicitPack1WithByteAndIntWrapper2();
            var addressStart = &value;
            var addressField = &value.FieldA;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_StructExplicitPack1WithByteAndIntWrapper2_FieldOffset_FieldB()
        {
            var value = new StructExplicitPack1WithByteAndIntWrapper2();
            var addressStart = &value;
            var addressField = &value.FieldB;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_StructExplicitWithSizeAndPack_Size()
        {
            return UnsafeUtility.SizeOf<StructExplicitWithSizeAndPack>();
        }

        [TestCompiler]
        public static unsafe int Test_StructExplicitWithSizeAndPack_FieldOffset_FieldA()
        {
            var value = new StructExplicitWithSizeAndPack();
            var addressStart = &value;
            var addressField = &value.FieldA;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_StructExplicitWithSizeAndPack_FieldOffset_FieldB()
        {
            var value = new StructExplicitWithSizeAndPack();
            var addressStart = &value;
            var addressField = &value.FieldB;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_StructExplicitWithSizeAndPackWrapper_Size()
        {
            return UnsafeUtility.SizeOf<StructExplicitWithSizeAndPackWrapper>();
        }

        [TestCompiler]
        public static unsafe int Test_StructExplicitWithSizeAndPackWrapper_FieldOffset_FieldA()
        {
            var value = new StructExplicitWithSizeAndPackWrapper();
            var addressStart = &value;
            var addressField = &value.FieldA;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_StructExplicitWithSizeAndPackWrapper_FieldOffset_FieldB()
        {
            var value = new StructExplicitWithSizeAndPackWrapper();
            var addressStart = &value;
            var addressField = &value.FieldB;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_StructExplicitWithSizeAndPack4_Size()
        {
            return UnsafeUtility.SizeOf<StructExplicitWithSizeAndPack4>();
        }

        [TestCompiler]
        public static unsafe int Test_StructExplicitWithSizeAndPack4_FieldOffset_FieldA()
        {
            var value = new StructExplicitWithSizeAndPack4();
            var addressStart = &value;
            var addressField = &value.FieldA;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_StructExplicitWithSizeAndPack4_FieldOffset_FieldB()
        {
            var value = new StructExplicitWithSizeAndPack4();
            var addressStart = &value;
            var addressField = &value.FieldB;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        // Commented out until upstream IL2CPP bug is fixed
#if BURST_TESTS_ONLY
        [TestCompiler]
        public static int Test_StructExplicitWithSizeAndPack4Wrapper_Size()
        {
            return UnsafeUtility.SizeOf<StructExplicitWithSizeAndPack4Wrapper>();
        }
#endif

        [TestCompiler]
        public static unsafe int Test_StructExplicitWithSizeAndPack4Wrapper_FieldOffset_FieldA()
        {
            var value = new StructExplicitWithSizeAndPack4Wrapper();
            var addressStart = &value;
            var addressField = &value.FieldA;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        // Commented out until upstream IL2CPP bug is fixed
#if BURST_TESTS_ONLY
        [TestCompiler]
        public static unsafe int Test_StructExplicitWithSizeAndPack4Wrapper_FieldOffset_FieldB()
        {
            var value = new StructExplicitWithSizeAndPack4Wrapper();
            var addressStart = &value;
            var addressField = &value.FieldB;
            return (int)((byte*)addressField - (byte*)addressStart);
        }
#endif

        [TestCompiler]
        public static int Test_Vector64Container_Size()
        {
            return UnsafeUtility.SizeOf<Vector64Container>();
        }

        [TestCompiler]
        public static unsafe int Test_Vector64Container_FieldOffset_Byte()
        {
            var value = new Vector64Container();
            var addressStart = &value;
            var addressField = &value.Byte;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_Vector64Container_FieldOffset_Vector()
        {
            var value = new Vector64Container();
            var addressStart = &value;
            var addressField = &value.Vector;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_Vector128Container_Size()
        {
            return UnsafeUtility.SizeOf<Vector128Container>();
        }

        [TestCompiler]
        public static unsafe int Test_Vector128Container_FieldOffset_Byte()
        {
            var value = new Vector128Container();
            var addressStart = &value;
            var addressField = &value.Byte;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_Vector128Container_FieldOffset_Vector()
        {
            var value = new Vector128Container();
            var addressStart = &value;
            var addressField = &value.Vector;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static int Test_Vector256Container_Size()
        {
            return UnsafeUtility.SizeOf<Vector256Container>();
        }

        [TestCompiler]
        public static unsafe int Test_Vector256Container_FieldOffset_Byte()
        {
            var value = new Vector256Container();
            var addressStart = &value;
            var addressField = &value.Byte;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

        [TestCompiler]
        public static unsafe int Test_Vector256Container_FieldOffset_Vector()
        {
            var value = new Vector256Container();
            var addressStart = &value;
            var addressField = &value.Vector;
            return (int)((byte*)addressField - (byte*)addressStart);
        }

    }
}
