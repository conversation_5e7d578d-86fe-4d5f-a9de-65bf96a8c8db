{
	SubShader
	{
		${VFXInclude("Shaders/VFXParticleHeader.template")}
		${VFXInclude("Shaders/ParticleMeshes/PassSelection.template")}
		${VFXInclude("Shaders/ParticleMeshes/PassDepth.template"),IS_OPAQUE_PARTICLE}
		${VFXInclude("Shaders/ParticleMeshes/PassDepthNormal.template"),IS_OPAQUE_PARTICLE}
		${VFXInclude("Shaders/ParticleMeshes/PassForward.template")}
		${VFXInclude("Shaders/ParticleMeshes/PassShadowCaster.template"),USE_CAST_SHADOWS_PASS}
		${VFXIncludeRP("Templates/ParticleMeshes/PassForward2D.template")}
	}
}
