using UnityEngine;
using UnityEditor;
using VRoidFaceCustomization;
using System.Collections.Generic;
using System.Linq;

namespace VRoidFaceCustomization.Editor
{
    /// <summary>
    /// VRM10服装调试工具
    /// 专门用于调试服装穿戴过程中的问题
    /// </summary>
    public class VRM10ClothDebugger : EditorWindow
    {
        private VRM10ClothBinder clothBinder;
        private GameObject clothPrefab;
        private Vector2 scrollPosition;
        private bool autoRefresh = true;
        private float refreshInterval = 1.0f;
        private double lastRefreshTime;
        
        // 调试信息
        private List<string> debugLogs = new List<string>();
        private Dictionary<string, Transform> currentBoneMap = new Dictionary<string, Transform>();
        private List<VRM10WornCloth> currentWornClothes = new List<VRM10WornCloth>();
        
        [MenuItem("Tools/VRM 1.0/Cloth Debugger")]
        public static void ShowWindow()
        {
            GetWindow<VRM10ClothDebugger>("服装调试器");
        }
        
        private void OnEnable()
        {
            RefreshDebugInfo();
        }
        
        private void Update()
        {
            if (autoRefresh && EditorApplication.timeSinceStartup - lastRefreshTime > refreshInterval)
            {
                RefreshDebugInfo();
                lastRefreshTime = EditorApplication.timeSinceStartup;
            }
        }
        
        private void OnGUI()
        {
            EditorGUILayout.LabelField("VRM10 服装调试器", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("用于调试服装穿戴过程中的骨骼绑定问题", MessageType.Info);
            EditorGUILayout.Space();
            
            // 设置区域
            EditorGUILayout.LabelField("设置", EditorStyles.boldLabel);
            clothBinder = (VRM10ClothBinder)EditorGUILayout.ObjectField(
                "服装绑定器", clothBinder, typeof(VRM10ClothBinder), true);
            
            clothPrefab = (GameObject)EditorGUILayout.ObjectField(
                "测试服装", clothPrefab, typeof(GameObject), false);
            
            autoRefresh = EditorGUILayout.Toggle("自动刷新", autoRefresh);
            if (autoRefresh)
            {
                refreshInterval = EditorGUILayout.Slider("刷新间隔", refreshInterval, 0.1f, 5.0f);
            }
            
            EditorGUILayout.Space();
            
            // 操作按钮
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("刷新信息"))
            {
                RefreshDebugInfo();
            }
            
            if (GUILayout.Button("清空日志"))
            {
                debugLogs.Clear();
            }
            
            if (clothBinder != null && GUILayout.Button("诊断系统"))
            {
                DiagnoseSystem();
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            // 测试按钮
            if (clothBinder != null && clothPrefab != null && Application.isPlaying)
            {
                EditorGUILayout.LabelField("测试操作", EditorStyles.boldLabel);
                EditorGUILayout.BeginHorizontal();
                
                if (GUILayout.Button("穿戴服装"))
                {
                    TestWearCloth();
                }
                
                if (GUILayout.Button("脱下服装"))
                {
                    TestRemoveCloth();
                }
                
                if (GUILayout.Button("强制刷新渲染"))
                {
                    clothBinder.RefreshAllClothRendering();
                    AddDebugLog("执行强制刷新渲染");
                }
                
                EditorGUILayout.EndHorizontal();
            }
            
            EditorGUILayout.Space();
            
            // 显示调试信息
            DrawDebugInfo();
        }
        
        private void RefreshDebugInfo()
        {
            if (clothBinder == null) return;
            
            // 更新骨骼映射信息
            currentBoneMap.Clear();
            var bones = clothBinder.transform.GetComponentsInChildren<Transform>();
            foreach (var bone in bones)
            {
                if (!currentBoneMap.ContainsKey(bone.name))
                {
                    currentBoneMap[bone.name] = bone;
                }
            }
            
            // 更新已穿戴服装信息
            currentWornClothes = clothBinder.GetWornClothes();
        }
        
        private void DiagnoseSystem()
        {
            AddDebugLog("=== 开始系统诊断 ===");
            
            if (clothBinder == null)
            {
                AddDebugLog("❌ 服装绑定器未设置");
                return;
            }
            
            // 检查VRM组件
            var vrmInstance = clothBinder.GetComponent<UniVRM10.Vrm10Instance>();
            AddDebugLog($"VRM实例: {(vrmInstance != null ? "✅" : "❌")}");
            
            // 检查骨骼映射
            AddDebugLog($"骨骼映射数量: {currentBoneMap.Count}");
            
            // 检查已穿戴服装
            AddDebugLog($"已穿戴服装: {currentWornClothes.Count}");
            foreach (var cloth in currentWornClothes)
            {
                AddDebugLog($"  - {cloth.name} ({cloth.type})");
                
                // 检查骨骼数据
                var boneData = cloth.clothObject?.GetComponent<VRM10ClothBoneData>();
                if (boneData != null)
                {
                    AddDebugLog($"    骨骼数据: ✅ ({boneData.GetBoneInfos().Count} 个骨骼)");
                    AddDebugLog($"    数据有效性: {(boneData.ValidateBoneData() ? "✅" : "❌")}");
                }
                else
                {
                    AddDebugLog($"    骨骼数据: ❌");
                }
                
                // 检查渲染器状态
                if (cloth.renderer != null)
                {
                    AddDebugLog($"    渲染器: ✅ (骨骼: {cloth.renderer.bones?.Length ?? 0})");
                    AddDebugLog($"    启用状态: {cloth.renderer.enabled}");
                }
                else
                {
                    AddDebugLog($"    渲染器: ❌");
                }
            }
            
            // 检查动态骨骼
            var dynamicBones = clothBinder.transform.Find("DynamicClothBones");
            if (dynamicBones != null)
            {
                AddDebugLog($"动态骨骼节点: ✅ ({dynamicBones.childCount} 个子骨骼)");
            }
            else
            {
                AddDebugLog($"动态骨骼节点: ❌");
            }
            
            AddDebugLog("=== 诊断完成 ===");
        }
        
        private void TestWearCloth()
        {
            if (clothBinder == null || clothPrefab == null) return;
            
            AddDebugLog($"=== 开始测试穿戴: {clothPrefab.name} ===");
            
            // 记录穿戴前状态
            AddDebugLog($"穿戴前骨骼数量: {currentBoneMap.Count}");
            AddDebugLog($"穿戴前服装数量: {currentWornClothes.Count}");
            
            // 执行穿戴
            var result = clothBinder.WearCloth(clothPrefab);
            
            // 记录穿戴后状态
            RefreshDebugInfo();
            AddDebugLog($"穿戴结果: {(result != null ? "✅ 成功" : "❌ 失败")}");
            AddDebugLog($"穿戴后骨骼数量: {currentBoneMap.Count}");
            AddDebugLog($"穿戴后服装数量: {currentWornClothes.Count}");
            
            if (result != null)
            {
                AddDebugLog($"新服装骨骼数量: {result.mappedBones?.Length ?? 0}");
                
                // 检查骨骼绑定状态
                if (result.renderer != null && result.renderer.bones != null)
                {
                    int validBones = result.renderer.bones.Count(b => b != null);
                    AddDebugLog($"有效骨骼绑定: {validBones}/{result.renderer.bones.Length}");
                }
            }
            
            AddDebugLog("=== 穿戴测试完成 ===");
        }
        
        private void TestRemoveCloth()
        {
            if (clothBinder == null) return;
            
            AddDebugLog("=== 开始测试脱下服装 ===");
            
            clothBinder.RemoveAllClothes();
            RefreshDebugInfo();
            
            AddDebugLog($"脱下后服装数量: {currentWornClothes.Count}");
            AddDebugLog("=== 脱下测试完成 ===");
        }
        
        private void DrawDebugInfo()
        {
            EditorGUILayout.LabelField("调试信息", EditorStyles.boldLabel);
            
            // 系统状态
            if (clothBinder != null)
            {
                EditorGUILayout.LabelField($"骨骼映射: {currentBoneMap.Count} 个");
                EditorGUILayout.LabelField($"已穿戴服装: {currentWornClothes.Count} 件");
                
                if (currentWornClothes.Count > 0)
                {
                    EditorGUILayout.LabelField("当前服装:");
                    EditorGUI.indentLevel++;
                    foreach (var cloth in currentWornClothes)
                    {
                        string status = cloth.renderer?.enabled == true ? "✅" : "❌";
                        EditorGUILayout.LabelField($"{status} {cloth.name} ({cloth.type})");
                    }
                    EditorGUI.indentLevel--;
                }
            }
            
            EditorGUILayout.Space();
            
            // 调试日志
            EditorGUILayout.LabelField($"调试日志 ({debugLogs.Count})", EditorStyles.boldLabel);
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(200));
            
            foreach (string log in debugLogs)
            {
                EditorGUILayout.LabelField(log, EditorStyles.miniLabel);
            }
            
            EditorGUILayout.EndScrollView();
        }
        
        private void AddDebugLog(string message)
        {
            string timestamp = System.DateTime.Now.ToString("HH:mm:ss.fff");
            debugLogs.Add($"[{timestamp}] {message}");
            
            // 限制日志数量
            if (debugLogs.Count > 100)
            {
                debugLogs.RemoveAt(0);
            }
            
            Debug.Log($"[VRM10ClothDebugger] {message}");
            Repaint();
        }
    }
}
