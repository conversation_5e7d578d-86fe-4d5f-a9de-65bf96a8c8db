//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using System;
using System.Runtime.CompilerServices;
using Unity.IL2CPP.CompilerServices;

#pragma warning disable 0660, 0661

namespace Unity.Mathematics
{
    /// <summary>A 4x3 matrix of floats.</summary>
    [System.Serializable]
    [Il2CppEagerStaticClassConstruction]
    public partial struct float4x3 : System.IEquatable<float4x3>, IFormattable
    {
        /// <summary>Column 0 of the matrix.</summary>
        public float4 c0;
        /// <summary>Column 1 of the matrix.</summary>
        public float4 c1;
        /// <summary>Column 2 of the matrix.</summary>
        public float4 c2;

        /// <summary>float4x3 zero value.</summary>
        public static readonly float4x3 zero;

        /// <summary>Constructs a float4x3 matrix from three float4 vectors.</summary>
        /// <param name="c0">The matrix column c0 will be set to this value.</param>
        /// <param name="c1">The matrix column c1 will be set to this value.</param>
        /// <param name="c2">The matrix column c2 will be set to this value.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public float4x3(float4 c0, float4 c1, float4 c2)
        {
            this.c0 = c0;
            this.c1 = c1;
            this.c2 = c2;
        }

        /// <summary>Constructs a float4x3 matrix from 12 float values given in row-major order.</summary>
        /// <param name="m00">The matrix at row 0, column 0 will be set to this value.</param>
        /// <param name="m01">The matrix at row 0, column 1 will be set to this value.</param>
        /// <param name="m02">The matrix at row 0, column 2 will be set to this value.</param>
        /// <param name="m10">The matrix at row 1, column 0 will be set to this value.</param>
        /// <param name="m11">The matrix at row 1, column 1 will be set to this value.</param>
        /// <param name="m12">The matrix at row 1, column 2 will be set to this value.</param>
        /// <param name="m20">The matrix at row 2, column 0 will be set to this value.</param>
        /// <param name="m21">The matrix at row 2, column 1 will be set to this value.</param>
        /// <param name="m22">The matrix at row 2, column 2 will be set to this value.</param>
        /// <param name="m30">The matrix at row 3, column 0 will be set to this value.</param>
        /// <param name="m31">The matrix at row 3, column 1 will be set to this value.</param>
        /// <param name="m32">The matrix at row 3, column 2 will be set to this value.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public float4x3(float m00, float m01, float m02,
                        float m10, float m11, float m12,
                        float m20, float m21, float m22,
                        float m30, float m31, float m32)
        {
            this.c0 = new float4(m00, m10, m20, m30);
            this.c1 = new float4(m01, m11, m21, m31);
            this.c2 = new float4(m02, m12, m22, m32);
        }

        /// <summary>Constructs a float4x3 matrix from a single float value by assigning it to every component.</summary>
        /// <param name="v">float to convert to float4x3</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public float4x3(float v)
        {
            this.c0 = v;
            this.c1 = v;
            this.c2 = v;
        }

        /// <summary>Constructs a float4x3 matrix from a single bool value by converting it to float and assigning it to every component.</summary>
        /// <param name="v">bool to convert to float4x3</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public float4x3(bool v)
        {
            this.c0 = math.select(new float4(0.0f), new float4(1.0f), v);
            this.c1 = math.select(new float4(0.0f), new float4(1.0f), v);
            this.c2 = math.select(new float4(0.0f), new float4(1.0f), v);
        }

        /// <summary>Constructs a float4x3 matrix from a bool4x3 matrix by componentwise conversion.</summary>
        /// <param name="v">bool4x3 to convert to float4x3</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public float4x3(bool4x3 v)
        {
            this.c0 = math.select(new float4(0.0f), new float4(1.0f), v.c0);
            this.c1 = math.select(new float4(0.0f), new float4(1.0f), v.c1);
            this.c2 = math.select(new float4(0.0f), new float4(1.0f), v.c2);
        }

        /// <summary>Constructs a float4x3 matrix from a single int value by converting it to float and assigning it to every component.</summary>
        /// <param name="v">int to convert to float4x3</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public float4x3(int v)
        {
            this.c0 = v;
            this.c1 = v;
            this.c2 = v;
        }

        /// <summary>Constructs a float4x3 matrix from a int4x3 matrix by componentwise conversion.</summary>
        /// <param name="v">int4x3 to convert to float4x3</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public float4x3(int4x3 v)
        {
            this.c0 = v.c0;
            this.c1 = v.c1;
            this.c2 = v.c2;
        }

        /// <summary>Constructs a float4x3 matrix from a single uint value by converting it to float and assigning it to every component.</summary>
        /// <param name="v">uint to convert to float4x3</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public float4x3(uint v)
        {
            this.c0 = v;
            this.c1 = v;
            this.c2 = v;
        }

        /// <summary>Constructs a float4x3 matrix from a uint4x3 matrix by componentwise conversion.</summary>
        /// <param name="v">uint4x3 to convert to float4x3</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public float4x3(uint4x3 v)
        {
            this.c0 = v.c0;
            this.c1 = v.c1;
            this.c2 = v.c2;
        }

        /// <summary>Constructs a float4x3 matrix from a single double value by converting it to float and assigning it to every component.</summary>
        /// <param name="v">double to convert to float4x3</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public float4x3(double v)
        {
            this.c0 = (float4)v;
            this.c1 = (float4)v;
            this.c2 = (float4)v;
        }

        /// <summary>Constructs a float4x3 matrix from a double4x3 matrix by componentwise conversion.</summary>
        /// <param name="v">double4x3 to convert to float4x3</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public float4x3(double4x3 v)
        {
            this.c0 = (float4)v.c0;
            this.c1 = (float4)v.c1;
            this.c2 = (float4)v.c2;
        }


        /// <summary>Implicitly converts a single float value to a float4x3 matrix by assigning it to every component.</summary>
        /// <param name="v">float to convert to float4x3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator float4x3(float v) { return new float4x3(v); }

        /// <summary>Explicitly converts a single bool value to a float4x3 matrix by converting it to float and assigning it to every component.</summary>
        /// <param name="v">bool to convert to float4x3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator float4x3(bool v) { return new float4x3(v); }

        /// <summary>Explicitly converts a bool4x3 matrix to a float4x3 matrix by componentwise conversion.</summary>
        /// <param name="v">bool4x3 to convert to float4x3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator float4x3(bool4x3 v) { return new float4x3(v); }

        /// <summary>Implicitly converts a single int value to a float4x3 matrix by converting it to float and assigning it to every component.</summary>
        /// <param name="v">int to convert to float4x3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator float4x3(int v) { return new float4x3(v); }

        /// <summary>Implicitly converts a int4x3 matrix to a float4x3 matrix by componentwise conversion.</summary>
        /// <param name="v">int4x3 to convert to float4x3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator float4x3(int4x3 v) { return new float4x3(v); }

        /// <summary>Implicitly converts a single uint value to a float4x3 matrix by converting it to float and assigning it to every component.</summary>
        /// <param name="v">uint to convert to float4x3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator float4x3(uint v) { return new float4x3(v); }

        /// <summary>Implicitly converts a uint4x3 matrix to a float4x3 matrix by componentwise conversion.</summary>
        /// <param name="v">uint4x3 to convert to float4x3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator float4x3(uint4x3 v) { return new float4x3(v); }

        /// <summary>Explicitly converts a single double value to a float4x3 matrix by converting it to float and assigning it to every component.</summary>
        /// <param name="v">double to convert to float4x3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator float4x3(double v) { return new float4x3(v); }

        /// <summary>Explicitly converts a double4x3 matrix to a float4x3 matrix by componentwise conversion.</summary>
        /// <param name="v">double4x3 to convert to float4x3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator float4x3(double4x3 v) { return new float4x3(v); }


        /// <summary>Returns the result of a componentwise multiplication operation on two float4x3 matrices.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise multiplication.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise multiplication.</param>
        /// <returns>float4x3 result of the componentwise multiplication.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 operator * (float4x3 lhs, float4x3 rhs) { return new float4x3 (lhs.c0 * rhs.c0, lhs.c1 * rhs.c1, lhs.c2 * rhs.c2); }

        /// <summary>Returns the result of a componentwise multiplication operation on a float4x3 matrix and a float value.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise multiplication.</param>
        /// <param name="rhs">Right hand side float to use to compute componentwise multiplication.</param>
        /// <returns>float4x3 result of the componentwise multiplication.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 operator * (float4x3 lhs, float rhs) { return new float4x3 (lhs.c0 * rhs, lhs.c1 * rhs, lhs.c2 * rhs); }

        /// <summary>Returns the result of a componentwise multiplication operation on a float value and a float4x3 matrix.</summary>
        /// <param name="lhs">Left hand side float to use to compute componentwise multiplication.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise multiplication.</param>
        /// <returns>float4x3 result of the componentwise multiplication.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 operator * (float lhs, float4x3 rhs) { return new float4x3 (lhs * rhs.c0, lhs * rhs.c1, lhs * rhs.c2); }


        /// <summary>Returns the result of a componentwise addition operation on two float4x3 matrices.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise addition.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise addition.</param>
        /// <returns>float4x3 result of the componentwise addition.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 operator + (float4x3 lhs, float4x3 rhs) { return new float4x3 (lhs.c0 + rhs.c0, lhs.c1 + rhs.c1, lhs.c2 + rhs.c2); }

        /// <summary>Returns the result of a componentwise addition operation on a float4x3 matrix and a float value.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise addition.</param>
        /// <param name="rhs">Right hand side float to use to compute componentwise addition.</param>
        /// <returns>float4x3 result of the componentwise addition.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 operator + (float4x3 lhs, float rhs) { return new float4x3 (lhs.c0 + rhs, lhs.c1 + rhs, lhs.c2 + rhs); }

        /// <summary>Returns the result of a componentwise addition operation on a float value and a float4x3 matrix.</summary>
        /// <param name="lhs">Left hand side float to use to compute componentwise addition.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise addition.</param>
        /// <returns>float4x3 result of the componentwise addition.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 operator + (float lhs, float4x3 rhs) { return new float4x3 (lhs + rhs.c0, lhs + rhs.c1, lhs + rhs.c2); }


        /// <summary>Returns the result of a componentwise subtraction operation on two float4x3 matrices.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise subtraction.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise subtraction.</param>
        /// <returns>float4x3 result of the componentwise subtraction.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 operator - (float4x3 lhs, float4x3 rhs) { return new float4x3 (lhs.c0 - rhs.c0, lhs.c1 - rhs.c1, lhs.c2 - rhs.c2); }

        /// <summary>Returns the result of a componentwise subtraction operation on a float4x3 matrix and a float value.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise subtraction.</param>
        /// <param name="rhs">Right hand side float to use to compute componentwise subtraction.</param>
        /// <returns>float4x3 result of the componentwise subtraction.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 operator - (float4x3 lhs, float rhs) { return new float4x3 (lhs.c0 - rhs, lhs.c1 - rhs, lhs.c2 - rhs); }

        /// <summary>Returns the result of a componentwise subtraction operation on a float value and a float4x3 matrix.</summary>
        /// <param name="lhs">Left hand side float to use to compute componentwise subtraction.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise subtraction.</param>
        /// <returns>float4x3 result of the componentwise subtraction.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 operator - (float lhs, float4x3 rhs) { return new float4x3 (lhs - rhs.c0, lhs - rhs.c1, lhs - rhs.c2); }


        /// <summary>Returns the result of a componentwise division operation on two float4x3 matrices.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise division.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise division.</param>
        /// <returns>float4x3 result of the componentwise division.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 operator / (float4x3 lhs, float4x3 rhs) { return new float4x3 (lhs.c0 / rhs.c0, lhs.c1 / rhs.c1, lhs.c2 / rhs.c2); }

        /// <summary>Returns the result of a componentwise division operation on a float4x3 matrix and a float value.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise division.</param>
        /// <param name="rhs">Right hand side float to use to compute componentwise division.</param>
        /// <returns>float4x3 result of the componentwise division.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 operator / (float4x3 lhs, float rhs) { return new float4x3 (lhs.c0 / rhs, lhs.c1 / rhs, lhs.c2 / rhs); }

        /// <summary>Returns the result of a componentwise division operation on a float value and a float4x3 matrix.</summary>
        /// <param name="lhs">Left hand side float to use to compute componentwise division.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise division.</param>
        /// <returns>float4x3 result of the componentwise division.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 operator / (float lhs, float4x3 rhs) { return new float4x3 (lhs / rhs.c0, lhs / rhs.c1, lhs / rhs.c2); }


        /// <summary>Returns the result of a componentwise modulus operation on two float4x3 matrices.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise modulus.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise modulus.</param>
        /// <returns>float4x3 result of the componentwise modulus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 operator % (float4x3 lhs, float4x3 rhs) { return new float4x3 (lhs.c0 % rhs.c0, lhs.c1 % rhs.c1, lhs.c2 % rhs.c2); }

        /// <summary>Returns the result of a componentwise modulus operation on a float4x3 matrix and a float value.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise modulus.</param>
        /// <param name="rhs">Right hand side float to use to compute componentwise modulus.</param>
        /// <returns>float4x3 result of the componentwise modulus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 operator % (float4x3 lhs, float rhs) { return new float4x3 (lhs.c0 % rhs, lhs.c1 % rhs, lhs.c2 % rhs); }

        /// <summary>Returns the result of a componentwise modulus operation on a float value and a float4x3 matrix.</summary>
        /// <param name="lhs">Left hand side float to use to compute componentwise modulus.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise modulus.</param>
        /// <returns>float4x3 result of the componentwise modulus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 operator % (float lhs, float4x3 rhs) { return new float4x3 (lhs % rhs.c0, lhs % rhs.c1, lhs % rhs.c2); }


        /// <summary>Returns the result of a componentwise increment operation on a float4x3 matrix.</summary>
        /// <param name="val">Value to use when computing the componentwise increment.</param>
        /// <returns>float4x3 result of the componentwise increment.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 operator ++ (float4x3 val) { return new float4x3 (++val.c0, ++val.c1, ++val.c2); }


        /// <summary>Returns the result of a componentwise decrement operation on a float4x3 matrix.</summary>
        /// <param name="val">Value to use when computing the componentwise decrement.</param>
        /// <returns>float4x3 result of the componentwise decrement.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 operator -- (float4x3 val) { return new float4x3 (--val.c0, --val.c1, --val.c2); }


        /// <summary>Returns the result of a componentwise less than operation on two float4x3 matrices.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise less than.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise less than.</param>
        /// <returns>bool4x3 result of the componentwise less than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4x3 operator < (float4x3 lhs, float4x3 rhs) { return new bool4x3 (lhs.c0 < rhs.c0, lhs.c1 < rhs.c1, lhs.c2 < rhs.c2); }

        /// <summary>Returns the result of a componentwise less than operation on a float4x3 matrix and a float value.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise less than.</param>
        /// <param name="rhs">Right hand side float to use to compute componentwise less than.</param>
        /// <returns>bool4x3 result of the componentwise less than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4x3 operator < (float4x3 lhs, float rhs) { return new bool4x3 (lhs.c0 < rhs, lhs.c1 < rhs, lhs.c2 < rhs); }

        /// <summary>Returns the result of a componentwise less than operation on a float value and a float4x3 matrix.</summary>
        /// <param name="lhs">Left hand side float to use to compute componentwise less than.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise less than.</param>
        /// <returns>bool4x3 result of the componentwise less than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4x3 operator < (float lhs, float4x3 rhs) { return new bool4x3 (lhs < rhs.c0, lhs < rhs.c1, lhs < rhs.c2); }


        /// <summary>Returns the result of a componentwise less or equal operation on two float4x3 matrices.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise less or equal.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise less or equal.</param>
        /// <returns>bool4x3 result of the componentwise less or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4x3 operator <= (float4x3 lhs, float4x3 rhs) { return new bool4x3 (lhs.c0 <= rhs.c0, lhs.c1 <= rhs.c1, lhs.c2 <= rhs.c2); }

        /// <summary>Returns the result of a componentwise less or equal operation on a float4x3 matrix and a float value.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise less or equal.</param>
        /// <param name="rhs">Right hand side float to use to compute componentwise less or equal.</param>
        /// <returns>bool4x3 result of the componentwise less or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4x3 operator <= (float4x3 lhs, float rhs) { return new bool4x3 (lhs.c0 <= rhs, lhs.c1 <= rhs, lhs.c2 <= rhs); }

        /// <summary>Returns the result of a componentwise less or equal operation on a float value and a float4x3 matrix.</summary>
        /// <param name="lhs">Left hand side float to use to compute componentwise less or equal.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise less or equal.</param>
        /// <returns>bool4x3 result of the componentwise less or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4x3 operator <= (float lhs, float4x3 rhs) { return new bool4x3 (lhs <= rhs.c0, lhs <= rhs.c1, lhs <= rhs.c2); }


        /// <summary>Returns the result of a componentwise greater than operation on two float4x3 matrices.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise greater than.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise greater than.</param>
        /// <returns>bool4x3 result of the componentwise greater than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4x3 operator > (float4x3 lhs, float4x3 rhs) { return new bool4x3 (lhs.c0 > rhs.c0, lhs.c1 > rhs.c1, lhs.c2 > rhs.c2); }

        /// <summary>Returns the result of a componentwise greater than operation on a float4x3 matrix and a float value.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise greater than.</param>
        /// <param name="rhs">Right hand side float to use to compute componentwise greater than.</param>
        /// <returns>bool4x3 result of the componentwise greater than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4x3 operator > (float4x3 lhs, float rhs) { return new bool4x3 (lhs.c0 > rhs, lhs.c1 > rhs, lhs.c2 > rhs); }

        /// <summary>Returns the result of a componentwise greater than operation on a float value and a float4x3 matrix.</summary>
        /// <param name="lhs">Left hand side float to use to compute componentwise greater than.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise greater than.</param>
        /// <returns>bool4x3 result of the componentwise greater than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4x3 operator > (float lhs, float4x3 rhs) { return new bool4x3 (lhs > rhs.c0, lhs > rhs.c1, lhs > rhs.c2); }


        /// <summary>Returns the result of a componentwise greater or equal operation on two float4x3 matrices.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise greater or equal.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise greater or equal.</param>
        /// <returns>bool4x3 result of the componentwise greater or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4x3 operator >= (float4x3 lhs, float4x3 rhs) { return new bool4x3 (lhs.c0 >= rhs.c0, lhs.c1 >= rhs.c1, lhs.c2 >= rhs.c2); }

        /// <summary>Returns the result of a componentwise greater or equal operation on a float4x3 matrix and a float value.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise greater or equal.</param>
        /// <param name="rhs">Right hand side float to use to compute componentwise greater or equal.</param>
        /// <returns>bool4x3 result of the componentwise greater or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4x3 operator >= (float4x3 lhs, float rhs) { return new bool4x3 (lhs.c0 >= rhs, lhs.c1 >= rhs, lhs.c2 >= rhs); }

        /// <summary>Returns the result of a componentwise greater or equal operation on a float value and a float4x3 matrix.</summary>
        /// <param name="lhs">Left hand side float to use to compute componentwise greater or equal.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise greater or equal.</param>
        /// <returns>bool4x3 result of the componentwise greater or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4x3 operator >= (float lhs, float4x3 rhs) { return new bool4x3 (lhs >= rhs.c0, lhs >= rhs.c1, lhs >= rhs.c2); }


        /// <summary>Returns the result of a componentwise unary minus operation on a float4x3 matrix.</summary>
        /// <param name="val">Value to use when computing the componentwise unary minus.</param>
        /// <returns>float4x3 result of the componentwise unary minus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 operator - (float4x3 val) { return new float4x3 (-val.c0, -val.c1, -val.c2); }


        /// <summary>Returns the result of a componentwise unary plus operation on a float4x3 matrix.</summary>
        /// <param name="val">Value to use when computing the componentwise unary plus.</param>
        /// <returns>float4x3 result of the componentwise unary plus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 operator + (float4x3 val) { return new float4x3 (+val.c0, +val.c1, +val.c2); }


        /// <summary>Returns the result of a componentwise equality operation on two float4x3 matrices.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise equality.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise equality.</param>
        /// <returns>bool4x3 result of the componentwise equality.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4x3 operator == (float4x3 lhs, float4x3 rhs) { return new bool4x3 (lhs.c0 == rhs.c0, lhs.c1 == rhs.c1, lhs.c2 == rhs.c2); }

        /// <summary>Returns the result of a componentwise equality operation on a float4x3 matrix and a float value.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise equality.</param>
        /// <param name="rhs">Right hand side float to use to compute componentwise equality.</param>
        /// <returns>bool4x3 result of the componentwise equality.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4x3 operator == (float4x3 lhs, float rhs) { return new bool4x3 (lhs.c0 == rhs, lhs.c1 == rhs, lhs.c2 == rhs); }

        /// <summary>Returns the result of a componentwise equality operation on a float value and a float4x3 matrix.</summary>
        /// <param name="lhs">Left hand side float to use to compute componentwise equality.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise equality.</param>
        /// <returns>bool4x3 result of the componentwise equality.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4x3 operator == (float lhs, float4x3 rhs) { return new bool4x3 (lhs == rhs.c0, lhs == rhs.c1, lhs == rhs.c2); }


        /// <summary>Returns the result of a componentwise not equal operation on two float4x3 matrices.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise not equal.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise not equal.</param>
        /// <returns>bool4x3 result of the componentwise not equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4x3 operator != (float4x3 lhs, float4x3 rhs) { return new bool4x3 (lhs.c0 != rhs.c0, lhs.c1 != rhs.c1, lhs.c2 != rhs.c2); }

        /// <summary>Returns the result of a componentwise not equal operation on a float4x3 matrix and a float value.</summary>
        /// <param name="lhs">Left hand side float4x3 to use to compute componentwise not equal.</param>
        /// <param name="rhs">Right hand side float to use to compute componentwise not equal.</param>
        /// <returns>bool4x3 result of the componentwise not equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4x3 operator != (float4x3 lhs, float rhs) { return new bool4x3 (lhs.c0 != rhs, lhs.c1 != rhs, lhs.c2 != rhs); }

        /// <summary>Returns the result of a componentwise not equal operation on a float value and a float4x3 matrix.</summary>
        /// <param name="lhs">Left hand side float to use to compute componentwise not equal.</param>
        /// <param name="rhs">Right hand side float4x3 to use to compute componentwise not equal.</param>
        /// <returns>bool4x3 result of the componentwise not equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool4x3 operator != (float lhs, float4x3 rhs) { return new bool4x3 (lhs != rhs.c0, lhs != rhs.c1, lhs != rhs.c2); }



        /// <summary>Returns the float4 element at a specified index.</summary>
        unsafe public ref float4 this[int index]
        {
            get
            {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
                if ((uint)index >= 3)
                    throw new System.ArgumentException("index must be between[0...2]");
#endif
                fixed (float4x3* array = &this) { return ref ((float4*)array)[index]; }
            }
        }

        /// <summary>Returns true if the float4x3 is equal to a given float4x3, false otherwise.</summary>
        /// <param name="rhs">Right hand side argument to compare equality with.</param>
        /// <returns>The result of the equality comparison.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public bool Equals(float4x3 rhs) { return c0.Equals(rhs.c0) && c1.Equals(rhs.c1) && c2.Equals(rhs.c2); }

        /// <summary>Returns true if the float4x3 is equal to a given float4x3, false otherwise.</summary>
        /// <param name="o">Right hand side argument to compare equality with.</param>
        /// <returns>The result of the equality comparison.</returns>
        public override bool Equals(object o) { return o is float4x3 converted && Equals(converted); }


        /// <summary>Returns a hash code for the float4x3.</summary>
        /// <returns>The computed hash code.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override int GetHashCode() { return (int)math.hash(this); }


        /// <summary>Returns a string representation of the float4x3.</summary>
        /// <returns>String representation of the value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override string ToString()
        {
            return string.Format("float4x3({0}f, {1}f, {2}f,  {3}f, {4}f, {5}f,  {6}f, {7}f, {8}f,  {9}f, {10}f, {11}f)", c0.x, c1.x, c2.x, c0.y, c1.y, c2.y, c0.z, c1.z, c2.z, c0.w, c1.w, c2.w);
        }

        /// <summary>Returns a string representation of the float4x3 using a specified format and culture-specific format information.</summary>
        /// <param name="format">Format string to use during string formatting.</param>
        /// <param name="formatProvider">Format provider to use during string formatting.</param>
        /// <returns>String representation of the value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public string ToString(string format, IFormatProvider formatProvider)
        {
            return string.Format("float4x3({0}f, {1}f, {2}f,  {3}f, {4}f, {5}f,  {6}f, {7}f, {8}f,  {9}f, {10}f, {11}f)", c0.x.ToString(format, formatProvider), c1.x.ToString(format, formatProvider), c2.x.ToString(format, formatProvider), c0.y.ToString(format, formatProvider), c1.y.ToString(format, formatProvider), c2.y.ToString(format, formatProvider), c0.z.ToString(format, formatProvider), c1.z.ToString(format, formatProvider), c2.z.ToString(format, formatProvider), c0.w.ToString(format, formatProvider), c1.w.ToString(format, formatProvider), c2.w.ToString(format, formatProvider));
        }

    }

    public static partial class math
    {
        /// <summary>Returns a float4x3 matrix constructed from three float4 vectors.</summary>
        /// <param name="c0">The matrix column c0 will be set to this value.</param>
        /// <param name="c1">The matrix column c1 will be set to this value.</param>
        /// <param name="c2">The matrix column c2 will be set to this value.</param>
        /// <returns>float4x3 constructed from arguments.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 float4x3(float4 c0, float4 c1, float4 c2) { return new float4x3(c0, c1, c2); }

        /// <summary>Returns a float4x3 matrix constructed from from 12 float values given in row-major order.</summary>
        /// <param name="m00">The matrix at row 0, column 0 will be set to this value.</param>
        /// <param name="m01">The matrix at row 0, column 1 will be set to this value.</param>
        /// <param name="m02">The matrix at row 0, column 2 will be set to this value.</param>
        /// <param name="m10">The matrix at row 1, column 0 will be set to this value.</param>
        /// <param name="m11">The matrix at row 1, column 1 will be set to this value.</param>
        /// <param name="m12">The matrix at row 1, column 2 will be set to this value.</param>
        /// <param name="m20">The matrix at row 2, column 0 will be set to this value.</param>
        /// <param name="m21">The matrix at row 2, column 1 will be set to this value.</param>
        /// <param name="m22">The matrix at row 2, column 2 will be set to this value.</param>
        /// <param name="m30">The matrix at row 3, column 0 will be set to this value.</param>
        /// <param name="m31">The matrix at row 3, column 1 will be set to this value.</param>
        /// <param name="m32">The matrix at row 3, column 2 will be set to this value.</param>
        /// <returns>float4x3 constructed from arguments.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 float4x3(float m00, float m01, float m02,
                                        float m10, float m11, float m12,
                                        float m20, float m21, float m22,
                                        float m30, float m31, float m32)
        {
            return new float4x3(m00, m01, m02,
                                m10, m11, m12,
                                m20, m21, m22,
                                m30, m31, m32);
        }

        /// <summary>Returns a float4x3 matrix constructed from a single float value by assigning it to every component.</summary>
        /// <param name="v">float to convert to float4x3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 float4x3(float v) { return new float4x3(v); }

        /// <summary>Returns a float4x3 matrix constructed from a single bool value by converting it to float and assigning it to every component.</summary>
        /// <param name="v">bool to convert to float4x3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 float4x3(bool v) { return new float4x3(v); }

        /// <summary>Return a float4x3 matrix constructed from a bool4x3 matrix by componentwise conversion.</summary>
        /// <param name="v">bool4x3 to convert to float4x3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 float4x3(bool4x3 v) { return new float4x3(v); }

        /// <summary>Returns a float4x3 matrix constructed from a single int value by converting it to float and assigning it to every component.</summary>
        /// <param name="v">int to convert to float4x3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 float4x3(int v) { return new float4x3(v); }

        /// <summary>Return a float4x3 matrix constructed from a int4x3 matrix by componentwise conversion.</summary>
        /// <param name="v">int4x3 to convert to float4x3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 float4x3(int4x3 v) { return new float4x3(v); }

        /// <summary>Returns a float4x3 matrix constructed from a single uint value by converting it to float and assigning it to every component.</summary>
        /// <param name="v">uint to convert to float4x3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 float4x3(uint v) { return new float4x3(v); }

        /// <summary>Return a float4x3 matrix constructed from a uint4x3 matrix by componentwise conversion.</summary>
        /// <param name="v">uint4x3 to convert to float4x3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 float4x3(uint4x3 v) { return new float4x3(v); }

        /// <summary>Returns a float4x3 matrix constructed from a single double value by converting it to float and assigning it to every component.</summary>
        /// <param name="v">double to convert to float4x3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 float4x3(double v) { return new float4x3(v); }

        /// <summary>Return a float4x3 matrix constructed from a double4x3 matrix by componentwise conversion.</summary>
        /// <param name="v">double4x3 to convert to float4x3</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float4x3 float4x3(double4x3 v) { return new float4x3(v); }

        /// <summary>Return the float3x4 transpose of a float4x3 matrix.</summary>
        /// <param name="v">Value to transpose.</param>
        /// <returns>Transposed value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static float3x4 transpose(float4x3 v)
        {
            return float3x4(
                v.c0.x, v.c0.y, v.c0.z, v.c0.w,
                v.c1.x, v.c1.y, v.c1.z, v.c1.w,
                v.c2.x, v.c2.y, v.c2.z, v.c2.w);
        }

        /// <summary>Returns a uint hash code of a float4x3 matrix.</summary>
        /// <param name="v">Matrix value to hash.</param>
        /// <returns>uint hash of the argument.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint hash(float4x3 v)
        {
            return csum(asuint(v.c0) * uint4(0xC53F4755u, 0x6985C229u, 0xE133B0B3u, 0xC3E0A3B9u) +
                        asuint(v.c1) * uint4(0xFE31134Fu, 0x712A34D7u, 0x9D77A59Bu, 0x4942CA39u) +
                        asuint(v.c2) * uint4(0xB40EC62Du, 0x565ED63Fu, 0x93C30C2Bu, 0xDCAF0351u)) + 0x6E050B01u;
        }

        /// <summary>
        /// Returns a uint4 vector hash code of a float4x3 matrix.
        /// When multiple elements are to be hashes together, it can more efficient to calculate and combine wide hash
        /// that are only reduced to a narrow uint hash at the very end instead of at every step.
        /// </summary>
        /// <param name="v">Matrix value to hash.</param>
        /// <returns>uint4 hash of the argument.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint4 hashwide(float4x3 v)
        {
            return (asuint(v.c0) * uint4(0x750FDBF5u, 0x7F3DD499u, 0x52EAAEBBu, 0x4599C793u) +
                    asuint(v.c1) * uint4(0x83B5E729u, 0xC267163Fu, 0x67BC9149u, 0xAD7C5EC1u) +
                    asuint(v.c2) * uint4(0x822A7D6Du, 0xB492BF15u, 0xD37220E3u, 0x7AA2C2BDu)) + 0xE16BC89Du;
        }

    }
}
