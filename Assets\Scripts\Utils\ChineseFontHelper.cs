using UnityEngine;
using TMPro;

namespace VRoidFaceCustomization
{
    /// <summary>
    /// 中文字体辅助工具
    /// 解决Unity中中文字符显示为方框的问题
    /// </summary>
    public class ChineseFontHelper : MonoBehaviour
    {
        [Header("字体设置")]
        [SerializeField] private TMP_FontAsset chineseFontAsset;
        [SerializeField] private bool autoApplyOnStart = true;
        [SerializeField] private bool debugMode = true;
        
        [Header("自动查找字体")]
        [SerializeField] private string[] fontSearchPaths = {
            "Fonts & Materials/Arial Unicode MS SDF",
            "Fonts & Materials/NotoSansCJK SDF", 
            "Fonts & Materials/SimHei SDF",
            "Fonts & Materials/Microsoft YaHei SDF",
            "TextMeshPro/Fonts & Materials/LiberationSans SDF"
        };
        
        void Start()
        {
            if (autoApplyOnStart)
            {
                SetupChineseFont();
            }
        }
        
        /// <summary>
        /// 设置中文字体
        /// </summary>
        [ContextMenu("设置中文字体")]
        public void SetupChineseFont()
        {
            LogDebug("🔤 开始设置中文字体...");
            
            // 如果已经指定了字体资源，直接使用
            if (chineseFontAsset != null)
            {
                ApplyFontToAllTexts(chineseFontAsset);
                LogDebug($"✅ 使用指定的字体资源: {chineseFontAsset.name}");
                return;
            }
            
            // 自动查找可用的中文字体
            TMP_FontAsset foundFont = FindChineseFont();
            if (foundFont != null)
            {
                chineseFontAsset = foundFont;
                ApplyFontToAllTexts(foundFont);
                LogDebug($"✅ 找到并应用中文字体: {foundFont.name}");
            }
            else
            {
                LogDebug("❌ 未找到合适的中文字体");
                ShowFontInstallationGuide();
            }
        }
        
        /// <summary>
        /// 查找可用的中文字体
        /// </summary>
        TMP_FontAsset FindChineseFont()
        {
            foreach (string path in fontSearchPaths)
            {
                var font = Resources.Load<TMP_FontAsset>(path);
                if (font != null)
                {
                    LogDebug($"🔍 找到字体: {path}");
                    return font;
                }
            }
            
            // 尝试在项目中查找所有TMP字体资源
            #if UNITY_EDITOR
            string[] guids = UnityEditor.AssetDatabase.FindAssets("t:TMP_FontAsset");
            foreach (string guid in guids)
            {
                string assetPath = UnityEditor.AssetDatabase.GUIDToAssetPath(guid);
                var font = UnityEditor.AssetDatabase.LoadAssetAtPath<TMP_FontAsset>(assetPath);
                if (font != null && IsSuitableForChinese(font))
                {
                    LogDebug($"🔍 在项目中找到合适字体: {assetPath}");
                    return font;
                }
            }
            #endif
            
            return null;
        }
        
        /// <summary>
        /// 检查字体是否适合显示中文
        /// </summary>
        bool IsSuitableForChinese(TMP_FontAsset font)
        {
            if (font == null) return false;
            
            // 检查字体名称是否包含中文字体的关键词
            string fontName = font.name.ToLower();
            string[] chineseKeywords = {
                "unicode", "cjk", "chinese", "simhei", "yahei", 
                "noto", "source", "han", "simsun", "kaiti"
            };
            
            foreach (string keyword in chineseKeywords)
            {
                if (fontName.Contains(keyword))
                {
                    return true;
                }
            }
            
            return false;
        }
        
        /// <summary>
        /// 将字体应用到所有文本组件
        /// </summary>
        void ApplyFontToAllTexts(TMP_FontAsset font)
        {
            // 应用到当前场景中的所有TextMeshProUGUI组件
            var allTexts = FindObjectsOfType<TextMeshProUGUI>();
            int appliedCount = 0;
            
            foreach (var text in allTexts)
            {
                text.font = font;
                appliedCount++;
            }
            
            LogDebug($"✅ 已将字体应用到 {appliedCount} 个文本组件");
        }
        
        /// <summary>
        /// 显示字体安装指南
        /// </summary>
        void ShowFontInstallationGuide()
        {
            LogDebug("📖 中文字体安装指南:");
            LogDebug("方法1: 下载免费中文字体");
            LogDebug("  - 推荐: Noto Sans CJK (Google开源字体)");
            LogDebug("  - 下载地址: https://fonts.google.com/noto/specimen/Noto+Sans+SC");
            LogDebug("");
            LogDebug("方法2: 使用系统字体");
            LogDebug("  - Windows: 微软雅黑 (Microsoft YaHei)");
            LogDebug("  - macOS: PingFang SC");
            LogDebug("  - Linux: Noto Sans CJK");
            LogDebug("");
            LogDebug("方法3: 创建TextMeshPro字体资源");
            LogDebug("  1. Window → TextMeshPro → Font Asset Creator");
            LogDebug("  2. 选择系统中的中文字体");
            LogDebug("  3. 设置Character Set为Unicode Range");
            LogDebug("  4. 添加中文字符范围: 0x4E00-0x9FFF");
            LogDebug("  5. 点击Generate Font Atlas");
            LogDebug("  6. 保存为SDF字体资源");
        }
        
        /// <summary>
        /// 为单个文本组件设置中文字体
        /// </summary>
        public void ApplyChineseFontToText(TextMeshProUGUI text)
        {
            if (text == null) return;
            
            if (chineseFontAsset != null)
            {
                text.font = chineseFontAsset;
                LogDebug($"✅ 为文本组件 {text.name} 设置中文字体");
            }
            else
            {
                LogDebug("⚠️ 没有可用的中文字体资源");
            }
        }
        
        /// <summary>
        /// 创建简单的英文替代文本
        /// </summary>
        public static string GetEnglishAlternative(string chineseText)
        {
            var translations = new System.Collections.Generic.Dictionary<string, string>
            {
                {"VRM场景切换测试面板", "VRM Scene Transition Test Panel"},
                {"等待测试", "Waiting for test"},
                {"VRM信息将显示在这里", "VRM info will be displayed here"},
                {"测试VRM捕获", "Test VRM Capture"},
                {"测试VRM加载", "Test VRM Load"},
                {"清除数据", "Clear Data"},
                {"显示详细信息", "Show Details"},
                {"选择文件", "Select File"},
                {"确认", "Confirm"},
                {"VRM文件加载成功", "VRM file loaded successfully"},
                {"VRM文件加载失败", "VRM file loading failed"},
                {"没有VRM数据", "No VRM data"},
                {"VRM模型加载成功", "VRM model loaded successfully"},
                {"场景切换测试", "Scene Transition Test"}
            };
            
            return translations.ContainsKey(chineseText) ? translations[chineseText] : chineseText;
        }
        
        /// <summary>
        /// 批量替换场景中的中文文本为英文
        /// </summary>
        [ContextMenu("替换中文文本为英文")]
        public void ReplaceChineseWithEnglish()
        {
            var allTexts = FindObjectsOfType<TextMeshProUGUI>();
            int replacedCount = 0;
            
            foreach (var text in allTexts)
            {
                string originalText = text.text;
                string englishText = GetEnglishAlternative(originalText);
                
                if (originalText != englishText)
                {
                    text.text = englishText;
                    replacedCount++;
                    LogDebug($"🔄 替换文本: '{originalText}' → '{englishText}'");
                }
            }
            
            LogDebug($"✅ 已替换 {replacedCount} 个中文文本为英文");
        }
        
        /// <summary>
        /// 检查当前场景中的字体使用情况
        /// </summary>
        [ContextMenu("检查字体使用情况")]
        public void CheckFontUsage()
        {
            var allTexts = FindObjectsOfType<TextMeshProUGUI>();
            LogDebug($"📊 场景中共有 {allTexts.Length} 个文本组件");
            
            var fontUsage = new System.Collections.Generic.Dictionary<string, int>();
            
            foreach (var text in allTexts)
            {
                string fontName = text.font != null ? text.font.name : "Default";
                if (fontUsage.ContainsKey(fontName))
                {
                    fontUsage[fontName]++;
                }
                else
                {
                    fontUsage[fontName] = 1;
                }
            }
            
            LogDebug("📋 字体使用统计:");
            foreach (var kvp in fontUsage)
            {
                LogDebug($"  {kvp.Key}: {kvp.Value} 个组件");
            }
        }
        
        void LogDebug(string message)
        {
            if (debugMode)
            {
                Debug.Log($"[ChineseFontHelper] {message}");
            }
        }
    }
}
