* [About Cinemachine](index.md)
    * [Using Cinemachine](CinemachineUsing.md)
    * [Using Virtual Cameras](CinemachineSetUpVCam.md)
        * [Setting Virtual Camera properties](CinemachineVirtualCamera.md)
            * [Scene Handles](handles.md)
            * [Body properties](CinemachineVirtualCameraBody.md)
                * [Do Nothing](CinemachineBodyDoNothing.md)
                * [3rd Person Follow](Cinemachine3rdPersonFollow.md)
                * [Framing Transposer](CinemachineBodyFramingTransposer.md)
                * [Hard Lock to Target](CinemachineBodyHardLockTarget.md)
                * [Orbital Transposer](CinemachineBodyOrbitalTransposer.md)
                * [Tracked Dolly](CinemachineBodyTrackedDolly.md)
                * [Transposer](CinemachineBodyTransposer.md)
                  * [Binding Modes](CinemachineBindingModes.md)
            * [Aim properties](CinemachineVirtualCameraAim.md)
                * [Composer](CinemachineAimComposer.md)
                * [Group Composer](CinemachineAimGroupComposer.md)
                * [Do Nothing](CinemachineAimDoNothing.md)
                * [POV](CinemachineAimPOV.md)
                * [Same As Follow Target](CinemachineAimSameAsFollow.md)
                * [Hard Look At](CinemachineAimHardLook.md)
            * [Noise properties](CinemachineVirtualCameraNoise.md)
                * [Working with noise profiles](CinemachineNoiseProfiles.md)
        * [Blending between Virtual Cameras](CinemachineBlending.md)
        * [Managing and grouping Virtual Cameras](CinemachineManagerCameras.md)
            * [Cinemachine Free Look](CinemachineFreeLook.md)
            * [Cinemachine Mixing Camera](CinemachineMixingCamera.md)
            * [Cinemachine Blend List Camera](CinemachineBlendListCamera.md)
            * [Cinemachine Clear Shot](CinemachineClearShot.md)
            * [Cinemachine State-Driven camera](CinemachineStateDrivenCamera.md)
        * [Virtual Camera extensions](CinemachineVirtualCameraExtensions.md)
            * [Extensions for avoiding collisions and evaluating shots](CinemachineColliderConfiner.md)
                * [Cinemachine Collider](CinemachineCollider.md)
                * [Cinemachine Confiner](CinemachineConfiner.md)
                * [Cinemachine Confiner2D](CinemachineConfiner2D.md)
            * [Follow Zoom extension](CinemachineFollowZoom.md)
            * [Pixel Perfect extension](CinemachinePixelPerfect.md)
            * [Post Processing extension](CinemachinePostProcessing.md)
            * [Volume Settings extension](CinemachineVolumeSettings.md)
            * [Storyboard extension](CinemachineStoryboard.md)
            * [3rd Person Aim extension](Cinemachine3rdPersonAim.md)
            * [Recomposer extension](CinemachineRecomposer.md)
        * [Saving in Play Mode](CinemachineSavingDuringPlay.md)
        * [Using dolly paths](CinemachineDolly.md)
            * [Cinemachine Path](CinemachinePath.md)
            * [Cinemachine Smooth Path](CinemachineSmoothPath.md)
            * [Cinemachine Dolly Cart](CinemachineDollyCart.md)
        * [Multiple Unity cameras](CinemachineMultipleCameras.md)
        * [Cinemachine External Camera](CinemachineExternalCamera.md)
        * [Cinemachine Target Group](CinemachineTargetGroup.md)
    * [Cinemachine Brain](CinemachineBrainProperties.md)
    * [Cinemachine and Timeline](CinemachineTimeline.md)
    * [Cinemachine and 2D graphics](Cinemachine2D.md)
    * [Cinemachine and top-down games](CinemachineTopDown.md)
    * [Alternative Input Systems](CinemachineAlternativeInput.md)
    * [Cinemachine Impulse](CinemachineImpulse.md)
        * [Cinemachine Impulse Sources](CinemachineImpulseSourceOverview.md)
            * [Cinemachine Collision Impulse Source](CinemachineCollisionImpulseSource.md)
            * [Cinemachine Impulse Source](CinemachineImpulseSource.md)
        * [Cinemachine Impulse Listener](CinemachineImpulseListener.md)
        * [Filtering impulses](CinemachineImpulseFiltering.md)
