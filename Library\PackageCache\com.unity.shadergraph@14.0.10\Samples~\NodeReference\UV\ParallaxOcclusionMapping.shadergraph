{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "0d9eff7e1d28428a9267619546bf77e5",
    "m_Properties": [],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "099149efbf984ff08ae86792a5942f4c"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "a021cce48c37413b8f47d08ff5ae46e3"
        },
        {
            "m_Id": "8b697e57e5034d4780650c812eb486c2"
        },
        {
            "m_Id": "3a6d4d6d6c4f47d1ac25fe89cf34b46d"
        },
        {
            "m_Id": "6b9cbe08e61e46a59540a4c49febc7dd"
        },
        {
            "m_Id": "b0e3867ac40049f6a0d16826bb821bd2"
        },
        {
            "m_Id": "3c0c651997cb4cd0bbeef8f91a6d1268"
        },
        {
            "m_Id": "8a75fce29b684df18daf01499bb516ab"
        },
        {
            "m_Id": "d7a0a6c34ba848e5a8bcf2996fc0aa99"
        },
        {
            "m_Id": "fef236502329444790440f5f3b5d845d"
        },
        {
            "m_Id": "338b41427e9f48559c7142053d7015d8"
        },
        {
            "m_Id": "97e33ddd70d74caab6c3a9bfdae06a09"
        },
        {
            "m_Id": "2de0a72a07de45ae811ac015ddbd3ac4"
        },
        {
            "m_Id": "4db3408377974cce8f3592335cdc73da"
        },
        {
            "m_Id": "b9b8466dfca1458e94cd44f9b2691ce9"
        },
        {
            "m_Id": "6b9befc4bc9d42d7ac7baaac0de82d9c"
        },
        {
            "m_Id": "1bde5f75b4914da780dc15a5f13ab0d3"
        },
        {
            "m_Id": "00d8acb8503149dc9afd376d13f2a81d"
        },
        {
            "m_Id": "c68a118cc14c400dbcf783225ccf5a8e"
        },
        {
            "m_Id": "adb7e2d5aa8844359372b922a8db4a42"
        },
        {
            "m_Id": "f34d78ac004c45a79c2000593e524200"
        },
        {
            "m_Id": "d3b3d9cc10a14c79932e3e8a69153ce5"
        },
        {
            "m_Id": "23619391d0c14d0d94f93bd964f72290"
        },
        {
            "m_Id": "3d0377c12d5e429381f37cc78c20b9d9"
        },
        {
            "m_Id": "60abbb2ee0b34b4c8f5ea2df5c1007a3"
        },
        {
            "m_Id": "6eedb52f38844fb6bfb4bc75e99d874f"
        },
        {
            "m_Id": "24ba408cebbc4d809a8ce948b6adde83"
        },
        {
            "m_Id": "fa5fd5e854694f6abc192906be373ba1"
        },
        {
            "m_Id": "3d850563b2754a7ca44628be311aee81"
        }
    ],
    "m_GroupDatas": [
        {
            "m_Id": "b07a1e4a2e114b838633e6e111d6677d"
        },
        {
            "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
        }
    ],
    "m_StickyNoteDatas": [
        {
            "m_Id": "d9b9cc9cfad34edf9360533b4cc430c7"
        },
        {
            "m_Id": "920ab3f2858542a0ba493e90ad48d57d"
        },
        {
            "m_Id": "c870da7e1c8c4aee862098c4529a451a"
        },
        {
            "m_Id": "eac8a5b9eeb5479ab960f52a9477a967"
        },
        {
            "m_Id": "cdf69e179deb4bc186dc969174e8a46b"
        },
        {
            "m_Id": "1512a82849114d8187ca81942adca8b3"
        },
        {
            "m_Id": "2c2c397d006c4eb58fd3da5f8080a38f"
        },
        {
            "m_Id": "48e1ea21b4f54daf9d14e5cc0d00048b"
        },
        {
            "m_Id": "ceb3123854e14f75bf77126a04268a11"
        },
        {
            "m_Id": "6b2ed4a99c334fec925efbed4cb783d5"
        },
        {
            "m_Id": "2eb884973d784f32af4770d2676deb1c"
        },
        {
            "m_Id": "95bd39994f2649168620bd7e082ed7de"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "00d8acb8503149dc9afd376d13f2a81d"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "c68a118cc14c400dbcf783225ccf5a8e"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "1bde5f75b4914da780dc15a5f13ab0d3"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "00d8acb8503149dc9afd376d13f2a81d"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "23619391d0c14d0d94f93bd964f72290"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "f34d78ac004c45a79c2000593e524200"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "2de0a72a07de45ae811ac015ddbd3ac4"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "b9b8466dfca1458e94cd44f9b2691ce9"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "2de0a72a07de45ae811ac015ddbd3ac4"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "b9b8466dfca1458e94cd44f9b2691ce9"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "338b41427e9f48559c7142053d7015d8"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "fef236502329444790440f5f3b5d845d"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "3c0c651997cb4cd0bbeef8f91a6d1268"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "8a75fce29b684df18daf01499bb516ab"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "3d0377c12d5e429381f37cc78c20b9d9"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "adb7e2d5aa8844359372b922a8db4a42"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "4db3408377974cce8f3592335cdc73da"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "fef236502329444790440f5f3b5d845d"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "60abbb2ee0b34b4c8f5ea2df5c1007a3"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "23619391d0c14d0d94f93bd964f72290"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "6b9befc4bc9d42d7ac7baaac0de82d9c"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1bde5f75b4914da780dc15a5f13ab0d3"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "6eedb52f38844fb6bfb4bc75e99d874f"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "24ba408cebbc4d809a8ce948b6adde83"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "8a75fce29b684df18daf01499bb516ab"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "6b9cbe08e61e46a59540a4c49febc7dd"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "97e33ddd70d74caab6c3a9bfdae06a09"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "2de0a72a07de45ae811ac015ddbd3ac4"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "adb7e2d5aa8844359372b922a8db4a42"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "f34d78ac004c45a79c2000593e524200"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "b9b8466dfca1458e94cd44f9b2691ce9"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "23619391d0c14d0d94f93bd964f72290"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "c68a118cc14c400dbcf783225ccf5a8e"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "3d0377c12d5e429381f37cc78c20b9d9"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "d3b3d9cc10a14c79932e3e8a69153ce5"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "6eedb52f38844fb6bfb4bc75e99d874f"
                },
                "m_SlotId": 5
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "f34d78ac004c45a79c2000593e524200"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "d3b3d9cc10a14c79932e3e8a69153ce5"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "fef236502329444790440f5f3b5d845d"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "97e33ddd70d74caab6c3a9bfdae06a09"
                },
                "m_SlotId": 0
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": [
            {
                "m_Id": "a021cce48c37413b8f47d08ff5ae46e3"
            },
            {
                "m_Id": "8b697e57e5034d4780650c812eb486c2"
            },
            {
                "m_Id": "3a6d4d6d6c4f47d1ac25fe89cf34b46d"
            }
        ]
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 200.0
        },
        "m_Blocks": [
            {
                "m_Id": "6b9cbe08e61e46a59540a4c49febc7dd"
            },
            {
                "m_Id": "fa5fd5e854694f6abc192906be373ba1"
            },
            {
                "m_Id": "3d850563b2754a7ca44628be311aee81"
            }
        ]
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Shader Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": ""
    },
    "m_ActiveTargets": [
        {
            "m_Id": "416bfeedb8ba416f8c01dd01a413c47f"
        },
        {
            "m_Id": "232238db58314728a5bc68de62a39b61"
        },
        {
            "m_Id": "75140e68b97c43659e2144fc7c29c645"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MultiplyNode",
    "m_ObjectId": "00d8acb8503149dc9afd376d13f2a81d",
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    },
    "m_Name": "Multiply",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1038.5001220703125,
            "y": 512.0000610351563,
            "width": 126.00006103515625,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "fe6ef3931dbc419f906108f48f93c72e"
        },
        {
            "m_Id": "927578b0fccd40b0993e6978c2be7075"
        },
        {
            "m_Id": "c644d5f0280446adb8633920853ce5c6"
        }
    ],
    "synonyms": [
        "multiplication",
        "times",
        "x"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "0460ccd75c414810b11e795b1380886a",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "06c73121a4f34c1a9a41b84112d6b7e4",
    "m_Id": 2,
    "m_DisplayName": "Heightmap",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Heightmap",
    "m_StageCapability": 2,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"ca1502b9036d689468a5686c2adfa47e\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 1
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "084bbe5bc70e49a3bab3cb2a9441e7ac",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "099149efbf984ff08ae86792a5942f4c",
    "m_Name": "",
    "m_ChildObjectList": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TangentMaterialSlot",
    "m_ObjectId": "0999c565f0d64cfca759edd70bcb45d2",
    "m_Id": 0,
    "m_DisplayName": "Tangent",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tangent",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0c9b90d2008e4da898b548547ba3e390",
    "m_Id": 4,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "0d98c87cd13c4150a7e6657ea27c78be",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0ee5327a9c214398b1d7d3fb6a76521e",
    "m_Id": 4,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "0fc941090e5d46a19b38e0c94056c236",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "125018e816944a14914e50f15b7d776e",
    "m_Id": 0,
    "m_DisplayName": "PixelDepthOffset",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "PixelDepthOffset",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "138147e5c5db466e95691690930f7b26",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "1512a82849114d8187ca81942adca8b3",
    "m_Title": "On",
    "m_Content": "",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1546.5001220703125,
        "y": 513.0000610351563,
        "width": 80.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "b07a1e4a2e114b838633e6e111d6677d"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "163756abc0a74c4a87385bb9b18cbfb9",
    "m_Id": 6,
    "m_DisplayName": "UVs",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UVs",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "16846cf907ff4fd890a8d74dcc797cfe",
    "m_Id": 7,
    "m_DisplayName": "LOD",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "LOD",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "17bc3790ee2c42ca93f65c2ad121860b",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.LengthNode",
    "m_ObjectId": "1bde5f75b4914da780dc15a5f13ab0d3",
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    },
    "m_Name": "Length",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1168.5001220703125,
            "y": 512.0000610351563,
            "width": 129.5,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "6400e2d67b584ba49d6bb40ed0befa71"
        },
        {
            "m_Id": "5604794c83e346f9ab0e570c9db0521e"
        }
    ],
    "synonyms": [
        "measure"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "1d8af63151f345299f54709ed75729de",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "1ee963db3ec8474d98745f75cb896dde",
    "m_Id": 2,
    "m_DisplayName": "Heightmap",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Heightmap",
    "m_StageCapability": 2,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"ca1502b9036d689468a5686c2adfa47e\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 1
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "2207f35b030d49149691c2fcb60f62b7",
    "m_Id": 10,
    "m_DisplayName": "Tiling",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tiling",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 1.0,
        "y": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDTarget",
    "m_ObjectId": "232238db58314728a5bc68de62a39b61",
    "m_ActiveSubTarget": {
        "m_Id": "f30a6ae64e51490b962c384a29046026"
    },
    "m_Datas": [
        {
            "m_Id": "4c496decfc9f44f48824b34c3c0c6e3d"
        },
        {
            "m_Id": "61ec53cd96634b0393e37ac141262ef1"
        },
        {
            "m_Id": "7ebffef5fd7c4c329700a9d9a340273f"
        }
    ],
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false,
    "m_SupportLineRendering": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MultiplyNode",
    "m_ObjectId": "23619391d0c14d0d94f93bd964f72290",
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    },
    "m_Name": "Multiply",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -642.0000610351563,
            "y": 688.0000610351563,
            "width": 126.0,
            "height": 118.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "17bc3790ee2c42ca93f65c2ad121860b"
        },
        {
            "m_Id": "5d0bb40a10dd4964a52dfbfef14894cd"
        },
        {
            "m_Id": "138147e5c5db466e95691690930f7b26"
        }
    ],
    "synonyms": [
        "multiplication",
        "times",
        "x"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "2439fccf72de4870811b3528973b83e7",
    "m_Id": 8,
    "m_DisplayName": "LODThreshold",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "LODThreshold",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleTexture2DNode",
    "m_ObjectId": "24ba408cebbc4d809a8ce948b6adde83",
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    },
    "m_Name": "Sample Texture 2D",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -35.500038146972659,
            "y": 622.5000610351563,
            "width": 208.0,
            "height": 338.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "8bb4d0a6afa346b8b6abf89a48b2f318"
        },
        {
            "m_Id": "0ee5327a9c214398b1d7d3fb6a76521e"
        },
        {
            "m_Id": "29a029dda42a4980a4ddc782553a306b"
        },
        {
            "m_Id": "268081a7f17549b6a0289720fa7a47da"
        },
        {
            "m_Id": "e9d6253224514dbe8b425453349c29ea"
        },
        {
            "m_Id": "652a2c09ea02400c903701828d360c7e"
        },
        {
            "m_Id": "e78af1214cd74b49a756756d7004983c"
        },
        {
            "m_Id": "0460ccd75c414810b11e795b1380886a"
        }
    ],
    "synonyms": [
        "tex2d"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_TextureType": 0,
    "m_NormalMapSpace": 0,
    "m_EnableGlobalMipBias": true,
    "m_MipSamplingMode": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "25ace89f7e1843d3a2fec47a50b4a082",
    "m_Id": 0,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "2610f90317414030ab31f6000697839a",
    "m_Id": 3,
    "m_DisplayName": "World Space Position",
    "m_SlotType": 0,
    "m_Hidden": true,
    "m_ShaderOutputName": "WorldSpacePosition",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "268081a7f17549b6a0289720fa7a47da",
    "m_Id": 6,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "286c087377c54ccda2ff987b8184cb38",
    "m_Id": 4,
    "m_DisplayName": "Amplitude",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Amplitude",
    "m_StageCapability": 2,
    "m_Value": 10.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "29a029dda42a4980a4ddc782553a306b",
    "m_Id": 5,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "2c2c397d006c4eb58fd3da5f8080a38f",
    "m_Title": "Off",
    "m_Content": "",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1547.0001220703125,
        "y": 845.5000610351563,
        "width": 80.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "b07a1e4a2e114b838633e6e111d6677d"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.OneMinusNode",
    "m_ObjectId": "2de0a72a07de45ae811ac015ddbd3ac4",
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    },
    "m_Name": "One Minus",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -912.0001220703125,
            "y": 700.0000610351563,
            "width": 127.50006103515625,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "62849573475c403a8b2c87d932926907"
        },
        {
            "m_Id": "57f694a36ef84193b774537d99233579"
        }
    ],
    "synonyms": [
        "complement",
        "invert",
        "opposite"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "2decdc38896245e9b52d0ea05f1a7af5",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "2e67d5ace9994700be96c1bb916217e9",
    "m_Id": 6,
    "m_DisplayName": "UVs",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UVs",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "2eb884973d784f32af4770d2676deb1c",
    "m_Title": "",
    "m_Content": "This value controls the maximum number of steps",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -678.5000610351563,
        "y": 936.5000610351563,
        "width": 80.5,
        "height": 100.00006103515625
    },
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "31a2ee8b2c204283954d986bd4c0da7b",
    "m_Id": 8,
    "m_DisplayName": "LODThreshold",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "LODThreshold",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalVectorNode",
    "m_ObjectId": "338b41427e9f48559c7142053d7015d8",
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    },
    "m_Name": "Normal Vector",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1401.5001220703125,
            "y": 641.5000610351563,
            "width": 206.0,
            "height": 130.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "a293ccf87fb449e8b3e49ba1a631cc4c"
        }
    ],
    "synonyms": [
        "surface direction"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "374584970b8c485ba886bea74e1e1d2c",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "379eec2556924b37bc9ae8a74127bae1",
    "m_Id": 5,
    "m_DisplayName": "Steps",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Steps",
    "m_StageCapability": 2,
    "m_Value": 10.0,
    "m_DefaultValue": 5.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "37c3cca0413346a4b2d3327202328a4d",
    "m_Id": 11,
    "m_DisplayName": "Offset",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Offset",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "3a6d4d6d6c4f47d1ac25fe89cf34b46d",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Tangent",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "0999c565f0d64cfca759edd70bcb45d2"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Tangent"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "3b60ec70868c481ba9e3dbab5a4c9889",
    "m_Id": 11,
    "m_DisplayName": "Offset",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Offset",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ParallaxOcclusionMappingNode",
    "m_ObjectId": "3c0c651997cb4cd0bbeef8f91a6d1268",
    "m_Group": {
        "m_Id": "b07a1e4a2e114b838633e6e111d6677d"
    },
    "m_Name": "Parallax Occlusion Mapping",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -2123.5,
            "y": 505.5000305175781,
            "width": 296.5,
            "height": 327.4999694824219
        }
    },
    "m_Slots": [
        {
            "m_Id": "06c73121a4f34c1a9a41b84112d6b7e4"
        },
        {
            "m_Id": "d739c97c252c4562afd9bdf348b14564"
        },
        {
            "m_Id": "4ab3ae42023d44d18239620a99b8369a"
        },
        {
            "m_Id": "379eec2556924b37bc9ae8a74127bae1"
        },
        {
            "m_Id": "163756abc0a74c4a87385bb9b18cbfb9"
        },
        {
            "m_Id": "2207f35b030d49149691c2fcb60f62b7"
        },
        {
            "m_Id": "3b60ec70868c481ba9e3dbab5a4c9889"
        },
        {
            "m_Id": "c4ac2159e15746a78eea9b4f7d684654"
        },
        {
            "m_Id": "16846cf907ff4fd890a8d74dcc797cfe"
        },
        {
            "m_Id": "31a2ee8b2c204283954d986bd4c0da7b"
        },
        {
            "m_Id": "ff0bd00ad74642aa8a77d2efcda93838"
        },
        {
            "m_Id": "3eebd4c3238a4280907aef58499f120f"
        }
    ],
    "synonyms": [
        "pom"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PowerNode",
    "m_ObjectId": "3d0377c12d5e429381f37cc78c20b9d9",
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    },
    "m_Name": "Power",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -784.5000610351563,
            "y": 512.0000610351563,
            "width": 125.99993896484375,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "1d8af63151f345299f54709ed75729de"
        },
        {
            "m_Id": "6b6e6f3f7a7b4fbb9cab302d752d2542"
        },
        {
            "m_Id": "4f2422d29b434b7f8bcee6a8ea92caeb"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "3d850563b2754a7ca44628be311aee81",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Alpha",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "b0569231d2bc4cf4843f4a8f4068da9d"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Alpha"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "3eebd4c3238a4280907aef58499f120f",
    "m_Id": 1,
    "m_DisplayName": "ParallaxUVs",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "ParallaxUVs",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "3f3ca737cb4547afb44bd9a32655af42",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "411eacc7401247a7ad474704c148ce69",
    "m_Id": 1,
    "m_DisplayName": "ParallaxUVs",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "ParallaxUVs",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInTarget",
    "m_ObjectId": "416bfeedb8ba416f8c01dd01a413c47f",
    "m_ActiveSubTarget": {
        "m_Id": "60b0122596c5458480ae563d3cab7812"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZWriteControl": 0,
    "m_ZTestMode": 4,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CustomEditorGUI": ""
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "41e031c70d03465f93f45b8bbdd876b2",
    "m_Id": 7,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "43504da2245345648f0f7d55ab5cd845",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 1.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "48e1ea21b4f54daf9d14e5cc0d00048b",
    "m_Title": "",
    "m_Content": "This part creates a mask based on the distance from the camera. Fewer samples are needed when we're farther from the surface.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1067.0001220703125,
        "y": 403.0000305175781,
        "width": 162.00006103515626,
        "height": 100.00003051757813
    },
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "4ab3ae42023d44d18239620a99b8369a",
    "m_Id": 4,
    "m_DisplayName": "Amplitude",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Amplitude",
    "m_StageCapability": 2,
    "m_Value": 10.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "4c496decfc9f44f48824b34c3c0c6e3d",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.ViewDirectionNode",
    "m_ObjectId": "4db3408377974cce8f3592335cdc73da",
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    },
    "m_Name": "View Direction",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1401.5001220703125,
            "y": 772.0000610351563,
            "width": 206.0,
            "height": 130.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "f1f49417aee14010809a94106b4e4217"
        }
    ],
    "synonyms": [
        "eye direction"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "4f2422d29b434b7f8bcee6a8ea92caeb",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "5604794c83e346f9ab0e570c9db0521e",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "5609bf59fd694de6b7563a753a2e0e0a",
    "m_Id": 12,
    "m_DisplayName": "PrimitiveSize",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "PrimitiveSize",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 1.0,
        "y": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "57f694a36ef84193b774537d99233579",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "5958533429e34880b905fe2440851eb8",
    "m_Id": 2,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "5d0bb40a10dd4964a52dfbfef14894cd",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 16.0,
        "e01": 2.0,
        "e02": 2.0,
        "e03": 2.0,
        "e10": 2.0,
        "e11": 2.0,
        "e12": 2.0,
        "e13": 2.0,
        "e20": 2.0,
        "e21": 2.0,
        "e22": 2.0,
        "e23": 2.0,
        "e30": 2.0,
        "e31": 2.0,
        "e32": 2.0,
        "e33": 2.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "5fed9643ded04f8492be19ab7e7c0160",
    "m_Id": 2,
    "m_DisplayName": "Heightmap",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Heightmap",
    "m_StageCapability": 2,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"instanceID\":0}}",
        "m_Guid": ""
    },
    "m_DefaultType": 1
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.IntegerNode",
    "m_ObjectId": "60abbb2ee0b34b4c8f5ea2df5c1007a3",
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    },
    "m_Name": "Integer",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -842.5000610351563,
            "y": 929.0000610351563,
            "width": 130.0,
            "height": 112.50006103515625
        }
    },
    "m_Slots": [
        {
            "m_Id": "e904ce350aee43e084c560042e468460"
        }
    ],
    "synonyms": [
        "whole number"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": 32
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInUnlitSubTarget",
    "m_ObjectId": "60b0122596c5458480ae563d3cab7812"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "61ec53cd96634b0393e37ac141262ef1",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "62849573475c403a8b2c87d932926907",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalUnlitSubTarget",
    "m_ObjectId": "62dda3666ec248d3b266b71e847d8a09"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "6400e2d67b584ba49d6bb40ed0befa71",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "652a2c09ea02400c903701828d360c7e",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"583384ba064432b41891bec94e35c8af\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "659788bd48824f33ba377ab95ca6e238",
    "m_Id": 4,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "699942f4f3de4906881f4155becf2128",
    "m_Id": 4,
    "m_DisplayName": "Amplitude",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Amplitude",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "6b2ed4a99c334fec925efbed4cb783d5",
    "m_Title": "",
    "m_Content": "Reducing the number of Steps is critical to getting good performance with Parallax Occlusion mapping.  This set of nodes reduces Step count based on the camera angle and distance - so you get a high Step count where you need it, but it gets reduced when it's not needed. It can give you a massive performance improvement over just entering a static value for the Step count. You may need to tune it to fit your specific application.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -364.5000305175781,
        "y": 420.0000305175781,
        "width": 478.5000305175781,
        "height": 100.00003051757813
    },
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "6b6e6f3f7a7b4fbb9cab302d752d2542",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.44999998807907107,
        "y": 2.0,
        "z": 2.0,
        "w": 2.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ViewVectorNode",
    "m_ObjectId": "6b9befc4bc9d42d7ac7baaac0de82d9c",
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    },
    "m_Name": "View Vector",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1374.5001220703125,
            "y": 512.0000610351563,
            "width": 206.0,
            "height": 130.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "2610f90317414030ab31f6000697839a"
        },
        {
            "m_Id": "c8416abe614449c7b759452be01c568c"
        }
    ],
    "synonyms": [
        "eye vector"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "6b9cbe08e61e46a59540a4c49febc7dd",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.BaseColor",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "bda43ea9df5b4af3a30d4a9cdb651211"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.BaseColor"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ParallaxOcclusionMappingNode",
    "m_ObjectId": "6eedb52f38844fb6bfb4bc75e99d874f",
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    },
    "m_Name": "Parallax Occlusion Mapping",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -238.00001525878907,
            "y": 622.5000610351563,
            "width": 202.49996948242188,
            "height": 111.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "1ee963db3ec8474d98745f75cb896dde"
        },
        {
            "m_Id": "ef37850590a142189db7bb64cf0c23e4"
        },
        {
            "m_Id": "286c087377c54ccda2ff987b8184cb38"
        },
        {
            "m_Id": "7d66796de76c47c2a780472b1214f119"
        },
        {
            "m_Id": "86522e58089b4f22b8aa159ea1cf3d60"
        },
        {
            "m_Id": "9f969d8fc8cd4651aeb946c091e0ef7e"
        },
        {
            "m_Id": "37c3cca0413346a4b2d3327202328a4d"
        },
        {
            "m_Id": "5609bf59fd694de6b7563a753a2e0e0a"
        },
        {
            "m_Id": "b2a3a8808b434e2cb1cde0310fe18f6b"
        },
        {
            "m_Id": "2439fccf72de4870811b3528973b83e7"
        },
        {
            "m_Id": "c6f333e8c3f4441ab2f3911d0f495650"
        },
        {
            "m_Id": "c71fd2b0ca4b487a952eef7c67355d8d"
        }
    ],
    "synonyms": [
        "pom"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Channel": 0
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget",
    "m_ObjectId": "75140e68b97c43659e2144fc7c29c645",
    "m_Datas": [],
    "m_ActiveSubTarget": {
        "m_Id": "62dda3666ec248d3b266b71e847d8a09"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZTestMode": 4,
    "m_ZWriteControl": 0,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CastShadows": true,
    "m_ReceiveShadows": true,
    "m_SupportsLODCrossFade": false,
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "76d7eb0fd1a04d5a9105855f41cc274d",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "78f4b536729f4c68931a82011a938553",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"583384ba064432b41891bec94e35c8af\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "7c344e203331401c8d9356bfd0de1665",
    "m_Id": 8,
    "m_DisplayName": "LODThreshold",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "LODThreshold",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "7d66796de76c47c2a780472b1214f119",
    "m_Id": 5,
    "m_DisplayName": "Steps",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Steps",
    "m_StageCapability": 2,
    "m_Value": 10.0,
    "m_DefaultValue": 5.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "7e0d9085f9854d31917ab50b9f939024",
    "m_Id": 3,
    "m_DisplayName": "HeightmapSampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "HeightmapSampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "7e1512c626554e9490e79754e9720f64",
    "m_Id": 5,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "7ebffef5fd7c4c329700a9d9a340273f",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "81c53980e9f243218259e8c6d312c15c",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "82acb1e7dd9d403e8c9992a539075c9f",
    "m_Id": 10,
    "m_DisplayName": "Tiling",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tiling",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 1.0,
        "y": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "836e25f96dae43bfb9476f29c2c9e745",
    "m_Id": 5,
    "m_DisplayName": "Steps",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Steps",
    "m_StageCapability": 2,
    "m_Value": 5.0,
    "m_DefaultValue": 5.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "86522e58089b4f22b8aa159ea1cf3d60",
    "m_Id": 6,
    "m_DisplayName": "UVs",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UVs",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "872e51eb550040299902be48c25f0d83",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleTexture2DNode",
    "m_ObjectId": "8a75fce29b684df18daf01499bb516ab",
    "m_Group": {
        "m_Id": "b07a1e4a2e114b838633e6e111d6677d"
    },
    "m_Name": "Sample Texture 2D",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1768.0,
            "y": 505.5000305175781,
            "width": 208.0,
            "height": 338.0000305175781
        }
    },
    "m_Slots": [
        {
            "m_Id": "25ace89f7e1843d3a2fec47a50b4a082"
        },
        {
            "m_Id": "659788bd48824f33ba377ab95ca6e238"
        },
        {
            "m_Id": "dae0613399d740549219f525a9070801"
        },
        {
            "m_Id": "db560d60b970412e9a978f3f482b5194"
        },
        {
            "m_Id": "e0a7a1b934074341a894ebee42a1520a"
        },
        {
            "m_Id": "78f4b536729f4c68931a82011a938553"
        },
        {
            "m_Id": "ddaf7f48e5d84d468acb480e195bcd54"
        },
        {
            "m_Id": "81c53980e9f243218259e8c6d312c15c"
        }
    ],
    "synonyms": [
        "tex2d"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_TextureType": 0,
    "m_NormalMapSpace": 0,
    "m_EnableGlobalMipBias": true,
    "m_MipSamplingMode": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "8b697e57e5034d4780650c812eb486c2",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Normal",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "9be01698dee44d0486960c69de3c502c"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Normal"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "8bb4d0a6afa346b8b6abf89a48b2f318",
    "m_Id": 0,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "8cd0ea26178a412fa04c62739cbb9011",
    "m_Id": 6,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "920ab3f2858542a0ba493e90ad48d57d",
    "m_Title": "",
    "m_Content": "The heightmap is a black and white image that defines the depth of the texture. Tall areas are white and low areas are black. Gradient images without hard edges between black and white work best.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -2573.000244140625,
        "y": 438.0000305175781,
        "width": 294.0,
        "height": 100.00003051757813
    },
    "m_Group": {
        "m_Id": "b07a1e4a2e114b838633e6e111d6677d"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "927578b0fccd40b0993e6978c2be7075",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.09000000357627869,
        "e01": 2.0,
        "e02": 2.0,
        "e03": 2.0,
        "e10": 2.0,
        "e11": 2.0,
        "e12": 2.0,
        "e13": 2.0,
        "e20": 2.0,
        "e21": 2.0,
        "e22": 2.0,
        "e23": 2.0,
        "e30": 2.0,
        "e31": 2.0,
        "e32": 2.0,
        "e33": 2.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "95bd39994f2649168620bd7e082ed7de",
    "m_Title": "",
    "m_Content": "The output UVs should be used for all of the textures in the material - the color map, the normal map, the mask map, etc.\n\nDon't use multiple Parallax Occlusion Mapping nodes in a shader.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1919.0001220703125,
        "y": 400.0000305175781,
        "width": 243.0,
        "height": 102.00003051757813
    },
    "m_Group": {
        "m_Id": "b07a1e4a2e114b838633e6e111d6677d"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SaturateNode",
    "m_ObjectId": "97e33ddd70d74caab6c3a9bfdae06a09",
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    },
    "m_Name": "Saturate",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1039.5001220703125,
            "y": 700.0000610351563,
            "width": 127.5,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "cde32115d6074331901b1883046a7f0b"
        },
        {
            "m_Id": "084bbe5bc70e49a3bab3cb2a9441e7ac"
        }
    ],
    "synonyms": [
        "clamp"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "9be01698dee44d0486960c69de3c502c",
    "m_Id": 0,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "9f969d8fc8cd4651aeb946c091e0ef7e",
    "m_Id": 10,
    "m_DisplayName": "Tiling",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tiling",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 1.0,
        "y": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "a021cce48c37413b8f47d08ff5ae46e3",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "872e51eb550040299902be48c25f0d83"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Position"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "a293ccf87fb449e8b3e49ba1a631cc4c",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.OneMinusNode",
    "m_ObjectId": "adb7e2d5aa8844359372b922a8db4a42",
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    },
    "m_Name": "One Minus",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -658.5001220703125,
            "y": 512.0000610351563,
            "width": 127.50006103515625,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "0fc941090e5d46a19b38e0c94056c236"
        },
        {
            "m_Id": "0d98c87cd13c4150a7e6657ea27c78be"
        }
    ],
    "synonyms": [
        "complement",
        "invert",
        "opposite"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "ae31bf7ed4054ba2a1a588c3cf049b0b",
    "m_Id": 0,
    "m_DisplayName": "Emission",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Emission",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 1,
    "m_DefaultColor": {
        "r": 0.0,
        "g": 0.0,
        "b": 0.0,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "af2dc7db8c794ad6bf1d6c1415c75a33",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b0569231d2bc4cf4843f4a8f4068da9d",
    "m_Id": 0,
    "m_DisplayName": "Alpha",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Alpha",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "b07a1e4a2e114b838633e6e111d6677d",
    "m_Title": "The Basics",
    "m_Position": {
        "x": -2598.0,
        "y": 341.50006103515627
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ParallaxOcclusionMappingNode",
    "m_ObjectId": "b0e3867ac40049f6a0d16826bb821bd2",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Parallax Occlusion Mapping",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1295.0001220703125,
            "y": -39.00001907348633,
            "width": 296.50018310546877,
            "height": 327.50006103515627
        }
    },
    "m_Slots": [
        {
            "m_Id": "5fed9643ded04f8492be19ab7e7c0160"
        },
        {
            "m_Id": "7e0d9085f9854d31917ab50b9f939024"
        },
        {
            "m_Id": "699942f4f3de4906881f4155becf2128"
        },
        {
            "m_Id": "836e25f96dae43bfb9476f29c2c9e745"
        },
        {
            "m_Id": "2e67d5ace9994700be96c1bb916217e9"
        },
        {
            "m_Id": "82acb1e7dd9d403e8c9992a539075c9f"
        },
        {
            "m_Id": "d8bc484c380c4d709aa46188e3ed4438"
        },
        {
            "m_Id": "e56a65482eb44c11b0e91b4b33191f32"
        },
        {
            "m_Id": "b27aeb7d756a4f05b0ff0507cafc7b8d"
        },
        {
            "m_Id": "7c344e203331401c8d9356bfd0de1665"
        },
        {
            "m_Id": "125018e816944a14914e50f15b7d776e"
        },
        {
            "m_Id": "411eacc7401247a7ad474704c148ce69"
        }
    ],
    "synonyms": [
        "pom"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b27aeb7d756a4f05b0ff0507cafc7b8d",
    "m_Id": 7,
    "m_DisplayName": "LOD",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "LOD",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b2a3a8808b434e2cb1cde0310fe18f6b",
    "m_Id": 7,
    "m_DisplayName": "LOD",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "LOD",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "b2d61980a2ac4239bfbd836ee5799324",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "b3db345e6d8348288b344d5e0830ff2b",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MultiplyNode",
    "m_ObjectId": "b9b8466dfca1458e94cd44f9b2691ce9",
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    },
    "m_Name": "Multiply",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -768.0001220703125,
            "y": 688.0000610351563,
            "width": 126.00006103515625,
            "height": 118.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "2decdc38896245e9b52d0ea05f1a7af5"
        },
        {
            "m_Id": "fd6b7e7509a9422588c89736f4c09d5d"
        },
        {
            "m_Id": "3f3ca737cb4547afb44bd9a32655af42"
        }
    ],
    "synonyms": [
        "multiplication",
        "times",
        "x"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "bda43ea9df5b4af3a30d4a9cdb651211",
    "m_Id": 0,
    "m_DisplayName": "Base Color",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BaseColor",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.5,
        "y": 0.5,
        "z": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.5,
        "g": 0.5,
        "b": 0.5,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "c49fa53b8bc14943bfc5f7015364cfe9",
    "m_Id": 0,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "c4ac2159e15746a78eea9b4f7d684654",
    "m_Id": 12,
    "m_DisplayName": "PrimitiveSize",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "PrimitiveSize",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 1.0,
        "y": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "c644d5f0280446adb8633920853ce5c6",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SaturateNode",
    "m_ObjectId": "c68a118cc14c400dbcf783225ccf5a8e",
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    },
    "m_Name": "Saturate",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -913.0000610351563,
            "y": 512.0000610351563,
            "width": 127.49993896484375,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "76d7eb0fd1a04d5a9105855f41cc274d"
        },
        {
            "m_Id": "374584970b8c485ba886bea74e1e1d2c"
        }
    ],
    "synonyms": [
        "clamp"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "c6f333e8c3f4441ab2f3911d0f495650",
    "m_Id": 0,
    "m_DisplayName": "PixelDepthOffset",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "PixelDepthOffset",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "c71fd2b0ca4b487a952eef7c67355d8d",
    "m_Id": 1,
    "m_DisplayName": "ParallaxUVs",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "ParallaxUVs",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "c7960aa1a5d14b8580d2a4824aa92e9a",
    "m_Title": "Steps Optimization",
    "m_Position": {
        "x": -1426.5001220703125,
        "y": 344.5000305175781
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "c8416abe614449c7b759452be01c568c",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "c870da7e1c8c4aee862098c4529a451a",
    "m_Title": "",
    "m_Content": "Amplitude controls the maximum height range of the effect. Values that are too high tend to pull the effect apart and break the illusion.  Values that are too low aren't visible, so finding the right balance is important.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -2572.500244140625,
        "y": 543.5000610351563,
        "width": 294.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "b07a1e4a2e114b838633e6e111d6677d"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "cac2e041a8af40f79cc4136c70d0d68e",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "cbbbb686871546c1bf025c876bd42637",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "cde32115d6074331901b1883046a7f0b",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "cdf69e179deb4bc186dc969174e8a46b",
    "m_Title": "",
    "m_Content": "It's hard to see the effect in these still previews, but you can see it in the Main Preview window. If you rotate the preview sphere around you'll see the cobblestones bumping out as the point of view changes.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1976.0001220703125,
        "y": 848.5000610351563,
        "width": 200.0,
        "height": 129.5
    },
    "m_Group": {
        "m_Id": "b07a1e4a2e114b838633e6e111d6677d"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "ceb3123854e14f75bf77126a04268a11",
    "m_Title": "",
    "m_Content": "This part creates a mask based on the angle of the camera with the surface. When the surface is parallel to the camera, we need the maximum number of samples, but when it's perpendicular, we can reduce the samples a lot.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1148.5001220703125,
        "y": 838.5000610351563,
        "width": 199.50006103515626,
        "height": 117.0
    },
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CeilingNode",
    "m_ObjectId": "d3b3d9cc10a14c79932e3e8a69153ce5",
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    },
    "m_Name": "Ceiling",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -365.50006103515627,
            "y": 622.5000610351563,
            "width": 127.50004577636719,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "b3db345e6d8348288b344d5e0830ff2b"
        },
        {
            "m_Id": "cbbbb686871546c1bf025c876bd42637"
        }
    ],
    "synonyms": [
        "up"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "d739c97c252c4562afd9bdf348b14564",
    "m_Id": 3,
    "m_DisplayName": "HeightmapSampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "HeightmapSampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleTexture2DNode",
    "m_ObjectId": "d7a0a6c34ba848e5a8bcf2996fc0aa99",
    "m_Group": {
        "m_Id": "b07a1e4a2e114b838633e6e111d6677d"
    },
    "m_Name": "Sample Texture 2D",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1768.0,
            "y": 843.5000610351563,
            "width": 208.0,
            "height": 305.00006103515627
        }
    },
    "m_Slots": [
        {
            "m_Id": "c49fa53b8bc14943bfc5f7015364cfe9"
        },
        {
            "m_Id": "0c9b90d2008e4da898b548547ba3e390"
        },
        {
            "m_Id": "7e1512c626554e9490e79754e9720f64"
        },
        {
            "m_Id": "8cd0ea26178a412fa04c62739cbb9011"
        },
        {
            "m_Id": "41e031c70d03465f93f45b8bbdd876b2"
        },
        {
            "m_Id": "e76fa33f8ac8451a8c025e42088543d1"
        },
        {
            "m_Id": "5958533429e34880b905fe2440851eb8"
        },
        {
            "m_Id": "af2dc7db8c794ad6bf1d6c1415c75a33"
        }
    ],
    "synonyms": [
        "tex2d"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_TextureType": 0,
    "m_NormalMapSpace": 0,
    "m_EnableGlobalMipBias": true,
    "m_MipSamplingMode": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "d82ede983fe148b280019862a5aca3e9",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 2.0,
        "e01": 2.0,
        "e02": 2.0,
        "e03": 2.0,
        "e10": 2.0,
        "e11": 2.0,
        "e12": 2.0,
        "e13": 2.0,
        "e20": 2.0,
        "e21": 2.0,
        "e22": 2.0,
        "e23": 2.0,
        "e30": 2.0,
        "e31": 2.0,
        "e32": 2.0,
        "e33": 2.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "d8bc484c380c4d709aa46188e3ed4438",
    "m_Id": 11,
    "m_DisplayName": "Offset",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Offset",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "d9b9cc9cfad34edf9360533b4cc430c7",
    "m_Title": "Parallax Occlusion Mapping",
    "m_Content": "The Parallax Occlusion Mapping Node creates a parallax effect that displaces a material's UVs and depth to create the illusion of depth.\n\nThis effect requires that the heightmap texture be sampled many times (controlled by the Steps input) and is therefore significantly more expensive than standard normal mapping or Parallax Mapping.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -971.5000610351563,
        "y": -32.500003814697269,
        "width": 284.0,
        "height": 160.00001525878907
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "dae0613399d740549219f525a9070801",
    "m_Id": 5,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "db560d60b970412e9a978f3f482b5194",
    "m_Id": 6,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "ddaf7f48e5d84d468acb480e195bcd54",
    "m_Id": 2,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e0a7a1b934074341a894ebee42a1520a",
    "m_Id": 7,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "e56a65482eb44c11b0e91b4b33191f32",
    "m_Id": 12,
    "m_DisplayName": "PrimitiveSize",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "PrimitiveSize",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 1.0,
        "y": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "e76fa33f8ac8451a8c025e42088543d1",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"583384ba064432b41891bec94e35c8af\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "e78af1214cd74b49a756756d7004983c",
    "m_Id": 2,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e904ce350aee43e084c560042e468460",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e9d6253224514dbe8b425453349c29ea",
    "m_Id": 7,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "eac8a5b9eeb5479ab960f52a9477a967",
    "m_Title": "",
    "m_Content": "Steps controls the number of times the height map texture will be sampled in order to create the effect.  Keeping this value low can vastly improve performance, but also reduces the quality of the output. See the section on Optimization for an idea on how to control this.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -2571.500244140625,
        "y": 647.5000610351563,
        "width": 295.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "b07a1e4a2e114b838633e6e111d6677d"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "ef37850590a142189db7bb64cf0c23e4",
    "m_Id": 3,
    "m_DisplayName": "HeightmapSampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "HeightmapSampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "f1f49417aee14010809a94106b4e4217",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "f30a6ae64e51490b962c384a29046026"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MultiplyNode",
    "m_ObjectId": "f34d78ac004c45a79c2000593e524200",
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    },
    "m_Name": "Multiply",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -492.0000305175781,
            "y": 622.5000610351563,
            "width": 125.99996948242188,
            "height": 118.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "b2d61980a2ac4239bfbd836ee5799324"
        },
        {
            "m_Id": "d82ede983fe148b280019862a5aca3e9"
        },
        {
            "m_Id": "cac2e041a8af40f79cc4136c70d0d68e"
        }
    ],
    "synonyms": [
        "multiplication",
        "times",
        "x"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "f391d1ea9eb54679a45e417c69d89125",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "f7a37c35dbe442cca6fd8ca1a5c0f1f1",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "fa5fd5e854694f6abc192906be373ba1",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Emission",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "ae31bf7ed4054ba2a1a588c3cf049b0b"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Emission"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "fd6b7e7509a9422588c89736f4c09d5d",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 2.0,
        "e01": 2.0,
        "e02": 2.0,
        "e03": 2.0,
        "e10": 2.0,
        "e11": 2.0,
        "e12": 2.0,
        "e13": 2.0,
        "e20": 2.0,
        "e21": 2.0,
        "e22": 2.0,
        "e23": 2.0,
        "e30": 2.0,
        "e31": 2.0,
        "e32": 2.0,
        "e33": 2.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "fe6ef3931dbc419f906108f48f93c72e",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DotProductNode",
    "m_ObjectId": "fef236502329444790440f5f3b5d845d",
    "m_Group": {
        "m_Id": "c7960aa1a5d14b8580d2a4824aa92e9a"
    },
    "m_Name": "Dot Product",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1166.5001220703125,
            "y": 700.0000610351563,
            "width": 127.5,
            "height": 118.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "f7a37c35dbe442cca6fd8ca1a5c0f1f1"
        },
        {
            "m_Id": "43504da2245345648f0f7d55ab5cd845"
        },
        {
            "m_Id": "f391d1ea9eb54679a45e417c69d89125"
        }
    ],
    "synonyms": [
        "scalar product"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ff0bd00ad74642aa8a77d2efcda93838",
    "m_Id": 0,
    "m_DisplayName": "PixelDepthOffset",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "PixelDepthOffset",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

