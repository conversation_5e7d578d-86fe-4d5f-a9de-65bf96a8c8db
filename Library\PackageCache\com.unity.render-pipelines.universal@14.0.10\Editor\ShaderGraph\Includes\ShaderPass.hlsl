#ifndef UNIVERSAL_SHADERPASS_INCLUDED
#define UNIVERSAL_SHADERPASS_INCLUDED

#define SHADERPASS_FORWARD (0)
#define SHADERPASS_GBUFFER (1)
#define SHADERPASS_DEPTHONLY (2)
#define SHADERPASS_SHADOWCASTER (3)
#define SHADERPASS_META (4)
#define SHADERPASS_2D (5)
#define SHADERPASS_UNLIT (6)
#define SHADERPASS_SPRITELIT (7)
#define SHADERPASS_SPRITENORMAL (8)
#define SHADERPASS_SPRITEFORWARD (9)
#define SHADERPASS_SPRITEUNLIT (10)
#define SHADERPASS_DEPTHNORMALSONLY (11)
#define SHADERPASS_DBUFFER_PROJECTOR (12)
#define SHADERPASS_DBUFFER_MESH (13)
#define SHADERPASS_FORWARD_EMISSIVE_PROJECTOR (14)
#define SHADERPASS_FORWARD_EMISSIVE_MESH (15)
#define SHADERPASS_FORWARD_PREVIEW (16)
#define SHADERPASS_DECAL_SCREEN_SPACE_PROJECTOR (17)
#define SHADERPASS_DECAL_SCREEN_SPACE_MESH (18)
#define SHADERPASS_DECAL_GBUFFER_PROJECTOR (19)
#define SHADERPASS_DECAL_GBUFFER_MESH (20)
#define SHADERPASS_DEPTHNORMALS (21)
#endif
