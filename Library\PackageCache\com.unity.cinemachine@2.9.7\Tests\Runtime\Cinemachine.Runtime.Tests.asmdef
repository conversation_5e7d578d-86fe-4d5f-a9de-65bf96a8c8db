{"name": "Cinemachine.Runtime.Tests", "rootNamespace": "", "references": ["Cinemachine", "UnityEngine.TestRunner", "UnityEditor.TestRunner", "Unity.RenderPipelines.HighDefinition.Runtime"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": true, "precompiledReferences": ["nunit.framework.dll"], "autoReferenced": false, "defineConstraints": ["UNITY_INCLUDE_TESTS"], "versionDefines": [{"name": "com.unity.modules.physics2d", "expression": "1.0.0", "define": "CINEMACHINE_PHYSICS_2D"}, {"name": "com.unity.modules.physics", "expression": "1.0.0", "define": "CINEMACHINE_PHYSICS"}, {"name": "com.unity.modules.animation", "expression": "1.0.0", "define": "CINEMACHINE_UNITY_ANIMATION"}, {"name": "com.unity.render-pipelines.high-definition-config", "expression": "0", "define": "CINEMACHINE_HDRP"}], "noEngineReferences": false}