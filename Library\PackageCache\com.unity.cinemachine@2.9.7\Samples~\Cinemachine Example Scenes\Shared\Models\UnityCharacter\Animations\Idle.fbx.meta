fileFormatVersion: 2
guid: 080945c46afd34f3ebc364c1696e6678
timeCreated: **********
licenseType: Pro
ModelImporter:
  serializedVersion: 21
  fileIDToRecycleName:
    100000: Chest
    100002: Head
    100004: Hips
    100006: //RootNode
    100008: Jaw
    100010: <PERSON><PERSON><PERSON>D
    100012: LeftArm
    100014: LeftCheek
    100016: Left<PERSON>ye
    100018: LeftEyelidLower
    100020: LeftEyelidUpper
    100022: LeftFoot
    100024: LeftForeArm
    100026: LeftHand
    100028: LeftHandIndex1
    100030: LeftHandIndex2
    100032: LeftHandIndex3
    100034: LeftHandMiddle1
    100036: LeftHandMiddle2
    100038: LeftHandMiddle3
    100040: LeftHandPinky1
    100042: LeftHandPinky2
    100044: LeftHandPinky3
    100046: LeftHandRing1
    100048: LeftHandRing2
    100050: LeftHandRing3
    100052: LeftHandThumb1
    100054: LeftHandThumb2
    100056: LeftHandThumb3
    100058: <PERSON><PERSON><PERSON><PERSON><PERSON>
    100060: LeftIOuterBrow
    100062: LeftLeg
    100064: <PERSON><PERSON><PERSON><PERSON>orn<PERSON>
    100066: LeftLipLower
    100068: Left<PERSON>ipUpper
    100070: LeftNostril
    100072: LeftShoulder
    100074: LeftToes
    100076: LeftUpLeg
    100078: Neck
    100080: Pivot
    100082: Reference
    100084: RightArm
    100086: RightCheek
    100088: RightEye
    100090: RightEyelidLower
    100092: RightEyelidUpper
    100094: RightFoot
    100096: RightForeArm
    100098: RightHand
    100100: RightHandIndex1
    100102: RightHandIndex2
    100104: RightHandIndex3
    100106: RightHandMiddle1
    100108: RightHandMiddle2
    100110: RightHandMiddle3
    100112: RightHandPinky1
    100114: RightHandPinky2
    100116: RightHandPinky3
    100118: RightHandRing1
    100120: RightHandRing2
    100122: RightHandRing3
    100124: RightHandThumb1
    100126: RightHandThumb2
    100128: RightHandThumb3
    100130: RightInnerBrow
    100132: RightIOuterBrow
    100134: RightLeg
    100136: RightLipCorner
    100138: RightLipLower
    100140: RightLipUpper
    100142: RightNostril
    100144: RightShoulder
    100146: RightToes
    100148: RightUpLeg
    100150: Root
    100152: Spine
    100154: TongueBack
    100156: TongueTip
    400000: Chest
    400002: Head
    400004: Hips
    400006: //RootNode
    400008: Jaw
    400010: JawEND
    400012: LeftArm
    400014: LeftCheek
    400016: LeftEye
    400018: LeftEyelidLower
    400020: LeftEyelidUpper
    400022: LeftFoot
    400024: LeftForeArm
    400026: LeftHand
    400028: LeftHandIndex1
    400030: LeftHandIndex2
    400032: LeftHandIndex3
    400034: LeftHandMiddle1
    400036: LeftHandMiddle2
    400038: LeftHandMiddle3
    400040: LeftHandPinky1
    400042: LeftHandPinky2
    400044: LeftHandPinky3
    400046: LeftHandRing1
    400048: LeftHandRing2
    400050: LeftHandRing3
    400052: LeftHandThumb1
    400054: LeftHandThumb2
    400056: LeftHandThumb3
    400058: LeftInnerBrow
    400060: LeftIOuterBrow
    400062: LeftLeg
    400064: LeftLipCorner
    400066: LeftLipLower
    400068: LeftLipUpper
    400070: LeftNostril
    400072: LeftShoulder
    400074: LeftToes
    400076: LeftUpLeg
    400078: Neck
    400080: Pivot
    400082: Reference
    400084: RightArm
    400086: RightCheek
    400088: RightEye
    400090: RightEyelidLower
    400092: RightEyelidUpper
    400094: RightFoot
    400096: RightForeArm
    400098: RightHand
    400100: RightHandIndex1
    400102: RightHandIndex2
    400104: RightHandIndex3
    400106: RightHandMiddle1
    400108: RightHandMiddle2
    400110: RightHandMiddle3
    400112: RightHandPinky1
    400114: RightHandPinky2
    400116: RightHandPinky3
    400118: RightHandRing1
    400120: RightHandRing2
    400122: RightHandRing3
    400124: RightHandThumb1
    400126: RightHandThumb2
    400128: RightHandThumb3
    400130: RightInnerBrow
    400132: RightIOuterBrow
    400134: RightLeg
    400136: RightLipCorner
    400138: RightLipLower
    400140: RightLipUpper
    400142: RightNostril
    400144: RightShoulder
    400146: RightToes
    400148: RightUpLeg
    400150: Root
    400152: Spine
    400154: TongueBack
    400156: TongueTip
    7400000: Stance
    7400002: Idle
    7400004: Idle_A
    7400006: Idle_B
    7400008: Idle_C
    7400010: Idle_D
    9500000: //RootNode
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    animationCompression: 0
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: Stance
      takeName: Stance
      firstFrame: 0
      lastFrame: 30
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Reference
        weight: 1
      - path: Reference/Pivot
        weight: 1
      - path: Reference/Pivot/Root
        weight: 1
      - path: Reference/Pivot/Root/Hips
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg/LeftLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg/LeftLeg/LeftFoot
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg/LeftLeg/LeftFoot/LeftToes
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg/RightLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg/RightLeg/RightFoot
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg/RightLeg/RightFoot/RightToes
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2/LeftHandIndex3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2/LeftHandMiddle3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2/LeftHandPinky3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2/LeftHandRing3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2/LeftHandThumb3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/JawEND
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/LeftLipCorner
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/LeftLipLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/RightLipCorner
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/RightLipLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/TongueBack
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/TongueTip
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftCheek
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftEye
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftEyelidLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftEyelidUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftInnerBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftIOuterBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftLipUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftNostril
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightCheek
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightEye
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightEyelidLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightEyelidUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightInnerBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightIOuterBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightLipUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightNostril
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2/RightHandIndex3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2/RightHandMiddle3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2/RightHandPinky3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2/RightHandRing3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2/RightHandThumb3
        weight: 1
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Idle
      takeName: Idle
      firstFrame: 50
      lastFrame: 3275
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Reference
        weight: 1
      - path: Reference/Pivot
        weight: 1
      - path: Reference/Pivot/Root
        weight: 1
      - path: Reference/Pivot/Root/Hips
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg/LeftLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg/LeftLeg/LeftFoot
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg/LeftLeg/LeftFoot/LeftToes
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg/RightLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg/RightLeg/RightFoot
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg/RightLeg/RightFoot/RightToes
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2/LeftHandIndex3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2/LeftHandMiddle3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2/LeftHandPinky3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2/LeftHandRing3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2/LeftHandThumb3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/JawEND
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/LeftLipCorner
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/LeftLipLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/RightLipCorner
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/RightLipLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/TongueBack
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/TongueTip
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftCheek
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftEye
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftEyelidLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftEyelidUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftInnerBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftIOuterBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftLipUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftNostril
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightCheek
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightEye
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightEyelidLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightEyelidUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightInnerBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightIOuterBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightLipUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightNostril
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2/RightHandIndex3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2/RightHandMiddle3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2/RightHandPinky3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2/RightHandRing3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2/RightHandThumb3
        weight: 1
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Idle_A
      takeName: Idle
      firstFrame: 157
      lastFrame: 228
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 0
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Reference
        weight: 1
      - path: Reference/Pivot
        weight: 1
      - path: Reference/Pivot/Root
        weight: 1
      - path: Reference/Pivot/Root/Hips
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg/LeftLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg/LeftLeg/LeftFoot
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg/LeftLeg/LeftFoot/LeftToes
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg/RightLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg/RightLeg/RightFoot
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg/RightLeg/RightFoot/RightToes
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2/LeftHandIndex3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2/LeftHandMiddle3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2/LeftHandPinky3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2/LeftHandRing3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2/LeftHandThumb3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/JawEND
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/LeftLipCorner
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/LeftLipLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/RightLipCorner
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/RightLipLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/TongueBack
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/TongueTip
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftCheek
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftEye
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftEyelidLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftEyelidUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftInnerBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftIOuterBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftLipUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftNostril
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightCheek
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightEye
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightEyelidLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightEyelidUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightInnerBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightIOuterBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightLipUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightNostril
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2/RightHandIndex3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2/RightHandMiddle3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2/RightHandPinky3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2/RightHandRing3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2/RightHandThumb3
        weight: 1
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Idle_B
      takeName: Idle
      firstFrame: 243
      lastFrame: 401
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 0
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Reference
        weight: 1
      - path: Reference/Pivot
        weight: 1
      - path: Reference/Pivot/Root
        weight: 1
      - path: Reference/Pivot/Root/Hips
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg/LeftLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg/LeftLeg/LeftFoot
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg/LeftLeg/LeftFoot/LeftToes
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg/RightLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg/RightLeg/RightFoot
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg/RightLeg/RightFoot/RightToes
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2/LeftHandIndex3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2/LeftHandMiddle3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2/LeftHandPinky3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2/LeftHandRing3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2/LeftHandThumb3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/JawEND
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/LeftLipCorner
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/LeftLipLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/RightLipCorner
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/RightLipLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/TongueBack
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/TongueTip
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftCheek
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftEye
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftEyelidLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftEyelidUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftInnerBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftIOuterBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftLipUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftNostril
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightCheek
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightEye
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightEyelidLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightEyelidUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightInnerBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightIOuterBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightLipUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightNostril
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2/RightHandIndex3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2/RightHandMiddle3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2/RightHandPinky3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2/RightHandRing3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2/RightHandThumb3
        weight: 1
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Idle_C
      takeName: Idle
      firstFrame: 1780
      lastFrame: 1931
      wrapMode: 0
      orientationOffsetY: -1
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 0
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Reference
        weight: 1
      - path: Reference/Pivot
        weight: 1
      - path: Reference/Pivot/Root
        weight: 1
      - path: Reference/Pivot/Root/Hips
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg/LeftLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg/LeftLeg/LeftFoot
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg/LeftLeg/LeftFoot/LeftToes
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg/RightLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg/RightLeg/RightFoot
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg/RightLeg/RightFoot/RightToes
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2/LeftHandIndex3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2/LeftHandMiddle3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2/LeftHandPinky3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2/LeftHandRing3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2/LeftHandThumb3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/JawEND
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/LeftLipCorner
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/LeftLipLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/RightLipCorner
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/RightLipLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/TongueBack
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/TongueTip
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftCheek
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftEye
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftEyelidLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftEyelidUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftInnerBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftIOuterBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftLipUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftNostril
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightCheek
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightEye
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightEyelidLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightEyelidUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightInnerBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightIOuterBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightLipUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightNostril
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2/RightHandIndex3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2/RightHandMiddle3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2/RightHandPinky3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2/RightHandRing3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2/RightHandThumb3
        weight: 1
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Idle_D
      takeName: Idle
      firstFrame: 1917
      lastFrame: 2028
      wrapMode: 0
      orientationOffsetY: -1
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 0
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Reference
        weight: 1
      - path: Reference/Pivot
        weight: 1
      - path: Reference/Pivot/Root
        weight: 1
      - path: Reference/Pivot/Root/Hips
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg/LeftLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg/LeftLeg/LeftFoot
        weight: 1
      - path: Reference/Pivot/Root/Hips/LeftUpLeg/LeftLeg/LeftFoot/LeftToes
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg/RightLeg
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg/RightLeg/RightFoot
        weight: 1
      - path: Reference/Pivot/Root/Hips/RightUpLeg/RightLeg/RightFoot/RightToes
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandIndex1/LeftHandIndex2/LeftHandIndex3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandMiddle1/LeftHandMiddle2/LeftHandMiddle3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandPinky1/LeftHandPinky2/LeftHandPinky3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandRing1/LeftHandRing2/LeftHandRing3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/LeftShoulder/LeftArm/LeftForeArm/LeftHand/LeftHandThumb1/LeftHandThumb2/LeftHandThumb3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/JawEND
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/LeftLipCorner
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/LeftLipLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/RightLipCorner
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/RightLipLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/TongueBack
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/Jaw/TongueTip
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftCheek
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftEye
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftEyelidLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftEyelidUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftInnerBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftIOuterBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftLipUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/LeftNostril
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightCheek
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightEye
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightEyelidLower
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightEyelidUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightInnerBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightIOuterBrow
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightLipUpper
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/Neck/Head/RightNostril
        weight: 0
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandIndex1/RightHandIndex2/RightHandIndex3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandMiddle1/RightHandMiddle2/RightHandMiddle3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandPinky1/RightHandPinky2/RightHandPinky3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandRing1/RightHandRing2/RightHandRing3
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2
        weight: 1
      - path: Reference/Pivot/Root/Hips/Spine/Chest/RightShoulder/RightArm/RightForeArm/RightHand/RightHandThumb1/RightHandThumb2/RightHandThumb3
        weight: 1
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 0
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftUpLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightUpLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Chest
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftForeArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightForeArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftToes
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightToes
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftEye
      humanName: LeftEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightEye
      humanName: RightEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Jaw
      humanName: Jaw
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb1
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb2
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb3
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex1
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex2
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex3
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle1
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle2
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle3
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing1
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing2
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing3
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky1
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky2
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky3
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb1
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb2
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb3
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex1
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex2
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex3
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle1
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle2
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle3
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing1
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing2
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing3
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky1
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky2
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky3
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Idle(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Reference
      parentName: Idle(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Pivot
      parentName: Reference
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Root
      parentName: Pivot
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Hips
      parentName: Root
      position: {x: -0, y: 0.9516149, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftUpLeg
      parentName: Hips
      position: {x: -0.0754495, y: -0.04566402, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftLeg
      parentName: LeftUpLeg
      position: {x: -0.020550499, y: -0.40912998, z: -0.00071864796}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftFoot
      parentName: LeftLeg
      position: {x: -0.0051529994, y: -0.4231559, z: -0.027648851}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftToes
      parentName: LeftFoot
      position: {x: -0.007487, y: -0.0731673, z: 0.14542712}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightUpLeg
      parentName: Hips
      position: {x: 0.075449534, y: -0.04566399, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightLeg
      parentName: RightUpLeg
      position: {x: 0.020550467, y: -0.40913, z: -0.00071864796}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightFoot
      parentName: RightLeg
      position: {x: 0.0051529994, y: -0.4231559, z: -0.027648851}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightToes
      parentName: RightFoot
      position: {x: 0.007487, y: -0.0731673, z: 0.1454275}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Spine
      parentName: Hips
      position: {x: 2.646978e-25, y: 0.092263184, z: 0.015771331}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Chest
      parentName: Spine
      position: {x: -0, y: 0.16254029, z: -0.0016560555}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftShoulder
      parentName: Chest
      position: {x: -0.038285997, y: 0.2216225, z: -0.017063085}
      rotation: {x: -0.013464523, y: -0.04310245, z: 0.15681057, w: 0.98659587}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftArm
      parentName: LeftShoulder
      position: {x: -0.10050205, y: 0, z: -1.110223e-18}
      rotation: {x: 0.010683384, y: 0.043541655, z: -0.19634834, w: 0.97950876}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftForeArm
      parentName: LeftArm
      position: {x: -0.2540493, y: 0, z: 2.6645352e-17}
      rotation: {x: -0.0003239586, y: 0.014908556, z: 0.0024018358, w: 0.9998859}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHand
      parentName: LeftForeArm
      position: {x: -0.24638927, y: 2.842171e-16, z: -1.5987211e-16}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandIndex1
      parentName: LeftHand
      position: {x: -0.0751258, y: -0.0078414045, z: 0.032652643}
      rotation: {x: -0.0021189204, y: 0.08025744, z: 0.017538186, w: 0.9966176}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandIndex2
      parentName: LeftHandIndex1
      position: {x: -0.03979728, y: 0.000049808405, z: 0.0011857504}
      rotation: {x: 0.0005018685, y: 0.015471047, z: 0.040410817, w: 0.9990633}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandIndex3
      parentName: LeftHandIndex2
      position: {x: -0.027968477, y: -0.000000006281225, z: -0.00000005171866}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandMiddle1
      parentName: LeftHand
      position: {x: -0.076023825, y: -0.0018851344, z: 0.010141229}
      rotation: {x: -0.00076887343, y: 0.033321083, z: 0.020907538, w: 0.99922574}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandMiddle2
      parentName: LeftHandMiddle1
      position: {x: -0.044280436, y: 0.000004798874, z: -0.00042540013}
      rotation: {x: -0.0013621211, y: -0.019152503, z: 0.03788471, w: 0.9990976}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandMiddle3
      parentName: LeftHandMiddle2
      position: {x: -0.033964828, y: -0.000000012197929, z: 0.0000000037564827}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandPinky1
      parentName: LeftHand
      position: {x: -0.06565995, y: -0.007825106, z: -0.032251246}
      rotation: {x: -0.0009125867, y: 0.012160932, z: 0.021224529, w: 0.9997003}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandPinky2
      parentName: LeftHandPinky1
      position: {x: -0.030805448, y: -0.000030874573, z: -0.0014480775}
      rotation: {x: -0.00017062847, y: -0.009661348, z: -0.0053624013, w: 0.99993896}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandPinky3
      parentName: LeftHandPinky2
      position: {x: -0.023064027, y: -0.0000064025808, z: 0.000000018332095}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandRing1
      parentName: LeftHand
      position: {x: -0.07030211, y: -0.0037453093, z: -0.011411792}
      rotation: {x: -0.00032416338, y: 0.011597977, z: 0.02473746, w: 0.9996267}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandRing2
      parentName: LeftHandRing1
      position: {x: -0.043135457, y: -0.000020882308, z: -0.0022351784}
      rotation: {x: -0.0012034153, y: -0.023113327, z: 0.040986076, w: 0.9988916}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandRing3
      parentName: LeftHandRing2
      position: {x: -0.030835565, y: 7.710298e-11, z: -0.00000001649327}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandThumb1
      parentName: LeftHand
      position: {x: -0.014231241, y: -0.012377825, z: 0.025531668}
      rotation: {x: -0.013216841, y: -0.008700804, z: 0.0119097335, w: 0.99980384}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandThumb2
      parentName: LeftHandThumb1
      position: {x: -0.016374, y: -0.00529, z: 0.023491409}
      rotation: {x: -0.026061894, y: 0.096689746, z: 0.0036080794, w: 0.99496675}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandThumb3
      parentName: LeftHandThumb2
      position: {x: -0.02546, y: -0.00764, z: 0.020833}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Neck
      parentName: Chest
      position: {x: -0, y: 0.2590093, z: -0.032413255}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Head
      parentName: Neck
      position: {x: -2.646978e-25, y: 0.08307038, z: 0.0113267815}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Jaw
      parentName: Head
      position: {x: 1.7347234e-20, y: 0.0111267585, z: 0.010327543}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: JawEND
      parentName: Jaw
      position: {x: -1.7347234e-20, y: -0.04828876, z: 0.07185171}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftLipCorner
      parentName: Jaw
      position: {x: -0.032843262, y: -0.01657876, z: 0.066121764}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftLipLower
      parentName: Jaw
      position: {x: -0.014250817, y: -0.02168876, z: 0.08224063}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightLipCorner
      parentName: Jaw
      position: {x: 0.03284, y: -0.01657876, z: 0.066118784}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightLipLower
      parentName: Jaw
      position: {x: 0.014250817, y: -0.02168876, z: 0.082238786}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: TongueBack
      parentName: Jaw
      position: {x: -1.7347234e-20, y: -0.022869369, z: 0.010095409}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: TongueTip
      parentName: Jaw
      position: {x: -1.7347234e-20, y: -0.023278812, z: 0.03832271}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftCheek
      parentName: Head
      position: {x: -0.054244027, y: 0.03370195, z: 0.0594304}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftEye
      parentName: Head
      position: {x: -0.020848233, y: 0.0825027, z: 0.055427432}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftEyelidLower
      parentName: Head
      position: {x: -0.035618957, y: 0.06507366, z: 0.07623474}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftEyelidUpper
      parentName: Head
      position: {x: -0.034406897, y: 0.10060814, z: 0.08020531}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftInnerBrow
      parentName: Head
      position: {x: -0.012062691, y: 0.118765265, z: 0.093466826}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftIOuterBrow
      parentName: Head
      position: {x: -0.05503987, y: 0.11482529, z: 0.061777398}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftLipUpper
      parentName: Head
      position: {x: -0.014501322, y: -0.005111811, z: 0.09461884}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftNostril
      parentName: Head
      position: {x: -0.0179, y: 0.026312828, z: 0.0908674}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightCheek
      parentName: Head
      position: {x: 0.054239996, y: 0.033702828, z: 0.0594274}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightEye
      parentName: Head
      position: {x: 0.020849999, y: 0.08250283, z: 0.0554274}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightEyelidLower
      parentName: Head
      position: {x: 0.03562, y: 0.06507283, z: 0.0762374}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightEyelidUpper
      parentName: Head
      position: {x: 0.03441, y: 0.10061283, z: 0.08020739}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightInnerBrow
      parentName: Head
      position: {x: 0.012062687, y: 0.118765265, z: 0.093466826}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightIOuterBrow
      parentName: Head
      position: {x: 0.055040002, y: 0.11482283, z: 0.061777398}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightLipUpper
      parentName: Head
      position: {x: 0.014501322, y: -0.0051071714, z: 0.094617404}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightNostril
      parentName: Head
      position: {x: 0.0179, y: 0.026308905, z: 0.09087062}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightShoulder
      parentName: Chest
      position: {x: 0.038286015, y: 0.22162114, z: -0.017063085}
      rotation: {x: 0.15679811, y: 0.98659784, z: -0.0134636415, w: -0.043104075}
      scale: {x: 1, y: 1, z: 1}
    - name: RightArm
      parentName: RightShoulder
      position: {x: -0.100501455, y: -0.0000024995522, z: -0.000000051557407}
      rotation: {x: 0.18426873, y: 0.9798042, z: -0.017650131, w: 0.07561213}
      scale: {x: 1, y: 1, z: 1}
    - name: RightForeArm
      parentName: RightArm
      position: {x: 0.25342825, y: 0.006011353, z: -0.016704524}
      rotation: {x: -0.0011232316, y: 0.029230207, z: -0.03451959, w: 0.9989759}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHand
      parentName: RightForeArm
      position: {x: 0.2453737, y: 0.021641772, z: 0.005550465}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandIndex1
      parentName: RightHand
      position: {x: 0.0747695, y: -0.0012430536, z: 0.034344498}
      rotation: {x: -0.0021189204, y: 0.08025744, z: 0.017538186, w: 0.9966176}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandIndex2
      parentName: RightHandIndex1
      position: {x: 0.0370584, y: 0.00072612107, z: 0.014538894}
      rotation: {x: -0.003325548, y: 0.015931565, z: 0.060631223, w: 0.9980276}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandIndex3
      parentName: RightHandIndex2
      position: {x: 0.025225038, y: -0.0049664653, z: 0.011012146}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandMiddle1
      parentName: RightHand
      position: {x: 0.075647645, y: 0.0047914027, z: 0.011853182}
      rotation: {x: -0.00076887343, y: 0.033321083, z: 0.020907538, w: 0.99922574}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandMiddle2
      parentName: RightHandMiddle1
      position: {x: 0.043809064, y: 0.00019418815, z: 0.006454936}
      rotation: {x: -0.0041305316, y: -0.03351179, z: 0.0761208, w: 0.9965267}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandMiddle3
      parentName: RightHandMiddle2
      position: {x: 0.03307247, y: -0.007547537, z: 0.0016898462}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandPinky1
      parentName: RightHand
      position: {x: 0.06680334, y: -0.0019941088, z: -0.030756146}
      rotation: {x: 0.0031761636, y: -0.19200565, z: 0.045114137, w: 0.98035127}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandPinky2
      parentName: RightHandPinky1
      position: {x: 0.028530842, y: -0.001397143, z: -0.011623796}
      rotation: {x: -0.00017062847, y: -0.009661348, z: -0.0053624013, w: 0.99993896}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandPinky3
      parentName: RightHandPinky2
      position: {x: 0.02142686, y: -0.00055350893, z: -0.008516608}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandRing1
      parentName: RightHand
      position: {x: 0.070598476, y: 0.0024570965, z: -0.009821458}
      rotation: {x: 0.0007109462, y: -0.054342244, z: 0.034944206, w: 0.9979105}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandRing2
      parentName: RightHandRing1
      position: {x: 0.042887185, y: -0.0013753821, z: -0.004945858}
      rotation: {x: 0.00048439333, y: -0.021289855, z: 0.069860645, w: 0.9973294}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandRing3
      parentName: RightHandRing2
      position: {x: 0.029500604, y: -0.0076929354, z: -0.004622256}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandThumb1
      parentName: RightHand
      position: {x: 0.014684916, y: -0.011104942, z: 0.025858095}
      rotation: {x: -0.013451211, y: -0.0031628697, z: 0.03191902, w: 0.999395}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandThumb2
      parentName: RightHandThumb1
      position: {x: 0.016374, y: -0.00529, z: 0.02349136}
      rotation: {x: -0.026062576, y: -0.0966899, z: -0.0036074002, w: 0.9949668}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandThumb3
      parentName: RightHandThumb2
      position: {x: 0.02546, y: -0.00764, z: 0.020833}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 3
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
