This sample shows how to tag devices with custom "usages" and how to bind actions specifically to devices with only those usages.

This is useful if you have the same type of device that appears in multiple different roles that you want to distinguish when binding to the device. For example, when a device may appear in both the left and the right hand or may appear held in different orientations (say, horizontal vs vertical).
