//
// This file was automatically generated. Please don't edit by hand. Execute Editor command [ Edit > Rendering > Generate Shader Includes ] instead
//

#ifndef PROBEREFERENCEVOLUME_DEBUG_CS_HLSL
#define PROBEREFERENCEVOLUME_DEBUG_CS_HLSL
//
// UnityEngine.Rendering.DebugProbeShadingMode:  static fields
//
#define DEBUGPROBESHADINGMODE_SH (0)
#define DEBUGPROBESHADINGMODE_SHL0 (1)
#define DEBUGPROBESHADINGMODE_SHL0L1 (2)
#define DEBUGPROBESHADINGMODE_VALIDITY (3)
#define DEBUGPROBESHADINGMODE_VALIDITY_OVER_DILATION_THRESHOLD (4)
#define DEBUGPROBESHADINGMODE_INVALIDATED_BY_TOUCHUP_VOLUMES (5)
#define DEBUGPROBESHADINGMODE_SIZE (6)


#endif
