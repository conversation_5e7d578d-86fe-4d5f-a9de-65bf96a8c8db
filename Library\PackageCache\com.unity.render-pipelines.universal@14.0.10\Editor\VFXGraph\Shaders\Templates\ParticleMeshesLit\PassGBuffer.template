Pass
{
    Tags { "LightMode"="UniversalGBuffer" }

    ${VFXStencilGBuffer}

    HLSLPROGRAM
    #pragma target 4.5
    ${VFXPassGBufferAdditionalPragma}
    ${VFXURPGBufferDefines}

    ${VFXIncludeRP("VFXLitVaryings.template")}

    struct ps_input
    {
        float4 pos : SV_POSITION;

        ${VFXURPLitDeclareVaryings}

        #if USE_FLIPBOOK_INTERPOLATION
        float4 uv : TEXCOORD1;
        #else
        #if USE_FLIPBOOK_ARRAY_LAYOUT
        float3 uv : TEXCOORD1;
        #else
        float2 uv : TEXCOORD1;
        #endif
        #endif
        #if VFX_SHADERGRAPH_HAS_UV1
        float4 uv1 : COLOR2;
        #endif
        #if VFX_SHADERGRAPH_HAS_UV2
        float4 uv2 : COLOR3;
        #endif
        #if VFX_SHADERGRAPH_HAS_UV3
        float4 uv3 : COLOR4;
        #endif
        #if VFX_SHADERGRAPH_HAS_COLOR
        float4 vertexColor : COLOR1;
        #endif
        float3 normal : TEXCOORD2;
        #if USE_NORMAL_MAP || SHADERGRAPH_NEEDS_TANGENT_GBUFFER
        float4 tangent : TEXCOORD3;
        #endif
        #if USE_SOFT_PARTICLE || USE_ALPHA_TEST || USE_FLIPBOOK_INTERPOLATION
        // x: inverse soft particles fade distance
        // y: alpha threshold
        // z: frame blending factor
        nointerpolation float3 builtInInterpolants : TEXCOORD4;
        #endif
        #if USE_FLIPBOOK_MOTIONVECTORS
        // x: motion vector scale u
        // y: motion vector scale v
        nointerpolation float2 builtInInterpolants2 : TEXCOORD5;
        #endif

        float3 posWS : TEXCOORD6; //Needed for GI

        ${VFXAdditionalInterpolantsDeclaration}

        UNITY_VERTEX_OUTPUT_STEREO
        VFX_VERTEX_OUTPUT_INSTANCE_INDEX
    };

    struct ps_output
    {
        float4 color : SV_Target0;
    };

${VFXURPLitVaryingsMacros}

#define VFX_VARYING_PS_INPUTS ps_input
#define VFX_VARYING_POSCS pos
#define VFX_VARYING_UV uv
#define VFX_VARYING_INVSOFTPARTICLEFADEDISTANCE builtInInterpolants.x
#define VFX_VARYING_ALPHATHRESHOLD builtInInterpolants.y
#define VFX_VARYING_FRAMEBLEND builtInInterpolants.z
#define VFX_VARYING_MOTIONVECTORSCALE builtInInterpolants2.xy
#define VFX_VARYING_NORMAL normal

#if USE_NORMAL_MAP || SHADERGRAPH_NEEDS_TANGENT_GBUFFER
#define VFX_VARYING_TANGENT tangent
#endif
#define VFX_VARYING_POSWS posWS

${VFXBegin:VFXVertexAdditionalProcess}
${VFXURPLitFillVaryings}
${VFXEnd}

    ${VFXInclude("Shaders/ParticleMeshes/Pass.template")}
    #define SHADERPASS SHADERPASS_GBUFFER
    ${VFXIncludeRP("VFXLit.template")}

    ${SHADERGRAPH_PIXEL_CODE_GBUFFER}

    #pragma fragment frag
    void frag(ps_input i
    , out FragmentOutput outGBuffer
#if USE_DOUBLE_SIDED
    , bool frontFace : SV_IsFrontFace
#endif
    )
    {
        UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(i);
        VFXTransformPSInputs(i);
        VFX_FRAG_SETUP_INSTANCE_ID(i);
        ${VFXComputeNormalWS}

        #ifdef VFX_SHADERGRAPH
            ${VFXAdditionalInterpolantsPreparation}
            ${SHADERGRAPH_PIXEL_CALL_GBUFFER}
            ${VFXIncludeRP("VFXSGSurfaceData.template")}

            VFXComputePixelOutputToGBufferShaderGraph(i, surface, normalWS, outGBuffer);
        #else
            VFXComputePixelOutputToGBuffer(i, normalWS, uvData, outGBuffer);
        #endif
    }
    ENDHLSL
}
