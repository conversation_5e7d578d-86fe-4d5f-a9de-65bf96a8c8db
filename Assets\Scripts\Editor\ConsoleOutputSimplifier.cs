using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Linq;

namespace VRoidFaceCustomization.Tools
{
    /// <summary>
    /// 控制台输出精简工具
    /// 帮助过滤和精简Unity控制台的输出
    /// </summary>
    public class ConsoleOutputSimplifier : EditorWindow
    {
        [MenuItem("Tools/VRoid Face Customization/Console Output Simplifier")]
        public static void ShowWindow()
        {
            GetWindow<ConsoleOutputSimplifier>("控制台精简工具");
        }
        
        private Vector2 scrollPosition;
        private string inputText = "";
        private string outputText = "";
        
        [Header("过滤设置")]
        private bool removeStackTrace = true;
        private bool removeTimestamps = true;
        private bool removeUnityInternals = true;
        private bool groupSimilarMessages = true;
        private bool showOnlyKeywords = false;
        private string keywordFilter = "ThirdPersonSceneLoader,VRMModelManager,SceneTransitionManager";
        
        [Header("显示设置")]
        private bool showLineNumbers = false;
        private bool colorCodeMessages = true;
        private int maxLinesPerMessage = 1;
        
        private void OnGUI()
        {
            EditorGUILayout.LabelField("控制台输出精简工具", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            // 设置区域
            DrawSettings();
            
            EditorGUILayout.Space();
            
            // 输入区域
            DrawInputArea();
            
            EditorGUILayout.Space();
            
            // 按钮区域
            DrawButtons();
            
            EditorGUILayout.Space();
            
            // 输出区域
            DrawOutputArea();
        }
        
        private void DrawSettings()
        {
            EditorGUILayout.LabelField("过滤设置", EditorStyles.boldLabel);
            
            removeStackTrace = EditorGUILayout.Toggle("移除堆栈跟踪", removeStackTrace);
            removeTimestamps = EditorGUILayout.Toggle("移除时间戳", removeTimestamps);
            removeUnityInternals = EditorGUILayout.Toggle("移除Unity内部调用", removeUnityInternals);
            groupSimilarMessages = EditorGUILayout.Toggle("合并相似消息", groupSimilarMessages);
            
            EditorGUILayout.Space();
            
            showOnlyKeywords = EditorGUILayout.Toggle("只显示关键词消息", showOnlyKeywords);
            if (showOnlyKeywords)
            {
                EditorGUILayout.LabelField("关键词 (用逗号分隔):");
                keywordFilter = EditorGUILayout.TextArea(keywordFilter, GUILayout.Height(40));
            }
            
            EditorGUILayout.Space();
            
            EditorGUILayout.LabelField("显示设置", EditorStyles.boldLabel);
            showLineNumbers = EditorGUILayout.Toggle("显示行号", showLineNumbers);
            colorCodeMessages = EditorGUILayout.Toggle("彩色编码消息", colorCodeMessages);
            maxLinesPerMessage = EditorGUILayout.IntSlider("每条消息最大行数", maxLinesPerMessage, 1, 5);
        }
        
        private void DrawInputArea()
        {
            EditorGUILayout.LabelField("输入控制台日志:", EditorStyles.boldLabel);
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(150));
            inputText = EditorGUILayout.TextArea(inputText, GUILayout.ExpandHeight(true));
            EditorGUILayout.EndScrollView();
        }
        
        private void DrawButtons()
        {
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("精简输出", GUILayout.Height(30)))
            {
                SimplifyOutput();
            }
            
            if (GUILayout.Button("清空输入", GUILayout.Height(30)))
            {
                inputText = "";
                outputText = "";
            }
            
            if (GUILayout.Button("复制输出", GUILayout.Height(30)))
            {
                EditorGUIUtility.systemCopyBuffer = outputText;
                ShowNotification(new GUIContent("已复制到剪贴板"));
            }
            
            if (GUILayout.Button("从剪贴板粘贴", GUILayout.Height(30)))
            {
                inputText = EditorGUIUtility.systemCopyBuffer;
            }
            
            EditorGUILayout.EndHorizontal();
        }
        
        private void DrawOutputArea()
        {
            EditorGUILayout.LabelField("精简后的输出:", EditorStyles.boldLabel);
            
            var outputStyle = new GUIStyle(EditorStyles.textArea);
            if (colorCodeMessages)
            {
                outputStyle.richText = true;
            }
            
            EditorGUILayout.TextArea(outputText, outputStyle, GUILayout.Height(200));
        }
        
        private void SimplifyOutput()
        {
            if (string.IsNullOrEmpty(inputText))
            {
                outputText = "请输入控制台日志内容";
                return;
            }
            
            var lines = inputText.Split('\n');
            var processedLines = new List<string>();
            var messageGroups = new Dictionary<string, int>();
            
            for (int i = 0; i < lines.Length; i++)
            {
                string line = lines[i].Trim();
                if (string.IsNullOrEmpty(line)) continue;
                
                // 处理单行
                string processedLine = ProcessLine(line, i);
                if (string.IsNullOrEmpty(processedLine)) continue;
                
                // 关键词过滤
                if (showOnlyKeywords && !ContainsKeywords(processedLine))
                {
                    continue;
                }
                
                // 合并相似消息
                if (groupSimilarMessages)
                {
                    string messageKey = ExtractMessageKey(processedLine);
                    if (messageGroups.ContainsKey(messageKey))
                    {
                        messageGroups[messageKey]++;
                        continue;
                    }
                    else
                    {
                        messageGroups[messageKey] = 1;
                    }
                }
                
                processedLines.Add(processedLine);
            }
            
            // 添加重复消息统计
            if (groupSimilarMessages)
            {
                var finalLines = new List<string>();
                foreach (var line in processedLines)
                {
                    string messageKey = ExtractMessageKey(line);
                    int count = messageGroups[messageKey];
                    if (count > 1)
                    {
                        finalLines.Add($"{line} <color=yellow>(×{count})</color>");
                    }
                    else
                    {
                        finalLines.Add(line);
                    }
                }
                processedLines = finalLines;
            }
            
            outputText = string.Join("\n", processedLines);
            
            if (processedLines.Count == 0)
            {
                outputText = "没有匹配的日志内容";
            }
        }
        
        private string ProcessLine(string line, int lineNumber)
        {
            // 移除Unity内部调用
            if (removeUnityInternals && IsUnityInternal(line))
            {
                return "";
            }
            
            // 移除堆栈跟踪
            if (removeStackTrace && IsStackTrace(line))
            {
                return "";
            }
            
            // 移除时间戳
            if (removeTimestamps)
            {
                line = RemoveTimestamp(line);
            }
            
            // 提取关键信息
            line = ExtractKeyInfo(line);
            
            // 添加行号
            if (showLineNumbers)
            {
                line = $"[{lineNumber + 1:D3}] {line}";
            }
            
            // 颜色编码
            if (colorCodeMessages)
            {
                line = ApplyColorCoding(line);
            }
            
            return line;
        }
        
        private bool IsUnityInternal(string line)
        {
            return line.Contains("UnityEngine.") || 
                   line.Contains("UnityEditor.") ||
                   line.Contains("System.Collections.") ||
                   line.Contains("SetupCoroutine:InvokeMoveNext");
        }
        
        private bool IsStackTrace(string line)
        {
            return line.Contains(" (at Assets/") && line.Contains(":") ||
                   line.Contains("UnityEngine.MonoBehaviour:StartCoroutine") ||
                   line.Contains("UnityEngine.Debug:Log");
        }
        
        private string RemoveTimestamp(string line)
        {
            // 移除常见的时间戳格式
            line = Regex.Replace(line, @"\d{2}:\d{2}:\d{2}\.\d{3}", "");
            line = Regex.Replace(line, @"\[\d{2}:\d{2}:\d{2}\]", "");
            return line.Trim();
        }
        
        private string ExtractKeyInfo(string line)
        {
            // 提取主要的日志消息，移除文件路径等
            if (line.Contains("] "))
            {
                int index = line.LastIndexOf("] ");
                if (index >= 0 && index < line.Length - 2)
                {
                    line = line.Substring(index + 2);
                }
            }
            
            return line.Trim();
        }
        
        private bool ContainsKeywords(string line)
        {
            if (string.IsNullOrEmpty(keywordFilter)) return true;
            
            var keywords = keywordFilter.Split(',').Select(k => k.Trim()).Where(k => !string.IsNullOrEmpty(k));
            return keywords.Any(keyword => line.Contains(keyword));
        }
        
        private string ExtractMessageKey(string line)
        {
            // 提取消息的关键部分用于分组
            // 移除表情符号和变量部分
            string key = Regex.Replace(line, @"[🎮📂🏗️✅❌🎭📦🆕➕⏳🗑️📍🎨📷👤🎉]", "");
            key = Regex.Replace(key, @"\d+", "X"); // 将数字替换为X
            key = Regex.Replace(key, @"\([^)]*\)", ""); // 移除括号内容
            return key.Trim();
        }
        
        private string ApplyColorCoding(string line)
        {
            // 成功消息 - 绿色
            if (line.Contains("✅") || line.Contains("成功"))
            {
                return $"<color=green>{line}</color>";
            }
            
            // 错误消息 - 红色
            if (line.Contains("❌") || line.Contains("失败") || line.Contains("错误"))
            {
                return $"<color=red>{line}</color>";
            }
            
            // 警告消息 - 黄色
            if (line.Contains("⚠️") || line.Contains("警告"))
            {
                return $"<color=yellow>{line}</color>";
            }
            
            // 信息消息 - 蓝色
            if (line.Contains("🎮") || line.Contains("📂") || line.Contains("🏗️"))
            {
                return $"<color=cyan>{line}</color>";
            }
            
            return line;
        }
    }
}
