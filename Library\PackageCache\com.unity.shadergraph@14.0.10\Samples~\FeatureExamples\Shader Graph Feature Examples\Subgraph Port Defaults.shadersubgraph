{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "88462436e5e942799b58352d3513cf3a",
    "m_Properties": [
        {
            "m_Id": "ce69c51a32284691b56c88eb4d718af5"
        }
    ],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "457e16a3b1764ce498e5af2c92aeddfa"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "3c01b72d7f0945c586bb40726362681f"
        },
        {
            "m_Id": "f71a795da5c44b748c218f5005fef583"
        },
        {
            "m_Id": "618810e61cb24bcebba0b018f989ad17"
        },
        {
            "m_Id": "d7d05ebde41140889bffe4915706e08f"
        },
        {
            "m_Id": "736e0e42ae6947c89a49f0ddd26bb8b5"
        }
    ],
    "m_GroupDatas": [],
    "m_StickyNoteDatas": [
        {
            "m_Id": "5b8b72af59a04d5d843acfc38855e46a"
        },
        {
            "m_Id": "e976b35d6f33479e9419756c2b246fdd"
        },
        {
            "m_Id": "a3d94bda122241cc875d8a7e8b15edbc"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "618810e61cb24bcebba0b018f989ad17"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "736e0e42ae6947c89a49f0ddd26bb8b5"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "736e0e42ae6947c89a49f0ddd26bb8b5"
                },
                "m_SlotId": 3
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "3c01b72d7f0945c586bb40726362681f"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "d7d05ebde41140889bffe4915706e08f"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "736e0e42ae6947c89a49f0ddd26bb8b5"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "d7d05ebde41140889bffe4915706e08f"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "736e0e42ae6947c89a49f0ddd26bb8b5"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "f71a795da5c44b748c218f5005fef583"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "618810e61cb24bcebba0b018f989ad17"
                },
                "m_SlotId": 0
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Sub Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": "3c01b72d7f0945c586bb40726362681f"
    },
    "m_SubDatas": [],
    "m_ActiveTargets": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "23a7d5d7046944a4ab63ce3539e63947",
    "m_Id": 3,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "312f39dbd0f94bfd9b73c1117d2ffe92",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "3951fd4a46e74f129a86aae15e00f78f",
    "m_Id": 1,
    "m_DisplayName": "Out_Vector4",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out_Vector4",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphOutputNode",
    "m_ObjectId": "3c01b72d7f0945c586bb40726362681f",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Output",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -255.5,
            "y": -78.5000228881836,
            "width": 119.00004577636719,
            "height": 76.99998474121094
        }
    },
    "m_Slots": [
        {
            "m_Id": "3951fd4a46e74f129a86aae15e00f78f"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "IsFirstSlotValid": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "457e16a3b1764ce498e5af2c92aeddfa",
    "m_Name": "",
    "m_ChildObjectList": [
        {
            "m_Id": "ce69c51a32284691b56c88eb4d718af5"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "5b8b72af59a04d5d843acfc38855e46a",
    "m_Title": "Branch on Input Connection",
    "m_Content": "You can use the Branch on Input Connection node to define a default for an input port as shown in this example.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -771.0000610351563,
        "y": -217.50001525878907,
        "width": 330.0000305175781,
        "height": 100.00000762939453
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "5ec506603efb44b5a8c1819455fd4634",
    "m_Id": 1,
    "m_DisplayName": "Connected",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Connected",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.SwizzleNode",
    "m_ObjectId": "618810e61cb24bcebba0b018f989ad17",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Swizzle",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -850.0001220703125,
            "y": -17.499996185302736,
            "width": 131.0,
            "height": 121.50003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "f10cfa1ad94547d994d2108e2ff1028f"
        },
        {
            "m_Id": "312f39dbd0f94bfd9b73c1117d2ffe92"
        }
    ],
    "synonyms": [
        "swap",
        "reorder",
        "component mask"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "_maskInput": "xy",
    "convertedMask": "xy"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "698312b9f8ca499aaa2b331f4d15b1b6",
    "m_Id": 2,
    "m_DisplayName": "NotConnected",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "NotConnected",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "6b23368288bc49389b3bd028cb42af78",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyConnectionStateMaterialSlot",
    "m_ObjectId": "71decf991fad40189814f465256c0cf6",
    "m_Id": 0,
    "m_DisplayName": "Input",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Input",
    "m_StageCapability": 3
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BranchOnInputConnectionNode",
    "m_ObjectId": "736e0e42ae6947c89a49f0ddd26bb8b5",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Branch On Input Connection",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -649.0001220703125,
            "y": -78.50001525878906,
            "width": 206.00006103515626,
            "height": 142.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "71decf991fad40189814f465256c0cf6"
        },
        {
            "m_Id": "5ec506603efb44b5a8c1819455fd4634"
        },
        {
            "m_Id": "698312b9f8ca499aaa2b331f4d15b1b6"
        },
        {
            "m_Id": "23a7d5d7046944a4ab63ce3539e63947"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "a3d94bda122241cc875d8a7e8b15edbc",
    "m_Title": "",
    "m_Content": "Steps to set up an input port default:\n\n1. Create an Input port for the Subgraph by adding a parameter in the Blackboard.\n\n2. Select the Blackboard parameter and open the Graph Inspector.\n\n3. In the Graph Inspector, check the \"Use Custom Binding\" box for the parameter and type a name in the Label box.  This label will be displayed on the input port when nothing is connected.\n\n4. Drag the Blackboard parameter into the Subgraph.\n\n5. Add a Branch on Input Connection node to the Subgraph.\n\n6. Connect the parameter to both the Input and Connected input ports of the Branch on Input Connection node as shown above.\n\n7. Create a node or nodes for the default value and connect the output to the NotConnected input port of the Branch On Input Connection node.  Instead of just a single node, you could use a large collection of nodes to define the input port's default behavior if needed.\n\nWith the Branch On Input Connection node set up as described here, the Out port of the node will use what's connected to the Subgraph's input port if there is a connection.  If there isn't a connection, it will use the values from the node connected to the NotConnected port.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -450.0000305175781,
        "y": 103.00001525878906,
        "width": 457.0000305175781,
        "height": 379.00006103515627
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector2ShaderProperty",
    "m_ObjectId": "ce69c51a32284691b56c88eb4d718af5",
    "m_Guid": {
        "m_GuidSerialized": "333ca457-57dd-477c-94b3-381e5aee2591"
    },
    "m_Name": "UV",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "UV",
    "m_DefaultReferenceName": "_UV",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": true,
    "m_CustomSlotLabel": "UV0",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "d7d05ebde41140889bffe4915706e08f",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -811.5000610351563,
            "y": -68.00000762939453,
            "width": 92.5,
            "height": 34.00000762939453
        }
    },
    "m_Slots": [
        {
            "m_Id": "ee66369e3f7940a3a96cac757e7708b5"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "ce69c51a32284691b56c88eb4d718af5"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "e976b35d6f33479e9419756c2b246fdd",
    "m_Title": "",
    "m_Content": "In this example, if the user connects something to the Subgraph's UV input port, that connection will be used.  But if the UV input port is left without a connection, the UV node above will be used instead - thus making UV0 the default.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -781.5000610351563,
        "y": 120.00001525878906,
        "width": 251.5,
        "height": 109.50001525878906
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "ee66369e3f7940a3a96cac757e7708b5",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "f10cfa1ad94547d994d2108e2ff1028f",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVNode",
    "m_ObjectId": "f71a795da5c44b748c218f5005fef583",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "UV",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -995.0001220703125,
            "y": -17.499996185302736,
            "width": 145.0,
            "height": 128.50001525878907
        }
    },
    "m_Slots": [
        {
            "m_Id": "6b23368288bc49389b3bd028cb42af78"
        }
    ],
    "synonyms": [
        "texcoords",
        "coords",
        "coordinates"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_OutputChannel": 0
}

