{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "b66df428b43b4cb0a78762e3a32bbaed",
    "m_Properties": [],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "02409c2b8120453eb9d30502f2ac2386"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "046535ac56224c789d7b61c2d2432789"
        },
        {
            "m_Id": "350b2feff4114bbd83db830cdae877aa"
        },
        {
            "m_Id": "a0a10696e4b742d9bf0a8a11a646b3dc"
        },
        {
            "m_Id": "9aa13176b2d743e7b21e00010b1d3ed4"
        },
        {
            "m_Id": "c875373bfc6848eb9ccf036c2c39d53c"
        },
        {
            "m_Id": "02278b668ecb40bb81497ebc84e73637"
        },
        {
            "m_Id": "e2169fdce2e9460a933252aaa6fc2a88"
        },
        {
            "m_Id": "e3fbea880e79437aaf8f2bbe696ec436"
        },
        {
            "m_Id": "0d3abf241168457494ebd09a8a0cb070"
        },
        {
            "m_Id": "c1c96de999a44f6b95aab1bce9b7a97f"
        },
        {
            "m_Id": "8355682a52464812a5fed28ea6c0b022"
        },
        {
            "m_Id": "d5844f66c7d344cf8180f230ac85b1eb"
        },
        {
            "m_Id": "9bcb6c3298be4dbc89315717546abbd8"
        },
        {
            "m_Id": "fc73fe5fafa5476aa079d07ffe0d5bff"
        },
        {
            "m_Id": "1907d4a093df40b681918b643852cfe8"
        },
        {
            "m_Id": "df75c7f767f248f792074f0005147841"
        },
        {
            "m_Id": "ec04f143d0e84556a9063c904aadb425"
        },
        {
            "m_Id": "3ebb86d8b0c344a08e01fd4961e20f1f"
        },
        {
            "m_Id": "c72f861f8eea40998e7539e7bdd1ad2d"
        },
        {
            "m_Id": "59b7dcc65e5547698c3aac7a668f1953"
        },
        {
            "m_Id": "26f638b764cc44c0909cc5aabd7e4b20"
        },
        {
            "m_Id": "5a4161e8da054bd99e4a8674756ab449"
        },
        {
            "m_Id": "6639511b8bdf49058aac233beac69f4c"
        },
        {
            "m_Id": "8901eefa5f4a49f2bb2ee41e1594aca7"
        },
        {
            "m_Id": "f44f8bfc81da453eb910c0928cd0bacc"
        },
        {
            "m_Id": "6d53561541314905b71ad8a517046b5a"
        }
    ],
    "m_GroupDatas": [
        {
            "m_Id": "74744a6759794646bd5d8b833844b353"
        },
        {
            "m_Id": "c9cbff2e7c4540148c993f87acac8c06"
        },
        {
            "m_Id": "ec48d68701394d00ada30241f09ded9b"
        },
        {
            "m_Id": "8ab83206be4041d4b4a8b05eaa8e55de"
        }
    ],
    "m_StickyNoteDatas": [
        {
            "m_Id": "96c7a1f7d6004df4bb9d0e67369b8c24"
        },
        {
            "m_Id": "d1f4819b1a904848a4e63d373ff6d42d"
        },
        {
            "m_Id": "5cc566916b794649a5f0a749d8bf6bd1"
        },
        {
            "m_Id": "132e3c3072554bb292a9b48b72e3c32a"
        },
        {
            "m_Id": "9d17c833e5ed489a9945b44c82b0f257"
        },
        {
            "m_Id": "d80f1076ccf14ca8b9e3362a7074b862"
        },
        {
            "m_Id": "db539324f18a4a3e95b855eb8454a2dd"
        },
        {
            "m_Id": "cf0830aa0c804808a26bf8abf278fdf7"
        },
        {
            "m_Id": "3bc9348779a546108cd22e97a10cf66d"
        },
        {
            "m_Id": "a1954820575149989443059b4741cab1"
        },
        {
            "m_Id": "1082fc083fda42c1b50401ec365448a3"
        },
        {
            "m_Id": "b68aee0764dc472a88a48dee075c5576"
        },
        {
            "m_Id": "e0a617a469bd4af7819808304a698b45"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "02278b668ecb40bb81497ebc84e73637"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "e2169fdce2e9460a933252aaa6fc2a88"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "26f638b764cc44c0909cc5aabd7e4b20"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "3ebb86d8b0c344a08e01fd4961e20f1f"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "3ebb86d8b0c344a08e01fd4961e20f1f"
                },
                "m_SlotId": 5
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "5a4161e8da054bd99e4a8674756ab449"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "59b7dcc65e5547698c3aac7a668f1953"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "3ebb86d8b0c344a08e01fd4961e20f1f"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "6639511b8bdf49058aac233beac69f4c"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "5a4161e8da054bd99e4a8674756ab449"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "6639511b8bdf49058aac233beac69f4c"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "8901eefa5f4a49f2bb2ee41e1594aca7"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "8355682a52464812a5fed28ea6c0b022"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "d5844f66c7d344cf8180f230ac85b1eb"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "9bcb6c3298be4dbc89315717546abbd8"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "d5844f66c7d344cf8180f230ac85b1eb"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "c72f861f8eea40998e7539e7bdd1ad2d"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "3ebb86d8b0c344a08e01fd4961e20f1f"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "d5844f66c7d344cf8180f230ac85b1eb"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "fc73fe5fafa5476aa079d07ffe0d5bff"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "df75c7f767f248f792074f0005147841"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1907d4a093df40b681918b643852cfe8"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "e2169fdce2e9460a933252aaa6fc2a88"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "e3fbea880e79437aaf8f2bbe696ec436"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "e2169fdce2e9460a933252aaa6fc2a88"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "0d3abf241168457494ebd09a8a0cb070"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "e2169fdce2e9460a933252aaa6fc2a88"
                },
                "m_SlotId": 3
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "c1c96de999a44f6b95aab1bce9b7a97f"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "ec04f143d0e84556a9063c904aadb425"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1907d4a093df40b681918b643852cfe8"
                },
                "m_SlotId": 1
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": [
            {
                "m_Id": "046535ac56224c789d7b61c2d2432789"
            },
            {
                "m_Id": "350b2feff4114bbd83db830cdae877aa"
            },
            {
                "m_Id": "a0a10696e4b742d9bf0a8a11a646b3dc"
            }
        ]
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 200.0
        },
        "m_Blocks": [
            {
                "m_Id": "9aa13176b2d743e7b21e00010b1d3ed4"
            },
            {
                "m_Id": "f44f8bfc81da453eb910c0928cd0bacc"
            },
            {
                "m_Id": "6d53561541314905b71ad8a517046b5a"
            }
        ]
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Shader Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": ""
    },
    "m_SubDatas": [],
    "m_ActiveTargets": [
        {
            "m_Id": "c0ad5e3747e249c58ab284bf9604e712"
        },
        {
            "m_Id": "09fdff9a94134714908ab38b86d0ad43"
        },
        {
            "m_Id": "408c36a1d05047b0a2c15f2bb2f8950f"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalVectorNode",
    "m_ObjectId": "02278b668ecb40bb81497ebc84e73637",
    "m_Group": {
        "m_Id": "74744a6759794646bd5d8b833844b353"
    },
    "m_Name": "Normal Vector",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -2423.5,
            "y": 231.5000762939453,
            "width": 206.0,
            "height": 130.4999237060547
        }
    },
    "m_Slots": [
        {
            "m_Id": "f75931a2ae414be599565d7bdc570259"
        }
    ],
    "synonyms": [
        "surface direction"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "02409c2b8120453eb9d30502f2ac2386",
    "m_Name": "",
    "m_ChildObjectList": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "03af90f7b13f460fb86bd93a1c69e79a",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TangentMaterialSlot",
    "m_ObjectId": "0460048e16964e44b953a3b348b3e3d9",
    "m_Id": 0,
    "m_DisplayName": "Tangent",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tangent",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "046535ac56224c789d7b61c2d2432789",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "e413ea42de984df88720f07f4ede3196"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Position"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDTarget",
    "m_ObjectId": "09fdff9a94134714908ab38b86d0ad43",
    "m_ActiveSubTarget": {
        "m_Id": "6bc7bd98c57d45d98a6a993c15a1acb6"
    },
    "m_Datas": [
        {
            "m_Id": "99419abd4ebf49049974e0a2f860debe"
        },
        {
            "m_Id": "326fe691d86641168a496f74dde8583b"
        },
        {
            "m_Id": "c7cc80f90cc54b3a98d0d4fcf3f93ed3"
        }
    ],
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false,
    "m_SupportLineRendering": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PreviewNode",
    "m_ObjectId": "0d3abf241168457494ebd09a8a0cb070",
    "m_Group": {
        "m_Id": "74744a6759794646bd5d8b833844b353"
    },
    "m_Name": "Preview",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1999.4998779296875,
            "y": 258.0,
            "width": 208.0001220703125,
            "height": 278.00006103515627
        }
    },
    "m_Slots": [
        {
            "m_Id": "5a15e9715a8547b0a8b81c09acd93579"
        },
        {
            "m_Id": "81e3e84cb28741f9908e75a557848747"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Width": 208.0,
    "m_Height": 208.0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInUnlitSubTarget",
    "m_ObjectId": "0d41a5a5bcfd45f7ba612ccbd95e6b42"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0dc70e4ccca34c198cda8e6d8cd7e412",
    "m_Id": 7,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "1063cba480c24f08b4f96075d6fa40f6",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 1.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "1082fc083fda42c1b50401ec365448a3",
    "m_Title": "",
    "m_Content": "The Tangent, Bitangent, and Normal Vectors can be used to construct the Tangent Basis - a matrix that can use used to transform a tangent space normal to world space.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1506.5001220703125,
        "y": 1271.0001220703125,
        "width": 200.0,
        "height": 105.0
    },
    "m_Group": {
        "m_Id": "8ab83206be4041d4b4a8b05eaa8e55de"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Matrix2MaterialSlot",
    "m_ObjectId": "12d6511cef6146ac847e0030fb125203",
    "m_Id": 6,
    "m_DisplayName": "2x2",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "2x2",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "132e3c3072554bb292a9b48b72e3c32a",
    "m_Title": "",
    "m_Content": "The green channel creates a mask in the positive Y direction.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1773.5001220703125,
        "y": 347.5000305175781,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "74744a6759794646bd5d8b833844b353"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "13407309844f43e18bf97aada220e819",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DotProductNode",
    "m_ObjectId": "1907d4a093df40b681918b643852cfe8",
    "m_Group": {
        "m_Id": "ec48d68701394d00ada30241f09ded9b"
    },
    "m_Name": "Dot Product",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -468.0000915527344,
            "y": 42.000003814697269,
            "width": 207.99993896484376,
            "height": 302.0000305175781
        }
    },
    "m_Slots": [
        {
            "m_Id": "c3ff506ce8714b5db847c4d7efc3fb3f"
        },
        {
            "m_Id": "1063cba480c24f08b4f96075d6fa40f6"
        },
        {
            "m_Id": "4d14cf606daa4e8597c1aa7c9b0928b0"
        }
    ],
    "synonyms": [
        "scalar product"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "21f47d1fe52e4606bd00efa71c04312d",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "25ae9a694ab643c6abda91123c352861",
    "m_Id": 1,
    "m_DisplayName": "M1",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "M1",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "267ca60077cc4f9e92e37e7206b0c76f",
    "m_Id": 0,
    "m_DisplayName": "Base Color",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BaseColor",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.5,
        "y": 0.5,
        "z": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.5,
        "g": 0.5,
        "b": 0.5,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BitangentVectorNode",
    "m_ObjectId": "26f638b764cc44c0909cc5aabd7e4b20",
    "m_Group": {
        "m_Id": "8ab83206be4041d4b4a8b05eaa8e55de"
    },
    "m_Name": "Bitangent Vector",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1509.000244140625,
            "y": 1005.0001220703125,
            "width": 206.0,
            "height": 130.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "d0cf45e2103f413885c3fdf7e70d5f91"
        }
    ],
    "synonyms": [
        "binormal"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "326fe691d86641168a496f74dde8583b",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "350b2feff4114bbd83db830cdae877aa",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Normal",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "e55ce073b8fa4200b9a50349ecc977ab"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Normal"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "3bc9348779a546108cd22e97a10cf66d",
    "m_Title": "",
    "m_Content": "Diffuse lighting can be created by comparing the Normal Vector and the Main Light Direction.  When the two vectors are parallel, we get a lit surface, but when they're perpendicular or facing in opposite directions, the surface is not lit.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -726.0000610351563,
        "y": 230.00003051757813,
        "width": 200.0,
        "height": 123.0
    },
    "m_Group": {
        "m_Id": "ec48d68701394d00ada30241f09ded9b"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MatrixConstructionNode",
    "m_ObjectId": "3ebb86d8b0c344a08e01fd4961e20f1f",
    "m_Group": {
        "m_Id": "8ab83206be4041d4b4a8b05eaa8e55de"
    },
    "m_Name": "Matrix Construction",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1278.000244140625,
            "y": 980.5001220703125,
            "width": 158.4998779296875,
            "height": 159.5001220703125
        }
    },
    "m_Slots": [
        {
            "m_Id": "5e0f455c6e4e49d6aca8b1bee27b55bf"
        },
        {
            "m_Id": "25ae9a694ab643c6abda91123c352861"
        },
        {
            "m_Id": "ff459aa4269e4b4b88f60752eb1d602f"
        },
        {
            "m_Id": "fac931c8c6684a6ba454470637cff9ab"
        },
        {
            "m_Id": "c56905579acf45b5a3d4573ba0c31ace"
        },
        {
            "m_Id": "e224f85eef9545adb2e75fe536ead8b1"
        },
        {
            "m_Id": "12d6511cef6146ac847e0030fb125203"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Axis": 0
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget",
    "m_ObjectId": "408c36a1d05047b0a2c15f2bb2f8950f",
    "m_Datas": [],
    "m_ActiveSubTarget": {
        "m_Id": "c5ea2ef662644b4cb52d09351c3805d7"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZTestMode": 4,
    "m_ZWriteControl": 0,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CastShadows": true,
    "m_ReceiveShadows": true,
    "m_SupportsLODCrossFade": false,
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "428ec33eb87742c6a770c9d94dfb7037",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "42a81945b09a4a8d87c73c368e23f05c",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 1.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "4343bcbbc0e54b6a97696686f4da9cf3",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "439f15797b414c24b111e38771686eb3",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "4b2a0e9d3574419d8041647cda8eca6a",
    "m_Id": 3,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "4d14cf606daa4e8597c1aa7c9b0928b0",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "566fee12e0544a30bfc40f03f0bbcfb9",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 2.0,
        "e01": 2.0,
        "e02": 2.0,
        "e03": 2.0,
        "e10": 2.0,
        "e11": 2.0,
        "e12": 2.0,
        "e13": 2.0,
        "e20": 2.0,
        "e21": 2.0,
        "e22": 2.0,
        "e23": 2.0,
        "e30": 2.0,
        "e31": 2.0,
        "e32": 2.0,
        "e33": 2.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "56e435bcfd1449aab5efd8633b7ab5da",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TangentVectorNode",
    "m_ObjectId": "59b7dcc65e5547698c3aac7a668f1953",
    "m_Group": {
        "m_Id": "8ab83206be4041d4b4a8b05eaa8e55de"
    },
    "m_Name": "Tangent Vector",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1509.000244140625,
            "y": 874.5001220703125,
            "width": 206.0,
            "height": 130.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "eec1792020614f0db748aa7cb3b5cd1f"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "5a15e9715a8547b0a8b81c09acd93579",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MultiplyNode",
    "m_ObjectId": "5a4161e8da054bd99e4a8674756ab449",
    "m_Group": {
        "m_Id": "8ab83206be4041d4b4a8b05eaa8e55de"
    },
    "m_Name": "Multiply",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1096.000244140625,
            "y": 875.5000610351563,
            "width": 208.0,
            "height": 302.00018310546877
        }
    },
    "m_Slots": [
        {
            "m_Id": "428ec33eb87742c6a770c9d94dfb7037"
        },
        {
            "m_Id": "566fee12e0544a30bfc40f03f0bbcfb9"
        },
        {
            "m_Id": "21f47d1fe52e4606bd00efa71c04312d"
        }
    ],
    "synonyms": [
        "multiplication",
        "times",
        "x"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "5cc566916b794649a5f0a749d8bf6bd1",
    "m_Title": "",
    "m_Content": "The red channel creates a mask in the positive X direction.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1769.0001220703125,
        "y": 75.0,
        "width": 200.0,
        "height": 100.00000762939453
    },
    "m_Group": {
        "m_Id": "74744a6759794646bd5d8b833844b353"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "5e0f455c6e4e49d6aca8b1bee27b55bf",
    "m_Id": 0,
    "m_DisplayName": "M0",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "M0",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "5f62ebdd9f4b466f8e54a2ff250b49d1",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"d7aa8e05f3b6118478e7caf71b9232c2\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 3
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "661a4d8209e744709a170a8e2cf6442d",
    "m_Id": 6,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleTexture2DNode",
    "m_ObjectId": "6639511b8bdf49058aac233beac69f4c",
    "m_Group": {
        "m_Id": "8ab83206be4041d4b4a8b05eaa8e55de"
    },
    "m_Name": "Sample Texture 2D",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1330.0003662109375,
            "y": 525.0000610351563,
            "width": 208.0001220703125,
            "height": 337.00006103515627
        }
    },
    "m_Slots": [
        {
            "m_Id": "d5fbb1ab9d834b598a63d9200d9b8a10"
        },
        {
            "m_Id": "e56b0b3936224c3da44b1a79fe1e24c0"
        },
        {
            "m_Id": "a492e354b36b4248a1d30760df78120e"
        },
        {
            "m_Id": "661a4d8209e744709a170a8e2cf6442d"
        },
        {
            "m_Id": "0dc70e4ccca34c198cda8e6d8cd7e412"
        },
        {
            "m_Id": "5f62ebdd9f4b466f8e54a2ff250b49d1"
        },
        {
            "m_Id": "9243f2d4fb094ad6abc06ea7090ca369"
        },
        {
            "m_Id": "785b75a8c4af49b8b655153b650e2c25"
        }
    ],
    "synonyms": [
        "tex2d"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_TextureType": 1,
    "m_NormalMapSpace": 0,
    "m_EnableGlobalMipBias": true,
    "m_MipSamplingMode": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "6bc7bd98c57d45d98a6a993c15a1acb6"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "6d53561541314905b71ad8a517046b5a",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Alpha",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "70ae24b77dcd426ebe550e1edb05ff1b"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Alpha"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "6d585ae23ba04e378a971a811671183d",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "70ae24b77dcd426ebe550e1edb05ff1b",
    "m_Id": 0,
    "m_DisplayName": "Alpha",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Alpha",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "74744a6759794646bd5d8b833844b353",
    "m_Title": "Direction Masks",
    "m_Position": {
        "x": -2448.5,
        "y": -78.49989318847656
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "7814bbc95dcb4a629e741c79bc1c5183",
    "m_Id": 0,
    "m_DisplayName": "Direction",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Direction",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "785b75a8c4af49b8b655153b650e2c25",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "78d596b2bef24586b5972029228bf574",
    "m_Id": 1,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "7f36ee2a04e94130a8b1df5d7837f7ad",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "81e3e84cb28741f9908e75a557848747",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.ViewDirectionNode",
    "m_ObjectId": "8355682a52464812a5fed28ea6c0b022",
    "m_Group": {
        "m_Id": "c9cbff2e7c4540148c993f87acac8c06"
    },
    "m_Name": "View Direction",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1512.000244140625,
            "y": 111.5000228881836,
            "width": 206.0,
            "height": 130.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "9aa6da708cfa4c629c6209cd790b2364"
        }
    ],
    "synonyms": [
        "eye direction"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.ShaderGraph.TransformNode",
    "m_ObjectId": "8901eefa5f4a49f2bb2ee41e1594aca7",
    "m_Group": {
        "m_Id": "8ab83206be4041d4b4a8b05eaa8e55de"
    },
    "m_Name": "Transform",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1096.000244140625,
            "y": 525.0000610351563,
            "width": 212.50006103515626,
            "height": 340.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "03af90f7b13f460fb86bd93a1c69e79a"
        },
        {
            "m_Id": "56e435bcfd1449aab5efd8633b7ab5da"
        }
    ],
    "synonyms": [
        "world",
        "tangent",
        "object",
        "view",
        "screen",
        "convert"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Conversion": {
        "from": 3,
        "to": 2
    },
    "m_ConversionType": 2,
    "m_Normalize": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "8ab83206be4041d4b4a8b05eaa8e55de",
    "m_Title": "Construct A Transform Matrix",
    "m_Position": {
        "x": -1534.000244140625,
        "y": 466.4999694824219
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "9243f2d4fb094ad6abc06ea7090ca369",
    "m_Id": 2,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "961f555ad6924e20a99fb1d3333f75e8",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "96c7a1f7d6004df4bb9d0e67369b8c24",
    "m_Title": "Normal Vector Node",
    "m_Content": "The Normal Vector node brings the mesh's vertex normal into the shader.  The vertex normal is a vector that indicates the direction that the surface of the mesh is facing.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1142.5001220703125,
        "y": -322.0000305175781,
        "width": 214.00006103515626,
        "height": 121.62142944335938
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "99419abd4ebf49049974e0a2f860debe",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "9aa13176b2d743e7b21e00010b1d3ed4",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.BaseColor",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "267ca60077cc4f9e92e37e7206b0c76f"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.BaseColor"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "9aa6da708cfa4c629c6209cd790b2364",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalVectorNode",
    "m_ObjectId": "9bcb6c3298be4dbc89315717546abbd8",
    "m_Group": {
        "m_Id": "c9cbff2e7c4540148c993f87acac8c06"
    },
    "m_Name": "Normal Vector",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1512.000244140625,
            "y": -18.99998664855957,
            "width": 206.0,
            "height": 130.50001525878907
        }
    },
    "m_Slots": [
        {
            "m_Id": "ae71865dfb83485cba36978ae3e892f4"
        }
    ],
    "synonyms": [
        "surface direction"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "9d17c833e5ed489a9945b44c82b0f257",
    "m_Title": "",
    "m_Content": "The blue channel creates a mask in the positive Z direction. (It's on the back side of the preview sphere so it's not visible.)",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1771.0001220703125,
        "y": 636.0000610351563,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "74744a6759794646bd5d8b833844b353"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "a0a10696e4b742d9bf0a8a11a646b3dc",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Tangent",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "0460048e16964e44b953a3b348b3e3d9"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Tangent"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "a1954820575149989443059b4741cab1",
    "m_Title": "",
    "m_Content": "Take a look at the Dot Product example to learn more about that node.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -460.5000305175781,
        "y": 367.5000305175781,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "ec48d68701394d00ada30241f09ded9b"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a492e354b36b4248a1d30760df78120e",
    "m_Id": 5,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "a829a2a163da4ad792555b99cc71a61c",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "ae71865dfb83485cba36978ae3e892f4",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "b68aee0764dc472a88a48dee075c5576",
    "m_Title": "",
    "m_Content": "Using our constructed matrix, we get the exact same results as we get with the Transform node.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1088.0001220703125,
        "y": 1180.5001220703125,
        "width": 200.00006103515626,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "8ab83206be4041d4b4a8b05eaa8e55de"
    }
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInTarget",
    "m_ObjectId": "c0ad5e3747e249c58ab284bf9604e712",
    "m_ActiveSubTarget": {
        "m_Id": "0d41a5a5bcfd45f7ba612ccbd95e6b42"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZWriteControl": 0,
    "m_ZTestMode": 4,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CustomEditorGUI": ""
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PreviewNode",
    "m_ObjectId": "c1c96de999a44f6b95aab1bce9b7a97f",
    "m_Group": {
        "m_Id": "74744a6759794646bd5d8b833844b353"
    },
    "m_Name": "Preview",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1999.4998779296875,
            "y": 536.0000610351563,
            "width": 208.0001220703125,
            "height": 278.00006103515627
        }
    },
    "m_Slots": [
        {
            "m_Id": "dc64f8316ed348a88056ea6fde414c0f"
        },
        {
            "m_Id": "dd69d0e40bc6414f9a763f0ec03bdaaa"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Width": 208.0,
    "m_Height": 208.0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "c3ff506ce8714b5db847c4d7efc3fb3f",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Matrix4MaterialSlot",
    "m_ObjectId": "c56905579acf45b5a3d4573ba0c31ace",
    "m_Id": 4,
    "m_DisplayName": "4x4",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "4x4",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalUnlitSubTarget",
    "m_ObjectId": "c5ea2ef662644b4cb52d09351c3805d7"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalVectorNode",
    "m_ObjectId": "c72f861f8eea40998e7539e7bdd1ad2d",
    "m_Group": {
        "m_Id": "8ab83206be4041d4b4a8b05eaa8e55de"
    },
    "m_Name": "Normal Vector",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1509.000244140625,
            "y": 1135.5001220703125,
            "width": 206.0,
            "height": 130.5001220703125
        }
    },
    "m_Slots": [
        {
            "m_Id": "7f36ee2a04e94130a8b1df5d7837f7ad"
        }
    ],
    "synonyms": [
        "surface direction"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "c7cc80f90cc54b3a98d0d4fcf3f93ed3",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalVectorNode",
    "m_ObjectId": "c875373bfc6848eb9ccf036c2c39d53c",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Normal Vector",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1355.499755859375,
            "y": -325.9999694824219,
            "width": 206.0,
            "height": 130.49998474121095
        }
    },
    "m_Slots": [
        {
            "m_Id": "f3f5042260084299b30d6cf9f92f1a7b"
        }
    ],
    "synonyms": [
        "surface direction"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "c9cbff2e7c4540148c993f87acac8c06",
    "m_Title": "Camera Facing Mask or Edge Mask",
    "m_Position": {
        "x": -1537.000244140625,
        "y": -77.4999771118164
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "cecd3f0a97f147adb0d956681fc86865",
    "m_Id": 4,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "cf0830aa0c804808a26bf8abf278fdf7",
    "m_Title": "",
    "m_Content": "If we add a One Minus node, the effect is inverted and we get white on the edges.  This is often called a Fresnel effect.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1021.0000610351563,
        "y": 333.0000305175781,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "c9cbff2e7c4540148c993f87acac8c06"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "d0cf45e2103f413885c3fdf7e70d5f91",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "d1f4819b1a904848a4e63d373ff6d42d",
    "m_Title": "",
    "m_Content": "You can use the individual channels of the Normal Vector as directional masks.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -2348.500244140625,
        "y": 93.50001525878906,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "74744a6759794646bd5d8b833844b353"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "d55f0bcb979848a9bf9b61f006fc2347",
    "m_Id": 0,
    "m_DisplayName": "Emission",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Emission",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 1,
    "m_DefaultColor": {
        "r": 0.0,
        "g": 0.0,
        "b": 0.0,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DotProductNode",
    "m_ObjectId": "d5844f66c7d344cf8180f230ac85b1eb",
    "m_Group": {
        "m_Id": "c9cbff2e7c4540148c993f87acac8c06"
    },
    "m_Name": "Dot Product",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1259.000244140625,
            "y": 52.5000114440918,
            "width": 208.0,
            "height": 302.0000305175781
        }
    },
    "m_Slots": [
        {
            "m_Id": "439f15797b414c24b111e38771686eb3"
        },
        {
            "m_Id": "42a81945b09a4a8d87c73c368e23f05c"
        },
        {
            "m_Id": "961f555ad6924e20a99fb1d3333f75e8"
        }
    ],
    "synonyms": [
        "scalar product"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "d5fbb1ab9d834b598a63d9200d9b8a10",
    "m_Id": 0,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "d80f1076ccf14ca8b9e3362a7074b862",
    "m_Title": "",
    "m_Content": "Remember that the ranges of values go from -1 to 1, so the back areas in the previews have negative values.  If you want to prevent the data from going into the negative, just add a saturate node.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -2318.500244140625,
        "y": 647.0000610351563,
        "width": 200.0,
        "height": 116.5
    },
    "m_Group": {
        "m_Id": "74744a6759794646bd5d8b833844b353"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "db539324f18a4a3e95b855eb8454a2dd",
    "m_Title": "",
    "m_Content": "A dot product between the View Direction node and the Normal Vector gives us a mask that's white when the surface is pointing toward the camera, and black when the surface is perpendicular to the camera.  ",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1509.5001220703125,
        "y": 263.5000305175781,
        "width": 200.0,
        "height": 138.5
    },
    "m_Group": {
        "m_Id": "c9cbff2e7c4540148c993f87acac8c06"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "dc64f8316ed348a88056ea6fde414c0f",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "dd69d0e40bc6414f9a763f0ec03bdaaa",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalVectorNode",
    "m_ObjectId": "df75c7f767f248f792074f0005147841",
    "m_Group": {
        "m_Id": "ec48d68701394d00ada30241f09ded9b"
    },
    "m_Name": "Normal Vector",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -763.0000610351563,
            "y": -18.0,
            "width": 205.9998779296875,
            "height": 130.50001525878907
        }
    },
    "m_Slots": [
        {
            "m_Id": "6d585ae23ba04e378a971a811671183d"
        }
    ],
    "synonyms": [
        "surface direction"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "e0a617a469bd4af7819808304a698b45",
    "m_Title": "",
    "m_Content": "Normally, we do matrix transformations with the Transform node like this ->\n\nBut we can also use the Tangent, Bitangent, and Normal vectors like this to do the same thing:\n         |\n        V",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1507.0001220703125,
        "y": 711.5000610351563,
        "width": 164.5,
        "height": 160.0
    },
    "m_Group": {
        "m_Id": "8ab83206be4041d4b4a8b05eaa8e55de"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SplitNode",
    "m_ObjectId": "e2169fdce2e9460a933252aaa6fc2a88",
    "m_Group": {
        "m_Id": "74744a6759794646bd5d8b833844b353"
    },
    "m_Name": "Split",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -2217.5,
            "y": 231.5000762939453,
            "width": 119.0,
            "height": 124.99992370605469
        }
    },
    "m_Slots": [
        {
            "m_Id": "e957678c6e244e01866723338ecd0b32"
        },
        {
            "m_Id": "78d596b2bef24586b5972029228bf574"
        },
        {
            "m_Id": "ec5cc72dbf944cd4bc536dd732f1f1ce"
        },
        {
            "m_Id": "4b2a0e9d3574419d8041647cda8eca6a"
        },
        {
            "m_Id": "cecd3f0a97f147adb0d956681fc86865"
        }
    ],
    "synonyms": [
        "separate"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Matrix3MaterialSlot",
    "m_ObjectId": "e224f85eef9545adb2e75fe536ead8b1",
    "m_Id": 5,
    "m_DisplayName": "3x3",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "3x3",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "e37411e3967747828fce131baaddd76b",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PreviewNode",
    "m_ObjectId": "e3fbea880e79437aaf8f2bbe696ec436",
    "m_Group": {
        "m_Id": "74744a6759794646bd5d8b833844b353"
    },
    "m_Name": "Preview",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1999.4998779296875,
            "y": -19.999958038330079,
            "width": 208.0001220703125,
            "height": 277.9999694824219
        }
    },
    "m_Slots": [
        {
            "m_Id": "13407309844f43e18bf97aada220e819"
        },
        {
            "m_Id": "a829a2a163da4ad792555b99cc71a61c"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Width": 208.0,
    "m_Height": 208.0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "e413ea42de984df88720f07f4ede3196",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "e55ce073b8fa4200b9a50349ecc977ab",
    "m_Id": 0,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e56b0b3936224c3da44b1a79fe1e24c0",
    "m_Id": 4,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "e957678c6e244e01866723338ecd0b32",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MainLightDirectionNode",
    "m_ObjectId": "ec04f143d0e84556a9063c904aadb425",
    "m_Group": {
        "m_Id": "ec48d68701394d00ada30241f09ded9b"
    },
    "m_Name": "Main Light Direction",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -763.0000610351563,
            "y": 128.5000457763672,
            "width": 159.49993896484376,
            "height": 76.99998474121094
        }
    },
    "m_Slots": [
        {
            "m_Id": "7814bbc95dcb4a629e741c79bc1c5183"
        }
    ],
    "synonyms": [
        "sun"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "ec48d68701394d00ada30241f09ded9b",
    "m_Title": "Diffuse Lighting",
    "m_Position": {
        "x": -788.000244140625,
        "y": -76.5
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ec5cc72dbf944cd4bc536dd732f1f1ce",
    "m_Id": 2,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "eec1792020614f0db748aa7cb3b5cd1f",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "f3f5042260084299b30d6cf9f92f1a7b",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "f44f8bfc81da453eb910c0928cd0bacc",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Emission",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "d55f0bcb979848a9bf9b61f006fc2347"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Emission"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "f75931a2ae414be599565d7bdc570259",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "fac931c8c6684a6ba454470637cff9ab",
    "m_Id": 3,
    "m_DisplayName": "M3",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "M3",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.OneMinusNode",
    "m_ObjectId": "fc73fe5fafa5476aa079d07ffe0d5bff",
    "m_Group": {
        "m_Id": "c9cbff2e7c4540148c993f87acac8c06"
    },
    "m_Name": "One Minus",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1028.5001220703125,
            "y": 52.5000114440918,
            "width": 208.00006103515626,
            "height": 278.0000305175781
        }
    },
    "m_Slots": [
        {
            "m_Id": "4343bcbbc0e54b6a97696686f4da9cf3"
        },
        {
            "m_Id": "e37411e3967747828fce131baaddd76b"
        }
    ],
    "synonyms": [
        "complement",
        "invert",
        "opposite"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "ff459aa4269e4b4b88f60752eb1d602f",
    "m_Id": 2,
    "m_DisplayName": "M2",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "M2",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

