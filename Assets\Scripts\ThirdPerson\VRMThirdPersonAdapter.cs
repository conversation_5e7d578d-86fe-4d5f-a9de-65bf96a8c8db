using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using UniVRM10;
using UniHumanoid;
using VRoidFaceCustomization;
using StarterAssets;

#if ENABLE_INPUT_SYSTEM
using UnityEngine.InputSystem;
#endif

namespace VRoidFaceCustomization.ThirdPerson
{
    /// <summary>
    /// VRM第三人称适配器 - 核心集成组件
    /// 负责将VRM系统与Starter Assets第三人称控制器完美集成
    /// </summary>
    public class VRMThirdPersonAdapter : MonoBehaviour
    {
        [Header("VRM系统引用")]
        [SerializeField] private Vrm10Instance vrmInstance;
        [SerializeField] private VRM10UnifiedManager vrmManager;
        [SerializeField] private VRM10FaceController faceController;
        [SerializeField] private VRM10ClothBinder clothBinder;
        
        [Header("第三人称控制器组件")]
        [SerializeField] private ThirdPersonController thirdPersonController;
        [SerializeField] private StarterAssetsInputs starterInputs;
        [SerializeField] private CharacterController characterController;
        [SerializeField] private Animator animator;
        
        #if ENABLE_INPUT_SYSTEM
        [SerializeField] private PlayerInput playerInput;
        #endif
        
        [Header("配置参数")]
        [SerializeField] private VRMControllerSettings controllerSettings;
        [SerializeField] private bool autoInitializeOnStart = true;
        [SerializeField] private bool enableDebugLogs = true;
        
        [Header("状态监控")]
        [SerializeField] private bool isVRMSystemReady = false;
        [SerializeField] private bool isThirdPersonControllerReady = false;
        [SerializeField] private bool isFullyIntegrated = false;
        
        // 事件系统
        public System.Action OnIntegrationComplete;
        public System.Action<string> OnIntegrationError;
        
        void Start()
        {
            if (autoInitializeOnStart)
            {
                StartCoroutine(InitializeIntegration());
            }
        }
        
        /// <summary>
        /// 初始化VRM与第三人称控制器集成
        /// </summary>
        public IEnumerator InitializeIntegration()
        {
            LogDebug("🚀 开始VRM第三人称控制器集成...");
            
            // 阶段1：查找和验证VRM组件
            yield return StartCoroutine(FindAndValidateVRMComponents());
            
            // 阶段2：确保VRM组件完整性
            yield return StartCoroutine(EnsureVRMComponentsIntegrity());
            
            // 阶段3：添加和配置第三人称控制器
            yield return StartCoroutine(SetupThirdPersonController());
            
            // 阶段4：验证集成完整性
            yield return StartCoroutine(ValidateIntegration());
            
            // 阶段5：最终初始化
            FinalizeIntegration();
            
            LogDebug("✅ VRM第三人称控制器集成完成！");
        }
        
        /// <summary>
        /// 查找和验证VRM组件
        /// </summary>
        private IEnumerator FindAndValidateVRMComponents()
        {
            LogDebug("🔍 查找VRM组件...");
            
            // 查找VRM实例
            if (vrmInstance == null)
            {
                vrmInstance = GetComponent<Vrm10Instance>();
                if (vrmInstance == null)
                {
                    vrmInstance = FindObjectOfType<Vrm10Instance>();
                }
            }
            
            if (vrmInstance != null)
            {
                LogDebug($"✅ 找到VRM实例: {vrmInstance.name}");
                
                // 查找统一管理器
                if (vrmManager == null)
                {
                    vrmManager = FindObjectOfType<VRM10UnifiedManager>();
                }
                
                // 查找面部控制器
                if (faceController == null)
                {
                    faceController = vrmInstance.GetComponent<VRM10FaceController>();
                    if (faceController == null)
                    {
                        faceController = FindObjectOfType<VRM10FaceController>();
                    }
                }
                
                // 查找服装绑定器
                if (clothBinder == null)
                {
                    clothBinder = vrmInstance.GetComponent<VRM10ClothBinder>();
                    if (clothBinder == null)
                    {
                        clothBinder = FindObjectOfType<VRM10ClothBinder>();
                    }
                }
                
                LogDebug($"VRM组件查找结果:");
                LogDebug($"  统一管理器: {(vrmManager != null ? "✅" : "❌")}");
                LogDebug($"  面部控制器: {(faceController != null ? "✅" : "❌")}");
                LogDebug($"  服装绑定器: {(clothBinder != null ? "✅" : "❌")}");
            }
            else
            {
                LogDebug("❌ 未找到VRM实例");
            }
            
            yield return new WaitForSeconds(0.1f);
        }
        
        /// <summary>
        /// 确保VRM组件完整性
        /// </summary>
        private IEnumerator EnsureVRMComponentsIntegrity()
        {
            LogDebug("🔧 确保VRM组件完整性...");
            
            if (vrmInstance == null)
            {
                LogDebug("❌ VRM实例不存在，无法确保组件完整性");
                yield break;
            }
            
            GameObject vrmObject = vrmInstance.gameObject;
            
            // 确保面部控制器存在
            if (faceController == null)
            {
                faceController = vrmObject.GetComponent<VRM10FaceController>();
                if (faceController == null)
                {
                    faceController = vrmObject.AddComponent<VRM10FaceController>();
                    LogDebug("✅ 添加面部控制器");
                    
                    // 初始化面部控制器
                    yield return new WaitForSeconds(0.1f);
                    if (!faceController.IsInitialized())
                    {
                        faceController.InitializeFaceController();
                        LogDebug("🔧 初始化面部控制器");
                    }
                }
            }
            
            // 确保服装绑定器存在
            if (clothBinder == null)
            {
                clothBinder = vrmObject.GetComponent<VRM10ClothBinder>();
                if (clothBinder == null)
                {
                    clothBinder = vrmObject.AddComponent<VRM10ClothBinder>();
                    LogDebug("✅ 添加服装绑定器");
                    
                    // 服装绑定器会在Awake和Start中自动初始化
                    yield return new WaitForSeconds(0.1f);
                    LogDebug("🔧 服装绑定器已添加，将自动初始化");
                }
            }
            
            // 确保统一管理器关联
            if (vrmManager != null && vrmInstance != null)
            {
                // VRM10UnifiedManager会自动查找和关联VRM实例
                // 我们只需要确保它知道当前的VRM实例
                LogDebug("✅ 统一管理器将自动关联VRM实例");
            }
            
            isVRMSystemReady = true;
            LogDebug("✅ VRM系统准备就绪");
            
            yield return new WaitForSeconds(0.1f);
        }
        
        /// <summary>
        /// 设置第三人称控制器
        /// </summary>
        private IEnumerator SetupThirdPersonController()
        {
            LogDebug("🎮 设置第三人称控制器...");
            
            // 1. 确保基础组件存在
            EnsureBaseComponents();
            
            // 2. 添加第三人称控制器
            AddThirdPersonController();
            
            // 3. 配置输入系统
            ConfigureInputSystem();
            
            // 4. 设置摄像机目标
            SetupCameraTarget();
            
            // 5. 配置控制器参数
            ConfigureControllerParameters();
            
            isThirdPersonControllerReady = true;
            LogDebug("✅ 第三人称控制器准备就绪");
            
            yield return new WaitForSeconds(0.1f);
        }
        
        /// <summary>
        /// 确保基础组件存在
        /// </summary>
        private void EnsureBaseComponents()
        {
            // 确保CharacterController存在
            if (characterController == null)
            {
                characterController = GetComponent<CharacterController>();
                if (characterController == null)
                {
                    characterController = gameObject.AddComponent<CharacterController>();
                    LogDebug("✅ 添加CharacterController");
                }
            }
            
            // 配置CharacterController参数
            if (controllerSettings != null)
            {
                characterController.height = controllerSettings.height;
                characterController.radius = controllerSettings.radius;
                characterController.center = controllerSettings.center;
            }
            else
            {
                // 使用默认参数
                characterController.height = 1.8f;
                characterController.radius = 0.3f;
                characterController.center = new Vector3(0, 0.9f, 0);
            }
            
            // 确保Animator存在
            if (animator == null)
            {
                animator = GetComponent<Animator>();
                if (animator == null)
                {
                    animator = gameObject.AddComponent<Animator>();
                    LogDebug("✅ 添加Animator");
                    
                    // 尝试从VRM实例获取或创建Avatar
                    if (vrmInstance != null)
                    {
                        var humanoid = vrmInstance.GetComponent<Humanoid>();
                        if (humanoid != null)
                        {
                            try
                            {
                                // 创建Avatar
                                var avatar = humanoid.CreateAvatar();
                                if (avatar != null)
                                {
                                    animator.avatar = avatar;
                                    LogDebug("🔧 设置Animator Avatar");
                                }
                                else
                                {
                                    LogDebug("⚠️ 无法创建Avatar");
                                }
                            }
                            catch (System.Exception e)
                            {
                                LogDebug($"⚠️ 创建Avatar失败: {e.Message}");
                            }
                        }
                        else
                        {
                            LogDebug("⚠️ 未找到Humanoid组件");
                        }
                    }

                    // 设置Animator Controller
                    SetupAnimatorController();
                }
            }
            
            // 确保StarterAssetsInputs存在
            if (starterInputs == null)
            {
                starterInputs = GetComponent<StarterAssetsInputs>();
                if (starterInputs == null)
                {
                    starterInputs = gameObject.AddComponent<StarterAssetsInputs>();
                    LogDebug("✅ 添加StarterAssetsInputs");
                }
            }
        }
        
        /// <summary>
        /// 添加第三人称控制器
        /// </summary>
        private void AddThirdPersonController()
        {
            if (thirdPersonController == null)
            {
                thirdPersonController = GetComponent<ThirdPersonController>();
                if (thirdPersonController == null)
                {
                    thirdPersonController = gameObject.AddComponent<ThirdPersonController>();
                    LogDebug("✅ 添加ThirdPersonController");

                    // 配置ThirdPersonController的关键引用
                    ConfigureThirdPersonControllerReferences();
                }
            }
        }

        /// <summary>
        /// 配置第三人称控制器的引用
        /// </summary>
        private void ConfigureThirdPersonControllerReferences()
        {
            if (thirdPersonController == null) return;

            // 设置CinemachineCameraTarget引用
            var cameraTarget = transform.Find("CinemachineCameraTarget");
            if (cameraTarget != null)
            {
                // 使用反射设置CinemachineCameraTarget字段
                var field = typeof(StarterAssets.ThirdPersonController).GetField("CinemachineCameraTarget");
                if (field != null)
                {
                    field.SetValue(thirdPersonController, cameraTarget.gameObject);
                    LogDebug("🔧 设置CinemachineCameraTarget引用");
                }
            }

            // 配置基本参数
            if (controllerSettings != null)
            {
                thirdPersonController.MoveSpeed = controllerSettings.moveSpeed;
                thirdPersonController.SprintSpeed = controllerSettings.sprintSpeed;
                thirdPersonController.JumpHeight = controllerSettings.jumpHeight;
                thirdPersonController.GroundLayers = controllerSettings.groundLayers;
                LogDebug("🔧 配置控制器参数");
            }
        }
        
        /// <summary>
        /// 配置输入系统
        /// </summary>
        private void ConfigureInputSystem()
        {
            #if ENABLE_INPUT_SYSTEM
            if (playerInput == null)
            {
                playerInput = GetComponent<PlayerInput>();
                if (playerInput == null)
                {
                    playerInput = gameObject.AddComponent<PlayerInput>();
                    LogDebug("✅ 添加PlayerInput");
                }
            }
            
            // 配置Input Actions
            if (playerInput != null)
            {
                // 尝试加载Starter Assets的Input Actions
                string assetPath = controllerSettings?.inputActionsAssetPath ?? "StarterAssets.inputactions";
                var inputActions = Resources.Load<InputActionAsset>(assetPath);
                
                if (inputActions == null)
                {
                    // 尝试从Assets文件夹加载
                    inputActions = UnityEditor.AssetDatabase.LoadAssetAtPath<InputActionAsset>($"Assets/StarterAssets/InputSystem/{assetPath}");
                }
                
                if (inputActions != null)
                {
                    playerInput.actions = inputActions;
                    playerInput.defaultActionMap = controllerSettings?.defaultActionMap ?? "Player";
                    LogDebug("🔧 配置Input Actions");
                }
                else
                {
                    LogDebug("⚠️ 未找到Input Actions资源");
                }
            }
            #else
            LogDebug("⚠️ Input System未启用");
            #endif
        }
        
        /// <summary>
        /// 设置摄像机目标
        /// </summary>
        private void SetupCameraTarget()
        {
            // 查找或创建CinemachineCameraTarget
            Transform cameraTarget = transform.Find("CinemachineCameraTarget");
            if (cameraTarget == null)
            {
                GameObject targetObj = new GameObject("CinemachineCameraTarget");
                targetObj.transform.SetParent(transform);
                
                float targetHeight = controllerSettings?.cameraTargetHeight ?? 1.375f;
                targetObj.transform.localPosition = new Vector3(0, targetHeight, 0);
                
                LogDebug("✅ 创建CinemachineCameraTarget");
            }
        }
        
        /// <summary>
        /// 配置控制器参数
        /// </summary>
        private void ConfigureControllerParameters()
        {
            if (thirdPersonController != null && controllerSettings != null)
            {
                thirdPersonController.MoveSpeed = controllerSettings.moveSpeed;
                thirdPersonController.SprintSpeed = controllerSettings.sprintSpeed;
                thirdPersonController.JumpHeight = controllerSettings.jumpHeight;
                thirdPersonController.GroundLayers = controllerSettings.groundLayers;

                LogDebug("🔧 配置控制器参数");
            }
        }

        /// <summary>
        /// 设置Animator Controller
        /// </summary>
        private void SetupAnimatorController()
        {
            if (animator == null) return;

            // 尝试加载Starter Assets的Animator Controller
            var animatorController = Resources.Load<RuntimeAnimatorController>("ThirdPersonAnimatorController");

            if (animatorController == null)
            {
                // 尝试从Assets文件夹加载
                #if UNITY_EDITOR
                animatorController = UnityEditor.AssetDatabase.LoadAssetAtPath<RuntimeAnimatorController>(
                    "Assets/StarterAssets/ThirdPersonController/Animations/ThirdPersonAnimatorController.controller");
                #endif
            }

            if (animatorController != null)
            {
                animator.runtimeAnimatorController = animatorController;
                LogDebug("🔧 设置Animator Controller");
            }
            else
            {
                LogDebug("⚠️ 未找到ThirdPersonAnimatorController，角色可能无法播放动画");

                // 创建一个基本的Animator Controller作为备用
                CreateBasicAnimatorController();
            }
        }

        /// <summary>
        /// 创建基本的Animator Controller
        /// </summary>
        private void CreateBasicAnimatorController()
        {
            if (animator == null) return;

            #if UNITY_EDITOR
            // 在编辑器模式下创建Animator Controller
            var controller = new UnityEditor.Animations.AnimatorController();
            controller.name = "BasicVRMController";

            // 添加基本参数
            controller.AddParameter("Speed", AnimatorControllerParameterType.Float);
            controller.AddParameter("Grounded", AnimatorControllerParameterType.Bool);
            controller.AddParameter("Jump", AnimatorControllerParameterType.Bool);
            controller.AddParameter("FreeFall", AnimatorControllerParameterType.Bool);
            controller.AddParameter("MotionSpeed", AnimatorControllerParameterType.Float);

            // 创建基本状态
            var rootStateMachine = controller.layers[0].stateMachine;
            var idleState = rootStateMachine.AddState("Idle");

            animator.runtimeAnimatorController = controller;
            LogDebug("🔧 创建基本Animator Controller");
            #else
            LogDebug("⚠️ 运行时无法创建Animator Controller，请在编辑器中设置");
            #endif
        }
        
        /// <summary>
        /// 验证集成完整性
        /// </summary>
        private IEnumerator ValidateIntegration()
        {
            LogDebug("🔍 验证集成完整性...");
            
            bool vrmValid = ValidateVRMSystem();
            bool controllerValid = ValidateThirdPersonController();
            
            isFullyIntegrated = vrmValid && controllerValid;
            
            if (isFullyIntegrated)
            {
                LogDebug("✅ 集成验证通过");
            }
            else
            {
                LogDebug("❌ 集成验证失败");
                LogDebug($"   VRM系统: {(vrmValid ? "✅" : "❌")}");
                LogDebug($"   第三人称控制器: {(controllerValid ? "✅" : "❌")}");
            }
            
            yield return new WaitForSeconds(0.1f);
        }
        
        /// <summary>
        /// 验证VRM系统
        /// </summary>
        private bool ValidateVRMSystem()
        {
            return vrmInstance != null && 
                   faceController != null && 
                   clothBinder != null;
        }
        
        /// <summary>
        /// 验证第三人称控制器
        /// </summary>
        private bool ValidateThirdPersonController()
        {
            bool hasController = thirdPersonController != null;
            bool hasCharacterController = characterController != null;
            bool hasInputs = starterInputs != null;
            bool hasAnimator = animator != null;
            
            #if ENABLE_INPUT_SYSTEM
            bool hasPlayerInput = playerInput != null;
            return hasController && hasCharacterController && hasInputs && hasAnimator && hasPlayerInput;
            #else
            return hasController && hasCharacterController && hasInputs && hasAnimator;
            #endif
        }
        
        /// <summary>
        /// 最终初始化
        /// </summary>
        private void FinalizeIntegration()
        {
            if (isFullyIntegrated)
            {
                LogDebug("🎉 VRM第三人称控制器集成成功！");
                OnIntegrationComplete?.Invoke();
            }
            else
            {
                string error = "集成未完全成功，部分功能可能不可用";
                LogDebug($"⚠️ {error}");
                OnIntegrationError?.Invoke(error);
            }
        }
        
        /// <summary>
        /// 日志输出
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[VRMThirdPersonAdapter] {message}");
            }
        }
        
        // 公共接口
        public bool IsFullyIntegrated => isFullyIntegrated;
        public bool IsVRMSystemReady => isVRMSystemReady;
        public bool IsThirdPersonControllerReady => isThirdPersonControllerReady;
        
        /// <summary>
        /// 手动触发集成
        /// </summary>
        [ContextMenu("手动触发集成")]
        public void TriggerIntegration()
        {
            StartCoroutine(InitializeIntegration());
        }
        
        /// <summary>
        /// 获取集成状态报告
        /// </summary>
        public string GetIntegrationStatusReport()
        {
            return $"VRM第三人称适配器状态:\n" +
                   $"- VRM系统: {(isVRMSystemReady ? "✅" : "❌")}\n" +
                   $"- 第三人称控制器: {(isThirdPersonControllerReady ? "✅" : "❌")}\n" +
                   $"- 完全集成: {(isFullyIntegrated ? "✅" : "❌")}";
        }
    }
}
