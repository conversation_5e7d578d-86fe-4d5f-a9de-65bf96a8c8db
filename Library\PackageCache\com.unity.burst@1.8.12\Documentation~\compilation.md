# Compilation

Burst compiles your code in different ways, depending on its context. You can also change the way that the compiler behaves when compiling code.

|**Topic**|**Description**|
|---|---|
|[Compilation overview](compilation-overview.md)| Understand the different compilation types.|
[Synchronous compilation](compilation-synchronous.md)| Understand synchronous compilation.|
|[BurstCompile attribute](compilation-burstcompile.md)|Customize the BurstCompile attribute to improve performance.|
|[BurstDiscard attribute](compilation-burstdiscard.md)| Use the BurstDiscard attribute to select which portions of code to compile.|
|[Generic jobs](compilation-generic-jobs.md)|Understand how Burst compiles jobs.|
|[Compilation warnings](compilation-warnings.md)|Fix common compilation warnings.|

## Additional resources

* [C# language support](csharp-language-support.md)