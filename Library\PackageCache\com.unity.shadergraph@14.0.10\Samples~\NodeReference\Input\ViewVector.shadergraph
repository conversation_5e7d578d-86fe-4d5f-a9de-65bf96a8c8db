{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "85bfb06210dd471bbd8269b5c70d605e",
    "m_Properties": [
        {
            "m_Id": "abd0c7a1c344469e9c8cf8ac8823d2df"
        },
        {
            "m_Id": "009b99837e1a4ffb92ac1b290148e6f7"
        }
    ],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "e40d238ad26243c3b8658facfd122aa6"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "256528849f3b4b60aeb128c6cc61b5d3"
        },
        {
            "m_Id": "4357dc94eeae42fdb8127bd345eb4684"
        },
        {
            "m_Id": "08f1a591b88f4e298f38a3a06e636675"
        },
        {
            "m_Id": "29b9f1e5a70e4df58bef8083041e8c43"
        },
        {
            "m_Id": "178241276cc4401b807885c4bb28ba75"
        },
        {
            "m_Id": "2c8bbc22cfff495cab8bd8c5820c1f69"
        },
        {
            "m_Id": "075e693203b64ae995165078dd28954f"
        },
        {
            "m_Id": "39ce0123e1314c57b31b0487cc2f2d2c"
        },
        {
            "m_Id": "b61be81c2cb7492e811c523934e4adab"
        },
        {
            "m_Id": "6d907e3b3433466290802602d1d5156d"
        },
        {
            "m_Id": "3722bc9066b14401b1ef072684403be7"
        },
        {
            "m_Id": "35ec4e561c4e4313adebef64e1e55111"
        },
        {
            "m_Id": "e12729ae91374afcba9a949f7be1a1a9"
        },
        {
            "m_Id": "25404f012f1948a79b5d8c72a8a13ab7"
        },
        {
            "m_Id": "d9c0a03dde6c46e4a112f8d1d1351dcb"
        },
        {
            "m_Id": "6a814d33d384412eb0f8fabfb52d5acb"
        },
        {
            "m_Id": "b72f8cc732864d10952c6f23ee3980c5"
        },
        {
            "m_Id": "7f4cde534020410fb306edafa24ae1bf"
        },
        {
            "m_Id": "b9389414d4c442e7af26f016dc4ec7df"
        }
    ],
    "m_GroupDatas": [
        {
            "m_Id": "0017903f73f04318a1d802686cad454b"
        },
        {
            "m_Id": "4d149e211c5547bc92a6614b019a8fbe"
        },
        {
            "m_Id": "7907559e62b84e51ab1d060749094c7f"
        }
    ],
    "m_StickyNoteDatas": [
        {
            "m_Id": "386b15c1dc1648baafb431bddfacec09"
        },
        {
            "m_Id": "0ab036a10ebc4fe28abbfa25b4a2b33c"
        },
        {
            "m_Id": "8ac252d54963466e9a40c19630bc3a30"
        },
        {
            "m_Id": "d26f5463833a4515aa4f058ed5cd276a"
        },
        {
            "m_Id": "9e52e1317e5b4675be9880007b4896aa"
        },
        {
            "m_Id": "b8a6d3f0b4c84905b9e6a482e4fb722d"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "075e693203b64ae995165078dd28954f"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "39ce0123e1314c57b31b0487cc2f2d2c"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "25404f012f1948a79b5d8c72a8a13ab7"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "d9c0a03dde6c46e4a112f8d1d1351dcb"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "2c8bbc22cfff495cab8bd8c5820c1f69"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "39ce0123e1314c57b31b0487cc2f2d2c"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "35ec4e561c4e4313adebef64e1e55111"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "e12729ae91374afcba9a949f7be1a1a9"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "6a814d33d384412eb0f8fabfb52d5acb"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "25404f012f1948a79b5d8c72a8a13ab7"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "6d907e3b3433466290802602d1d5156d"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "3722bc9066b14401b1ef072684403be7"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "b72f8cc732864d10952c6f23ee3980c5"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "d9c0a03dde6c46e4a112f8d1d1351dcb"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "e12729ae91374afcba9a949f7be1a1a9"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "25404f012f1948a79b5d8c72a8a13ab7"
                },
                "m_SlotId": 0
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": [
            {
                "m_Id": "256528849f3b4b60aeb128c6cc61b5d3"
            },
            {
                "m_Id": "4357dc94eeae42fdb8127bd345eb4684"
            },
            {
                "m_Id": "08f1a591b88f4e298f38a3a06e636675"
            }
        ]
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 200.0
        },
        "m_Blocks": [
            {
                "m_Id": "29b9f1e5a70e4df58bef8083041e8c43"
            },
            {
                "m_Id": "7f4cde534020410fb306edafa24ae1bf"
            },
            {
                "m_Id": "b9389414d4c442e7af26f016dc4ec7df"
            }
        ]
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Shader Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": ""
    },
    "m_ActiveTargets": [
        {
            "m_Id": "a7260a4d93514c17b7a288b45bf2e931"
        },
        {
            "m_Id": "8dc97c0d070041e78b0a4d45a88282d5"
        },
        {
            "m_Id": "70612408391f4688b8fb956853837bdf"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "0017903f73f04318a1d802686cad454b",
    "m_Title": "Manual Calculation",
    "m_Position": {
        "x": -2464.************,
        "y": 298.5
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "009b99837e1a4ffb92ac1b290148e6f7",
    "m_Guid": {
        "m_GuidSerialized": "7062c1d0-4d34-4713-a1ba-d82d847e773f"
    },
    "m_Name": "Mask Length",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "Mask Length",
    "m_DefaultReferenceName": "_Mask_Length",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": 0.0,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "056ce750f27c42938574248b48fb7eff",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.PositionNode",
    "m_ObjectId": "075e693203b64ae995165078dd28954f",
    "m_Group": {
        "m_Id": "0017903f73f04318a1d802686cad454b"
    },
    "m_Name": "Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -2439.************,
            "y": 434.0,
            "width": 206.0,
            "height": 130.50006103515626
        }
    },
    "m_Slots": [
        {
            "m_Id": "8c2c10727fe5470da9da725ef1bc7b9d"
        }
    ],
    "synonyms": [
        "location"
    ],
    "m_Precision": 1,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 4,
    "m_PositionSource": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "08f1a591b88f4e298f38a3a06e636675",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Tangent",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "c6046aa3999b4df1941340bfe394a13f"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Tangent"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "0ab036a10ebc4fe28abbfa25b4a2b33c",
    "m_Title": "",
    "m_Content": "You can create the same vector as the View Vector node by subtracting the Position in World Space from the Camera Position.  This is how the View Vector is generated.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -2429.000244140625,
        "y": 578.0000610351563,
        "width": 200.0,
        "height": 107.0
    },
    "m_Group": {
        "m_Id": "0017903f73f04318a1d802686cad454b"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "0e5e99bc57c04facb21ef9ecc23dadc6",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "0f522c71f1b042a3921963341b743a50",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalUnlitSubTarget",
    "m_ObjectId": "0fa9908ad6574e76a9fe9cf0a8df7576"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInUnlitSubTarget",
    "m_ObjectId": "10dfd6e4796b43c7be2d4d9fbda81244"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "1285c5a7763143eab3949087eab93d20",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "13150b589a7a4d00a466f44abf8a02e5",
    "m_Id": 1,
    "m_DisplayName": "Direction",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Direction",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "171b0ae692564c458cb0e14d536a6d68",
    "m_Id": 3,
    "m_DisplayName": "World Space Position",
    "m_SlotType": 0,
    "m_Hidden": true,
    "m_ShaderOutputName": "WorldSpacePosition",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ViewVectorNode",
    "m_ObjectId": "178241276cc4401b807885c4bb28ba75",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "View Vector",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1521.000244140625,
            "y": -72.50000762939453,
            "width": 208.0,
            "height": 314.5000305175781
        }
    },
    "m_Slots": [
        {
            "m_Id": "171b0ae692564c458cb0e14d536a6d68"
        },
        {
            "m_Id": "0f522c71f1b042a3921963341b743a50"
        }
    ],
    "synonyms": [
        "eye vector"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "23478e0a7d7a4a9596e844ffb869f8a7"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubtractNode",
    "m_ObjectId": "25404f012f1948a79b5d8c72a8a13ab7",
    "m_Group": {
        "m_Id": "7907559e62b84e51ab1d060749094c7f"
    },
    "m_Name": "Subtract",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -703.0000610351563,
            "y": 357.5000305175781,
            "width": 126.00018310546875,
            "height": 118.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "587601576dab49e0b94231f10746ea54"
        },
        {
            "m_Id": "292122f0af5f44c5a3eafc4e5d5564fe"
        },
        {
            "m_Id": "c026e8cdc6bd4dd2b7ec1ceabbd9f02e"
        }
    ],
    "synonyms": [
        "subtraction",
        "remove",
        "minus",
        "take away"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "256528849f3b4b60aeb128c6cc61b5d3",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "632259da20534eb68acafe232cefff67"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Position"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "292122f0af5f44c5a3eafc4e5d5564fe",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "2974d1cdf8844089b752ca5c28857012",
    "m_Id": 7,
    "m_DisplayName": "Height",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Height",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "2996f8c5464e4557aa2501de874f1eab",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "29b9f1e5a70e4df58bef8083041e8c43",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.BaseColor",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "cdbb46f907e14f27a7545940ee4852d5"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.BaseColor"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CameraNode",
    "m_ObjectId": "2c8bbc22cfff495cab8bd8c5820c1f69",
    "m_Group": {
        "m_Id": "0017903f73f04318a1d802686cad454b"
    },
    "m_Name": "Camera",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -2330.000244140625,
            "y": 357.0000305175781,
            "width": 96.5,
            "height": 76.99996948242188
        }
    },
    "m_Slots": [
        {
            "m_Id": "d5d064c27b274f9590aaca4e6ec4bd97"
        },
        {
            "m_Id": "13150b589a7a4d00a466f44abf8a02e5"
        },
        {
            "m_Id": "7046e346b2dd48dbbc1173abef53c6c2"
        },
        {
            "m_Id": "838a664943904a3caef7dc8311ab8402"
        },
        {
            "m_Id": "f5930090d69e433bad627f2d9c61ee2e"
        },
        {
            "m_Id": "37f9c22f244741bb8dcaf588cd3b9421"
        },
        {
            "m_Id": "4cb33ba6c40d4c01b76da146e0ab16f1"
        },
        {
            "m_Id": "2974d1cdf8844089b752ca5c28857012"
        }
    ],
    "synonyms": [
        "position",
        "direction",
        "orthographic",
        "near plane",
        "far plane",
        "width",
        "height"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ViewVectorNode",
    "m_ObjectId": "35ec4e561c4e4313adebef64e1e55111",
    "m_Group": {
        "m_Id": "7907559e62b84e51ab1d060749094c7f"
    },
    "m_Name": "View Vector",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1206.5,
            "y": 357.5000305175781,
            "width": 206.0,
            "height": 130.50006103515626
        }
    },
    "m_Slots": [
        {
            "m_Id": "9203a42fbe2a4c378af39d05336c73c2"
        },
        {
            "m_Id": "e1cb40ad4c6041f9881e3c6da2b6cef5"
        }
    ],
    "synonyms": [
        "eye vector"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.LengthNode",
    "m_ObjectId": "3722bc9066b14401b1ef072684403be7",
    "m_Group": {
        "m_Id": "4d149e211c5547bc92a6614b019a8fbe"
    },
    "m_Name": "Length",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1483.0001220703125,
            "y": 357.5000305175781,
            "width": 208.0,
            "height": 278.0000305175781
        }
    },
    "m_Slots": [
        {
            "m_Id": "2996f8c5464e4557aa2501de874f1eab"
        },
        {
            "m_Id": "1285c5a7763143eab3949087eab93d20"
        }
    ],
    "synonyms": [
        "measure"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "37f9c22f244741bb8dcaf588cd3b9421",
    "m_Id": 5,
    "m_DisplayName": "Z Buffer Sign",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Z Buffer Sign",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "386b15c1dc1648baafb431bddfacec09",
    "m_Title": "View Vector",
    "m_Content": "The View Vector is a line between the camera and the current vertex or pixel being rendered.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1306.0001220703125,
        "y": -68.50000762939453,
        "width": 200.0,
        "height": 69.79646301269531
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubtractNode",
    "m_ObjectId": "39ce0123e1314c57b31b0487cc2f2d2c",
    "m_Group": {
        "m_Id": "0017903f73f04318a1d802686cad454b"
    },
    "m_Name": "Subtract",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -2194.000244140625,
            "y": 375.0000305175781,
            "width": 208.0001220703125,
            "height": 301.9999694824219
        }
    },
    "m_Slots": [
        {
            "m_Id": "56df4e41053049918c38194b2c5276a4"
        },
        {
            "m_Id": "d9294be332944de48dd54130cbd75d07"
        },
        {
            "m_Id": "056ce750f27c42938574248b48fb7eff"
        }
    ],
    "synonyms": [
        "subtraction",
        "remove",
        "minus",
        "take away"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "41c3134ea617494e8e71f5d3fdcffa22",
    "m_Id": 0,
    "m_DisplayName": "Alpha",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Alpha",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "4357dc94eeae42fdb8127bd345eb4684",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Normal",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "5ba9ff0c80f44f1684d1b2bdeb998f6b"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Normal"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "4cb33ba6c40d4c01b76da146e0ab16f1",
    "m_Id": 6,
    "m_DisplayName": "Width",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Width",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "4d149e211c5547bc92a6614b019a8fbe",
    "m_Title": "Distance From The Camera",
    "m_Position": {
        "x": -1714.0001220703125,
        "y": 299.0000305175781
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "5554884b63c14f0aac1aa270c10d5020",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "56df4e41053049918c38194b2c5276a4",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "587601576dab49e0b94231f10746ea54",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "5ba9ff0c80f44f1684d1b2bdeb998f6b",
    "m_Id": 0,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "622ee3d03331483f9d1a64ea4d51e578",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "632259da20534eb68acafe232cefff67",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "6a814d33d384412eb0f8fabfb52d5acb",
    "m_Group": {
        "m_Id": "7907559e62b84e51ab1d060749094c7f"
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -904.5,
            "y": 509.00006103515627,
            "width": 180.00006103515626,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "a0d65a4dd3fa4dc28c9349c0af6db1bb"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "abd0c7a1c344469e9c8cf8ac8823d2df"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ViewVectorNode",
    "m_ObjectId": "6d907e3b3433466290802602d1d5156d",
    "m_Group": {
        "m_Id": "4d149e211c5547bc92a6614b019a8fbe"
    },
    "m_Name": "View Vector",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1689.0001220703125,
            "y": 357.5000305175781,
            "width": 206.0,
            "height": 130.50003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "f6ad65bdacb14aed81a77bab773e37b4"
        },
        {
            "m_Id": "784b4e6e6c8a45c6b5084c6011ea5ea5"
        }
    ],
    "synonyms": [
        "eye vector"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "6e94b03e1aeb45c4beaa81f519a4eeaf",
    "m_Id": 0,
    "m_DisplayName": "Emission",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Emission",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 1,
    "m_DefaultColor": {
        "r": 0.0,
        "g": 0.0,
        "b": 0.0,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "7046e346b2dd48dbbc1173abef53c6c2",
    "m_Id": 2,
    "m_DisplayName": "Orthographic",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Orthographic",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget",
    "m_ObjectId": "70612408391f4688b8fb956853837bdf",
    "m_Datas": [],
    "m_ActiveSubTarget": {
        "m_Id": "0fa9908ad6574e76a9fe9cf0a8df7576"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZTestMode": 4,
    "m_ZWriteControl": 0,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CastShadows": true,
    "m_ReceiveShadows": true,
    "m_SupportsLODCrossFade": false,
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "784b4e6e6c8a45c6b5084c6011ea5ea5",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "7907559e62b84e51ab1d060749094c7f",
    "m_Title": "Camera Distance Mask",
    "m_Position": {
        "x": -1232.5001220703125,
        "y": 299.00006103515627
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "7f4cde534020410fb306edafa24ae1bf",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Emission",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "6e94b03e1aeb45c4beaa81f519a4eeaf"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Emission"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "82c18ea6f82c4bb9b4d3077639270515",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 2.0,
        "z": 2.0,
        "w": 2.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "838a664943904a3caef7dc8311ab8402",
    "m_Id": 3,
    "m_DisplayName": "Near Plane",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Near Plane",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "8ac252d54963466e9a40c19630bc3a30",
    "m_Title": "",
    "m_Content": "By measuring the length of the View Vector, you can find out how far each pixel is from the camera. The resulting distance value is in meters.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1688.5001220703125,
        "y": 499.00006103515627,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "4d149e211c5547bc92a6614b019a8fbe"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "8c2c10727fe5470da9da725ef1bc7b9d",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDTarget",
    "m_ObjectId": "8dc97c0d070041e78b0a4d45a88282d5",
    "m_ActiveSubTarget": {
        "m_Id": "a5d2d3a6cbe949ff8add47c844e1fcb6"
    },
    "m_Datas": [
        {
            "m_Id": "c8205c711dd44193b4bb2fa0f1d5f8dd"
        },
        {
            "m_Id": "0e5e99bc57c04facb21ef9ecc23dadc6"
        },
        {
            "m_Id": "a1fee6609a0a4579a8adc2e9b049f7d0"
        }
    ],
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false,
    "m_SupportLineRendering": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "9203a42fbe2a4c378af39d05336c73c2",
    "m_Id": 3,
    "m_DisplayName": "World Space Position",
    "m_SlotType": 0,
    "m_Hidden": true,
    "m_ShaderOutputName": "WorldSpacePosition",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "9ddc2cd884294c22b19bd5ec092b2c6c",
    "m_Id": 0,
    "m_DisplayName": "Mask Length",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "9e52e1317e5b4675be9880007b4896aa",
    "m_Title": "",
    "m_Content": "This type of mask can be used to fade out close-up details when the camera is futher away or make other visual changes based on camera distance.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -521.0000610351563,
        "y": 559.5000610351563,
        "width": 200.00003051757813,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "7907559e62b84e51ab1d060749094c7f"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "9f3ca3768c524798b95f3bd98940a4a4",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a0d65a4dd3fa4dc28c9349c0af6db1bb",
    "m_Id": 0,
    "m_DisplayName": "Mask Start Distance",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "a1fee6609a0a4579a8adc2e9b049f7d0",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "a5d2d3a6cbe949ff8add47c844e1fcb6"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a6a631276ec94488a5dec726f0628d4f",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInTarget",
    "m_ObjectId": "a7260a4d93514c17b7a288b45bf2e931",
    "m_ActiveSubTarget": {
        "m_Id": "10dfd6e4796b43c7be2d4d9fbda81244"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZWriteControl": 0,
    "m_ZTestMode": 4,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CustomEditorGUI": ""
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "abd0c7a1c344469e9c8cf8ac8823d2df",
    "m_Guid": {
        "m_GuidSerialized": "430c58cb-aa46-4b18-8dc0-1eaf3425540b"
    },
    "m_Name": "Mask Start Distance",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "Mask Start Distance",
    "m_DefaultReferenceName": "_Mask_Start_Distance",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": 0.0,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ViewVectorNode",
    "m_ObjectId": "b61be81c2cb7492e811c523934e4adab",
    "m_Group": {
        "m_Id": "0017903f73f04318a1d802686cad454b"
    },
    "m_Name": "View Vector",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1956.0001220703125,
            "y": 375.0000305175781,
            "width": 208.0,
            "height": 314.5000305175781
        }
    },
    "m_Slots": [
        {
            "m_Id": "da3fbcb403b7445e8a604bfc20a777be"
        },
        {
            "m_Id": "5554884b63c14f0aac1aa270c10d5020"
        }
    ],
    "synonyms": [
        "eye vector"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "b72f8cc732864d10952c6f23ee3980c5",
    "m_Group": {
        "m_Id": "7907559e62b84e51ab1d060749094c7f"
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -611.5000610351563,
            "y": 509.00006103515627,
            "width": 142.5,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "9ddc2cd884294c22b19bd5ec092b2c6c"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "009b99837e1a4ffb92ac1b290148e6f7"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "b88c4ce437d149d9a7ec308ecf9b1e9a",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "b8a6d3f0b4c84905b9e6a482e4fb722d",
    "m_Title": "",
    "m_Content": "The difference between the View Vector and the View Direction:\n\nThe View Vector is a line that extends all the way from the current fragment or vertex to the camera - so it can be used to get the DISTANCE to the camera.\n\nThe View Direction is a normalized vector (so it's only 1 unit long). It's best used to find the ANGLE of the camera and other vectors.\n\nBoth vectors point in the same direction.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1091.5001220703125,
        "y": -69.00000762939453,
        "width": 200.00006103515626,
        "height": 234.50001525878907
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "b9389414d4c442e7af26f016dc4ec7df",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Alpha",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "41c3134ea617494e8e71f5d3fdcffa22"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Alpha"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "c026e8cdc6bd4dd2b7ec1ceabbd9f02e",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TangentMaterialSlot",
    "m_ObjectId": "c6046aa3999b4df1941340bfe394a13f",
    "m_Id": 0,
    "m_DisplayName": "Tangent",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tangent",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "c8205c711dd44193b4bb2fa0f1d5f8dd",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "cdbb46f907e14f27a7545940ee4852d5",
    "m_Id": 0,
    "m_DisplayName": "Base Color",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BaseColor",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.5,
        "y": 0.5,
        "z": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.5,
        "g": 0.5,
        "b": 0.5,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "d26f5463833a4515aa4f058ed5cd276a",
    "m_Title": "",
    "m_Content": "To create a camera distance mask, subtract the distance where you want the mask to start, and then divide by how long you want the mask to be.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1207.5001220703125,
        "y": 498.00006103515627,
        "width": 200.00006103515626,
        "height": 100.5
    },
    "m_Group": {
        "m_Id": "7907559e62b84e51ab1d060749094c7f"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "d5d064c27b274f9590aaca4e6ec4bd97",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "d9294be332944de48dd54130cbd75d07",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DivideNode",
    "m_ObjectId": "d9c0a03dde6c46e4a112f8d1d1351dcb",
    "m_Group": {
        "m_Id": "7907559e62b84e51ab1d060749094c7f"
    },
    "m_Name": "Divide",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -448.0000305175781,
            "y": 357.5000305175781,
            "width": 125.99996948242188,
            "height": 118.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "f955e69287144020b7af6f05366268c3"
        },
        {
            "m_Id": "82c18ea6f82c4bb9b4d3077639270515"
        },
        {
            "m_Id": "b88c4ce437d149d9a7ec308ecf9b1e9a"
        }
    ],
    "synonyms": [
        "division",
        "divided by"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "da3fbcb403b7445e8a604bfc20a777be",
    "m_Id": 3,
    "m_DisplayName": "World Space Position",
    "m_SlotType": 0,
    "m_Hidden": true,
    "m_ShaderOutputName": "WorldSpacePosition",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "df582768d9ab48e881cb4c964b808b02",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.LengthNode",
    "m_ObjectId": "e12729ae91374afcba9a949f7be1a1a9",
    "m_Group": {
        "m_Id": "7907559e62b84e51ab1d060749094c7f"
    },
    "m_Name": "Length",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1000.5,
            "y": 357.5000305175781,
            "width": 129.5,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "df582768d9ab48e881cb4c964b808b02"
        },
        {
            "m_Id": "a6a631276ec94488a5dec726f0628d4f"
        }
    ],
    "synonyms": [
        "measure"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "e1cb40ad4c6041f9881e3c6da2b6cef5",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "e40d238ad26243c3b8658facfd122aa6",
    "m_Name": "",
    "m_ChildObjectList": [
        {
            "m_Id": "abd0c7a1c344469e9c8cf8ac8823d2df"
        },
        {
            "m_Id": "009b99837e1a4ffb92ac1b290148e6f7"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "f5930090d69e433bad627f2d9c61ee2e",
    "m_Id": 4,
    "m_DisplayName": "Far Plane",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Far Plane",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "f6ad65bdacb14aed81a77bab773e37b4",
    "m_Id": 3,
    "m_DisplayName": "World Space Position",
    "m_SlotType": 0,
    "m_Hidden": true,
    "m_ShaderOutputName": "WorldSpacePosition",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "f73754bea63e4eee9eaae60f05a37f2a",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "f955e69287144020b7af6f05366268c3",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

