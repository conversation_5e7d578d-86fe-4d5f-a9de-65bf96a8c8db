<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../../../../../UIElementsSchema/UIElements.xsd" editor-extension-mode="True">
    <ui:VisualElement name="item-row" class="unity-list-view__item" style="flex-direction: column; flex-grow: 1; justify-content: center; margin-left: 4px;">
        <ui:VisualElement name="row" style="flex-direction: row; border-left-width: 0; border-left-color: rgb(89, 89, 89);">
            <ui:Toggle name="expando" class="unity-foldout__toggle" style="visibility: hidden; margin-left: 3px;" />
            <ui:VisualElement name="icon" style="justify-content: center; background-image: url(&apos;project://database/Packages/com.unity.inputsystem/InputSystem/Editor/Icons/d_InputControl.png?fileID=2800000&amp;guid=399cd90f4e31041e692a7d3a8b1aa4d0&amp;type=3#d_InputControl&apos;); width: 16px; height: 16px;" />
            <ui:Label text="binding-name" display-tooltip-when-elided="true" name="name" style="flex-grow: 1; justify-content: center; align-items: stretch; margin-left: 4px; -unity-font-style: normal;" />
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>
