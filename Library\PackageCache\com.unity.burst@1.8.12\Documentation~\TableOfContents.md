* [About Burst](index.md)
* [Getting started](getting-started.md)
* [C# language support](csharp-language-support.md)
    * [HPC# overview](csharp-hpc-overview.md)
    * [Static read-only fields and static constructor support](csharp-static-read-only-support.md)
    * [String support](csharp-string-support.md)
    * [Calling Burst compiled code](csharp-calling-burst-code.md)
    * [Function pointers](csharp-function-pointers.md)
    * [C#/.NET type support](csharp-type-support.md)
    * [C#/.NET System namespace support](csharp-system-support.md)
    * [DllImport and internal calls](csharp-burst-intrinsics-dllimport.md)
    * [SharedStatic struct](csharp-shared-static.md)
* [Burst instrinsics](csharp-burst-intrinsics.md)
    * [Burst intrinsics Common class](csharp-burst-intrinsics-common.md)
    * [Processor specific SIMD extensions](csharp-burst-intrinsics-processors.md)
        * [Arm Neon intrinsics reference](csharp-burst-intrinsics-neon.md)
* [Editor reference](editor-reference-overview.md)
    * [Burst menu](editor-burst-menu.md)
    * [Burst Inspector](editor-burst-inspector.md)
* [Burst compilation](compilation.md)
    * [Compilation overview](compilation-overview.md)
    * [Synchronous compilation](compilation-synchronous.md)
    * [BurstCompile attribute](compilation-burstcompile.md)
        * [Assembly level BurstCompile](compilation-burstcompile-assembly.md)
    * [BurstDiscard attribute](compilation-burstdiscard.md)
    * [Generic jobs](compilation-generic-jobs.md)
    * [Compilation warnings](compilation-warnings.md)
* [Building your project](building-projects.md)
    * [Burst AOT Player Settings reference](building-aot-settings.md)
* [Optimization](optimization-overview.md)
    * [Debugging and profiling tools](debugging-profiling-tools.md)
    * [Loop vectorization optimization](optimization-loop-vectorization.md)
    * [Memory aliasing](aliasing.md)
        * [NoAlias attribute](aliasing-noalias.md)
        * [Aliasing and the job system](aliasing-job-system.md)
    * [AssumeRange attribute](optimization-assumerange.md)
    * [Hint intrinsic](optimization-hint.md)
    * [Constant intrinsic](optimization-constant.md)
    * [SkipLocalsInit attribute](optimization-skiplocalsinit.md)
* [Modding support](modding-support.md)
