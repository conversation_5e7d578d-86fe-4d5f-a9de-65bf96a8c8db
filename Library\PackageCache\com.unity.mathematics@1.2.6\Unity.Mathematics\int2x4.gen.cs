//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using System;
using System.Runtime.CompilerServices;
using Unity.IL2CPP.CompilerServices;

#pragma warning disable 0660, 0661

namespace Unity.Mathematics
{
    /// <summary>A 2x4 matrix of ints.</summary>
    [System.Serializable]
    [Il2CppEagerStaticClassConstruction]
    public partial struct int2x4 : System.IEquatable<int2x4>, IFormattable
    {
        /// <summary>Column 0 of the matrix.</summary>
        public int2 c0;
        /// <summary>Column 1 of the matrix.</summary>
        public int2 c1;
        /// <summary>Column 2 of the matrix.</summary>
        public int2 c2;
        /// <summary>Column 3 of the matrix.</summary>
        public int2 c3;

        /// <summary>int2x4 zero value.</summary>
        public static readonly int2x4 zero;

        /// <summary>Constructs a int2x4 matrix from four int2 vectors.</summary>
        /// <param name="c0">The matrix column c0 will be set to this value.</param>
        /// <param name="c1">The matrix column c1 will be set to this value.</param>
        /// <param name="c2">The matrix column c2 will be set to this value.</param>
        /// <param name="c3">The matrix column c3 will be set to this value.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public int2x4(int2 c0, int2 c1, int2 c2, int2 c3)
        {
            this.c0 = c0;
            this.c1 = c1;
            this.c2 = c2;
            this.c3 = c3;
        }

        /// <summary>Constructs a int2x4 matrix from 8 int values given in row-major order.</summary>
        /// <param name="m00">The matrix at row 0, column 0 will be set to this value.</param>
        /// <param name="m01">The matrix at row 0, column 1 will be set to this value.</param>
        /// <param name="m02">The matrix at row 0, column 2 will be set to this value.</param>
        /// <param name="m03">The matrix at row 0, column 3 will be set to this value.</param>
        /// <param name="m10">The matrix at row 1, column 0 will be set to this value.</param>
        /// <param name="m11">The matrix at row 1, column 1 will be set to this value.</param>
        /// <param name="m12">The matrix at row 1, column 2 will be set to this value.</param>
        /// <param name="m13">The matrix at row 1, column 3 will be set to this value.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public int2x4(int m00, int m01, int m02, int m03,
                      int m10, int m11, int m12, int m13)
        {
            this.c0 = new int2(m00, m10);
            this.c1 = new int2(m01, m11);
            this.c2 = new int2(m02, m12);
            this.c3 = new int2(m03, m13);
        }

        /// <summary>Constructs a int2x4 matrix from a single int value by assigning it to every component.</summary>
        /// <param name="v">int to convert to int2x4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public int2x4(int v)
        {
            this.c0 = v;
            this.c1 = v;
            this.c2 = v;
            this.c3 = v;
        }

        /// <summary>Constructs a int2x4 matrix from a single bool value by converting it to int and assigning it to every component.</summary>
        /// <param name="v">bool to convert to int2x4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public int2x4(bool v)
        {
            this.c0 = math.select(new int2(0), new int2(1), v);
            this.c1 = math.select(new int2(0), new int2(1), v);
            this.c2 = math.select(new int2(0), new int2(1), v);
            this.c3 = math.select(new int2(0), new int2(1), v);
        }

        /// <summary>Constructs a int2x4 matrix from a bool2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">bool2x4 to convert to int2x4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public int2x4(bool2x4 v)
        {
            this.c0 = math.select(new int2(0), new int2(1), v.c0);
            this.c1 = math.select(new int2(0), new int2(1), v.c1);
            this.c2 = math.select(new int2(0), new int2(1), v.c2);
            this.c3 = math.select(new int2(0), new int2(1), v.c3);
        }

        /// <summary>Constructs a int2x4 matrix from a single uint value by converting it to int and assigning it to every component.</summary>
        /// <param name="v">uint to convert to int2x4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public int2x4(uint v)
        {
            this.c0 = (int2)v;
            this.c1 = (int2)v;
            this.c2 = (int2)v;
            this.c3 = (int2)v;
        }

        /// <summary>Constructs a int2x4 matrix from a uint2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">uint2x4 to convert to int2x4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public int2x4(uint2x4 v)
        {
            this.c0 = (int2)v.c0;
            this.c1 = (int2)v.c1;
            this.c2 = (int2)v.c2;
            this.c3 = (int2)v.c3;
        }

        /// <summary>Constructs a int2x4 matrix from a single float value by converting it to int and assigning it to every component.</summary>
        /// <param name="v">float to convert to int2x4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public int2x4(float v)
        {
            this.c0 = (int2)v;
            this.c1 = (int2)v;
            this.c2 = (int2)v;
            this.c3 = (int2)v;
        }

        /// <summary>Constructs a int2x4 matrix from a float2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">float2x4 to convert to int2x4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public int2x4(float2x4 v)
        {
            this.c0 = (int2)v.c0;
            this.c1 = (int2)v.c1;
            this.c2 = (int2)v.c2;
            this.c3 = (int2)v.c3;
        }

        /// <summary>Constructs a int2x4 matrix from a single double value by converting it to int and assigning it to every component.</summary>
        /// <param name="v">double to convert to int2x4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public int2x4(double v)
        {
            this.c0 = (int2)v;
            this.c1 = (int2)v;
            this.c2 = (int2)v;
            this.c3 = (int2)v;
        }

        /// <summary>Constructs a int2x4 matrix from a double2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">double2x4 to convert to int2x4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public int2x4(double2x4 v)
        {
            this.c0 = (int2)v.c0;
            this.c1 = (int2)v.c1;
            this.c2 = (int2)v.c2;
            this.c3 = (int2)v.c3;
        }


        /// <summary>Implicitly converts a single int value to a int2x4 matrix by assigning it to every component.</summary>
        /// <param name="v">int to convert to int2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator int2x4(int v) { return new int2x4(v); }

        /// <summary>Explicitly converts a single bool value to a int2x4 matrix by converting it to int and assigning it to every component.</summary>
        /// <param name="v">bool to convert to int2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator int2x4(bool v) { return new int2x4(v); }

        /// <summary>Explicitly converts a bool2x4 matrix to a int2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">bool2x4 to convert to int2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator int2x4(bool2x4 v) { return new int2x4(v); }

        /// <summary>Explicitly converts a single uint value to a int2x4 matrix by converting it to int and assigning it to every component.</summary>
        /// <param name="v">uint to convert to int2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator int2x4(uint v) { return new int2x4(v); }

        /// <summary>Explicitly converts a uint2x4 matrix to a int2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">uint2x4 to convert to int2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator int2x4(uint2x4 v) { return new int2x4(v); }

        /// <summary>Explicitly converts a single float value to a int2x4 matrix by converting it to int and assigning it to every component.</summary>
        /// <param name="v">float to convert to int2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator int2x4(float v) { return new int2x4(v); }

        /// <summary>Explicitly converts a float2x4 matrix to a int2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">float2x4 to convert to int2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator int2x4(float2x4 v) { return new int2x4(v); }

        /// <summary>Explicitly converts a single double value to a int2x4 matrix by converting it to int and assigning it to every component.</summary>
        /// <param name="v">double to convert to int2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator int2x4(double v) { return new int2x4(v); }

        /// <summary>Explicitly converts a double2x4 matrix to a int2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">double2x4 to convert to int2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator int2x4(double2x4 v) { return new int2x4(v); }


        /// <summary>Returns the result of a componentwise multiplication operation on two int2x4 matrices.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise multiplication.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise multiplication.</param>
        /// <returns>int2x4 result of the componentwise multiplication.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator * (int2x4 lhs, int2x4 rhs) { return new int2x4 (lhs.c0 * rhs.c0, lhs.c1 * rhs.c1, lhs.c2 * rhs.c2, lhs.c3 * rhs.c3); }

        /// <summary>Returns the result of a componentwise multiplication operation on an int2x4 matrix and an int value.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise multiplication.</param>
        /// <param name="rhs">Right hand side int to use to compute componentwise multiplication.</param>
        /// <returns>int2x4 result of the componentwise multiplication.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator * (int2x4 lhs, int rhs) { return new int2x4 (lhs.c0 * rhs, lhs.c1 * rhs, lhs.c2 * rhs, lhs.c3 * rhs); }

        /// <summary>Returns the result of a componentwise multiplication operation on an int value and an int2x4 matrix.</summary>
        /// <param name="lhs">Left hand side int to use to compute componentwise multiplication.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise multiplication.</param>
        /// <returns>int2x4 result of the componentwise multiplication.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator * (int lhs, int2x4 rhs) { return new int2x4 (lhs * rhs.c0, lhs * rhs.c1, lhs * rhs.c2, lhs * rhs.c3); }


        /// <summary>Returns the result of a componentwise addition operation on two int2x4 matrices.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise addition.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise addition.</param>
        /// <returns>int2x4 result of the componentwise addition.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator + (int2x4 lhs, int2x4 rhs) { return new int2x4 (lhs.c0 + rhs.c0, lhs.c1 + rhs.c1, lhs.c2 + rhs.c2, lhs.c3 + rhs.c3); }

        /// <summary>Returns the result of a componentwise addition operation on an int2x4 matrix and an int value.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise addition.</param>
        /// <param name="rhs">Right hand side int to use to compute componentwise addition.</param>
        /// <returns>int2x4 result of the componentwise addition.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator + (int2x4 lhs, int rhs) { return new int2x4 (lhs.c0 + rhs, lhs.c1 + rhs, lhs.c2 + rhs, lhs.c3 + rhs); }

        /// <summary>Returns the result of a componentwise addition operation on an int value and an int2x4 matrix.</summary>
        /// <param name="lhs">Left hand side int to use to compute componentwise addition.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise addition.</param>
        /// <returns>int2x4 result of the componentwise addition.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator + (int lhs, int2x4 rhs) { return new int2x4 (lhs + rhs.c0, lhs + rhs.c1, lhs + rhs.c2, lhs + rhs.c3); }


        /// <summary>Returns the result of a componentwise subtraction operation on two int2x4 matrices.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise subtraction.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise subtraction.</param>
        /// <returns>int2x4 result of the componentwise subtraction.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator - (int2x4 lhs, int2x4 rhs) { return new int2x4 (lhs.c0 - rhs.c0, lhs.c1 - rhs.c1, lhs.c2 - rhs.c2, lhs.c3 - rhs.c3); }

        /// <summary>Returns the result of a componentwise subtraction operation on an int2x4 matrix and an int value.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise subtraction.</param>
        /// <param name="rhs">Right hand side int to use to compute componentwise subtraction.</param>
        /// <returns>int2x4 result of the componentwise subtraction.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator - (int2x4 lhs, int rhs) { return new int2x4 (lhs.c0 - rhs, lhs.c1 - rhs, lhs.c2 - rhs, lhs.c3 - rhs); }

        /// <summary>Returns the result of a componentwise subtraction operation on an int value and an int2x4 matrix.</summary>
        /// <param name="lhs">Left hand side int to use to compute componentwise subtraction.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise subtraction.</param>
        /// <returns>int2x4 result of the componentwise subtraction.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator - (int lhs, int2x4 rhs) { return new int2x4 (lhs - rhs.c0, lhs - rhs.c1, lhs - rhs.c2, lhs - rhs.c3); }


        /// <summary>Returns the result of a componentwise division operation on two int2x4 matrices.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise division.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise division.</param>
        /// <returns>int2x4 result of the componentwise division.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator / (int2x4 lhs, int2x4 rhs) { return new int2x4 (lhs.c0 / rhs.c0, lhs.c1 / rhs.c1, lhs.c2 / rhs.c2, lhs.c3 / rhs.c3); }

        /// <summary>Returns the result of a componentwise division operation on an int2x4 matrix and an int value.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise division.</param>
        /// <param name="rhs">Right hand side int to use to compute componentwise division.</param>
        /// <returns>int2x4 result of the componentwise division.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator / (int2x4 lhs, int rhs) { return new int2x4 (lhs.c0 / rhs, lhs.c1 / rhs, lhs.c2 / rhs, lhs.c3 / rhs); }

        /// <summary>Returns the result of a componentwise division operation on an int value and an int2x4 matrix.</summary>
        /// <param name="lhs">Left hand side int to use to compute componentwise division.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise division.</param>
        /// <returns>int2x4 result of the componentwise division.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator / (int lhs, int2x4 rhs) { return new int2x4 (lhs / rhs.c0, lhs / rhs.c1, lhs / rhs.c2, lhs / rhs.c3); }


        /// <summary>Returns the result of a componentwise modulus operation on two int2x4 matrices.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise modulus.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise modulus.</param>
        /// <returns>int2x4 result of the componentwise modulus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator % (int2x4 lhs, int2x4 rhs) { return new int2x4 (lhs.c0 % rhs.c0, lhs.c1 % rhs.c1, lhs.c2 % rhs.c2, lhs.c3 % rhs.c3); }

        /// <summary>Returns the result of a componentwise modulus operation on an int2x4 matrix and an int value.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise modulus.</param>
        /// <param name="rhs">Right hand side int to use to compute componentwise modulus.</param>
        /// <returns>int2x4 result of the componentwise modulus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator % (int2x4 lhs, int rhs) { return new int2x4 (lhs.c0 % rhs, lhs.c1 % rhs, lhs.c2 % rhs, lhs.c3 % rhs); }

        /// <summary>Returns the result of a componentwise modulus operation on an int value and an int2x4 matrix.</summary>
        /// <param name="lhs">Left hand side int to use to compute componentwise modulus.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise modulus.</param>
        /// <returns>int2x4 result of the componentwise modulus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator % (int lhs, int2x4 rhs) { return new int2x4 (lhs % rhs.c0, lhs % rhs.c1, lhs % rhs.c2, lhs % rhs.c3); }


        /// <summary>Returns the result of a componentwise increment operation on an int2x4 matrix.</summary>
        /// <param name="val">Value to use when computing the componentwise increment.</param>
        /// <returns>int2x4 result of the componentwise increment.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator ++ (int2x4 val) { return new int2x4 (++val.c0, ++val.c1, ++val.c2, ++val.c3); }


        /// <summary>Returns the result of a componentwise decrement operation on an int2x4 matrix.</summary>
        /// <param name="val">Value to use when computing the componentwise decrement.</param>
        /// <returns>int2x4 result of the componentwise decrement.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator -- (int2x4 val) { return new int2x4 (--val.c0, --val.c1, --val.c2, --val.c3); }


        /// <summary>Returns the result of a componentwise less than operation on two int2x4 matrices.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise less than.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise less than.</param>
        /// <returns>bool2x4 result of the componentwise less than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator < (int2x4 lhs, int2x4 rhs) { return new bool2x4 (lhs.c0 < rhs.c0, lhs.c1 < rhs.c1, lhs.c2 < rhs.c2, lhs.c3 < rhs.c3); }

        /// <summary>Returns the result of a componentwise less than operation on an int2x4 matrix and an int value.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise less than.</param>
        /// <param name="rhs">Right hand side int to use to compute componentwise less than.</param>
        /// <returns>bool2x4 result of the componentwise less than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator < (int2x4 lhs, int rhs) { return new bool2x4 (lhs.c0 < rhs, lhs.c1 < rhs, lhs.c2 < rhs, lhs.c3 < rhs); }

        /// <summary>Returns the result of a componentwise less than operation on an int value and an int2x4 matrix.</summary>
        /// <param name="lhs">Left hand side int to use to compute componentwise less than.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise less than.</param>
        /// <returns>bool2x4 result of the componentwise less than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator < (int lhs, int2x4 rhs) { return new bool2x4 (lhs < rhs.c0, lhs < rhs.c1, lhs < rhs.c2, lhs < rhs.c3); }


        /// <summary>Returns the result of a componentwise less or equal operation on two int2x4 matrices.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise less or equal.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise less or equal.</param>
        /// <returns>bool2x4 result of the componentwise less or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator <= (int2x4 lhs, int2x4 rhs) { return new bool2x4 (lhs.c0 <= rhs.c0, lhs.c1 <= rhs.c1, lhs.c2 <= rhs.c2, lhs.c3 <= rhs.c3); }

        /// <summary>Returns the result of a componentwise less or equal operation on an int2x4 matrix and an int value.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise less or equal.</param>
        /// <param name="rhs">Right hand side int to use to compute componentwise less or equal.</param>
        /// <returns>bool2x4 result of the componentwise less or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator <= (int2x4 lhs, int rhs) { return new bool2x4 (lhs.c0 <= rhs, lhs.c1 <= rhs, lhs.c2 <= rhs, lhs.c3 <= rhs); }

        /// <summary>Returns the result of a componentwise less or equal operation on an int value and an int2x4 matrix.</summary>
        /// <param name="lhs">Left hand side int to use to compute componentwise less or equal.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise less or equal.</param>
        /// <returns>bool2x4 result of the componentwise less or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator <= (int lhs, int2x4 rhs) { return new bool2x4 (lhs <= rhs.c0, lhs <= rhs.c1, lhs <= rhs.c2, lhs <= rhs.c3); }


        /// <summary>Returns the result of a componentwise greater than operation on two int2x4 matrices.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise greater than.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise greater than.</param>
        /// <returns>bool2x4 result of the componentwise greater than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator > (int2x4 lhs, int2x4 rhs) { return new bool2x4 (lhs.c0 > rhs.c0, lhs.c1 > rhs.c1, lhs.c2 > rhs.c2, lhs.c3 > rhs.c3); }

        /// <summary>Returns the result of a componentwise greater than operation on an int2x4 matrix and an int value.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise greater than.</param>
        /// <param name="rhs">Right hand side int to use to compute componentwise greater than.</param>
        /// <returns>bool2x4 result of the componentwise greater than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator > (int2x4 lhs, int rhs) { return new bool2x4 (lhs.c0 > rhs, lhs.c1 > rhs, lhs.c2 > rhs, lhs.c3 > rhs); }

        /// <summary>Returns the result of a componentwise greater than operation on an int value and an int2x4 matrix.</summary>
        /// <param name="lhs">Left hand side int to use to compute componentwise greater than.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise greater than.</param>
        /// <returns>bool2x4 result of the componentwise greater than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator > (int lhs, int2x4 rhs) { return new bool2x4 (lhs > rhs.c0, lhs > rhs.c1, lhs > rhs.c2, lhs > rhs.c3); }


        /// <summary>Returns the result of a componentwise greater or equal operation on two int2x4 matrices.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise greater or equal.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise greater or equal.</param>
        /// <returns>bool2x4 result of the componentwise greater or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator >= (int2x4 lhs, int2x4 rhs) { return new bool2x4 (lhs.c0 >= rhs.c0, lhs.c1 >= rhs.c1, lhs.c2 >= rhs.c2, lhs.c3 >= rhs.c3); }

        /// <summary>Returns the result of a componentwise greater or equal operation on an int2x4 matrix and an int value.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise greater or equal.</param>
        /// <param name="rhs">Right hand side int to use to compute componentwise greater or equal.</param>
        /// <returns>bool2x4 result of the componentwise greater or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator >= (int2x4 lhs, int rhs) { return new bool2x4 (lhs.c0 >= rhs, lhs.c1 >= rhs, lhs.c2 >= rhs, lhs.c3 >= rhs); }

        /// <summary>Returns the result of a componentwise greater or equal operation on an int value and an int2x4 matrix.</summary>
        /// <param name="lhs">Left hand side int to use to compute componentwise greater or equal.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise greater or equal.</param>
        /// <returns>bool2x4 result of the componentwise greater or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator >= (int lhs, int2x4 rhs) { return new bool2x4 (lhs >= rhs.c0, lhs >= rhs.c1, lhs >= rhs.c2, lhs >= rhs.c3); }


        /// <summary>Returns the result of a componentwise unary minus operation on an int2x4 matrix.</summary>
        /// <param name="val">Value to use when computing the componentwise unary minus.</param>
        /// <returns>int2x4 result of the componentwise unary minus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator - (int2x4 val) { return new int2x4 (-val.c0, -val.c1, -val.c2, -val.c3); }


        /// <summary>Returns the result of a componentwise unary plus operation on an int2x4 matrix.</summary>
        /// <param name="val">Value to use when computing the componentwise unary plus.</param>
        /// <returns>int2x4 result of the componentwise unary plus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator + (int2x4 val) { return new int2x4 (+val.c0, +val.c1, +val.c2, +val.c3); }


        /// <summary>Returns the result of a componentwise left shift operation on an int2x4 matrix by a number of bits specified by a single int.</summary>
        /// <param name="x">The matrix to left shift.</param>
        /// <param name="n">The number of bits to left shift.</param>
        /// <returns>The result of the componentwise left shift.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator << (int2x4 x, int n) { return new int2x4 (x.c0 << n, x.c1 << n, x.c2 << n, x.c3 << n); }

        /// <summary>Returns the result of a componentwise right shift operation on an int2x4 matrix by a number of bits specified by a single int.</summary>
        /// <param name="x">The matrix to right shift.</param>
        /// <param name="n">The number of bits to right shift.</param>
        /// <returns>The result of the componentwise right shift.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator >> (int2x4 x, int n) { return new int2x4 (x.c0 >> n, x.c1 >> n, x.c2 >> n, x.c3 >> n); }

        /// <summary>Returns the result of a componentwise equality operation on two int2x4 matrices.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise equality.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise equality.</param>
        /// <returns>bool2x4 result of the componentwise equality.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator == (int2x4 lhs, int2x4 rhs) { return new bool2x4 (lhs.c0 == rhs.c0, lhs.c1 == rhs.c1, lhs.c2 == rhs.c2, lhs.c3 == rhs.c3); }

        /// <summary>Returns the result of a componentwise equality operation on an int2x4 matrix and an int value.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise equality.</param>
        /// <param name="rhs">Right hand side int to use to compute componentwise equality.</param>
        /// <returns>bool2x4 result of the componentwise equality.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator == (int2x4 lhs, int rhs) { return new bool2x4 (lhs.c0 == rhs, lhs.c1 == rhs, lhs.c2 == rhs, lhs.c3 == rhs); }

        /// <summary>Returns the result of a componentwise equality operation on an int value and an int2x4 matrix.</summary>
        /// <param name="lhs">Left hand side int to use to compute componentwise equality.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise equality.</param>
        /// <returns>bool2x4 result of the componentwise equality.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator == (int lhs, int2x4 rhs) { return new bool2x4 (lhs == rhs.c0, lhs == rhs.c1, lhs == rhs.c2, lhs == rhs.c3); }


        /// <summary>Returns the result of a componentwise not equal operation on two int2x4 matrices.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise not equal.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise not equal.</param>
        /// <returns>bool2x4 result of the componentwise not equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator != (int2x4 lhs, int2x4 rhs) { return new bool2x4 (lhs.c0 != rhs.c0, lhs.c1 != rhs.c1, lhs.c2 != rhs.c2, lhs.c3 != rhs.c3); }

        /// <summary>Returns the result of a componentwise not equal operation on an int2x4 matrix and an int value.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise not equal.</param>
        /// <param name="rhs">Right hand side int to use to compute componentwise not equal.</param>
        /// <returns>bool2x4 result of the componentwise not equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator != (int2x4 lhs, int rhs) { return new bool2x4 (lhs.c0 != rhs, lhs.c1 != rhs, lhs.c2 != rhs, lhs.c3 != rhs); }

        /// <summary>Returns the result of a componentwise not equal operation on an int value and an int2x4 matrix.</summary>
        /// <param name="lhs">Left hand side int to use to compute componentwise not equal.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise not equal.</param>
        /// <returns>bool2x4 result of the componentwise not equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator != (int lhs, int2x4 rhs) { return new bool2x4 (lhs != rhs.c0, lhs != rhs.c1, lhs != rhs.c2, lhs != rhs.c3); }


        /// <summary>Returns the result of a componentwise bitwise not operation on an int2x4 matrix.</summary>
        /// <param name="val">Value to use when computing the componentwise bitwise not.</param>
        /// <returns>int2x4 result of the componentwise bitwise not.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator ~ (int2x4 val) { return new int2x4 (~val.c0, ~val.c1, ~val.c2, ~val.c3); }


        /// <summary>Returns the result of a componentwise bitwise and operation on two int2x4 matrices.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise bitwise and.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise bitwise and.</param>
        /// <returns>int2x4 result of the componentwise bitwise and.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator & (int2x4 lhs, int2x4 rhs) { return new int2x4 (lhs.c0 & rhs.c0, lhs.c1 & rhs.c1, lhs.c2 & rhs.c2, lhs.c3 & rhs.c3); }

        /// <summary>Returns the result of a componentwise bitwise and operation on an int2x4 matrix and an int value.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise bitwise and.</param>
        /// <param name="rhs">Right hand side int to use to compute componentwise bitwise and.</param>
        /// <returns>int2x4 result of the componentwise bitwise and.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator & (int2x4 lhs, int rhs) { return new int2x4 (lhs.c0 & rhs, lhs.c1 & rhs, lhs.c2 & rhs, lhs.c3 & rhs); }

        /// <summary>Returns the result of a componentwise bitwise and operation on an int value and an int2x4 matrix.</summary>
        /// <param name="lhs">Left hand side int to use to compute componentwise bitwise and.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise bitwise and.</param>
        /// <returns>int2x4 result of the componentwise bitwise and.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator & (int lhs, int2x4 rhs) { return new int2x4 (lhs & rhs.c0, lhs & rhs.c1, lhs & rhs.c2, lhs & rhs.c3); }


        /// <summary>Returns the result of a componentwise bitwise or operation on two int2x4 matrices.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise bitwise or.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise bitwise or.</param>
        /// <returns>int2x4 result of the componentwise bitwise or.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator | (int2x4 lhs, int2x4 rhs) { return new int2x4 (lhs.c0 | rhs.c0, lhs.c1 | rhs.c1, lhs.c2 | rhs.c2, lhs.c3 | rhs.c3); }

        /// <summary>Returns the result of a componentwise bitwise or operation on an int2x4 matrix and an int value.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise bitwise or.</param>
        /// <param name="rhs">Right hand side int to use to compute componentwise bitwise or.</param>
        /// <returns>int2x4 result of the componentwise bitwise or.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator | (int2x4 lhs, int rhs) { return new int2x4 (lhs.c0 | rhs, lhs.c1 | rhs, lhs.c2 | rhs, lhs.c3 | rhs); }

        /// <summary>Returns the result of a componentwise bitwise or operation on an int value and an int2x4 matrix.</summary>
        /// <param name="lhs">Left hand side int to use to compute componentwise bitwise or.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise bitwise or.</param>
        /// <returns>int2x4 result of the componentwise bitwise or.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator | (int lhs, int2x4 rhs) { return new int2x4 (lhs | rhs.c0, lhs | rhs.c1, lhs | rhs.c2, lhs | rhs.c3); }


        /// <summary>Returns the result of a componentwise bitwise exclusive or operation on two int2x4 matrices.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise bitwise exclusive or.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise bitwise exclusive or.</param>
        /// <returns>int2x4 result of the componentwise bitwise exclusive or.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator ^ (int2x4 lhs, int2x4 rhs) { return new int2x4 (lhs.c0 ^ rhs.c0, lhs.c1 ^ rhs.c1, lhs.c2 ^ rhs.c2, lhs.c3 ^ rhs.c3); }

        /// <summary>Returns the result of a componentwise bitwise exclusive or operation on an int2x4 matrix and an int value.</summary>
        /// <param name="lhs">Left hand side int2x4 to use to compute componentwise bitwise exclusive or.</param>
        /// <param name="rhs">Right hand side int to use to compute componentwise bitwise exclusive or.</param>
        /// <returns>int2x4 result of the componentwise bitwise exclusive or.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator ^ (int2x4 lhs, int rhs) { return new int2x4 (lhs.c0 ^ rhs, lhs.c1 ^ rhs, lhs.c2 ^ rhs, lhs.c3 ^ rhs); }

        /// <summary>Returns the result of a componentwise bitwise exclusive or operation on an int value and an int2x4 matrix.</summary>
        /// <param name="lhs">Left hand side int to use to compute componentwise bitwise exclusive or.</param>
        /// <param name="rhs">Right hand side int2x4 to use to compute componentwise bitwise exclusive or.</param>
        /// <returns>int2x4 result of the componentwise bitwise exclusive or.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 operator ^ (int lhs, int2x4 rhs) { return new int2x4 (lhs ^ rhs.c0, lhs ^ rhs.c1, lhs ^ rhs.c2, lhs ^ rhs.c3); }



        /// <summary>Returns the int2 element at a specified index.</summary>
        unsafe public ref int2 this[int index]
        {
            get
            {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
                if ((uint)index >= 4)
                    throw new System.ArgumentException("index must be between[0...3]");
#endif
                fixed (int2x4* array = &this) { return ref ((int2*)array)[index]; }
            }
        }

        /// <summary>Returns true if the int2x4 is equal to a given int2x4, false otherwise.</summary>
        /// <param name="rhs">Right hand side argument to compare equality with.</param>
        /// <returns>The result of the equality comparison.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public bool Equals(int2x4 rhs) { return c0.Equals(rhs.c0) && c1.Equals(rhs.c1) && c2.Equals(rhs.c2) && c3.Equals(rhs.c3); }

        /// <summary>Returns true if the int2x4 is equal to a given int2x4, false otherwise.</summary>
        /// <param name="o">Right hand side argument to compare equality with.</param>
        /// <returns>The result of the equality comparison.</returns>
        public override bool Equals(object o) { return o is int2x4 converted && Equals(converted); }


        /// <summary>Returns a hash code for the int2x4.</summary>
        /// <returns>The computed hash code.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override int GetHashCode() { return (int)math.hash(this); }


        /// <summary>Returns a string representation of the int2x4.</summary>
        /// <returns>String representation of the value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override string ToString()
        {
            return string.Format("int2x4({0}, {1}, {2}, {3},  {4}, {5}, {6}, {7})", c0.x, c1.x, c2.x, c3.x, c0.y, c1.y, c2.y, c3.y);
        }

        /// <summary>Returns a string representation of the int2x4 using a specified format and culture-specific format information.</summary>
        /// <param name="format">Format string to use during string formatting.</param>
        /// <param name="formatProvider">Format provider to use during string formatting.</param>
        /// <returns>String representation of the value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public string ToString(string format, IFormatProvider formatProvider)
        {
            return string.Format("int2x4({0}, {1}, {2}, {3},  {4}, {5}, {6}, {7})", c0.x.ToString(format, formatProvider), c1.x.ToString(format, formatProvider), c2.x.ToString(format, formatProvider), c3.x.ToString(format, formatProvider), c0.y.ToString(format, formatProvider), c1.y.ToString(format, formatProvider), c2.y.ToString(format, formatProvider), c3.y.ToString(format, formatProvider));
        }

    }

    public static partial class math
    {
        /// <summary>Returns a int2x4 matrix constructed from four int2 vectors.</summary>
        /// <param name="c0">The matrix column c0 will be set to this value.</param>
        /// <param name="c1">The matrix column c1 will be set to this value.</param>
        /// <param name="c2">The matrix column c2 will be set to this value.</param>
        /// <param name="c3">The matrix column c3 will be set to this value.</param>
        /// <returns>int2x4 constructed from arguments.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 int2x4(int2 c0, int2 c1, int2 c2, int2 c3) { return new int2x4(c0, c1, c2, c3); }

        /// <summary>Returns a int2x4 matrix constructed from from 8 int values given in row-major order.</summary>
        /// <param name="m00">The matrix at row 0, column 0 will be set to this value.</param>
        /// <param name="m01">The matrix at row 0, column 1 will be set to this value.</param>
        /// <param name="m02">The matrix at row 0, column 2 will be set to this value.</param>
        /// <param name="m03">The matrix at row 0, column 3 will be set to this value.</param>
        /// <param name="m10">The matrix at row 1, column 0 will be set to this value.</param>
        /// <param name="m11">The matrix at row 1, column 1 will be set to this value.</param>
        /// <param name="m12">The matrix at row 1, column 2 will be set to this value.</param>
        /// <param name="m13">The matrix at row 1, column 3 will be set to this value.</param>
        /// <returns>int2x4 constructed from arguments.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 int2x4(int m00, int m01, int m02, int m03,
                                    int m10, int m11, int m12, int m13)
        {
            return new int2x4(m00, m01, m02, m03,
                              m10, m11, m12, m13);
        }

        /// <summary>Returns a int2x4 matrix constructed from a single int value by assigning it to every component.</summary>
        /// <param name="v">int to convert to int2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 int2x4(int v) { return new int2x4(v); }

        /// <summary>Returns a int2x4 matrix constructed from a single bool value by converting it to int and assigning it to every component.</summary>
        /// <param name="v">bool to convert to int2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 int2x4(bool v) { return new int2x4(v); }

        /// <summary>Return a int2x4 matrix constructed from a bool2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">bool2x4 to convert to int2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 int2x4(bool2x4 v) { return new int2x4(v); }

        /// <summary>Returns a int2x4 matrix constructed from a single uint value by converting it to int and assigning it to every component.</summary>
        /// <param name="v">uint to convert to int2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 int2x4(uint v) { return new int2x4(v); }

        /// <summary>Return a int2x4 matrix constructed from a uint2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">uint2x4 to convert to int2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 int2x4(uint2x4 v) { return new int2x4(v); }

        /// <summary>Returns a int2x4 matrix constructed from a single float value by converting it to int and assigning it to every component.</summary>
        /// <param name="v">float to convert to int2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 int2x4(float v) { return new int2x4(v); }

        /// <summary>Return a int2x4 matrix constructed from a float2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">float2x4 to convert to int2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 int2x4(float2x4 v) { return new int2x4(v); }

        /// <summary>Returns a int2x4 matrix constructed from a single double value by converting it to int and assigning it to every component.</summary>
        /// <param name="v">double to convert to int2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 int2x4(double v) { return new int2x4(v); }

        /// <summary>Return a int2x4 matrix constructed from a double2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">double2x4 to convert to int2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int2x4 int2x4(double2x4 v) { return new int2x4(v); }

        /// <summary>Return the int4x2 transpose of a int2x4 matrix.</summary>
        /// <param name="v">Value to transpose.</param>
        /// <returns>Transposed value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int4x2 transpose(int2x4 v)
        {
            return int4x2(
                v.c0.x, v.c0.y,
                v.c1.x, v.c1.y,
                v.c2.x, v.c2.y,
                v.c3.x, v.c3.y);
        }

        /// <summary>Returns a uint hash code of a int2x4 matrix.</summary>
        /// <param name="v">Matrix value to hash.</param>
        /// <returns>uint hash of the argument.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint hash(int2x4 v)
        {
            return csum(asuint(v.c0) * uint2(0x7AA07CD3u, 0xAF642BA9u) +
                        asuint(v.c1) * uint2(0xA8F2213Bu, 0x9F3FDC37u) +
                        asuint(v.c2) * uint2(0xAC60D0C3u, 0x9263662Fu) +
                        asuint(v.c3) * uint2(0xE69626FFu, 0xBD010EEBu)) + 0x9CEDE1D1u;
        }

        /// <summary>
        /// Returns a uint2 vector hash code of a int2x4 matrix.
        /// When multiple elements are to be hashes together, it can more efficient to calculate and combine wide hash
        /// that are only reduced to a narrow uint hash at the very end instead of at every step.
        /// </summary>
        /// <param name="v">Matrix value to hash.</param>
        /// <returns>uint2 hash of the argument.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2 hashwide(int2x4 v)
        {
            return (asuint(v.c0) * uint2(0x43BE0B51u, 0xAF836EE1u) +
                    asuint(v.c1) * uint2(0xB130C137u, 0x54834775u) +
                    asuint(v.c2) * uint2(0x7C022221u, 0xA2D00EDFu) +
                    asuint(v.c3) * uint2(0xA8977779u, 0x9F1C739Bu)) + 0x4B1BD187u;
        }

    }
}
