# Avoiding collisions and evaluating shots

As characters and objects move around in a complex Scene, obstacles in the Scene sometimes come between a camera and its target.  Similarly, you might need to move a camera to a position in the Scene that another GameObject already occupies. Cinemachine provides extensions to handle these situations:

* [Cinemachine Collider](CinemachineCollider.md)
* [Cinemachine Confiner](CinemachineConfiner.md)

