{"name": "com.unity.cinemachine", "displayName": "Cinemachine", "version": "2.9.7", "unity": "2019.4", "description": "Smart camera tools for passionate creators. \n\nNew starting from 2.7.1: Are you looking for the Cinemachine menu? It has moved to the GameObject menu.\n\nIMPORTANT NOTE: If you are upgrading from the legacy Asset Store version of Cinemachine, delete the Cinemachine asset from your project BEFORE installing this version from the Package Manager.", "keywords": ["camera", "follow", "rig", "fps", "cinematography", "aim", "orbit", "cutscene", "cinematic", "collision", "freelook", "cinemachine", "compose", "composition", "dolly", "track", "clearshot", "noise", "framing", "handheld", "lens", "impulse"], "category": "cinematography", "dependencies": {"com.unity.test-framework": "1.1.31"}, "samples": [{"displayName": "Cinemachine Example Scenes", "description": "Sample scenes illustrating various ways to use Cinemachine", "path": "Samples~/Cinemachine Example Scenes"}], "_upm": {"changelog": "- Bugfix: AxisState was not respecting timescale == 0.\n- Bugfix: Very occasional axis drift in SimpleFollow when viewing angle is +-90 degrees.\n- URP: add temporal effects reset on camera cut for URP 14.0.4 and up.\n- Bugfix: MixingCamera calls OnTransitionFromCamera correctly for all its children.\n- Bugfix: Passive vcams with noise were not respecting transform's z rotation during blends.\n- Regression fix: CinemachineSmoothPath.GetLocalOrientation was returning global orientations."}, "upmCi": {"footprint": "9da93759119bb2786ea56a4d58becb19a8690c0a"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.cinemachine@2.9/manual/index.html", "repository": {"url": "https://github.com/Unity-Technologies/com.unity.cinemachine.git", "type": "git", "revision": "46485ca80113434766f2b256525bb98def548fb9"}}