namespace UnityEngine.Rendering
{
    static class Hammersley
    {
        static float[]  k_Hammersley2dSeq16 = {
            0.00000000f, 0.00000000f, 0.0f, 0.0f,
            0.06250000f, 0.50000000f, 0.0f, 0.0f,
            0.12500000f, 0.25000000f, 0.0f, 0.0f,
            0.18750000f, 0.75000000f, 0.0f, 0.0f,
            0.25000000f, 0.12500000f, 0.0f, 0.0f,
            0.31250000f, 0.62500000f, 0.0f, 0.0f,
            0.37500000f, 0.37500000f, 0.0f, 0.0f,
            0.43750000f, 0.87500000f, 0.0f, 0.0f,
            0.50000000f, 0.06250000f, 0.0f, 0.0f,
            0.56250000f, 0.56250000f, 0.0f, 0.0f,
            0.62500000f, 0.31250000f, 0.0f, 0.0f,
            0.68750000f, 0.81250000f, 0.0f, 0.0f,
            0.75000000f, 0.18750000f, 0.0f, 0.0f,
            0.81250000f, 0.68750000f, 0.0f, 0.0f,
            0.87500000f, 0.43750000f, 0.0f, 0.0f,
            0.93750000f, 0.93750000f,  0.0f, 0.0f,
        };

        static float[] k_Hammersley2dSeq32 = {
            0.00000000f, 0.00000000f, 0.0f, 0.0f,
            0.03125000f, 0.50000000f, 0.0f, 0.0f,
            0.06250000f, 0.25000000f, 0.0f, 0.0f,
            0.09375000f, 0.75000000f, 0.0f, 0.0f,
            0.12500000f, 0.12500000f, 0.0f, 0.0f,
            0.15625000f, 0.62500000f, 0.0f, 0.0f,
            0.18750000f, 0.37500000f, 0.0f, 0.0f,
            0.21875000f, 0.87500000f, 0.0f, 0.0f,
            0.25000000f, 0.06250000f, 0.0f, 0.0f,
            0.28125000f, 0.56250000f, 0.0f, 0.0f,
            0.31250000f, 0.31250000f, 0.0f, 0.0f,
            0.34375000f, 0.81250000f, 0.0f, 0.0f,
            0.37500000f, 0.18750000f, 0.0f, 0.0f,
            0.40625000f, 0.68750000f, 0.0f, 0.0f,
            0.43750000f, 0.43750000f, 0.0f, 0.0f,
            0.46875000f, 0.93750000f, 0.0f, 0.0f,
            0.50000000f, 0.03125000f, 0.0f, 0.0f,
            0.53125000f, 0.53125000f, 0.0f, 0.0f,
            0.56250000f, 0.28125000f, 0.0f, 0.0f,
            0.59375000f, 0.78125000f, 0.0f, 0.0f,
            0.62500000f, 0.15625000f, 0.0f, 0.0f,
            0.65625000f, 0.65625000f, 0.0f, 0.0f,
            0.68750000f, 0.40625000f, 0.0f, 0.0f,
            0.71875000f, 0.90625000f, 0.0f, 0.0f,
            0.75000000f, 0.09375000f, 0.0f, 0.0f,
            0.78125000f, 0.59375000f, 0.0f, 0.0f,
            0.81250000f, 0.34375000f, 0.0f, 0.0f,
            0.84375000f, 0.84375000f, 0.0f, 0.0f,
            0.87500000f, 0.21875000f, 0.0f, 0.0f,
            0.90625000f, 0.71875000f, 0.0f, 0.0f,
            0.93750000f, 0.46875000f, 0.0f, 0.0f,
            0.96875000f, 0.96875000f, 0.0f, 0.0f,
        };

        static float[] k_Hammersley2dSeq64 = {
            0.00000000f, 0.00000000f, 0.0f, 0.0f,
            0.01562500f, 0.50000000f, 0.0f, 0.0f,
            0.03125000f, 0.25000000f, 0.0f, 0.0f,
            0.04687500f, 0.75000000f, 0.0f, 0.0f,
            0.06250000f, 0.12500000f, 0.0f, 0.0f,
            0.07812500f, 0.62500000f, 0.0f, 0.0f,
            0.09375000f, 0.37500000f, 0.0f, 0.0f,
            0.10937500f, 0.87500000f, 0.0f, 0.0f,
            0.12500000f, 0.06250000f, 0.0f, 0.0f,
            0.14062500f, 0.56250000f, 0.0f, 0.0f,
            0.15625000f, 0.31250000f, 0.0f, 0.0f,
            0.17187500f, 0.81250000f, 0.0f, 0.0f,
            0.18750000f, 0.18750000f, 0.0f, 0.0f,
            0.20312500f, 0.68750000f, 0.0f, 0.0f,
            0.21875000f, 0.43750000f, 0.0f, 0.0f,
            0.23437500f, 0.93750000f, 0.0f, 0.0f,
            0.25000000f, 0.03125000f, 0.0f, 0.0f,
            0.26562500f, 0.53125000f, 0.0f, 0.0f,
            0.28125000f, 0.28125000f, 0.0f, 0.0f,
            0.29687500f, 0.78125000f, 0.0f, 0.0f,
            0.31250000f, 0.15625000f, 0.0f, 0.0f,
            0.32812500f, 0.65625000f, 0.0f, 0.0f,
            0.34375000f, 0.40625000f, 0.0f, 0.0f,
            0.35937500f, 0.90625000f, 0.0f, 0.0f,
            0.37500000f, 0.09375000f, 0.0f, 0.0f,
            0.39062500f, 0.59375000f, 0.0f, 0.0f,
            0.40625000f, 0.34375000f, 0.0f, 0.0f,
            0.42187500f, 0.84375000f, 0.0f, 0.0f,
            0.43750000f, 0.21875000f, 0.0f, 0.0f,
            0.45312500f, 0.71875000f, 0.0f, 0.0f,
            0.46875000f, 0.46875000f, 0.0f, 0.0f,
            0.48437500f, 0.96875000f, 0.0f, 0.0f,
            0.50000000f, 0.01562500f, 0.0f, 0.0f,
            0.51562500f, 0.51562500f, 0.0f, 0.0f,
            0.53125000f, 0.26562500f, 0.0f, 0.0f,
            0.54687500f, 0.76562500f, 0.0f, 0.0f,
            0.56250000f, 0.14062500f, 0.0f, 0.0f,
            0.57812500f, 0.64062500f, 0.0f, 0.0f,
            0.59375000f, 0.39062500f, 0.0f, 0.0f,
            0.60937500f, 0.89062500f, 0.0f, 0.0f,
            0.62500000f, 0.07812500f, 0.0f, 0.0f,
            0.64062500f, 0.57812500f, 0.0f, 0.0f,
            0.65625000f, 0.32812500f, 0.0f, 0.0f,
            0.67187500f, 0.82812500f, 0.0f, 0.0f,
            0.68750000f, 0.20312500f, 0.0f, 0.0f,
            0.70312500f, 0.70312500f, 0.0f, 0.0f,
            0.71875000f, 0.45312500f, 0.0f, 0.0f,
            0.73437500f, 0.95312500f, 0.0f, 0.0f,
            0.75000000f, 0.04687500f, 0.0f, 0.0f,
            0.76562500f, 0.54687500f, 0.0f, 0.0f,
            0.78125000f, 0.29687500f, 0.0f, 0.0f,
            0.79687500f, 0.79687500f, 0.0f, 0.0f,
            0.81250000f, 0.17187500f, 0.0f, 0.0f,
            0.82812500f, 0.67187500f, 0.0f, 0.0f,
            0.84375000f, 0.42187500f, 0.0f, 0.0f,
            0.85937500f, 0.92187500f, 0.0f, 0.0f,
            0.87500000f, 0.10937500f, 0.0f, 0.0f,
            0.89062500f, 0.60937500f, 0.0f, 0.0f,
            0.90625000f, 0.35937500f, 0.0f, 0.0f,
            0.92187500f, 0.85937500f, 0.0f, 0.0f,
            0.93750000f, 0.23437500f, 0.0f, 0.0f,
            0.95312500f, 0.73437500f, 0.0f, 0.0f,
            0.96875000f, 0.48437500f, 0.0f, 0.0f,
            0.98437500f, 0.98437500f, 0.0f, 0.0f,
        };

        static float[] k_Hammersley2dSeq256 = {
            0.00000000f, 0.00000000f, 0.0f, 0.0f,
            0.00390625f, 0.50000000f, 0.0f, 0.0f,
            0.00781250f, 0.25000000f, 0.0f, 0.0f,
            0.01171875f, 0.75000000f, 0.0f, 0.0f,
            0.01562500f, 0.12500000f, 0.0f, 0.0f,
            0.01953125f, 0.62500000f, 0.0f, 0.0f,
            0.02343750f, 0.37500000f, 0.0f, 0.0f,
            0.02734375f, 0.87500000f, 0.0f, 0.0f,
            0.03125000f, 0.06250000f, 0.0f, 0.0f,
            0.03515625f, 0.56250000f, 0.0f, 0.0f,
            0.03906250f, 0.31250000f, 0.0f, 0.0f,
            0.04296875f, 0.81250000f, 0.0f, 0.0f,
            0.04687500f, 0.18750000f, 0.0f, 0.0f,
            0.05078125f, 0.68750000f, 0.0f, 0.0f,
            0.05468750f, 0.43750000f, 0.0f, 0.0f,
            0.05859375f, 0.93750000f, 0.0f, 0.0f,
            0.06250000f, 0.03125000f, 0.0f, 0.0f,
            0.06640625f, 0.53125000f, 0.0f, 0.0f,
            0.07031250f, 0.28125000f, 0.0f, 0.0f,
            0.07421875f, 0.78125000f, 0.0f, 0.0f,
            0.07812500f, 0.15625000f, 0.0f, 0.0f,
            0.08203125f, 0.65625000f, 0.0f, 0.0f,
            0.08593750f, 0.40625000f, 0.0f, 0.0f,
            0.08984375f, 0.90625000f, 0.0f, 0.0f,
            0.09375000f, 0.09375000f, 0.0f, 0.0f,
            0.09765625f, 0.59375000f, 0.0f, 0.0f,
            0.10156250f, 0.34375000f, 0.0f, 0.0f,
            0.10546875f, 0.84375000f, 0.0f, 0.0f,
            0.10937500f, 0.21875000f, 0.0f, 0.0f,
            0.11328125f, 0.71875000f, 0.0f, 0.0f,
            0.11718750f, 0.46875000f, 0.0f, 0.0f,
            0.12109375f, 0.96875000f, 0.0f, 0.0f,
            0.12500000f, 0.01562500f, 0.0f, 0.0f,
            0.12890625f, 0.51562500f, 0.0f, 0.0f,
            0.13281250f, 0.26562500f, 0.0f, 0.0f,
            0.13671875f, 0.76562500f, 0.0f, 0.0f,
            0.14062500f, 0.14062500f, 0.0f, 0.0f,
            0.14453125f, 0.64062500f, 0.0f, 0.0f,
            0.14843750f, 0.39062500f, 0.0f, 0.0f,
            0.15234375f, 0.89062500f, 0.0f, 0.0f,
            0.15625000f, 0.07812500f, 0.0f, 0.0f,
            0.16015625f, 0.57812500f, 0.0f, 0.0f,
            0.16406250f, 0.32812500f, 0.0f, 0.0f,
            0.16796875f, 0.82812500f, 0.0f, 0.0f,
            0.17187500f, 0.20312500f, 0.0f, 0.0f,
            0.17578125f, 0.70312500f, 0.0f, 0.0f,
            0.17968750f, 0.45312500f, 0.0f, 0.0f,
            0.18359375f, 0.95312500f, 0.0f, 0.0f,
            0.18750000f, 0.04687500f, 0.0f, 0.0f,
            0.19140625f, 0.54687500f, 0.0f, 0.0f,
            0.19531250f, 0.29687500f, 0.0f, 0.0f,
            0.19921875f, 0.79687500f, 0.0f, 0.0f,
            0.20312500f, 0.17187500f, 0.0f, 0.0f,
            0.20703125f, 0.67187500f, 0.0f, 0.0f,
            0.21093750f, 0.42187500f, 0.0f, 0.0f,
            0.21484375f, 0.92187500f, 0.0f, 0.0f,
            0.21875000f, 0.10937500f, 0.0f, 0.0f,
            0.22265625f, 0.60937500f, 0.0f, 0.0f,
            0.22656250f, 0.35937500f, 0.0f, 0.0f,
            0.23046875f, 0.85937500f, 0.0f, 0.0f,
            0.23437500f, 0.23437500f, 0.0f, 0.0f,
            0.23828125f, 0.73437500f, 0.0f, 0.0f,
            0.24218750f, 0.48437500f, 0.0f, 0.0f,
            0.24609375f, 0.98437500f, 0.0f, 0.0f,
            0.25000000f, 0.00781250f, 0.0f, 0.0f,
            0.25390625f, 0.50781250f, 0.0f, 0.0f,
            0.25781250f, 0.25781250f, 0.0f, 0.0f,
            0.26171875f, 0.75781250f, 0.0f, 0.0f,
            0.26562500f, 0.13281250f, 0.0f, 0.0f,
            0.26953125f, 0.63281250f, 0.0f, 0.0f,
            0.27343750f, 0.38281250f, 0.0f, 0.0f,
            0.27734375f, 0.88281250f, 0.0f, 0.0f,
            0.28125000f, 0.07031250f, 0.0f, 0.0f,
            0.28515625f, 0.57031250f, 0.0f, 0.0f,
            0.28906250f, 0.32031250f, 0.0f, 0.0f,
            0.29296875f, 0.82031250f, 0.0f, 0.0f,
            0.29687500f, 0.19531250f, 0.0f, 0.0f,
            0.30078125f, 0.69531250f, 0.0f, 0.0f,
            0.30468750f, 0.44531250f, 0.0f, 0.0f,
            0.30859375f, 0.94531250f, 0.0f, 0.0f,
            0.31250000f, 0.03906250f, 0.0f, 0.0f,
            0.31640625f, 0.53906250f, 0.0f, 0.0f,
            0.32031250f, 0.28906250f, 0.0f, 0.0f,
            0.32421875f, 0.78906250f, 0.0f, 0.0f,
            0.32812500f, 0.16406250f, 0.0f, 0.0f,
            0.33203125f, 0.66406250f, 0.0f, 0.0f,
            0.33593750f, 0.41406250f, 0.0f, 0.0f,
            0.33984375f, 0.91406250f, 0.0f, 0.0f,
            0.34375000f, 0.10156250f, 0.0f, 0.0f,
            0.34765625f, 0.60156250f, 0.0f, 0.0f,
            0.35156250f, 0.35156250f, 0.0f, 0.0f,
            0.35546875f, 0.85156250f, 0.0f, 0.0f,
            0.35937500f, 0.22656250f, 0.0f, 0.0f,
            0.36328125f, 0.72656250f, 0.0f, 0.0f,
            0.36718750f, 0.47656250f, 0.0f, 0.0f,
            0.37109375f, 0.97656250f, 0.0f, 0.0f,
            0.37500000f, 0.02343750f, 0.0f, 0.0f,
            0.37890625f, 0.52343750f, 0.0f, 0.0f,
            0.38281250f, 0.27343750f, 0.0f, 0.0f,
            0.38671875f, 0.77343750f, 0.0f, 0.0f,
            0.39062500f, 0.14843750f, 0.0f, 0.0f,
            0.39453125f, 0.64843750f, 0.0f, 0.0f,
            0.39843750f, 0.39843750f, 0.0f, 0.0f,
            0.40234375f, 0.89843750f, 0.0f, 0.0f,
            0.40625000f, 0.08593750f, 0.0f, 0.0f,
            0.41015625f, 0.58593750f, 0.0f, 0.0f,
            0.41406250f, 0.33593750f, 0.0f, 0.0f,
            0.41796875f, 0.83593750f, 0.0f, 0.0f,
            0.42187500f, 0.21093750f, 0.0f, 0.0f,
            0.42578125f, 0.71093750f, 0.0f, 0.0f,
            0.42968750f, 0.46093750f, 0.0f, 0.0f,
            0.43359375f, 0.96093750f, 0.0f, 0.0f,
            0.43750000f, 0.05468750f, 0.0f, 0.0f,
            0.44140625f, 0.55468750f, 0.0f, 0.0f,
            0.44531250f, 0.30468750f, 0.0f, 0.0f,
            0.44921875f, 0.80468750f, 0.0f, 0.0f,
            0.45312500f, 0.17968750f, 0.0f, 0.0f,
            0.45703125f, 0.67968750f, 0.0f, 0.0f,
            0.46093750f, 0.42968750f, 0.0f, 0.0f,
            0.46484375f, 0.92968750f, 0.0f, 0.0f,
            0.46875000f, 0.11718750f, 0.0f, 0.0f,
            0.47265625f, 0.61718750f, 0.0f, 0.0f,
            0.47656250f, 0.36718750f, 0.0f, 0.0f,
            0.48046875f, 0.86718750f, 0.0f, 0.0f,
            0.48437500f, 0.24218750f, 0.0f, 0.0f,
            0.48828125f, 0.74218750f, 0.0f, 0.0f,
            0.49218750f, 0.49218750f, 0.0f, 0.0f,
            0.49609375f, 0.99218750f, 0.0f, 0.0f,
            0.50000000f, 0.00390625f, 0.0f, 0.0f,
            0.50390625f, 0.50390625f, 0.0f, 0.0f,
            0.50781250f, 0.25390625f, 0.0f, 0.0f,
            0.51171875f, 0.75390625f, 0.0f, 0.0f,
            0.51562500f, 0.12890625f, 0.0f, 0.0f,
            0.51953125f, 0.62890625f, 0.0f, 0.0f,
            0.52343750f, 0.37890625f, 0.0f, 0.0f,
            0.52734375f, 0.87890625f, 0.0f, 0.0f,
            0.53125000f, 0.06640625f, 0.0f, 0.0f,
            0.53515625f, 0.56640625f, 0.0f, 0.0f,
            0.53906250f, 0.31640625f, 0.0f, 0.0f,
            0.54296875f, 0.81640625f, 0.0f, 0.0f,
            0.54687500f, 0.19140625f, 0.0f, 0.0f,
            0.55078125f, 0.69140625f, 0.0f, 0.0f,
            0.55468750f, 0.44140625f, 0.0f, 0.0f,
            0.55859375f, 0.94140625f, 0.0f, 0.0f,
            0.56250000f, 0.03515625f, 0.0f, 0.0f,
            0.56640625f, 0.53515625f, 0.0f, 0.0f,
            0.57031250f, 0.28515625f, 0.0f, 0.0f,
            0.57421875f, 0.78515625f, 0.0f, 0.0f,
            0.57812500f, 0.16015625f, 0.0f, 0.0f,
            0.58203125f, 0.66015625f, 0.0f, 0.0f,
            0.58593750f, 0.41015625f, 0.0f, 0.0f,
            0.58984375f, 0.91015625f, 0.0f, 0.0f,
            0.59375000f, 0.09765625f, 0.0f, 0.0f,
            0.59765625f, 0.59765625f, 0.0f, 0.0f,
            0.60156250f, 0.34765625f, 0.0f, 0.0f,
            0.60546875f, 0.84765625f, 0.0f, 0.0f,
            0.60937500f, 0.22265625f, 0.0f, 0.0f,
            0.61328125f, 0.72265625f, 0.0f, 0.0f,
            0.61718750f, 0.47265625f, 0.0f, 0.0f,
            0.62109375f, 0.97265625f, 0.0f, 0.0f,
            0.62500000f, 0.01953125f, 0.0f, 0.0f,
            0.62890625f, 0.51953125f, 0.0f, 0.0f,
            0.63281250f, 0.26953125f, 0.0f, 0.0f,
            0.63671875f, 0.76953125f, 0.0f, 0.0f,
            0.64062500f, 0.14453125f, 0.0f, 0.0f,
            0.64453125f, 0.64453125f, 0.0f, 0.0f,
            0.64843750f, 0.39453125f, 0.0f, 0.0f,
            0.65234375f, 0.89453125f, 0.0f, 0.0f,
            0.65625000f, 0.08203125f, 0.0f, 0.0f,
            0.66015625f, 0.58203125f, 0.0f, 0.0f,
            0.66406250f, 0.33203125f, 0.0f, 0.0f,
            0.66796875f, 0.83203125f, 0.0f, 0.0f,
            0.67187500f, 0.20703125f, 0.0f, 0.0f,
            0.67578125f, 0.70703125f, 0.0f, 0.0f,
            0.67968750f, 0.45703125f, 0.0f, 0.0f,
            0.68359375f, 0.95703125f, 0.0f, 0.0f,
            0.68750000f, 0.05078125f, 0.0f, 0.0f,
            0.69140625f, 0.55078125f, 0.0f, 0.0f,
            0.69531250f, 0.30078125f, 0.0f, 0.0f,
            0.69921875f, 0.80078125f, 0.0f, 0.0f,
            0.70312500f, 0.17578125f, 0.0f, 0.0f,
            0.70703125f, 0.67578125f, 0.0f, 0.0f,
            0.71093750f, 0.42578125f, 0.0f, 0.0f,
            0.71484375f, 0.92578125f, 0.0f, 0.0f,
            0.71875000f, 0.11328125f, 0.0f, 0.0f,
            0.72265625f, 0.61328125f, 0.0f, 0.0f,
            0.72656250f, 0.36328125f, 0.0f, 0.0f,
            0.73046875f, 0.86328125f, 0.0f, 0.0f,
            0.73437500f, 0.23828125f, 0.0f, 0.0f,
            0.73828125f, 0.73828125f, 0.0f, 0.0f,
            0.74218750f, 0.48828125f, 0.0f, 0.0f,
            0.74609375f, 0.98828125f, 0.0f, 0.0f,
            0.75000000f, 0.01171875f, 0.0f, 0.0f,
            0.75390625f, 0.51171875f, 0.0f, 0.0f,
            0.75781250f, 0.26171875f, 0.0f, 0.0f,
            0.76171875f, 0.76171875f, 0.0f, 0.0f,
            0.76562500f, 0.13671875f, 0.0f, 0.0f,
            0.76953125f, 0.63671875f, 0.0f, 0.0f,
            0.77343750f, 0.38671875f, 0.0f, 0.0f,
            0.77734375f, 0.88671875f, 0.0f, 0.0f,
            0.78125000f, 0.07421875f, 0.0f, 0.0f,
            0.78515625f, 0.57421875f, 0.0f, 0.0f,
            0.78906250f, 0.32421875f, 0.0f, 0.0f,
            0.79296875f, 0.82421875f, 0.0f, 0.0f,
            0.79687500f, 0.19921875f, 0.0f, 0.0f,
            0.80078125f, 0.69921875f, 0.0f, 0.0f,
            0.80468750f, 0.44921875f, 0.0f, 0.0f,
            0.80859375f, 0.94921875f, 0.0f, 0.0f,
            0.81250000f, 0.04296875f, 0.0f, 0.0f,
            0.81640625f, 0.54296875f, 0.0f, 0.0f,
            0.82031250f, 0.29296875f, 0.0f, 0.0f,
            0.82421875f, 0.79296875f, 0.0f, 0.0f,
            0.82812500f, 0.16796875f, 0.0f, 0.0f,
            0.83203125f, 0.66796875f, 0.0f, 0.0f,
            0.83593750f, 0.41796875f, 0.0f, 0.0f,
            0.83984375f, 0.91796875f, 0.0f, 0.0f,
            0.84375000f, 0.10546875f, 0.0f, 0.0f,
            0.84765625f, 0.60546875f, 0.0f, 0.0f,
            0.85156250f, 0.35546875f, 0.0f, 0.0f,
            0.85546875f, 0.85546875f, 0.0f, 0.0f,
            0.85937500f, 0.23046875f, 0.0f, 0.0f,
            0.86328125f, 0.73046875f, 0.0f, 0.0f,
            0.86718750f, 0.48046875f, 0.0f, 0.0f,
            0.87109375f, 0.98046875f, 0.0f, 0.0f,
            0.87500000f, 0.02734375f, 0.0f, 0.0f,
            0.87890625f, 0.52734375f, 0.0f, 0.0f,
            0.88281250f, 0.27734375f, 0.0f, 0.0f,
            0.88671875f, 0.77734375f, 0.0f, 0.0f,
            0.89062500f, 0.15234375f, 0.0f, 0.0f,
            0.89453125f, 0.65234375f, 0.0f, 0.0f,
            0.89843750f, 0.40234375f, 0.0f, 0.0f,
            0.90234375f, 0.90234375f, 0.0f, 0.0f,
            0.90625000f, 0.08984375f, 0.0f, 0.0f,
            0.91015625f, 0.58984375f, 0.0f, 0.0f,
            0.91406250f, 0.33984375f, 0.0f, 0.0f,
            0.91796875f, 0.83984375f, 0.0f, 0.0f,
            0.92187500f, 0.21484375f, 0.0f, 0.0f,
            0.92578125f, 0.71484375f, 0.0f, 0.0f,
            0.92968750f, 0.46484375f, 0.0f, 0.0f,
            0.93359375f, 0.96484375f, 0.0f, 0.0f,
            0.93750000f, 0.05859375f, 0.0f, 0.0f,
            0.94140625f, 0.55859375f, 0.0f, 0.0f,
            0.94531250f, 0.30859375f, 0.0f, 0.0f,
            0.94921875f, 0.80859375f, 0.0f, 0.0f,
            0.95312500f, 0.18359375f, 0.0f, 0.0f,
            0.95703125f, 0.68359375f, 0.0f, 0.0f,
            0.96093750f, 0.43359375f, 0.0f, 0.0f,
            0.96484375f, 0.93359375f, 0.0f, 0.0f,
            0.96875000f, 0.12109375f, 0.0f, 0.0f,
            0.97265625f, 0.62109375f, 0.0f, 0.0f,
            0.97656250f, 0.37109375f, 0.0f, 0.0f,
            0.98046875f, 0.87109375f, 0.0f, 0.0f,
            0.98437500f, 0.24609375f, 0.0f, 0.0f,
            0.98828125f, 0.74609375f, 0.0f, 0.0f,
            0.99218750f, 0.49609375f, 0.0f, 0.0f,
            0.99609375f, 0.99609375f, 0.0f, 0.0f,
        };

        [GenerateHLSL(needAccessors = false, generateCBuffer = true)]
        unsafe struct Hammersley2dSeq16
        {
            [HLSLArray(16, typeof(Vector4))]
            public fixed float hammersley2dSeq16[16 * 4];
        }

        [GenerateHLSL(needAccessors = false, generateCBuffer = true)]
        unsafe struct Hammersley2dSeq32
        {
            [HLSLArray(32, typeof(Vector4))]
            public fixed float hammersley2dSeq32[32 * 4];
        }

        [GenerateHLSL(needAccessors = false, generateCBuffer = true)]
        unsafe struct Hammersley2dSeq64
        {
            [HLSLArray(64, typeof(Vector4))]
            public fixed float hammersley2dSeq64[64 * 4];
        }

        [GenerateHLSL(needAccessors = false, generateCBuffer = true)]
        unsafe struct Hammersley2dSeq256
        {
            [HLSLArray(256, typeof(Vector4))]
            public fixed float hammersley2dSeq256[256 * 4];
        }

        static readonly int s_hammersley2DSeq16Id = Shader.PropertyToID("Hammersley2dSeq16");
        static readonly int s_hammersley2DSeq32Id = Shader.PropertyToID("Hammersley2dSeq32");
        static readonly int s_hammersley2DSeq64Id = Shader.PropertyToID("Hammersley2dSeq64");
        static readonly int s_hammersley2DSeq256Id = Shader.PropertyToID("Hammersley2dSeq256");

        unsafe public static void Initialize()
        {
            Hammersley2dSeq16 hammersley2DSeq16 = new Hammersley2dSeq16();
            Hammersley2dSeq32 hammersley2DSeq32 = new Hammersley2dSeq32();
            Hammersley2dSeq64 hammersley2DSeq64 = new Hammersley2dSeq64();
            Hammersley2dSeq256 hammersley2DSeq256 = new Hammersley2dSeq256();

            for (int i = 0; i < k_Hammersley2dSeq16.Length; ++i)
                hammersley2DSeq16.hammersley2dSeq16[i] = k_Hammersley2dSeq16[i];

            for (int i = 0; i < k_Hammersley2dSeq32.Length; ++i)
                hammersley2DSeq32.hammersley2dSeq32[i] = k_Hammersley2dSeq32[i];

            for (int i = 0; i < k_Hammersley2dSeq64.Length; ++i)
                hammersley2DSeq64.hammersley2dSeq64[i] = k_Hammersley2dSeq64[i];

            for (int i = 0; i < k_Hammersley2dSeq256.Length; ++i)
                hammersley2DSeq256.hammersley2dSeq256[i] = k_Hammersley2dSeq256[i];

            ConstantBuffer.UpdateData(hammersley2DSeq16);
            ConstantBuffer.UpdateData(hammersley2DSeq32);
            ConstantBuffer.UpdateData(hammersley2DSeq64);
            ConstantBuffer.UpdateData(hammersley2DSeq256);
        }

        public static void BindConstants(CommandBuffer cmd, ComputeShader cs)
        {
            ConstantBuffer.Set<Hammersley2dSeq16>(cmd, cs, s_hammersley2DSeq16Id);
            ConstantBuffer.Set<Hammersley2dSeq32>(cmd, cs, s_hammersley2DSeq32Id);
            ConstantBuffer.Set<Hammersley2dSeq64>(cmd, cs, s_hammersley2DSeq64Id);
            ConstantBuffer.Set<Hammersley2dSeq256>(cmd, cs, s_hammersley2DSeq256Id);
        }
    }
}
