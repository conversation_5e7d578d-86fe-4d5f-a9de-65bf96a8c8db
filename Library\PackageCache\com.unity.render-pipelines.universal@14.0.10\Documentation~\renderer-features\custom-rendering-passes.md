# Custom render passes

Create a custom render pass in a C# script and inject it into the Universal Render Pipeline (URP) frame rendering loop.

|Page|Description|
|-|-|
|[Custom render pass workflow in URP](custom-rendering-pass-workflow-in-urp.md)|Add and inject a custom render pass to change how URP renders a scene or the objects within a scene.|
|[Scriptable Render Passes](scriptable-render-passes.md)|Use the Scriptable Render Pass API to create a custom render pass.|
|[Scriptable Renderer Features](scriptable-renderer-features/scriptable-renderer-features-landing.md)|Use the Scriptable Renderer Feature API to inject a custom render pass into a URP renderer.|
|[Working with textures](../working-with-textures.md)|How to access and use textures in a custom render pass, including how to blit.|

