{"name": "InGameHintsActions", "maps": [{"name": "Gameplay", "id": "9af2d1b0-cc47-4300-854c-838acb4b168b", "actions": [{"name": "Move", "type": "Value", "id": "7e7492e7-1329-48bb-9fdc-279fd15473b4", "expectedControlType": "Vector2", "processors": "", "interactions": ""}, {"name": "Look", "type": "Value", "id": "981fecc2-2e7a-4d6a-b041-00b47626e0a1", "expectedControlType": "Vector2", "processors": "", "interactions": ""}, {"name": "PickUp", "type": "<PERSON><PERSON>", "id": "5a59bbc2-a3d4-4cbd-88bb-01120d97dc69", "expectedControlType": "", "processors": "", "interactions": ""}, {"name": "Drop", "type": "<PERSON><PERSON>", "id": "f37bbe7e-e241-443f-b868-c784e1219f25", "expectedControlType": "", "processors": "", "interactions": ""}, {"name": "<PERSON>hrow", "type": "<PERSON><PERSON>", "id": "e450d71c-7cc5-4879-afb5-f3ed682d9824", "expectedControlType": "", "processors": "", "interactions": ""}], "bindings": [{"name": "", "id": "5abc4d20-74bd-4f14-902f-2bd2cf59cc28", "path": "<Gamepad>/leftStick", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Move", "isComposite": false, "isPartOfComposite": false}, {"name": "WASD", "id": "b16141b1-1611-44db-9576-5a004eb451f2", "path": "2DVector", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Move", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "2b20de3f-1ad8-4b42-b591-595edf60dced", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "2f92eaa9-7f1f-4f42-9682-d105f7c2fc22", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "3ba79a56-c5f8-4999-8203-bef8471f4bd8", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "8d9acfe6-d844-4860-a151-01d6eb0dfb48", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "b69cbeb7-a5bf-4df1-8965-17d944634cef", "path": "<Gamepad>/rightStick", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "b2ddefc9-49da-485d-be28-58e3ec3f8080", "path": "<Mouse>/delta", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "92182492-7b62-47e0-94ad-53d9937d9905", "path": "<Gamepad>/buttonSouth", "interactions": "", "processors": "", "groups": "Gamepad", "action": "PickUp", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "e20635aa-ffe7-4ed9-8802-96c039d26a8f", "path": "<Keyboard>/q", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "PickUp", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "f5571cd9-1166-4ddc-9071-37dc597b1d4e", "path": "<Gamepad>/buttonEast", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Drop", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "950f549e-ec9c-4d03-aeff-f09ec4031d01", "path": "<Keyboard>/e", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Drop", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "47d1952d-797b-4f5b-986c-654b8e479deb", "path": "<Gamepad>/buttonSouth", "interactions": "", "processors": "", "groups": "Gamepad", "action": "<PERSON>hrow", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "02e2493d-1eb3-4334-9d25-92f2b5e21399", "path": "<Keyboard>/space", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "<PERSON>hrow", "isComposite": false, "isPartOfComposite": false}]}], "controlSchemes": [{"name": "Gamepad", "bindingGroup": "Gamepad", "devices": [{"devicePath": "<Gamepad>", "isOptional": false, "isOR": false}]}, {"name": "Keyboard&Mouse", "bindingGroup": "Keyboard&Mouse", "devices": [{"devicePath": "<Keyboard>", "isOptional": false, "isOR": false}, {"devicePath": "<Mouse>", "isOptional": false, "isOR": false}]}]}