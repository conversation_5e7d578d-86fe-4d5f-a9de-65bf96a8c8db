{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "d80b3371b7084961a777e5341e802c75",
    "m_Properties": [],
    "m_Keywords": [],
    "m_Dropdowns": [
        {
            "m_Id": "8ad9f8f1d50d42dc8afadfabadc0e660"
        }
    ],
    "m_CategoryData": [
        {
            "m_Id": "95bc13f53add4320a27e864facb45376"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "e52e1eead42d4b1190f0e367559f40d1"
        },
        {
            "m_Id": "500316ce02b449caaaff74b5cc788d43"
        },
        {
            "m_Id": "fe729eadce694bf28f76580a780c8b67"
        },
        {
            "m_Id": "efd9a6b95b484610b042c0b7c3ba799c"
        },
        {
            "m_Id": "52e85d376ba44316a9efa3dc0c72867b"
        }
    ],
    "m_GroupDatas": [],
    "m_StickyNoteDatas": [
        {
            "m_Id": "ecd7813de6f640d587773f3bbfc8a148"
        },
        {
            "m_Id": "7373a770330a4fd6acc5262b53b863f1"
        },
        {
            "m_Id": "d2300de440a442ac8fa123e7b881b35e"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "500316ce02b449caaaff74b5cc788d43"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "e52e1eead42d4b1190f0e367559f40d1"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "52e85d376ba44316a9efa3dc0c72867b"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "500316ce02b449caaaff74b5cc788d43"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "efd9a6b95b484610b042c0b7c3ba799c"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "500316ce02b449caaaff74b5cc788d43"
                },
                "m_SlotId": 4
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "fe729eadce694bf28f76580a780c8b67"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "500316ce02b449caaaff74b5cc788d43"
                },
                "m_SlotId": 3
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": []
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Sub Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": "e52e1eead42d4b1190f0e367559f40d1"
    },
    "m_SubDatas": [],
    "m_ActiveTargets": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "0844a95b89414c14a8415b32779d6b20",
    "m_Id": 3,
    "m_DisplayName": "Red",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Red",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "3b889d07793f40bca0f9e01614415a32",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DropdownNode",
    "m_ObjectId": "500316ce02b449caaaff74b5cc788d43",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Color Dropdown",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -285.0000305175781,
            "y": 0.000015436547982972115,
            "width": 153.5,
            "height": 142.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "3b889d07793f40bca0f9e01614415a32"
        },
        {
            "m_Id": "0844a95b89414c14a8415b32779d6b20"
        },
        {
            "m_Id": "54429eaef0954e9b80427b0f533f397d"
        },
        {
            "m_Id": "6ab4ff449d8e4c668c19e55dc73ae3d3"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Dropdown": {
        "m_Id": "8ad9f8f1d50d42dc8afadfabadc0e660"
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.ColorNode",
    "m_ObjectId": "52e85d376ba44316a9efa3dc0c72867b",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Color",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -576.0000610351563,
            "y": 125.00004577636719,
            "width": 208.00006103515626,
            "height": 125.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "869f01686b0949cfabf8830194b0103e"
        }
    ],
    "synonyms": [
        "rgba"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Color": {
        "color": {
            "r": 0.0,
            "g": 0.0,
            "b": 1.0,
            "a": 1.0
        },
        "mode": 0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "54429eaef0954e9b80427b0f533f397d",
    "m_Id": 4,
    "m_DisplayName": "Green",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Green",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "6a41319e942145a78581adf2ccf128c2",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "6ab4ff449d8e4c668c19e55dc73ae3d3",
    "m_Id": 2,
    "m_DisplayName": "Blue",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Blue",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "706054fa9c2043549a3172fded485c9f",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "7373a770330a4fd6acc5262b53b863f1",
    "m_Title": "",
    "m_Content": "Once created, select the Dropdown in the Blackboard and open the Graph Inspector.  From there, you can name the dropdown options and add or remove options.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -306.5000305175781,
        "y": 160.50001525878907,
        "width": 200.00003051757813,
        "height": 114.00001525878906
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "869f01686b0949cfabf8830194b0103e",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ShaderDropdown",
    "m_ObjectId": "8ad9f8f1d50d42dc8afadfabadc0e660",
    "m_Guid": {
        "m_GuidSerialized": "091d04c9-831d-45b6-ba04-ba45ab80f3b9"
    },
    "m_Name": "Color Dropdown",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "Color Dropdown",
    "m_DefaultReferenceName": "_Color_Dropdown",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Entries": [
        {
            "id": 3,
            "displayName": "Red"
        },
        {
            "id": 4,
            "displayName": "Green"
        },
        {
            "id": 2,
            "displayName": "Blue"
        }
    ],
    "m_Value": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "95bc13f53add4320a27e864facb45376",
    "m_Name": "",
    "m_ChildObjectList": [
        {
            "m_Id": "8ad9f8f1d50d42dc8afadfabadc0e660"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "d2300de440a442ac8fa123e7b881b35e",
    "m_Title": "",
    "m_Content": "This example shows very basic color inputs to the dropdown node, but you can also create complex groups of node logic for each input and the node will switch between them when the user makes a choice using the dropdown UI element on the Subgraph node.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -592.0,
        "y": 264.0,
        "width": 200.0,
        "height": 160.0
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphOutputNode",
    "m_ObjectId": "e52e1eead42d4b1190f0e367559f40d1",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Output",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "6a41319e942145a78581adf2ccf128c2"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "IsFirstSlotValid": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "ecd7813de6f640d587773f3bbfc8a148",
    "m_Title": "Subgraph Dropdown",
    "m_Content": "You can use the Blackboard Add menu to add a Dropdown node to your Subgraph.\n\nWhen the Subgraph is added to a graph asset, the Subgraph node will display the dropdown UI element and allow you to select one of the options.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -316.0,
        "y": -171.0,
        "width": 226.0,
        "height": 153.0
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "ef355f2a5d79426c84621b97187f44f5",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.ColorNode",
    "m_ObjectId": "efd9a6b95b484610b042c0b7c3ba799c",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Color",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -576.0000610351563,
            "y": 0.000015436547982972115,
            "width": 208.00006103515626,
            "height": 125.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "ef355f2a5d79426c84621b97187f44f5"
        }
    ],
    "synonyms": [
        "rgba"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Color": {
        "color": {
            "r": 0.0,
            "g": 1.0,
            "b": 0.0,
            "a": 1.0
        },
        "mode": 0
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.ColorNode",
    "m_ObjectId": "fe729eadce694bf28f76580a780c8b67",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Color",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -576.0000610351563,
            "y": -125.00001525878906,
            "width": 208.00006103515626,
            "height": 125.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "706054fa9c2043549a3172fded485c9f"
        }
    ],
    "synonyms": [
        "rgba"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Color": {
        "color": {
            "r": 1.0,
            "g": 0.0,
            "b": 0.0,
            "a": 1.0
        },
        "mode": 0
    }
}

