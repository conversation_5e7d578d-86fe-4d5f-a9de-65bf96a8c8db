# Upgrading to version 9.0.x of the Universal Render Pipeline

This page describes how to upgrade from an older version of the Universal Render Pipeline (URP) to version 9.0.x.

## Upgrading from URP 8.0.x and later 8.x releases

1. URP 9.0.x does not have breaking changes compared with URP 8.x.x. To upgrade URP to version 9.0.x, install the new version of the package.

## Upgrading from URP 7.2.x and later 7.x releases

1. URP 9.x.x does not support the package Post-Processing Stack v2. If your Project uses the package Post-Processing Stack v2, migrate the effects that use that package first.

## Upgrading from URP 7.0.x-7.1.x

1. Upgrade to URP 7.2.0 first. Refer to [Upgrading to version 7.2.0 of the Universal Render Pipeline](upgrade-guide-7-2-0.md).

2. URP 8.x.x does not support the package Post-Processing Stack v2. If your Project uses the package Post-Processing Stack v2, migrate the effects that use that package first.
