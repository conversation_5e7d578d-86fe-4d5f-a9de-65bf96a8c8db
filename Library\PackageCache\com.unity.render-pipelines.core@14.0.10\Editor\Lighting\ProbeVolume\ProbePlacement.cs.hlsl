//
// This file was automatically generated. Please don't edit by hand. Execute Editor command [ Edit > Rendering > Generate Shader Includes ] instead
//

#ifndef PROBEPLACEMENT_CS_HLSL
#define PROBEPLACEMENT_CS_HLSL
// Generated from UnityEngine.Rendering.ProbePlacement+GPUProbeVolumeOBB
// PackingRules = Exact
struct GPUProbeVolumeOBB
{
    float3 corner;
    float3 X;
    float3 Y;
    float3 Z;
    int minControllerSubdivLevel;
    int maxControllerSubdivLevel;
    int fillEmptySpaces;
    int maxSubdivLevelInsideVolume;
};


#endif
