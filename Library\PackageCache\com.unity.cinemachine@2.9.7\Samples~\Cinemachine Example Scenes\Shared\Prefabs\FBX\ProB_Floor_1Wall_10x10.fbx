; FBX 7.4.0 project file
; Copyright (C) 1997-2015 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7400
	CreationTimeStamp:  {
		Version: 1000
		Year: 2017
		Month: 10
		Day: 19
		Hour: 13
		Minute: 22
		Second: 40
		Millisecond: 673
	}
	Creator: "FBX SDK/FBX Plugins version 2017.1"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "exports static meshes with materials and textures"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "export mesh materials textures uvs"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "C:\Users\<USER>\AppData\Local\Temp\tmp173d6e19.tmp"
			P: "SrcDocumentUrl", "KString", "Url", "", "C:\Users\<USER>\AppData\Local\Temp\tmp173d6e19.tmp"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "1.0.0f1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "1.0.0f1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 1147066688, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 7
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 2
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 1
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
	ObjectType: "Video" {
		Count: 1
		PropertyTemplate: "FbxVideo" {
			Properties70:  {
				P: "ImageSequence", "bool", "", "",0
				P: "ImageSequenceOffset", "int", "Integer", "",0
				P: "FrameRate", "double", "Number", "",0
				P: "LastFrame", "int", "Integer", "",0
				P: "Width", "int", "Integer", "",0
				P: "Height", "int", "Integer", "",0
				P: "Path", "KString", "XRefUrl", "", ""
				P: "StartFrame", "int", "Integer", "",0
				P: "StopFrame", "int", "Integer", "",0
				P: "PlaySpeed", "double", "Number", "",0
				P: "Offset", "KTime", "Time", "",0
				P: "InterlaceMode", "enum", "", "",0
				P: "FreeRunning", "bool", "", "",0
				P: "Loop", "bool", "", "",0
				P: "AccessMode", "enum", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 1143716144, "Geometry::Scene", "Mesh" {
		Vertices: *924 {
			a: -0,0,0,-100,0,0,-0,100,0,-100,100,0,-1000,0,0,-1000,0,-100,-1000,100,0,-1000,100,-100,-100,0,-1000,-0,0,-1000,-100,100,-1000,-0,100,-1000,-0,0,-100,-0,100,-100,-100,100,-100,-100,0,-100,-100,0,-200,-0,0,-200,-1000,100,-200,-1000,0,-200,-0,100,-200,-100,100,-200,-100,0,-300,-0,0,-300,-1000,100,-300,-1000,0,-300,-0,100,-300,-100,100,-300,-100,0,-400,-0,0,-400,-1000,100,-400,-1000,0,-400,-0,100,-400,-100,100,-400,-100,0,-500,-0,0,-500,-1000,100,-500,-1000,0,-500,-0,100,-500,-100,100,-500,-100,0,-600,-0,0,-600,-1000,100,-600,-1000,0,-600,-0,100,-600,-100,100,-600,-100,0,-700,-0,0,-700,-1000,100,-700,-1000,0,-700,-0,100,-700,-100,100,-700,-100,0,-800,-0,0,-800,-1000,100,-800,-1000,0,-800,-0,100,-800,-100,100,-800,-100,0,-900,-0,0,-900,-1000,100,-900,-1000,0,-900,-0,100,-900,-100,100,-900,-1000,100,-1000,-1000,0,-1000,-200,0,0,-200,0,-100,-200,100,0,-200,100,-200,-200,100,-100,-200,0,-200,-200,100,-300,-200,0,-300,-200,100,-400,-200,0,-400,-200,100,-500,-200,0,-500,-200,100,-600,-200,0,-600,-200,100,-700,-200,0,-700,-200,100,-800,-200,0,-800,-200,100,-900,-200,0,-900,-200,100,-1000,-200,0,-1000,-300,0,0,-300,0,-100,-300,100,0,-300,100,-200,-300,100,-100,-300,0,-200,-300,100,-300,-300,0,-300,-300,100,-400,-300,0,-400,-300,100,-500,-300,0,-500,-300,100,-600,-300,0,-600,-300,100,-700,-300,0,-700,-300,100,-800,-300,0,-800,-300,100,-900,-300,0,-900,-300,100,-1000,-300,0,-1000,-400,0,0,-400,0,-100,-400,100,0,-400,100,-200,-400,100,-100,-400,0,-200,-400,100,-300,-400,0,-300,-400,100,-400,-400,0,-400,-400,100,-500,-400,0,-500,-400,100,-600,-400,0,-600,-400,100,-700,-400,0,-700,-400,100,-800,-400,0,-800,-400,100,-900,-400,0,-900,-400,100,-1000,-400,0,-1000,-500,0,0,-500,0,-100,-500,100,0,-500,100,-200,-500,100,-100,-500,0,-200,-500,100,-300,-500,0,-300,-500,100,-400,-500,0,-400,-500,100,-500,-500,0,-500,-500,100,-600,-500,0,-600,-500,100,-700,-500,0,-700,-500,100,-800,-500,0,-800,-500,100,-900,-500,0,-900,-500,100,-1000,-500,0,-1000,-600,0,0,-600,0,-100,-600,100,0,-600,100,-200,-600,100,-100,-600,0,-200,-600,100,-300,
-600,0,-300,-600,100,-400,-600,0,-400,-600,100,-500,-600,0,-500,-600,100,-600,-600,0,-600,-600,100,-700,-600,0,-700,-600,100,-800,-600,0,-800,-600,100,-900,-600,0,-900,-600,100,-1000,-600,0,-1000,-700,0,0,-700,0,-100,-700,100,0,-700,100,-200,-700,100,-100,-700,0,-200,-700,100,-300,-700,0,-300,-700,100,-400,-700,0,-400,-700,100,-500,-700,0,-500,-700,100,-600,-700,0,-600,-700,100,-700,-700,0,-700,-700,100,-800,-700,0,-800,-700,100,-900,-700,0,-900,-700,100,-1000,-700,0,-1000,-800,0,0,-800,0,-100,-800,100,0,-800,100,-200,-800,100,-100,-800,0,-200,-800,100,-300,-800,0,-300,-800,100,-400,-800,0,-400,-800,100,-500,-800,0,-500,-800,100,-600,-800,0,-600,-800,100,-700,-800,0,-700,-800,100,-800,-800,0,-800,-800,100,-900,-800,0,-900,-800,100,-1000,-800,0,-1000,-900,0,0,-900,0,-100,-900,100,0,-900,100,-200,-900,100,-100,-900,0,-200,-900,100,-300,-900,0,-300,-900,100,-400,-900,0,-400,-900,100,-500,-900,0,-500,-900,100,-600,-900,0,-600,-900,100,-700,-900,0,-700,-900,100,-800,-900,0,-800,-900,100,-900,-900,0,-900,-900,100,-1000,-900,0,-1000,-900,400,-200,-900,400,-100,-1000,400,-200,-1000,400,-100,-900,400,0,-1000,400,0,-900,400,-300,-1000,400,-300,-900,400,-400,-1000,400,-400,-900,400,-500,-1000,400,-500,-900,400,-600,-1000,400,-600,-900,400,-700,-1000,400,-700,-900,400,-800,-1000,400,-800,-900,400,-900,-1000,400,-900,-900,400,-1000,-1000,400,-1000,-900,200,-100,-900,200,0,-1000,200,-100,-1000,200,-200,-1000,200,0,-900,200,-200,-1000,200,-300,-900,200,-300,-1000,200,-400,-900,200,-400,-1000,200,-500,-900,200,-500,-1000,200,-600,-900,200,-600,-1000,200,-700,-900,200,-700,-1000,200,-800,-900,200,-800,-1000,200,-900,-900,200,-900,-1000,200,-1000,-900,200,-1000,-900,300,-100,-900,300,0,-1000,300,-100,-1000,300,-200,-1000,300,0,-900,300,-200,-1000,300,-300,-900,300,-300,-1000,300,-400,-900,300,-400,-1000,300,-500,-900,300,-500,-1000,300,-600,-900,300,-600,-1000,300,-700,-900,300,-700,-1000,300,-800,-900,300,-800,-1000,300,-900,-900,300,-900,-1000,300,-1000,-900,300,-1000
		} 
		PolygonVertexIndex: *1836 {
			a: 0,2,-2,1,2,-4,4,6,-6,5,6,-8,8,10,-10,9,10,-12,12,13,-1,0,13,-3,2,13,-4,3,13,-15,12,0,-16,15,0,-2,15,16,-13,12,16,-18,7,18,-6,5,18,-20,12,17,-14,13,17,-21,13,20,-15,14,20,-22,16,22,-18,17,22,-24,18,24,-20,19,24,-26,17,23,-21,20,23,-27,20,26,-22,21,26,-28,22,28,-24,23,28,-30,24,30,-26,25,30,-32,23,29,-27,26,29,-33,26,32,-28,27,32,-34,28,34,-30,29,34,-36,30,36,-32,31,36,-38,29,35,-33,32,35,-39,32,38,-34,33,38,-40,34,40,-36,35,40,-42,36,42,-38,37,42,-44,35,41,-39,38,41,-45,38,44,-40,39,44,-46,40,46,-42,41,46,-48,42,48,-44,43,48,-50,41,47,-45,44,47,-51,44,50,-46,45,50,-52,46,52,-48,47,52,-54,48,54,-50,49,54,-56,47,53,-51,50,53,-57,50,56,-52,51,56,-58,52,58,-54,53,58,-60,54,60,-56,55,60,-62,53,59,-57,56,59,-63,56,62,-58,57,62,-64,58,8,-60,59,8,-10,60,64,-62,61,64,-66,59,9,-63,62,9,-12,62,11,-64,63,11,-11,1,66,-16,15,66,-68,3,68,-2,1,68,-67,21,69,-15,14,69,-71,14,70,-4,3,70,-69,15,67,-17,16,67,-72,27,72,-22,21,72,-70,16,71,-23,22,71,-74,33,74,-28,27,74,-73,22,73,-29,28,73,-76,39,76,-34,33,76,-75,28,75,-35,34,75,-78,45,78,-40,39,78,-77,34,77,-41,40,77,-80,51,80,-46,45,80,-79,40,79,-47,46,79,-82,57,82,-52,51,82,-81,46,81,-53,52,81,-84,63,84,-58,57,84,-83,52,83,-59,58,83,-86,10,86,-64,63,86,-85,58,85,-9,8,85,-88,8,87,-11,10,87,-87,66,88,-68,67,88,-90,68,90,-67,66,90,-89,69,91,-71,70,91,-93,70,92,-69,68,92,-91,67,89,-72,71,89,-94,72,94,-70,69,94,-92,71,93,-74,73,93,-96,74,96,-73,72,96,-95,73,95,-76,75,95,-98,76,98,-75,74,98,-97,75,97,-78,77,97,-100,78,100,-77,76,100,-99,77,99,-80,79,99,-102,80,102,-79,78,102,-101,79,101,-82,81,101,-104,82,104,-81,80,104,-103,81,103,-84,83,103,-106,84,106,-83,82,106,-105,83,105,-86,85,105,-108,86,108,-85,84,108,-107,85,107,-88,87,107,-110,87,109,-87,86,109,-109,88,110,-90,89,110,-112,90,112,-89,88,112,-111,91,113,-93,92,113,-115,92,114,-91,90,114,-113,89,111,-94,93,111,-116,94,116,-92,91,116,-114,93,115,-96,95,115,-118,96,118,-95,94,118,-117,95,117,-98,97,117,-120,98,120,-97,96,120,-119,97,119,-100,99,119,-122,100,122,-99,98,122,-121,99,121,-102,101,121,-124,102,124,-101,100,
124,-123,101,123,-104,103,123,-126,104,126,-103,102,126,-125,103,125,-106,105,125,-128,106,128,-105,104,128,-127,105,127,-108,107,127,-130,108,130,-107,106,130,-129,107,129,-110,109,129,-132,109,131,-109,108,131,-131,110,132,-112,111,132,-134,112,134,-111,110,134,-133,113,135,-115,114,135,-137,114,136,-113,112,136,-135,111,133,-116,115,133,-138,116,138,-114,113,138,-136,115,137,-118,117,137,-140,118,140,-117,116,140,-139,117,139,-120,119,139,-142,120,142,-119,118,142,-141,119,141,-122,121,141,-144,122,144,-121,120,144,-143,121,143,-124,123,143,-146,124,146,-123,122,146,-145,123,145,-126,125,145,-148,126,148,-125,124,148,-147,125,147,-128,127,147,-150,128,150,-127,126,150,-149,127,149,-130,129,149,-152,130,152,-129,128,152,-151,129,151,-132,131,151,-154,131,153,-131,130,153,-153,132,154,-134,133,154,-156,134,156,-133,132,156,-155,135,157,-137,136,157,-159,136,158,-135,134,158,-157,133,155,-138,137,155,-160,138,160,-136,135,160,-158,137,159,-140,139,159,-162,140,162,-139,138,162,-161,139,161,-142,141,161,-164,142,164,-141,140,164,-163,141,163,-144,143,163,-166,144,166,-143,142,166,-165,143,165,-146,145,165,-168,146,168,-145,144,168,-167,145,167,-148,147,167,-170,148,170,-147,146,170,-169,147,169,-150,149,169,-172,150,172,-149,148,172,-171,149,171,-152,151,171,-174,152,174,-151,150,174,-173,151,173,-154,153,173,-176,153,175,-153,152,175,-175,154,176,-156,155,176,-178,156,178,-155,154,178,-177,157,179,-159,158,179,-181,158,180,-157,156,180,-179,155,177,-160,159,177,-182,160,182,-158,157,182,-180,159,181,-162,161,181,-184,162,184,-161,160,184,-183,161,183,-164,163,183,-186,164,186,-163,162,186,-185,163,185,-166,165,185,-188,166,188,-165,164,188,-187,165,187,-168,167,187,-190,168,190,-167,166,190,-189,167,189,-170,169,189,-192,170,192,-169,168,192,-191,169,191,-172,171,191,-194,172,194,-171,170,194,-193,171,193,-174,173,193,-196,174,196,-173,172,196,-195,173,195,-176,175,195,-198,175,197,-175,174,197,-197,176,198,-178,177,198,-200,178,200,-177,176,200,-199,179,201,-181,180,201,-203,180,202,-179,178,202,-201,177,199,-182,
181,199,-204,182,204,-180,179,204,-202,181,203,-184,183,203,-206,184,206,-183,182,206,-205,183,205,-186,185,205,-208,186,208,-185,184,208,-207,185,207,-188,187,207,-210,188,210,-187,186,210,-209,187,209,-190,189,209,-212,190,212,-189,188,212,-211,189,211,-192,191,211,-214,192,214,-191,190,214,-213,191,213,-194,193,213,-216,194,216,-193,192,216,-215,193,215,-196,195,215,-218,196,218,-195,194,218,-217,195,217,-198,197,217,-220,197,219,-197,196,219,-219,198,220,-200,199,220,-222,200,222,-199,198,222,-221,201,223,-203,202,223,-225,202,224,-201,200,224,-223,199,221,-204,203,221,-226,204,226,-202,201,226,-224,203,225,-206,205,225,-228,206,228,-205,204,228,-227,205,227,-208,207,227,-230,208,230,-207,206,230,-229,207,229,-210,209,229,-232,210,232,-209,208,232,-231,209,231,-212,211,231,-234,212,234,-211,210,234,-233,211,233,-214,213,233,-236,214,236,-213,212,236,-235,213,235,-216,215,235,-238,216,238,-215,214,238,-237,215,237,-218,217,237,-240,218,240,-217,216,240,-239,217,239,-220,219,239,-242,219,241,-219,218,241,-241,220,4,-222,221,4,-6,222,6,-221,220,6,-5,242,244,-244,243,244,-246,243,245,-247,246,245,-248,221,5,-226,225,5,-20,248,249,-243,242,249,-245,225,19,-228,227,19,-26,250,251,-249,248,251,-250,227,25,-230,229,25,-32,252,253,-251,250,253,-252,229,31,-232,231,31,-38,254,255,-253,252,255,-254,231,37,-234,233,37,-44,256,257,-255,254,257,-256,233,43,-236,235,43,-50,258,259,-257,256,259,-258,235,49,-238,237,49,-56,260,261,-259,258,261,-260,237,55,-240,239,55,-62,262,263,-261,260,263,-262,239,61,-242,241,61,-66,241,65,-241,240,65,-65,224,264,-223,222,264,-266,7,266,-19,18,266,-268,222,265,-7,6,265,-269,6,268,-8,7,268,-267,223,269,-225,224,269,-265,18,267,-25,24,267,-271,226,271,-224,223,271,-270,24,270,-31,30,270,-273,228,273,-227,226,273,-272,30,272,-37,36,272,-275,230,275,-229,228,275,-274,36,274,-43,42,274,-277,232,277,-231,230,277,-276,42,276,-49,48,276,-279,234,279,-233,232,279,-278,48,278,-55,54,278,-281,236,281,-235,234,281,-280,54,280,-61,60,280,-283,238,283,-237,236,283,-282,60,282,-65,64,282,-285,240,285,-239,
238,285,-284,64,284,-241,240,284,-286,264,286,-266,265,286,-288,266,288,-268,267,288,-290,265,287,-269,268,287,-291,268,290,-267,266,290,-289,269,291,-265,264,291,-287,267,289,-271,270,289,-293,271,293,-270,269,293,-292,270,292,-273,272,292,-295,273,295,-272,271,295,-294,272,294,-275,274,294,-297,275,297,-274,273,297,-296,274,296,-277,276,296,-299,277,299,-276,275,299,-298,276,298,-279,278,298,-301,279,301,-278,277,301,-300,278,300,-281,280,300,-303,281,303,-280,279,303,-302,280,302,-283,282,302,-305,283,305,-282,281,305,-304,282,304,-285,284,304,-307,285,307,-284,283,307,-306,284,306,-286,285,306,-308,286,243,-288,287,243,-247,288,245,-290,289,245,-245,287,246,-291,290,246,-248,290,247,-289,288,247,-246,291,242,-287,286,242,-244,289,244,-293,292,244,-250,293,248,-292,291,248,-243,292,249,-295,294,249,-252,295,250,-294,293,250,-249,294,251,-297,296,251,-254,297,252,-296,295,252,-251,296,253,-299,298,253,-256,299,254,-298,297,254,-253,298,255,-301,300,255,-258,301,256,-300,299,256,-255,300,257,-303,302,257,-260,303,258,-302,301,258,-257,302,259,-305,304,259,-262,305,260,-304,303,260,-259,304,261,-307,306,261,-264,307,262,-306,305,262,-261,306,263,-308,307,263,-263
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *5508 {
				a: -0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,
-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,
-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,
-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,
-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,
-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,
-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1
			} 
			NormalsW: *1836 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *5508 {
				a: 0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,
-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,
0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,
-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,
-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,
0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,
-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0
			} 
			BinormalsW: *1836 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *5508 {
				a: 1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,
-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,
-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,
-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,
1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,
-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,
-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0
			} 
			TangentsW: *1836 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementColor: 0 {
			Version: 101
			Name: "VertexColors"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			Colors: *1720 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
			ColorIndex: *1836 {
				a: 0,2,1,1,2,3,4,6,5,5,6,7,8,10,9,9,10,11,12,14,13,13,14,15,16,18,17,17,18,19,20,22,21,21,22,23,21,24,20,20,24,25,7,26,5,5,26,27,12,28,14,14,28,29,18,30,19,19,30,31,24,32,25,25,32,33,26,34,27,27,34,35,28,36,29,29,36,37,30,38,31,31,38,39,32,40,33,33,40,41,34,42,35,35,42,43,36,44,37,37,44,45,38,46,39,39,46,47,40,48,41,41,48,49,42,50,43,43,50,51,44,52,45,45,52,53,46,54,47,47,54,55,48,56,49,49,56,57,50,58,51,51,58,59,52,60,53,53,60,61,54,62,55,55,62,63,56,64,57,57,64,65,58,66,59,59,66,67,60,68,61,61,68,69,62,70,63,63,70,71,64,72,65,65,72,73,66,74,67,67,74,75,68,76,69,69,76,77,70,78,71,71,78,79,72,80,73,73,80,81,74,82,75,75,82,83,76,84,77,77,84,85,78,86,79,79,86,87,80,88,81,81,88,89,82,90,83,83,90,91,84,92,85,85,92,93,86,94,87,87,94,95,23,96,21,21,96,97,3,98,1,1,98,99,31,100,19,19,100,101,19,101,17,17,101,102,21,97,24,24,97,103,39,104,31,31,104,100,24,103,32,32,103,105,47,106,39,39,106,104,32,105,40,40,105,107,55,108,47,47,108,106,40,107,48,48,107,109,63,110,55,55,110,108,48,109,56,56,109,111,71,112,63,63,112,110,56,111,64,64,111,113,79,114,71,71,114,112,64,113,72,72,113,115,87,116,79,79,116,114,72,115,80,80,115,117,95,118,87,87,118,116,80,117,88,88,117,119,8,120,10,10,120,121,96,122,97,97,122,123,98,124,99,99,124,125,100,126,101,101,126,127,101,127,102,102,127,128,97,123,103,103,123,129,104,130,100,100,130,126,103,129,105,105,129,131,106,132,104,104,132,130,105,131,107,107,131,133,108,134,106,106,134,132,107,133,109,109,133,135,110,136,108,108,136,134,109,135,111,111,135,137,112,138,110,110,138,136,111,137,113,113,137,139,114,140,112,112,140,138,113,139,115,115,139,141,116,142,114,114,142,140,115,141,117,117,141,143,118,144,116,116,144,142,117,143,119,119,143,145,120,146,121,121,146,147,122,148,123,123,148,149,124,150,125,125,150,151,126,152,127,127,152,153,127,153,128,128,153,154,123,149,129,129,149,155,130,156,126,126,156,152,129,155,131,131,155,157,132,158,130,130,158,156,131,157,133,133,157,159,134,160,132,132,160,158,133,159,135,135,159,161,136,162,134,134,162,160,135,161,137,137,161,163,138,
164,136,136,164,162,137,163,139,139,163,165,140,166,138,138,166,164,139,165,141,141,165,167,142,168,140,140,168,166,141,167,143,143,167,169,144,170,142,142,170,168,143,169,145,145,169,171,146,172,147,147,172,173,148,174,149,149,174,175,150,176,151,151,176,177,152,178,153,153,178,179,153,179,154,154,179,180,149,175,155,155,175,181,156,182,152,152,182,178,155,181,157,157,181,183,158,184,156,156,184,182,157,183,159,159,183,185,160,186,158,158,186,184,159,185,161,161,185,187,162,188,160,160,188,186,161,187,163,163,187,189,164,190,162,162,190,188,163,189,165,165,189,191,166,192,164,164,192,190,165,191,167,167,191,193,168,194,166,166,194,192,167,193,169,169,193,195,170,196,168,168,196,194,169,195,171,171,195,197,172,198,173,173,198,199,174,200,175,175,200,201,176,202,177,177,202,203,178,204,179,179,204,205,179,205,180,180,205,206,175,201,181,181,201,207,182,208,178,178,208,204,181,207,183,183,207,209,184,210,182,182,210,208,183,209,185,185,209,211,186,212,184,184,212,210,185,211,187,187,211,213,188,214,186,186,214,212,187,213,189,189,213,215,190,216,188,188,216,214,189,215,191,191,215,217,192,218,190,190,218,216,191,217,193,193,217,219,194,220,192,192,220,218,193,219,195,195,219,221,196,222,194,194,222,220,195,221,197,197,221,223,198,224,199,199,224,225,200,226,201,201,226,227,202,228,203,203,228,229,204,230,205,205,230,231,205,231,206,206,231,232,201,227,207,207,227,233,208,234,204,204,234,230,207,233,209,209,233,235,210,236,208,208,236,234,209,235,211,211,235,237,212,238,210,210,238,236,211,237,213,213,237,239,214,240,212,212,240,238,213,239,215,215,239,241,216,242,214,214,242,240,215,241,217,217,241,243,218,244,216,216,244,242,217,243,219,219,243,245,220,246,218,218,246,244,219,245,221,221,245,247,222,248,220,220,248,246,221,247,223,223,247,249,224,250,225,225,250,251,226,252,227,227,252,253,228,254,229,229,254,255,230,256,231,231,256,257,231,257,232,232,257,258,227,253,233,233,253,259,234,260,230,230,260,256,233,259,235,235,259,261,236,262,234,234,262,260,235,261,237,237,261,263,238,264,236,236,264,262,237,263,239,239,
263,265,240,266,238,238,266,264,239,265,241,241,265,267,242,268,240,240,268,266,241,267,243,243,267,269,244,270,242,242,270,268,243,269,245,245,269,271,246,272,244,244,272,270,245,271,247,247,271,273,248,274,246,246,274,272,247,273,249,249,273,275,250,276,251,251,276,277,252,278,253,253,278,279,254,280,255,255,280,281,256,282,257,257,282,283,257,283,258,258,283,284,253,279,259,259,279,285,260,286,256,256,286,282,259,285,261,261,285,287,262,288,260,260,288,286,261,287,263,263,287,289,264,290,262,262,290,288,263,289,265,265,289,291,266,292,264,264,292,290,265,291,267,267,291,293,268,294,266,266,294,292,267,293,269,269,293,295,270,296,268,268,296,294,269,295,271,271,295,297,272,298,270,270,298,296,271,297,273,273,297,299,274,300,272,272,300,298,273,299,275,275,299,301,276,302,277,277,302,303,278,304,279,279,304,305,280,306,281,281,306,307,308,310,309,309,310,311,309,311,312,312,311,313,279,305,285,285,305,314,315,316,308,308,316,310,285,314,287,287,314,317,318,319,315,315,319,316,287,317,289,289,317,320,321,322,318,318,322,319,289,320,291,291,320,323,324,325,321,321,325,322,291,323,293,293,323,326,327,328,324,324,328,325,293,326,295,295,326,329,330,331,327,327,331,328,295,329,297,297,329,332,333,334,330,330,334,331,297,332,299,299,332,335,336,337,333,333,337,334,299,335,301,301,335,338,302,339,303,303,339,340,341,343,342,342,343,344,7,345,26,26,345,346,280,347,306,306,347,348,6,349,7,7,349,345,350,351,341,341,351,343,26,346,34,34,346,352,353,354,350,350,354,351,34,352,42,42,352,355,356,357,353,353,357,354,42,355,50,50,355,358,359,360,356,356,360,357,50,358,58,58,358,361,362,363,359,359,363,360,58,361,66,66,361,364,365,366,362,362,366,363,66,364,74,74,364,367,368,369,365,365,369,366,74,367,82,82,367,370,371,372,368,368,372,369,82,370,90,90,370,373,374,375,371,371,375,372,340,376,303,303,376,377,343,378,344,344,378,379,345,380,346,346,380,381,347,382,348,348,382,383,349,384,345,345,384,380,351,385,343,343,385,378,346,381,352,352,381,386,354,387,351,351,387,385,352,386,355,355,386,388,357,389,354,354,389,387,355,388,358,
358,388,390,360,391,357,357,391,389,358,390,361,361,390,392,363,393,360,360,393,391,361,392,364,364,392,394,366,395,363,363,395,393,364,394,367,367,394,396,369,397,366,366,397,395,367,396,370,370,396,398,372,399,369,369,399,397,370,398,373,373,398,400,375,401,372,372,401,399,376,402,377,377,402,403,378,404,379,379,404,405,380,406,381,381,406,407,382,408,383,383,408,409,384,410,380,380,410,406,385,411,378,378,411,404,381,407,386,386,407,412,387,413,385,385,413,411,386,412,388,388,412,414,389,415,387,387,415,413,388,414,390,390,414,416,391,417,389,389,417,415,390,416,392,392,416,418,393,419,391,391,419,417,392,418,394,394,418,420,395,421,393,393,421,419,394,420,396,396,420,422,397,423,395,395,423,421,396,422,398,398,422,424,399,425,397,397,425,423,398,424,400,400,424,426,401,427,399,399,427,425,402,428,403,403,428,429
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *860 {
				a: 0,0,-1,0,0,1,-1,1,0,0,-1,0,0,1,-1,1,1,0,0,0,1,1,0,1,1,0,0,0,1,1,0,1,0,0,1,0,0,-1,1,-1,0,-1,-1,-1,0,0,-1,0,-1,-2,0,-2,-2,1,-2,0,2,0,2,1,0,-2,1,-2,-1,-3,0,-3,-3,1,-3,0,3,0,3,1,0,-3,1,-3,-1,-4,0,-4,-4,1,-4,0,4,0,4,1,0,-4,1,-4,-1,-5,0,-5,-5,1,-5,0,5,0,5,1,0,-5,1,-5,-1,-6,0,-6,-6,1,-6,0,6,0,6,1,0,-6,1,-6,-1,-7,0,-7,-7,1,-7,0,7,0,7,1,0,-7,1,-7,-1,-8,0,-8,-8,1,-8,0,8,0,8,1,0,-8,1,-8,-1,-9,0,-9,-9,1,-9,0,9,0,9,1,0,-9,1,-9,-1,-10,0,-10,-10,1,-10,0,10,0,10,1,0,-10,1,-10,-2,0,-2,-1,-2,1,-2,0,2,-2,2,-1,2,0,-2,-2,2,-3,-2,-3,2,-4,-2,-4,2,-5,-2,-5,2,-6,-2,-6,2,-7,-2,-7,2,-8,-2,-8,2,-9,-2,-9,2,-10,-2,-10,2,0,2,1,-3,0,-3,-1,-3,1,-3,0,3,-2,3,-1,3,0,-3,-2,3,-3,-3,-3,3,-4,-3,-4,3,-5,-3,-5,3,-6,-3,-6,3,-7,-3,-7,3,-8,-3,-8,3,-9,-3,-9,3,-10,-3,-10,3,0,3,1,-4,0,-4,-1,-4,1,-4,0,4,-2,4,-1,4,0,-4,-2,4,-3,-4,-3,4,-4,-4,-4,4,-5,-4,-5,4,-6,-4,-6,4,-7,-4,-7,4,-8,-4,-8,4,-9,-4,-9,4,-10,-4,-10,4,0,4,1,-5,0,-5,-1,-5,1,-5,0,5,-2,5,-1,5,0,-5,-2,5,-3,-5,-3,5,-4,-5,-4,5,-5,-5,-5,5,-6,-5,-6,5,-7,-5,-7,5,-8,-5,-8,5,-9,-5,-9,5,-10,-5,-10,5,0,5,1,-6,0,-6,-1,-6,1,-6,0,6,-2,6,-1,6,0,-6,-2,6,-3,-6,-3,6,-4,-6,-4,6,-5,-6,-5,6,-6,-6,-6,6,-7,-6,-7,6,-8,-6,-8,6,-9,-6,-9,6,-10,-6,-10,6,0,6,1,-7,0,-7,-1,-7,1,-7,0,7,-2,7,-1,7,0,-7,-2,7,-3,-7,-3,7,-4,-7,-4,7,-5,-7,-5,7,-6,-7,-6,7,-7,-7,-7,7,-8,-7,-8,7,-9,-7,-9,7,-10,-7,-10,7,0,7,1,-8,0,-8,-1,-8,1,-8,0,8,-2,8,-1,8,0,-8,-2,8,-3,-8,-3,8,-4,-8,-4,8,-5,-8,-5,8,-6,-8,-6,8,-7,-8,-7,8,-8,-8,-8,8,-9,-8,-9,8,-10,-8,-10,8,0,8,1,-9,0,-9,-1,-9,1,-9,0,9,-2,9,-1,9,0,-9,-2,9,-3,-9,-3,9,-4,-9,-4,9,-5,-9,-5,9,-6,-9,-6,9,-7,-9,-7,9,-8,-9,-8,9,-9,-9,-9,9,-10,-9,-10,9,0,9,1,-10,0,-10,-1,-10,1,-10,0,9,-2,9,-1,10,-2,10,-1,9,0,10,0,-10,-2,9,-3,10,-3,-10,-3,9,-4,10,-4,-10,-4,9,-5,10,-5,-10,-5,9,-6,10,-6,-10,-6,9,-7,10,-7,-10,-7,9,-8,10,-8,-10,-8,9,-9,10,-9,-10,-9,9,-10,10,-10,-10,-10,10,0,10,1,1,1,0,1,1,2,0,2,-1,2,-2,2,-9,2,-10,2,0,2,2,1,2,2,-3,2,3,1,3,2,-4,2,4,1,4,2,-5,2,5,1,5,2,-6,2,6,1,6,2,-7,2,7,1,7,2,-8,2,8,1,8,2,-9,2,9,1,9,2,-10,2,10,1,10,2,10,2,9,2,1,3,0,3,-1,3,-2,3,-9,3,-10,3,0,3,2,3,-3,3,3,3,-4,3,4,3,-5,3,5,3,
-6,3,6,3,-7,3,7,3,-8,3,8,3,-9,3,9,3,-10,3,10,3,10,3,9,3,1,4,0,4,-1,4,-2,4,-9,4,-10,4,0,4,2,4,-3,4,3,4,-4,4,4,4,-5,4,5,4,-6,4,6,4,-7,4,7,4,-8,4,8,4,-9,4,9,4,-10,4,10,4,10,4,9,4
			} 
			UVIndex: *1836 {
				a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
164,136,136,164,162,137,163,139,139,163,165,140,166,138,138,166,164,139,165,141,141,165,167,142,168,140,140,168,166,141,167,143,143,167,169,144,170,142,142,170,168,143,169,145,145,169,171,146,172,147,147,172,173,148,174,149,149,174,175,150,176,151,151,176,177,152,178,153,153,178,179,153,179,154,154,179,180,149,175,155,155,175,181,156,182,152,152,182,178,155,181,157,157,181,183,158,184,156,156,184,182,157,183,159,159,183,185,160,186,158,158,186,184,159,185,161,161,185,187,162,188,160,160,188,186,161,187,163,163,187,189,164,190,162,162,190,188,163,189,165,165,189,191,166,192,164,164,192,190,165,191,167,167,191,193,168,194,166,166,194,192,167,193,169,169,193,195,170,196,168,168,196,194,169,195,171,171,195,197,172,198,173,173,198,199,174,200,175,175,200,201,176,202,177,177,202,203,178,204,179,179,204,205,179,205,180,180,205,206,175,201,181,181,201,207,182,208,178,178,208,204,181,207,183,183,207,209,184,210,182,182,210,208,183,209,185,185,209,211,186,212,184,184,212,210,185,211,187,187,211,213,188,214,186,186,214,212,187,213,189,189,213,215,190,216,188,188,216,214,189,215,191,191,215,217,192,218,190,190,218,216,191,217,193,193,217,219,194,220,192,192,220,218,193,219,195,195,219,221,196,222,194,194,222,220,195,221,197,197,221,223,198,224,199,199,224,225,200,226,201,201,226,227,202,228,203,203,228,229,204,230,205,205,230,231,205,231,206,206,231,232,201,227,207,207,227,233,208,234,204,204,234,230,207,233,209,209,233,235,210,236,208,208,236,234,209,235,211,211,235,237,212,238,210,210,238,236,211,237,213,213,237,239,214,240,212,212,240,238,213,239,215,215,239,241,216,242,214,214,242,240,215,241,217,217,241,243,218,244,216,216,244,242,217,243,219,219,243,245,220,246,218,218,246,244,219,245,221,221,245,247,222,248,220,220,248,246,221,247,223,223,247,249,224,250,225,225,250,251,226,252,227,227,252,253,228,254,229,229,254,255,230,256,231,231,256,257,231,257,232,232,257,258,227,253,233,233,253,259,234,260,230,230,260,256,233,259,235,235,259,261,236,262,234,234,262,260,235,261,237,237,261,263,238,264,236,236,264,262,237,263,239,239,
263,265,240,266,238,238,266,264,239,265,241,241,265,267,242,268,240,240,268,266,241,267,243,243,267,269,244,270,242,242,270,268,243,269,245,245,269,271,246,272,244,244,272,270,245,271,247,247,271,273,248,274,246,246,274,272,247,273,249,249,273,275,250,276,251,251,276,277,252,278,253,253,278,279,254,280,255,255,280,281,256,282,257,257,282,283,257,283,258,258,283,284,253,279,259,259,279,285,260,286,256,256,286,282,259,285,261,261,285,287,262,288,260,260,288,286,261,287,263,263,287,289,264,290,262,262,290,288,263,289,265,265,289,291,266,292,264,264,292,290,265,291,267,267,291,293,268,294,266,266,294,292,267,293,269,269,293,295,270,296,268,268,296,294,269,295,271,271,295,297,272,298,270,270,298,296,271,297,273,273,297,299,274,300,272,272,300,298,273,299,275,275,299,301,276,302,277,277,302,303,278,304,279,279,304,305,280,306,281,281,306,307,308,310,309,309,310,311,309,311,312,312,311,313,279,305,285,285,305,314,315,316,308,308,316,310,285,314,287,287,314,317,318,319,315,315,319,316,287,317,289,289,317,320,321,322,318,318,322,319,289,320,291,291,320,323,324,325,321,321,325,322,291,323,293,293,323,326,327,328,324,324,328,325,293,326,295,295,326,329,330,331,327,327,331,328,295,329,297,297,329,332,333,334,330,330,334,331,297,332,299,299,332,335,336,337,333,333,337,334,299,335,301,301,335,338,302,339,303,303,339,340,341,343,342,342,343,344,7,345,26,26,345,346,280,347,306,306,347,348,6,349,7,7,349,345,350,351,341,341,351,343,26,346,34,34,346,352,353,354,350,350,354,351,34,352,42,42,352,355,356,357,353,353,357,354,42,355,50,50,355,358,359,360,356,356,360,357,50,358,58,58,358,361,362,363,359,359,363,360,58,361,66,66,361,364,365,366,362,362,366,363,66,364,74,74,364,367,368,369,365,365,369,366,74,367,82,82,367,370,371,372,368,368,372,369,82,370,90,90,370,373,374,375,371,371,375,372,340,376,303,303,376,377,343,378,344,344,378,379,345,380,346,346,380,381,347,382,348,348,382,383,349,384,345,345,384,380,351,385,343,343,385,378,346,381,352,352,381,386,354,387,351,351,387,385,352,386,355,355,386,388,357,389,354,354,389,387,355,388,358,
358,388,390,360,391,357,357,391,389,358,390,361,361,390,392,363,393,360,360,393,391,361,392,364,364,392,394,366,395,363,363,395,393,364,394,367,367,394,396,369,397,366,366,397,395,367,396,370,370,396,398,372,399,369,369,399,397,370,398,373,373,398,400,375,401,372,372,401,399,376,402,377,377,402,403,378,404,379,379,404,405,380,406,381,381,406,407,382,408,383,383,408,409,384,410,380,380,410,406,385,411,378,378,411,404,381,407,386,386,407,412,387,413,385,385,413,411,386,412,388,388,412,414,389,415,387,387,415,413,388,414,390,390,414,416,391,417,389,389,417,415,390,416,392,392,416,418,393,419,391,391,419,417,392,418,394,394,418,420,395,421,393,393,421,419,394,420,396,396,420,422,397,423,395,395,423,421,396,422,398,398,422,424,399,425,397,397,425,423,398,424,400,400,424,426,401,427,399,399,427,425,402,428,403,403,428,429
			} 
		}
		LayerElementUV: 1 {
			Version: 101
			Name: "UVSet1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *860 {
				a: 0.497125208377838,0.647862315177917,0.54603773355484,0.647862315177917,0.497125238180161,0.69677472114563,0.54603773355484,0.69677472114563,0.497126281261444,0.00400003185495734,0.546038806438446,0.00400008028373122,0.497126221656799,0.0529124960303307,0.546038687229156,0.052912600338459,0.937336683273315,0.448212832212448,0.986249148845673,0.448212832212448,0.937336683273315,0.497125238180161,0.986249148845673,0.497125238180161,0.444211333990097,0.941338896751404,0.493123829364777,0.941338896751404,0.444211304187775,0.990251302719116,0.493123829364777,0.990251302719116,0.493123412132263,0.00400000018998981,0.493123680353165,0.0529123060405254,0.444211184978485,0.00400023907423019,0.444211393594742,0.0529124811291695,0.0040001105517149,0.497125834226608,0.0529125928878784,0.497125744819641,0.00400000018998981,0.448213398456573,0.052912525832653,0.448213338851929,0.0529126711189747,0.546038150787354,0.00400023115798831,0.546038210391998,0.594951152801514,0.0529126897454262,0.594951212406158,0.00400020135566592,0.395298987627029,0.941338896751404,0.395298957824707,0.990251302719116,0.395299017429352,0.00400040531530976,0.395299166440964,0.0529126338660717,0.0529127418994904,0.594950497150421,0.0040003526955843,0.594950556755066,0.643863558769226,0.0529127605259418,0.643863618373871,0.00400030985474586,0.346386641263962,0.941338837146759,0.346386641263962,0.990251302719116,0.346386849880219,0.00400051148608327,0.346386939287186,0.0529127717018127,0.0529128089547157,0.643862843513489,0.00400046491995454,0.643862903118134,0.692775964736938,0.0529128015041351,0.692776024341583,0.0040003964677453,0.297474265098572,0.941338837146759,0.297474265098572,0.990251302719116,0.297474652528763,0.00400057109072804,0.29747474193573,0.0529128536581993,0.0529128313064575,0.692775130271912,0.00400054175406694,0.692775130271912,0.741688370704651,0.0529128275811672,0.741688370704651,0.0040004258044064,0.248561918735504,0.941338837146759,0.248561918735504,0.990251302719116,0.248562470078468,0.00400057435035706,0.24856248497963,
0.0529128834605217,0.0529128052294254,0.74168735742569,0.00400057202205062,0.741687297821045,0.790600717067719,0.0529128089547157,0.790600657463074,0.00400044629350305,0.199649542570114,0.941338837146759,0.199649557471275,0.990251302719116,0.199650213122368,0.00400052685290575,0.199650198221207,0.0529128722846508,0.0529127381742001,0.790599584579468,0.00400052266195416,0.790599465370178,0.839513063430786,0.0529127605259418,0.839513003826141,0.00400041323155165,0.150737166404724,0.941338837146759,0.150737181305885,0.990251302719116,0.150737926363945,0.00400044582784176,0.150737851858139,0.052912812680006,0.0529126301407814,0.839511752128601,0.00400040810927749,0.839511632919312,0.888425409793854,0.0529126822948456,0.888425290584564,0.00400033360347152,0.101824782788754,0.941338837146759,0.101824797689915,0.990251302719116,0.101825594902039,0.00400029588490725,0.101825498044491,0.0529127307236195,0.052912462502718,0.888423979282379,0.0040002353489399,0.888423800468445,0.937337756156921,0.0529125705361366,0.937337577342987,0.00400020834058523,0.0529123917222023,0.941338896751404,0.0529124066233635,0.990251362323761,0.0529131852090359,0.00400014314800501,0.0529130734503269,0.0529126264154911,0.0529122911393642,0.937336266040802,0.00400000018998981,0.937336027622223,0.986250162124634,0.0529124215245247,0.986249923706055,0.00400000018998981,0.00400000018998981,0.941338896751404,0.004000015091151,0.990251362323761,0.00400074105709791,0.00400000018998981,0.00400064047425985,0.0529125295579433,0.101824909448624,0.448213219642639,0.10182498395443,0.497125655412674,0.594950079917908,0.69677472114563,0.594950079917908,0.647862255573273,0.395299315452576,0.10182486474514,0.444211542606354,0.101824708282948,0.493123799562454,0.101824522018433,0.101825043559074,0.546038091182709,0.346387058496475,0.101824998855591,0.101825095713139,0.594950437545776,0.297474771738052,0.101825103163719,0.10182511806488,0.643862783908844,0.24856248497963,0.101825162768364,0.101825095713139,0.692775130271912,0.199650153517723,0.101825177669525,0.101825028657913,
0.741687417030334,0.150737792253494,0.101825162768364,0.101824939250946,0.790599644184113,0.101825393736362,0.101825103163719,0.101824812591076,0.839511930942535,0.0529129356145859,0.101825021207333,0.101824663579464,0.888424158096313,0.00400051008909941,0.101824924349785,0.10182448476553,0.937336385250092,0.888424277305603,0.448212802410126,0.888424277305603,0.497125238180161,0.150737315416336,0.448213130235672,0.150737389922142,0.497125566005707,0.643862426280975,0.69677472114563,0.643862426280975,0.647862255573273,0.395299464464188,0.150737121701241,0.444211721420288,0.150736942887306,0.493123978376389,0.150736719369888,0.150737419724464,0.546038031578064,0.346387177705765,0.150737285614014,0.150737449526787,0.594950377941132,0.29747486114502,0.150737404823303,0.150737449526787,0.643862783908844,0.248562514781952,0.150737479329109,0.150737389922142,0.692775130271912,0.199650138616562,0.150737524032593,0.150737315416336,0.741687476634979,0.150737747550011,0.150737538933754,0.150737181305885,0.790599763393402,0.101825311779976,0.15073749423027,0.150737047195435,0.839512050151825,0.0529128462076187,0.150737434625626,0.1507368683815,0.888424336910248,0.00400038156658411,0.150737345218658,0.150736659765244,0.937336564064026,0.839511930942535,0.448212802410126,0.839511930942535,0.497125238180161,0.199649721384048,0.448213011026382,0.199649795889854,0.497125506401062,0.692774772644043,0.69677472114563,0.692774772644043,0.647862255573273,0.3952996134758,0.199649423360825,0.444211930036545,0.199649214744568,0.493124216794968,0.199648946523666,0.199649825692177,0.546037971973419,0.346387296915054,0.199649602174759,0.199649840593338,0.594950377941132,0.297474950551987,0.19964973628521,0.199649810791016,0.643862843513489,0.248562559485435,0.199649840593338,0.19964973628521,0.692775189876556,0.199650153517723,0.199649900197983,0.199649631977081,0.741687536239624,0.150737732648849,0.199649944901466,0.199649482965469,0.790599882602692,0.101825259625912,0.199649915099144,0.199649319052696,0.839512228965759,0.0529127642512321,0.199649885296822,
0.19964911043644,0.888424515724182,0.00400023953989148,0.199649810791016,0.199648857116699,0.937336802482605,0.790599584579468,0.448212802410126,0.790599584579468,0.497125238180161,0.248562186956406,0.448212921619415,0.248562231659889,0.497125446796417,0.741687178611755,0.69677472114563,0.741687178611755,0.647862255573273,0.395299822092056,0.24856173992157,0.444212168455124,0.24856148660183,0.493124485015869,0.248561203479767,0.24856224656105,0.546037971973419,0.346387445926666,0.248561948537827,0.24856224656105,0.594950437545776,0.297475039958954,0.2485621124506,0.248562201857567,0.643862843513489,0.248562633991241,0.248562231659889,0.248562097549438,0.692775249481201,0.199650198221207,0.248562321066856,0.248561978340149,0.741687655448914,0.150737732648849,0.248562380671501,0.248561799526215,0.790600061416626,0.101825222373009,0.248562380671501,0.248561590909958,0.839512407779694,0.0529127083718777,0.248562350869179,0.248561352491379,0.888424754142761,0.00400013057515025,0.248562306165695,0.248561069369316,0.937337040901184,0.7416872382164,0.448212802410126,0.7416872382164,0.497125238180161,0.297474682331085,0.448212832212448,0.297474712133408,0.497125416994095,0.790599524974823,0.69677472114563,0.790599584579468,0.647862255573273,0.395300030708313,0.297474086284637,0.444212436676025,0.297473818063736,0.493124812841415,0.29747349023819,0.297474712133408,0.546037912368774,0.346387624740601,0.297474324703217,0.297474682331085,0.594950437545776,0.297475188970566,0.297474503517151,0.29747462272644,0.643862903118134,0.24856273829937,0.297474652528763,0.297474503517151,0.692775368690491,0.199650257825851,0.297474771738052,0.297474354505539,0.741687834262848,0.150737747550011,0.297474831342697,0.297474175691605,0.79060024023056,0.101825222373009,0.29747486114502,0.297473937273026,0.839512646198273,0.0529126673936844,0.297474890947342,0.297473669052124,0.88842499256134,0.00400004768744111,0.29747486114502,0.297473341226578,0.937337338924408,0.692774891853333,0.448212772607803,0.692774832248688,0.497125238180161,0.34638723731041,
0.448212742805481,0.34638723731041,0.497125387191772,0.839511930942535,0.696774780750275,0.839511930942535,0.647862255573273,0.395300269126892,0.346386522054672,0.444212734699249,0.346386224031448,0.493125170469284,0.34638586640358,0.34638723731041,0.546037971973419,0.346387803554535,0.346386760473251,0.346387177705765,0.594950497150421,0.297475337982178,0.346386939287186,0.346387088298798,0.643863022327423,0.248562842607498,0.34638711810112,0.346386939287186,0.69277548789978,0.199650332331657,0.34638723731041,0.346386790275574,0.741687953472137,0.150737792253494,0.346387356519699,0.346386581659317,0.790600419044495,0.101825229823589,0.346387416124344,0.346386313438416,0.839512884616852,0.0529126524925232,0.346387445926666,0.346386015415192,0.888425290584564,0.00400000065565109,0.346387445926666,0.346385657787323,0.937337696552277,0.64386248588562,0.448212772607803,0.64386248588562,0.497125238180161,0.395299851894379,0.448212713003159,0.395299822092056,0.497125387191772,0.888424336910248,0.696774780750275,0.888424336910248,0.647862315177917,0.395300507545471,0.395298957824707,0.444213032722473,0.395298689603806,0.49312549829483,0.395298361778259,0.395299792289734,0.546037971973419,0.346388012170792,0.395299226045609,0.395299702882767,0.594950556755066,0.297475516796112,0.395299434661865,0.395299583673477,0.643863081932068,0.248562976717949,0.3952996134758,0.395299434661865,0.69277560710907,0.199650436639786,0.395299762487411,0.395299255847931,0.741688132286072,0.1507378667593,0.395299881696701,0.395299017429352,0.790600657463074,0.101825274527073,0.395299971103668,0.39529874920845,0.839513123035431,0.0529126673936844,0.395300030708313,0.395298451185226,0.888425648212433,0.00400000018998981,0.395300090312958,0.395298063755035,0.937338054180145,0.594950079917908,0.448212742805481,0.594950079917908,0.497125238180161,0.444212466478348,0.448212713003159,0.444212436676025,0.497125416994095,0.93733674287796,0.696774780750275,0.93733674287796,0.647862315177917,0.39530074596405,0.444211453199387,0.444213330745697,0.44421112537384,
0.493125855922699,0.444210797548294,0.444212347269058,0.546038031578064,0.346388250589371,0.444211721420288,0.444212257862091,0.594950616359711,0.297475695610046,0.444211959838867,0.444212108850479,0.643863201141357,0.248563140630722,0.444212168455124,0.444211959838867,0.692775785923004,0.199650555849075,0.444212317466736,0.44421175122261,0.741688370704651,0.150737941265106,0.444212436676025,0.444211512804031,0.790600895881653,0.101825341582298,0.444212555885315,0.444211214780807,0.839513421058655,0.0529127381742001,0.44421261548996,0.444210916757584,0.888425946235657,0.00400005746632814,0.444212704896927,0.444210529327393,0.937338471412659,0.546037673950195,0.448212742805481,0.546037673950195,0.497125238180161,0.493125230073929,0.448212772607803,0.493125081062317,0.497125506401062,0.986249148845673,0.696774780750275,0.986249148845673,0.647862315177917,0.888425290584564,0.203650057315826,0.937337756156921,0.203650057315826,0.888425290584564,0.252562463283539,0.937337756156921,0.252562433481216,0.986250281333923,0.203650042414665,0.986250281333923,0.252562433481216,0.493124961853027,0.546038091182709,0.839512884616852,0.203650072216988,0.839512884616852,0.252562463283539,0.493124842643738,0.594950735569,0.790600538253784,0.203650072216988,0.790600538253784,0.252562463283539,0.493124693632126,0.643863379955292,0.741688072681427,0.203650072216988,0.741688072681427,0.252562463283539,0.493124514818192,0.692775964736938,0.692775666713715,0.203650072216988,0.692775666713715,0.252562463283539,0.493124306201935,0.741688549518585,0.643863201141357,0.203650072216988,0.643863201141357,0.252562433481216,0.493124037981033,0.790601134300232,0.594950735569,0.203650072216988,0.594950735569,0.252562433481216,0.493123739957809,0.839513659477234,0.546038269996643,0.203650057315826,0.546038269996643,0.252562433481216,0.493123382329941,0.888426303863525,0.497125864028931,0.203650042414665,0.497125834226608,0.252562403678894,0.49312299489975,0.937338829040527,0.497125297784805,0.448212713003159,0.497125267982483,0.497125208377838,0.937337636947632,
0.256562530994415,0.986250102519989,0.256562471389771,0.937337636947632,0.305474936962128,0.986250162124634,0.305474936962128,0.546038568019867,0.101825028657913,0.594951033592224,0.1018251106143,0.93733674287796,0.745687127113342,0.986249148845673,0.745687127113342,0.497126072645187,0.101824939250946,0.888425230979919,0.256562560796738,0.888425230979919,0.305474936962128,0.643863499164581,0.101825155317783,0.839512825012207,0.256562560796738,0.839512825012207,0.305474936962128,0.692775905132294,0.101825185120106,0.790600419044495,0.256562530994415,0.790600419044495,0.305474907159805,0.741688311100006,0.101825185120106,0.741688013076782,0.256562530994415,0.741688013076782,0.305474907159805,0.790600717067719,0.101825155317783,0.69277560710907,0.256562530994415,0.69277560710907,0.305474907159805,0.839513123035431,0.101825103163719,0.643863201141357,0.256562501192093,0.643863201141357,0.305474907159805,0.888425469398499,0.101825013756752,0.594950795173645,0.256562501192093,0.594950735569,0.305474907159805,0.937337875366211,0.101824894547462,0.546038389205933,0.256562471389771,0.546038329601288,0.305474907159805,0.986250281333923,0.101824752986431,0.497125864028931,0.256562471389771,0.497125834226608,0.305474907159805,0.497125238180161,0.546037554740906,0.546037673950195,0.546037554740906,0.937337636947632,0.354387402534485,0.986250102519989,0.354387432336807,0.546038508415222,0.150737509131432,0.594950973987579,0.150737553834915,0.93733674287796,0.79459947347641,0.986249148845673,0.79459947347641,0.497125953435898,0.150737434625626,0.888425171375275,0.354387372732162,0.643863439559937,0.150737583637238,0.839512765407562,0.35438734292984,0.692775905132294,0.150737583637238,0.79060035943985,0.354387313127518,0.741688311100006,0.150737568736076,0.741687953472137,0.354387283325195,0.790600776672363,0.150737538933754,0.692775547504425,0.354387283325195,0.839513182640076,0.150737479329109,0.643863141536713,0.354387283325195,0.888425588607788,0.150737389922142,0.594950735569,0.354387313127518,0.9373379945755,0.150737285614014,
0.546038329601288,0.35438734292984,0.986250400543213,0.150737166404724,0.497125864028931,0.354387402534485,0.497125238180161,0.594949901103973,0.546037673950195,0.594949901103973,0.937337517738342,0.40329983830452,0.986250042915344,0.403299897909164,0.546038448810577,0.199650049209595,0.594950973987579,0.199650034308434,0.93733674287796,0.843511760234833,0.986249148845673,0.843511760234833,0.497125834226608,0.199650019407272,0.88842511177063,0.403299778699875,0.643863439559937,0.199650034308434,0.839512705802917,0.403299748897552,0.692775905132294,0.199650019407272,0.790600299835205,0.403299689292908,0.741688370704651,0.19964998960495,0.741687893867493,0.403299659490585,0.790600776672363,0.199649944901466,0.692775547504425,0.403299659490585,0.83951324224472,0.19964987039566,0.643863141536713,0.403299659490585,0.888425648212433,0.199649780988693,0.594950795173645,0.403299689292908,0.93733811378479,0.199649661779404,0.546038389205933,0.403299748897552,0.986250519752502,0.199649512767792,0.49712598323822,0.403299868106842,0.497125208377838,0.643862247467041,0.546037614345551,0.643862247467041
			} 
			UVIndex: *1836 {
				a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
164,136,136,164,162,137,163,139,139,163,165,140,166,138,138,166,164,139,165,141,141,165,167,142,168,140,140,168,166,141,167,143,143,167,169,144,170,142,142,170,168,143,169,145,145,169,171,146,172,147,147,172,173,148,174,149,149,174,175,150,176,151,151,176,177,152,178,153,153,178,179,153,179,154,154,179,180,149,175,155,155,175,181,156,182,152,152,182,178,155,181,157,157,181,183,158,184,156,156,184,182,157,183,159,159,183,185,160,186,158,158,186,184,159,185,161,161,185,187,162,188,160,160,188,186,161,187,163,163,187,189,164,190,162,162,190,188,163,189,165,165,189,191,166,192,164,164,192,190,165,191,167,167,191,193,168,194,166,166,194,192,167,193,169,169,193,195,170,196,168,168,196,194,169,195,171,171,195,197,172,198,173,173,198,199,174,200,175,175,200,201,176,202,177,177,202,203,178,204,179,179,204,205,179,205,180,180,205,206,175,201,181,181,201,207,182,208,178,178,208,204,181,207,183,183,207,209,184,210,182,182,210,208,183,209,185,185,209,211,186,212,184,184,212,210,185,211,187,187,211,213,188,214,186,186,214,212,187,213,189,189,213,215,190,216,188,188,216,214,189,215,191,191,215,217,192,218,190,190,218,216,191,217,193,193,217,219,194,220,192,192,220,218,193,219,195,195,219,221,196,222,194,194,222,220,195,221,197,197,221,223,198,224,199,199,224,225,200,226,201,201,226,227,202,228,203,203,228,229,204,230,205,205,230,231,205,231,206,206,231,232,201,227,207,207,227,233,208,234,204,204,234,230,207,233,209,209,233,235,210,236,208,208,236,234,209,235,211,211,235,237,212,238,210,210,238,236,211,237,213,213,237,239,214,240,212,212,240,238,213,239,215,215,239,241,216,242,214,214,242,240,215,241,217,217,241,243,218,244,216,216,244,242,217,243,219,219,243,245,220,246,218,218,246,244,219,245,221,221,245,247,222,248,220,220,248,246,221,247,223,223,247,249,224,250,225,225,250,251,226,252,227,227,252,253,228,254,229,229,254,255,230,256,231,231,256,257,231,257,232,232,257,258,227,253,233,233,253,259,234,260,230,230,260,256,233,259,235,235,259,261,236,262,234,234,262,260,235,261,237,237,261,263,238,264,236,236,264,262,237,263,239,239,
263,265,240,266,238,238,266,264,239,265,241,241,265,267,242,268,240,240,268,266,241,267,243,243,267,269,244,270,242,242,270,268,243,269,245,245,269,271,246,272,244,244,272,270,245,271,247,247,271,273,248,274,246,246,274,272,247,273,249,249,273,275,250,276,251,251,276,277,252,278,253,253,278,279,254,280,255,255,280,281,256,282,257,257,282,283,257,283,258,258,283,284,253,279,259,259,279,285,260,286,256,256,286,282,259,285,261,261,285,287,262,288,260,260,288,286,261,287,263,263,287,289,264,290,262,262,290,288,263,289,265,265,289,291,266,292,264,264,292,290,265,291,267,267,291,293,268,294,266,266,294,292,267,293,269,269,293,295,270,296,268,268,296,294,269,295,271,271,295,297,272,298,270,270,298,296,271,297,273,273,297,299,274,300,272,272,300,298,273,299,275,275,299,301,276,302,277,277,302,303,278,304,279,279,304,305,280,306,281,281,306,307,308,310,309,309,310,311,309,311,312,312,311,313,279,305,285,285,305,314,315,316,308,308,316,310,285,314,287,287,314,317,318,319,315,315,319,316,287,317,289,289,317,320,321,322,318,318,322,319,289,320,291,291,320,323,324,325,321,321,325,322,291,323,293,293,323,326,327,328,324,324,328,325,293,326,295,295,326,329,330,331,327,327,331,328,295,329,297,297,329,332,333,334,330,330,334,331,297,332,299,299,332,335,336,337,333,333,337,334,299,335,301,301,335,338,302,339,303,303,339,340,341,343,342,342,343,344,7,345,26,26,345,346,280,347,306,306,347,348,6,349,7,7,349,345,350,351,341,341,351,343,26,346,34,34,346,352,353,354,350,350,354,351,34,352,42,42,352,355,356,357,353,353,357,354,42,355,50,50,355,358,359,360,356,356,360,357,50,358,58,58,358,361,362,363,359,359,363,360,58,361,66,66,361,364,365,366,362,362,366,363,66,364,74,74,364,367,368,369,365,365,369,366,74,367,82,82,367,370,371,372,368,368,372,369,82,370,90,90,370,373,374,375,371,371,375,372,340,376,303,303,376,377,343,378,344,344,378,379,345,380,346,346,380,381,347,382,348,348,382,383,349,384,345,345,384,380,351,385,343,343,385,378,346,381,352,352,381,386,354,387,351,351,387,385,352,386,355,355,386,388,357,389,354,354,389,387,355,388,358,
358,388,390,360,391,357,357,391,389,358,390,361,361,390,392,363,393,360,360,393,391,361,392,364,364,392,394,366,395,363,363,395,393,364,394,367,367,394,396,369,397,366,366,397,395,367,396,370,370,396,398,372,399,369,369,399,397,370,398,373,373,398,400,375,401,372,372,401,399,376,402,377,377,402,403,378,404,379,379,404,405,380,406,381,381,406,407,382,408,383,383,408,409,384,410,380,380,410,406,385,411,378,378,411,404,381,407,386,386,407,412,387,413,385,385,413,411,386,412,388,388,412,414,389,415,387,387,415,413,388,414,390,390,414,416,391,417,389,389,417,415,390,416,392,392,416,418,393,419,391,391,419,417,392,418,394,394,418,420,395,421,393,393,421,419,394,420,396,396,420,422,397,423,395,395,423,421,396,422,398,398,422,424,399,425,397,397,425,423,398,424,400,400,424,426,401,427,399,399,427,425,402,428,403,403,428,429
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementColor"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
		Layer: 1 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 1
			}
		}
	}
	Geometry: 1143716656, "Geometry::Scene", "Mesh" {
		Vertices: *924 {
			a: -0,0,0,-100,0,0,-0,100,0,-100,100,0,-1000,0,0,-1000,0,-100,-1000,100,0,-1000,100,-100,-100,0,-1000,-0,0,-1000,-100,100,-1000,-0,100,-1000,-0,0,-100,-0,100,-100,-100,100,-100,-100,0,-100,-100,0,-200,-0,0,-200,-1000,100,-200,-1000,0,-200,-0,100,-200,-100,100,-200,-100,0,-300,-0,0,-300,-1000,100,-300,-1000,0,-300,-0,100,-300,-100,100,-300,-100,0,-400,-0,0,-400,-1000,100,-400,-1000,0,-400,-0,100,-400,-100,100,-400,-100,0,-500,-0,0,-500,-1000,100,-500,-1000,0,-500,-0,100,-500,-100,100,-500,-100,0,-600,-0,0,-600,-1000,100,-600,-1000,0,-600,-0,100,-600,-100,100,-600,-100,0,-700,-0,0,-700,-1000,100,-700,-1000,0,-700,-0,100,-700,-100,100,-700,-100,0,-800,-0,0,-800,-1000,100,-800,-1000,0,-800,-0,100,-800,-100,100,-800,-100,0,-900,-0,0,-900,-1000,100,-900,-1000,0,-900,-0,100,-900,-100,100,-900,-1000,100,-1000,-1000,0,-1000,-200,0,0,-200,0,-100,-200,100,0,-200,100,-200,-200,100,-100,-200,0,-200,-200,100,-300,-200,0,-300,-200,100,-400,-200,0,-400,-200,100,-500,-200,0,-500,-200,100,-600,-200,0,-600,-200,100,-700,-200,0,-700,-200,100,-800,-200,0,-800,-200,100,-900,-200,0,-900,-200,100,-1000,-200,0,-1000,-300,0,0,-300,0,-100,-300,100,0,-300,100,-200,-300,100,-100,-300,0,-200,-300,100,-300,-300,0,-300,-300,100,-400,-300,0,-400,-300,100,-500,-300,0,-500,-300,100,-600,-300,0,-600,-300,100,-700,-300,0,-700,-300,100,-800,-300,0,-800,-300,100,-900,-300,0,-900,-300,100,-1000,-300,0,-1000,-400,0,0,-400,0,-100,-400,100,0,-400,100,-200,-400,100,-100,-400,0,-200,-400,100,-300,-400,0,-300,-400,100,-400,-400,0,-400,-400,100,-500,-400,0,-500,-400,100,-600,-400,0,-600,-400,100,-700,-400,0,-700,-400,100,-800,-400,0,-800,-400,100,-900,-400,0,-900,-400,100,-1000,-400,0,-1000,-500,0,0,-500,0,-100,-500,100,0,-500,100,-200,-500,100,-100,-500,0,-200,-500,100,-300,-500,0,-300,-500,100,-400,-500,0,-400,-500,100,-500,-500,0,-500,-500,100,-600,-500,0,-600,-500,100,-700,-500,0,-700,-500,100,-800,-500,0,-800,-500,100,-900,-500,0,-900,-500,100,-1000,-500,0,-1000,-600,0,0,-600,0,-100,-600,100,0,-600,100,-200,-600,100,-100,-600,0,-200,-600,100,-300,
-600,0,-300,-600,100,-400,-600,0,-400,-600,100,-500,-600,0,-500,-600,100,-600,-600,0,-600,-600,100,-700,-600,0,-700,-600,100,-800,-600,0,-800,-600,100,-900,-600,0,-900,-600,100,-1000,-600,0,-1000,-700,0,0,-700,0,-100,-700,100,0,-700,100,-200,-700,100,-100,-700,0,-200,-700,100,-300,-700,0,-300,-700,100,-400,-700,0,-400,-700,100,-500,-700,0,-500,-700,100,-600,-700,0,-600,-700,100,-700,-700,0,-700,-700,100,-800,-700,0,-800,-700,100,-900,-700,0,-900,-700,100,-1000,-700,0,-1000,-800,0,0,-800,0,-100,-800,100,0,-800,100,-200,-800,100,-100,-800,0,-200,-800,100,-300,-800,0,-300,-800,100,-400,-800,0,-400,-800,100,-500,-800,0,-500,-800,100,-600,-800,0,-600,-800,100,-700,-800,0,-700,-800,100,-800,-800,0,-800,-800,100,-900,-800,0,-900,-800,100,-1000,-800,0,-1000,-900,0,0,-900,0,-100,-900,100,0,-900,100,-200,-900,100,-100,-900,0,-200,-900,100,-300,-900,0,-300,-900,100,-400,-900,0,-400,-900,100,-500,-900,0,-500,-900,100,-600,-900,0,-600,-900,100,-700,-900,0,-700,-900,100,-800,-900,0,-800,-900,100,-900,-900,0,-900,-900,100,-1000,-900,0,-1000,-900,400,-200,-900,400,-100,-1000,400,-200,-1000,400,-100,-900,400,0,-1000,400,0,-900,400,-300,-1000,400,-300,-900,400,-400,-1000,400,-400,-900,400,-500,-1000,400,-500,-900,400,-600,-1000,400,-600,-900,400,-700,-1000,400,-700,-900,400,-800,-1000,400,-800,-900,400,-900,-1000,400,-900,-900,400,-1000,-1000,400,-1000,-900,200,-100,-900,200,0,-1000,200,-100,-1000,200,-200,-1000,200,0,-900,200,-200,-1000,200,-300,-900,200,-300,-1000,200,-400,-900,200,-400,-1000,200,-500,-900,200,-500,-1000,200,-600,-900,200,-600,-1000,200,-700,-900,200,-700,-1000,200,-800,-900,200,-800,-1000,200,-900,-900,200,-900,-1000,200,-1000,-900,200,-1000,-900,300,-100,-900,300,0,-1000,300,-100,-1000,300,-200,-1000,300,0,-900,300,-200,-1000,300,-300,-900,300,-300,-1000,300,-400,-900,300,-400,-1000,300,-500,-900,300,-500,-1000,300,-600,-900,300,-600,-1000,300,-700,-900,300,-700,-1000,300,-800,-900,300,-800,-1000,300,-900,-900,300,-900,-1000,300,-1000,-900,300,-1000
		} 
		PolygonVertexIndex: *1836 {
			a: 0,2,-2,1,2,-4,4,6,-6,5,6,-8,8,10,-10,9,10,-12,12,13,-1,0,13,-3,2,13,-4,3,13,-15,12,0,-16,15,0,-2,15,16,-13,12,16,-18,7,18,-6,5,18,-20,12,17,-14,13,17,-21,13,20,-15,14,20,-22,16,22,-18,17,22,-24,18,24,-20,19,24,-26,17,23,-21,20,23,-27,20,26,-22,21,26,-28,22,28,-24,23,28,-30,24,30,-26,25,30,-32,23,29,-27,26,29,-33,26,32,-28,27,32,-34,28,34,-30,29,34,-36,30,36,-32,31,36,-38,29,35,-33,32,35,-39,32,38,-34,33,38,-40,34,40,-36,35,40,-42,36,42,-38,37,42,-44,35,41,-39,38,41,-45,38,44,-40,39,44,-46,40,46,-42,41,46,-48,42,48,-44,43,48,-50,41,47,-45,44,47,-51,44,50,-46,45,50,-52,46,52,-48,47,52,-54,48,54,-50,49,54,-56,47,53,-51,50,53,-57,50,56,-52,51,56,-58,52,58,-54,53,58,-60,54,60,-56,55,60,-62,53,59,-57,56,59,-63,56,62,-58,57,62,-64,58,8,-60,59,8,-10,60,64,-62,61,64,-66,59,9,-63,62,9,-12,62,11,-64,63,11,-11,1,66,-16,15,66,-68,3,68,-2,1,68,-67,21,69,-15,14,69,-71,14,70,-4,3,70,-69,15,67,-17,16,67,-72,27,72,-22,21,72,-70,16,71,-23,22,71,-74,33,74,-28,27,74,-73,22,73,-29,28,73,-76,39,76,-34,33,76,-75,28,75,-35,34,75,-78,45,78,-40,39,78,-77,34,77,-41,40,77,-80,51,80,-46,45,80,-79,40,79,-47,46,79,-82,57,82,-52,51,82,-81,46,81,-53,52,81,-84,63,84,-58,57,84,-83,52,83,-59,58,83,-86,10,86,-64,63,86,-85,58,85,-9,8,85,-88,8,87,-11,10,87,-87,66,88,-68,67,88,-90,68,90,-67,66,90,-89,69,91,-71,70,91,-93,70,92,-69,68,92,-91,67,89,-72,71,89,-94,72,94,-70,69,94,-92,71,93,-74,73,93,-96,74,96,-73,72,96,-95,73,95,-76,75,95,-98,76,98,-75,74,98,-97,75,97,-78,77,97,-100,78,100,-77,76,100,-99,77,99,-80,79,99,-102,80,102,-79,78,102,-101,79,101,-82,81,101,-104,82,104,-81,80,104,-103,81,103,-84,83,103,-106,84,106,-83,82,106,-105,83,105,-86,85,105,-108,86,108,-85,84,108,-107,85,107,-88,87,107,-110,87,109,-87,86,109,-109,88,110,-90,89,110,-112,90,112,-89,88,112,-111,91,113,-93,92,113,-115,92,114,-91,90,114,-113,89,111,-94,93,111,-116,94,116,-92,91,116,-114,93,115,-96,95,115,-118,96,118,-95,94,118,-117,95,117,-98,97,117,-120,98,120,-97,96,120,-119,97,119,-100,99,119,-122,100,122,-99,98,122,-121,99,121,-102,101,121,-124,102,124,-101,100,
124,-123,101,123,-104,103,123,-126,104,126,-103,102,126,-125,103,125,-106,105,125,-128,106,128,-105,104,128,-127,105,127,-108,107,127,-130,108,130,-107,106,130,-129,107,129,-110,109,129,-132,109,131,-109,108,131,-131,110,132,-112,111,132,-134,112,134,-111,110,134,-133,113,135,-115,114,135,-137,114,136,-113,112,136,-135,111,133,-116,115,133,-138,116,138,-114,113,138,-136,115,137,-118,117,137,-140,118,140,-117,116,140,-139,117,139,-120,119,139,-142,120,142,-119,118,142,-141,119,141,-122,121,141,-144,122,144,-121,120,144,-143,121,143,-124,123,143,-146,124,146,-123,122,146,-145,123,145,-126,125,145,-148,126,148,-125,124,148,-147,125,147,-128,127,147,-150,128,150,-127,126,150,-149,127,149,-130,129,149,-152,130,152,-129,128,152,-151,129,151,-132,131,151,-154,131,153,-131,130,153,-153,132,154,-134,133,154,-156,134,156,-133,132,156,-155,135,157,-137,136,157,-159,136,158,-135,134,158,-157,133,155,-138,137,155,-160,138,160,-136,135,160,-158,137,159,-140,139,159,-162,140,162,-139,138,162,-161,139,161,-142,141,161,-164,142,164,-141,140,164,-163,141,163,-144,143,163,-166,144,166,-143,142,166,-165,143,165,-146,145,165,-168,146,168,-145,144,168,-167,145,167,-148,147,167,-170,148,170,-147,146,170,-169,147,169,-150,149,169,-172,150,172,-149,148,172,-171,149,171,-152,151,171,-174,152,174,-151,150,174,-173,151,173,-154,153,173,-176,153,175,-153,152,175,-175,154,176,-156,155,176,-178,156,178,-155,154,178,-177,157,179,-159,158,179,-181,158,180,-157,156,180,-179,155,177,-160,159,177,-182,160,182,-158,157,182,-180,159,181,-162,161,181,-184,162,184,-161,160,184,-183,161,183,-164,163,183,-186,164,186,-163,162,186,-185,163,185,-166,165,185,-188,166,188,-165,164,188,-187,165,187,-168,167,187,-190,168,190,-167,166,190,-189,167,189,-170,169,189,-192,170,192,-169,168,192,-191,169,191,-172,171,191,-194,172,194,-171,170,194,-193,171,193,-174,173,193,-196,174,196,-173,172,196,-195,173,195,-176,175,195,-198,175,197,-175,174,197,-197,176,198,-178,177,198,-200,178,200,-177,176,200,-199,179,201,-181,180,201,-203,180,202,-179,178,202,-201,177,199,-182,
181,199,-204,182,204,-180,179,204,-202,181,203,-184,183,203,-206,184,206,-183,182,206,-205,183,205,-186,185,205,-208,186,208,-185,184,208,-207,185,207,-188,187,207,-210,188,210,-187,186,210,-209,187,209,-190,189,209,-212,190,212,-189,188,212,-211,189,211,-192,191,211,-214,192,214,-191,190,214,-213,191,213,-194,193,213,-216,194,216,-193,192,216,-215,193,215,-196,195,215,-218,196,218,-195,194,218,-217,195,217,-198,197,217,-220,197,219,-197,196,219,-219,198,220,-200,199,220,-222,200,222,-199,198,222,-221,201,223,-203,202,223,-225,202,224,-201,200,224,-223,199,221,-204,203,221,-226,204,226,-202,201,226,-224,203,225,-206,205,225,-228,206,228,-205,204,228,-227,205,227,-208,207,227,-230,208,230,-207,206,230,-229,207,229,-210,209,229,-232,210,232,-209,208,232,-231,209,231,-212,211,231,-234,212,234,-211,210,234,-233,211,233,-214,213,233,-236,214,236,-213,212,236,-235,213,235,-216,215,235,-238,216,238,-215,214,238,-237,215,237,-218,217,237,-240,218,240,-217,216,240,-239,217,239,-220,219,239,-242,219,241,-219,218,241,-241,220,4,-222,221,4,-6,222,6,-221,220,6,-5,242,244,-244,243,244,-246,243,245,-247,246,245,-248,221,5,-226,225,5,-20,248,249,-243,242,249,-245,225,19,-228,227,19,-26,250,251,-249,248,251,-250,227,25,-230,229,25,-32,252,253,-251,250,253,-252,229,31,-232,231,31,-38,254,255,-253,252,255,-254,231,37,-234,233,37,-44,256,257,-255,254,257,-256,233,43,-236,235,43,-50,258,259,-257,256,259,-258,235,49,-238,237,49,-56,260,261,-259,258,261,-260,237,55,-240,239,55,-62,262,263,-261,260,263,-262,239,61,-242,241,61,-66,241,65,-241,240,65,-65,224,264,-223,222,264,-266,7,266,-19,18,266,-268,222,265,-7,6,265,-269,6,268,-8,7,268,-267,223,269,-225,224,269,-265,18,267,-25,24,267,-271,226,271,-224,223,271,-270,24,270,-31,30,270,-273,228,273,-227,226,273,-272,30,272,-37,36,272,-275,230,275,-229,228,275,-274,36,274,-43,42,274,-277,232,277,-231,230,277,-276,42,276,-49,48,276,-279,234,279,-233,232,279,-278,48,278,-55,54,278,-281,236,281,-235,234,281,-280,54,280,-61,60,280,-283,238,283,-237,236,283,-282,60,282,-65,64,282,-285,240,285,-239,
238,285,-284,64,284,-241,240,284,-286,264,286,-266,265,286,-288,266,288,-268,267,288,-290,265,287,-269,268,287,-291,268,290,-267,266,290,-289,269,291,-265,264,291,-287,267,289,-271,270,289,-293,271,293,-270,269,293,-292,270,292,-273,272,292,-295,273,295,-272,271,295,-294,272,294,-275,274,294,-297,275,297,-274,273,297,-296,274,296,-277,276,296,-299,277,299,-276,275,299,-298,276,298,-279,278,298,-301,279,301,-278,277,301,-300,278,300,-281,280,300,-303,281,303,-280,279,303,-302,280,302,-283,282,302,-305,283,305,-282,281,305,-304,282,304,-285,284,304,-307,285,307,-284,283,307,-306,284,306,-286,285,306,-308,286,243,-288,287,243,-247,288,245,-290,289,245,-245,287,246,-291,290,246,-248,290,247,-289,288,247,-246,291,242,-287,286,242,-244,289,244,-293,292,244,-250,293,248,-292,291,248,-243,292,249,-295,294,249,-252,295,250,-294,293,250,-249,294,251,-297,296,251,-254,297,252,-296,295,252,-251,296,253,-299,298,253,-256,299,254,-298,297,254,-253,298,255,-301,300,255,-258,301,256,-300,299,256,-255,300,257,-303,302,257,-260,303,258,-302,301,258,-257,302,259,-305,304,259,-262,305,260,-304,303,260,-259,304,261,-307,306,261,-264,307,262,-306,305,262,-261,306,263,-308,307,263,-263
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *5508 {
				a: -0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,
-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,
-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,
-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,
-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,
-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,
-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1
			} 
			NormalsW: *1836 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *5508 {
				a: 0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,
-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,
0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,
-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,
-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,
0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,
-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0
			} 
			BinormalsW: *1836 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *5508 {
				a: 1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,
-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,
-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,
-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,
1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,
-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,
-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0
			} 
			TangentsW: *1836 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementColor: 0 {
			Version: 101
			Name: "VertexColors"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			Colors: *1720 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
			ColorIndex: *1836 {
				a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
164,136,136,164,162,137,163,139,139,163,165,140,166,138,138,166,164,139,165,141,141,165,167,142,168,140,140,168,166,141,167,143,143,167,169,144,170,142,142,170,168,143,169,145,145,169,171,146,172,147,147,172,173,148,174,149,149,174,175,150,176,151,151,176,177,152,178,153,153,178,179,153,179,154,154,179,180,149,175,155,155,175,181,156,182,152,152,182,178,155,181,157,157,181,183,158,184,156,156,184,182,157,183,159,159,183,185,160,186,158,158,186,184,159,185,161,161,185,187,162,188,160,160,188,186,161,187,163,163,187,189,164,190,162,162,190,188,163,189,165,165,189,191,166,192,164,164,192,190,165,191,167,167,191,193,168,194,166,166,194,192,167,193,169,169,193,195,170,196,168,168,196,194,169,195,171,171,195,197,172,198,173,173,198,199,174,200,175,175,200,201,176,202,177,177,202,203,178,204,179,179,204,205,179,205,180,180,205,206,175,201,181,181,201,207,182,208,178,178,208,204,181,207,183,183,207,209,184,210,182,182,210,208,183,209,185,185,209,211,186,212,184,184,212,210,185,211,187,187,211,213,188,214,186,186,214,212,187,213,189,189,213,215,190,216,188,188,216,214,189,215,191,191,215,217,192,218,190,190,218,216,191,217,193,193,217,219,194,220,192,192,220,218,193,219,195,195,219,221,196,222,194,194,222,220,195,221,197,197,221,223,198,224,199,199,224,225,200,226,201,201,226,227,202,228,203,203,228,229,204,230,205,205,230,231,205,231,206,206,231,232,201,227,207,207,227,233,208,234,204,204,234,230,207,233,209,209,233,235,210,236,208,208,236,234,209,235,211,211,235,237,212,238,210,210,238,236,211,237,213,213,237,239,214,240,212,212,240,238,213,239,215,215,239,241,216,242,214,214,242,240,215,241,217,217,241,243,218,244,216,216,244,242,217,243,219,219,243,245,220,246,218,218,246,244,219,245,221,221,245,247,222,248,220,220,248,246,221,247,223,223,247,249,224,250,225,225,250,251,226,252,227,227,252,253,228,254,229,229,254,255,230,256,231,231,256,257,231,257,232,232,257,258,227,253,233,233,253,259,234,260,230,230,260,256,233,259,235,235,259,261,236,262,234,234,262,260,235,261,237,237,261,263,238,264,236,236,264,262,237,263,239,239,
263,265,240,266,238,238,266,264,239,265,241,241,265,267,242,268,240,240,268,266,241,267,243,243,267,269,244,270,242,242,270,268,243,269,245,245,269,271,246,272,244,244,272,270,245,271,247,247,271,273,248,274,246,246,274,272,247,273,249,249,273,275,250,276,251,251,276,277,252,278,253,253,278,279,254,280,255,255,280,281,256,282,257,257,282,283,257,283,258,258,283,284,253,279,259,259,279,285,260,286,256,256,286,282,259,285,261,261,285,287,262,288,260,260,288,286,261,287,263,263,287,289,264,290,262,262,290,288,263,289,265,265,289,291,266,292,264,264,292,290,265,291,267,267,291,293,268,294,266,266,294,292,267,293,269,269,293,295,270,296,268,268,296,294,269,295,271,271,295,297,272,298,270,270,298,296,271,297,273,273,297,299,274,300,272,272,300,298,273,299,275,275,299,301,276,302,277,277,302,303,278,304,279,279,304,305,280,306,281,281,306,307,308,310,309,309,310,311,309,311,312,312,311,313,279,305,285,285,305,314,315,316,308,308,316,310,285,314,287,287,314,317,318,319,315,315,319,316,287,317,289,289,317,320,321,322,318,318,322,319,289,320,291,291,320,323,324,325,321,321,325,322,291,323,293,293,323,326,327,328,324,324,328,325,293,326,295,295,326,329,330,331,327,327,331,328,295,329,297,297,329,332,333,334,330,330,334,331,297,332,299,299,332,335,336,337,333,333,337,334,299,335,301,301,335,338,302,339,303,303,339,340,341,343,342,342,343,344,7,345,26,26,345,346,280,347,306,306,347,348,6,349,7,7,349,345,350,351,341,341,351,343,26,346,34,34,346,352,353,354,350,350,354,351,34,352,42,42,352,355,356,357,353,353,357,354,42,355,50,50,355,358,359,360,356,356,360,357,50,358,58,58,358,361,362,363,359,359,363,360,58,361,66,66,361,364,365,366,362,362,366,363,66,364,74,74,364,367,368,369,365,365,369,366,74,367,82,82,367,370,371,372,368,368,372,369,82,370,90,90,370,373,374,375,371,371,375,372,340,376,303,303,376,377,343,378,344,344,378,379,345,380,346,346,380,381,347,382,348,348,382,383,349,384,345,345,384,380,351,385,343,343,385,378,346,381,352,352,381,386,354,387,351,351,387,385,352,386,355,355,386,388,357,389,354,354,389,387,355,388,358,
358,388,390,360,391,357,357,391,389,358,390,361,361,390,392,363,393,360,360,393,391,361,392,364,364,392,394,366,395,363,363,395,393,364,394,367,367,394,396,369,397,366,366,397,395,367,396,370,370,396,398,372,399,369,369,399,397,370,398,373,373,398,400,375,401,372,372,401,399,376,402,377,377,402,403,378,404,379,379,404,405,380,406,381,381,406,407,382,408,383,383,408,409,384,410,380,380,410,406,385,411,378,378,411,404,381,407,386,386,407,412,387,413,385,385,413,411,386,412,388,388,412,414,389,415,387,387,415,413,388,414,390,390,414,416,391,417,389,389,417,415,390,416,392,392,416,418,393,419,391,391,419,417,392,418,394,394,418,420,395,421,393,393,421,419,394,420,396,396,420,422,397,423,395,395,423,421,396,422,398,398,422,424,399,425,397,397,425,423,398,424,400,400,424,426,401,427,399,399,427,425,402,428,403,403,428,429
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *860 {
				a: 0,0,-1,0,0,1,-1,1,0,0,-1,0,0,1,-1,1,1,0,0,0,1,1,0,1,1,0,0,0,1,1,0,1,0,0,1,0,0,-1,1,-1,0,-1,-1,-1,0,0,-1,0,-1,-2,0,-2,-2,1,-2,0,2,0,2,1,0,-2,1,-2,-1,-3,0,-3,-3,1,-3,0,3,0,3,1,0,-3,1,-3,-1,-4,0,-4,-4,1,-4,0,4,0,4,1,0,-4,1,-4,-1,-5,0,-5,-5,1,-5,0,5,0,5,1,0,-5,1,-5,-1,-6,0,-6,-6,1,-6,0,6,0,6,1,0,-6,1,-6,-1,-7,0,-7,-7,1,-7,0,7,0,7,1,0,-7,1,-7,-1,-8,0,-8,-8,1,-8,0,8,0,8,1,0,-8,1,-8,-1,-9,0,-9,-9,1,-9,0,9,0,9,1,0,-9,1,-9,-1,-10,0,-10,-10,1,-10,0,10,0,10,1,0,-10,1,-10,-2,0,-2,-1,-2,1,-2,0,2,-2,2,-1,2,0,-2,-2,2,-3,-2,-3,2,-4,-2,-4,2,-5,-2,-5,2,-6,-2,-6,2,-7,-2,-7,2,-8,-2,-8,2,-9,-2,-9,2,-10,-2,-10,2,0,2,1,-3,0,-3,-1,-3,1,-3,0,3,-2,3,-1,3,0,-3,-2,3,-3,-3,-3,3,-4,-3,-4,3,-5,-3,-5,3,-6,-3,-6,3,-7,-3,-7,3,-8,-3,-8,3,-9,-3,-9,3,-10,-3,-10,3,0,3,1,-4,0,-4,-1,-4,1,-4,0,4,-2,4,-1,4,0,-4,-2,4,-3,-4,-3,4,-4,-4,-4,4,-5,-4,-5,4,-6,-4,-6,4,-7,-4,-7,4,-8,-4,-8,4,-9,-4,-9,4,-10,-4,-10,4,0,4,1,-5,0,-5,-1,-5,1,-5,0,5,-2,5,-1,5,0,-5,-2,5,-3,-5,-3,5,-4,-5,-4,5,-5,-5,-5,5,-6,-5,-6,5,-7,-5,-7,5,-8,-5,-8,5,-9,-5,-9,5,-10,-5,-10,5,0,5,1,-6,0,-6,-1,-6,1,-6,0,6,-2,6,-1,6,0,-6,-2,6,-3,-6,-3,6,-4,-6,-4,6,-5,-6,-5,6,-6,-6,-6,6,-7,-6,-7,6,-8,-6,-8,6,-9,-6,-9,6,-10,-6,-10,6,0,6,1,-7,0,-7,-1,-7,1,-7,0,7,-2,7,-1,7,0,-7,-2,7,-3,-7,-3,7,-4,-7,-4,7,-5,-7,-5,7,-6,-7,-6,7,-7,-7,-7,7,-8,-7,-8,7,-9,-7,-9,7,-10,-7,-10,7,0,7,1,-8,0,-8,-1,-8,1,-8,0,8,-2,8,-1,8,0,-8,-2,8,-3,-8,-3,8,-4,-8,-4,8,-5,-8,-5,8,-6,-8,-6,8,-7,-8,-7,8,-8,-8,-8,8,-9,-8,-9,8,-10,-8,-10,8,0,8,1,-9,0,-9,-1,-9,1,-9,0,9,-2,9,-1,9,0,-9,-2,9,-3,-9,-3,9,-4,-9,-4,9,-5,-9,-5,9,-6,-9,-6,9,-7,-9,-7,9,-8,-9,-8,9,-9,-9,-9,9,-10,-9,-10,9,0,9,1,-10,0,-10,-1,-10,1,-10,0,9,-2,9,-1,10,-2,10,-1,9,0,10,0,-10,-2,9,-3,10,-3,-10,-3,9,-4,10,-4,-10,-4,9,-5,10,-5,-10,-5,9,-6,10,-6,-10,-6,9,-7,10,-7,-10,-7,9,-8,10,-8,-10,-8,9,-9,10,-9,-10,-9,9,-10,10,-10,-10,-10,10,0,10,1,1,1,0,1,1,2,0,2,-1,2,-2,2,-9,2,-10,2,0,2,2,1,2,2,-3,2,3,1,3,2,-4,2,4,1,4,2,-5,2,5,1,5,2,-6,2,6,1,6,2,-7,2,7,1,7,2,-8,2,8,1,8,2,-9,2,9,1,9,2,-10,2,10,1,10,2,10,2,9,2,1,3,0,3,-1,3,-2,3,-9,3,-10,3,0,3,2,3,-3,3,3,3,-4,3,4,3,-5,3,5,3,
-6,3,6,3,-7,3,7,3,-8,3,8,3,-9,3,9,3,-10,3,10,3,10,3,9,3,1,4,0,4,-1,4,-2,4,-9,4,-10,4,0,4,2,4,-3,4,3,4,-4,4,4,4,-5,4,5,4,-6,4,6,4,-7,4,7,4,-8,4,8,4,-9,4,9,4,-10,4,10,4,10,4,9,4
			} 
			UVIndex: *1836 {
				a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
164,136,136,164,162,137,163,139,139,163,165,140,166,138,138,166,164,139,165,141,141,165,167,142,168,140,140,168,166,141,167,143,143,167,169,144,170,142,142,170,168,143,169,145,145,169,171,146,172,147,147,172,173,148,174,149,149,174,175,150,176,151,151,176,177,152,178,153,153,178,179,153,179,154,154,179,180,149,175,155,155,175,181,156,182,152,152,182,178,155,181,157,157,181,183,158,184,156,156,184,182,157,183,159,159,183,185,160,186,158,158,186,184,159,185,161,161,185,187,162,188,160,160,188,186,161,187,163,163,187,189,164,190,162,162,190,188,163,189,165,165,189,191,166,192,164,164,192,190,165,191,167,167,191,193,168,194,166,166,194,192,167,193,169,169,193,195,170,196,168,168,196,194,169,195,171,171,195,197,172,198,173,173,198,199,174,200,175,175,200,201,176,202,177,177,202,203,178,204,179,179,204,205,179,205,180,180,205,206,175,201,181,181,201,207,182,208,178,178,208,204,181,207,183,183,207,209,184,210,182,182,210,208,183,209,185,185,209,211,186,212,184,184,212,210,185,211,187,187,211,213,188,214,186,186,214,212,187,213,189,189,213,215,190,216,188,188,216,214,189,215,191,191,215,217,192,218,190,190,218,216,191,217,193,193,217,219,194,220,192,192,220,218,193,219,195,195,219,221,196,222,194,194,222,220,195,221,197,197,221,223,198,224,199,199,224,225,200,226,201,201,226,227,202,228,203,203,228,229,204,230,205,205,230,231,205,231,206,206,231,232,201,227,207,207,227,233,208,234,204,204,234,230,207,233,209,209,233,235,210,236,208,208,236,234,209,235,211,211,235,237,212,238,210,210,238,236,211,237,213,213,237,239,214,240,212,212,240,238,213,239,215,215,239,241,216,242,214,214,242,240,215,241,217,217,241,243,218,244,216,216,244,242,217,243,219,219,243,245,220,246,218,218,246,244,219,245,221,221,245,247,222,248,220,220,248,246,221,247,223,223,247,249,224,250,225,225,250,251,226,252,227,227,252,253,228,254,229,229,254,255,230,256,231,231,256,257,231,257,232,232,257,258,227,253,233,233,253,259,234,260,230,230,260,256,233,259,235,235,259,261,236,262,234,234,262,260,235,261,237,237,261,263,238,264,236,236,264,262,237,263,239,239,
263,265,240,266,238,238,266,264,239,265,241,241,265,267,242,268,240,240,268,266,241,267,243,243,267,269,244,270,242,242,270,268,243,269,245,245,269,271,246,272,244,244,272,270,245,271,247,247,271,273,248,274,246,246,274,272,247,273,249,249,273,275,250,276,251,251,276,277,252,278,253,253,278,279,254,280,255,255,280,281,256,282,257,257,282,283,257,283,258,258,283,284,253,279,259,259,279,285,260,286,256,256,286,282,259,285,261,261,285,287,262,288,260,260,288,286,261,287,263,263,287,289,264,290,262,262,290,288,263,289,265,265,289,291,266,292,264,264,292,290,265,291,267,267,291,293,268,294,266,266,294,292,267,293,269,269,293,295,270,296,268,268,296,294,269,295,271,271,295,297,272,298,270,270,298,296,271,297,273,273,297,299,274,300,272,272,300,298,273,299,275,275,299,301,276,302,277,277,302,303,278,304,279,279,304,305,280,306,281,281,306,307,308,310,309,309,310,311,309,311,312,312,311,313,279,305,285,285,305,314,315,316,308,308,316,310,285,314,287,287,314,317,318,319,315,315,319,316,287,317,289,289,317,320,321,322,318,318,322,319,289,320,291,291,320,323,324,325,321,321,325,322,291,323,293,293,323,326,327,328,324,324,328,325,293,326,295,295,326,329,330,331,327,327,331,328,295,329,297,297,329,332,333,334,330,330,334,331,297,332,299,299,332,335,336,337,333,333,337,334,299,335,301,301,335,338,302,339,303,303,339,340,341,343,342,342,343,344,7,345,26,26,345,346,280,347,306,306,347,348,6,349,7,7,349,345,350,351,341,341,351,343,26,346,34,34,346,352,353,354,350,350,354,351,34,352,42,42,352,355,356,357,353,353,357,354,42,355,50,50,355,358,359,360,356,356,360,357,50,358,58,58,358,361,362,363,359,359,363,360,58,361,66,66,361,364,365,366,362,362,366,363,66,364,74,74,364,367,368,369,365,365,369,366,74,367,82,82,367,370,371,372,368,368,372,369,82,370,90,90,370,373,374,375,371,371,375,372,340,376,303,303,376,377,343,378,344,344,378,379,345,380,346,346,380,381,347,382,348,348,382,383,349,384,345,345,384,380,351,385,343,343,385,378,346,381,352,352,381,386,354,387,351,351,387,385,352,386,355,355,386,388,357,389,354,354,389,387,355,388,358,
358,388,390,360,391,357,357,391,389,358,390,361,361,390,392,363,393,360,360,393,391,361,392,364,364,392,394,366,395,363,363,395,393,364,394,367,367,394,396,369,397,366,366,397,395,367,396,370,370,396,398,372,399,369,369,399,397,370,398,373,373,398,400,375,401,372,372,401,399,376,402,377,377,402,403,378,404,379,379,404,405,380,406,381,381,406,407,382,408,383,383,408,409,384,410,380,380,410,406,385,411,378,378,411,404,381,407,386,386,407,412,387,413,385,385,413,411,386,412,388,388,412,414,389,415,387,387,415,413,388,414,390,390,414,416,391,417,389,389,417,415,390,416,392,392,416,418,393,419,391,391,419,417,392,418,394,394,418,420,395,421,393,393,421,419,394,420,396,396,420,422,397,423,395,395,423,421,396,422,398,398,422,424,399,425,397,397,425,423,398,424,400,400,424,426,401,427,399,399,427,425,402,428,403,403,428,429
			} 
		}
		LayerElementUV: 1 {
			Version: 101
			Name: "UVSet1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *860 {
				a: 0.497125208377838,0.647862315177917,0.54603773355484,0.647862315177917,0.497125238180161,0.69677472114563,0.54603773355484,0.69677472114563,0.497126281261444,0.00400003185495734,0.546038806438446,0.00400008028373122,0.497126221656799,0.0529124960303307,0.546038687229156,0.052912600338459,0.937336683273315,0.448212832212448,0.986249148845673,0.448212832212448,0.937336683273315,0.497125238180161,0.986249148845673,0.497125238180161,0.444211333990097,0.941338896751404,0.493123829364777,0.941338896751404,0.444211304187775,0.990251302719116,0.493123829364777,0.990251302719116,0.493123412132263,0.00400000018998981,0.493123680353165,0.0529123060405254,0.444211184978485,0.00400023907423019,0.444211393594742,0.0529124811291695,0.0040001105517149,0.497125834226608,0.0529125928878784,0.497125744819641,0.00400000018998981,0.448213398456573,0.052912525832653,0.448213338851929,0.0529126711189747,0.546038150787354,0.00400023115798831,0.546038210391998,0.594951152801514,0.0529126897454262,0.594951212406158,0.00400020135566592,0.395298987627029,0.941338896751404,0.395298957824707,0.990251302719116,0.395299017429352,0.00400040531530976,0.395299166440964,0.0529126338660717,0.0529127418994904,0.594950497150421,0.0040003526955843,0.594950556755066,0.643863558769226,0.0529127605259418,0.643863618373871,0.00400030985474586,0.346386641263962,0.941338837146759,0.346386641263962,0.990251302719116,0.346386849880219,0.00400051148608327,0.346386939287186,0.0529127717018127,0.0529128089547157,0.643862843513489,0.00400046491995454,0.643862903118134,0.692775964736938,0.0529128015041351,0.692776024341583,0.0040003964677453,0.297474265098572,0.941338837146759,0.297474265098572,0.990251302719116,0.297474652528763,0.00400057109072804,0.29747474193573,0.0529128536581993,0.0529128313064575,0.692775130271912,0.00400054175406694,0.692775130271912,0.741688370704651,0.0529128275811672,0.741688370704651,0.0040004258044064,0.248561918735504,0.941338837146759,0.248561918735504,0.990251302719116,0.248562470078468,0.00400057435035706,0.24856248497963,
0.0529128834605217,0.0529128052294254,0.74168735742569,0.00400057202205062,0.741687297821045,0.790600717067719,0.0529128089547157,0.790600657463074,0.00400044629350305,0.199649542570114,0.941338837146759,0.199649557471275,0.990251302719116,0.199650213122368,0.00400052685290575,0.199650198221207,0.0529128722846508,0.0529127381742001,0.790599584579468,0.00400052266195416,0.790599465370178,0.839513063430786,0.0529127605259418,0.839513003826141,0.00400041323155165,0.150737166404724,0.941338837146759,0.150737181305885,0.990251302719116,0.150737926363945,0.00400044582784176,0.150737851858139,0.052912812680006,0.0529126301407814,0.839511752128601,0.00400040810927749,0.839511632919312,0.888425409793854,0.0529126822948456,0.888425290584564,0.00400033360347152,0.101824782788754,0.941338837146759,0.101824797689915,0.990251302719116,0.101825594902039,0.00400029588490725,0.101825498044491,0.0529127307236195,0.052912462502718,0.888423979282379,0.0040002353489399,0.888423800468445,0.937337756156921,0.0529125705361366,0.937337577342987,0.00400020834058523,0.0529123917222023,0.941338896751404,0.0529124066233635,0.990251362323761,0.0529131852090359,0.00400014314800501,0.0529130734503269,0.0529126264154911,0.0529122911393642,0.937336266040802,0.00400000018998981,0.937336027622223,0.986250162124634,0.0529124215245247,0.986249923706055,0.00400000018998981,0.00400000018998981,0.941338896751404,0.004000015091151,0.990251362323761,0.00400074105709791,0.00400000018998981,0.00400064047425985,0.0529125295579433,0.101824909448624,0.448213219642639,0.10182498395443,0.497125655412674,0.594950079917908,0.69677472114563,0.594950079917908,0.647862255573273,0.395299315452576,0.10182486474514,0.444211542606354,0.101824708282948,0.493123799562454,0.101824522018433,0.101825043559074,0.546038091182709,0.346387058496475,0.101824998855591,0.101825095713139,0.594950437545776,0.297474771738052,0.101825103163719,0.10182511806488,0.643862783908844,0.24856248497963,0.101825162768364,0.101825095713139,0.692775130271912,0.199650153517723,0.101825177669525,0.101825028657913,
0.741687417030334,0.150737792253494,0.101825162768364,0.101824939250946,0.790599644184113,0.101825393736362,0.101825103163719,0.101824812591076,0.839511930942535,0.0529129356145859,0.101825021207333,0.101824663579464,0.888424158096313,0.00400051008909941,0.101824924349785,0.10182448476553,0.937336385250092,0.888424277305603,0.448212802410126,0.888424277305603,0.497125238180161,0.150737315416336,0.448213130235672,0.150737389922142,0.497125566005707,0.643862426280975,0.69677472114563,0.643862426280975,0.647862255573273,0.395299464464188,0.150737121701241,0.444211721420288,0.150736942887306,0.493123978376389,0.150736719369888,0.150737419724464,0.546038031578064,0.346387177705765,0.150737285614014,0.150737449526787,0.594950377941132,0.29747486114502,0.150737404823303,0.150737449526787,0.643862783908844,0.248562514781952,0.150737479329109,0.150737389922142,0.692775130271912,0.199650138616562,0.150737524032593,0.150737315416336,0.741687476634979,0.150737747550011,0.150737538933754,0.150737181305885,0.790599763393402,0.101825311779976,0.15073749423027,0.150737047195435,0.839512050151825,0.0529128462076187,0.150737434625626,0.1507368683815,0.888424336910248,0.00400038156658411,0.150737345218658,0.150736659765244,0.937336564064026,0.839511930942535,0.448212802410126,0.839511930942535,0.497125238180161,0.199649721384048,0.448213011026382,0.199649795889854,0.497125506401062,0.692774772644043,0.69677472114563,0.692774772644043,0.647862255573273,0.3952996134758,0.199649423360825,0.444211930036545,0.199649214744568,0.493124216794968,0.199648946523666,0.199649825692177,0.546037971973419,0.346387296915054,0.199649602174759,0.199649840593338,0.594950377941132,0.297474950551987,0.19964973628521,0.199649810791016,0.643862843513489,0.248562559485435,0.199649840593338,0.19964973628521,0.692775189876556,0.199650153517723,0.199649900197983,0.199649631977081,0.741687536239624,0.150737732648849,0.199649944901466,0.199649482965469,0.790599882602692,0.101825259625912,0.199649915099144,0.199649319052696,0.839512228965759,0.0529127642512321,0.199649885296822,
0.19964911043644,0.888424515724182,0.00400023953989148,0.199649810791016,0.199648857116699,0.937336802482605,0.790599584579468,0.448212802410126,0.790599584579468,0.497125238180161,0.248562186956406,0.448212921619415,0.248562231659889,0.497125446796417,0.741687178611755,0.69677472114563,0.741687178611755,0.647862255573273,0.395299822092056,0.24856173992157,0.444212168455124,0.24856148660183,0.493124485015869,0.248561203479767,0.24856224656105,0.546037971973419,0.346387445926666,0.248561948537827,0.24856224656105,0.594950437545776,0.297475039958954,0.2485621124506,0.248562201857567,0.643862843513489,0.248562633991241,0.248562231659889,0.248562097549438,0.692775249481201,0.199650198221207,0.248562321066856,0.248561978340149,0.741687655448914,0.150737732648849,0.248562380671501,0.248561799526215,0.790600061416626,0.101825222373009,0.248562380671501,0.248561590909958,0.839512407779694,0.0529127083718777,0.248562350869179,0.248561352491379,0.888424754142761,0.00400013057515025,0.248562306165695,0.248561069369316,0.937337040901184,0.7416872382164,0.448212802410126,0.7416872382164,0.497125238180161,0.297474682331085,0.448212832212448,0.297474712133408,0.497125416994095,0.790599524974823,0.69677472114563,0.790599584579468,0.647862255573273,0.395300030708313,0.297474086284637,0.444212436676025,0.297473818063736,0.493124812841415,0.29747349023819,0.297474712133408,0.546037912368774,0.346387624740601,0.297474324703217,0.297474682331085,0.594950437545776,0.297475188970566,0.297474503517151,0.29747462272644,0.643862903118134,0.24856273829937,0.297474652528763,0.297474503517151,0.692775368690491,0.199650257825851,0.297474771738052,0.297474354505539,0.741687834262848,0.150737747550011,0.297474831342697,0.297474175691605,0.79060024023056,0.101825222373009,0.29747486114502,0.297473937273026,0.839512646198273,0.0529126673936844,0.297474890947342,0.297473669052124,0.88842499256134,0.00400004768744111,0.29747486114502,0.297473341226578,0.937337338924408,0.692774891853333,0.448212772607803,0.692774832248688,0.497125238180161,0.34638723731041,
0.448212742805481,0.34638723731041,0.497125387191772,0.839511930942535,0.696774780750275,0.839511930942535,0.647862255573273,0.395300269126892,0.346386522054672,0.444212734699249,0.346386224031448,0.493125170469284,0.34638586640358,0.34638723731041,0.546037971973419,0.346387803554535,0.346386760473251,0.346387177705765,0.594950497150421,0.297475337982178,0.346386939287186,0.346387088298798,0.643863022327423,0.248562842607498,0.34638711810112,0.346386939287186,0.69277548789978,0.199650332331657,0.34638723731041,0.346386790275574,0.741687953472137,0.150737792253494,0.346387356519699,0.346386581659317,0.790600419044495,0.101825229823589,0.346387416124344,0.346386313438416,0.839512884616852,0.0529126524925232,0.346387445926666,0.346386015415192,0.888425290584564,0.00400000065565109,0.346387445926666,0.346385657787323,0.937337696552277,0.64386248588562,0.448212772607803,0.64386248588562,0.497125238180161,0.395299851894379,0.448212713003159,0.395299822092056,0.497125387191772,0.888424336910248,0.696774780750275,0.888424336910248,0.647862315177917,0.395300507545471,0.395298957824707,0.444213032722473,0.395298689603806,0.49312549829483,0.395298361778259,0.395299792289734,0.546037971973419,0.346388012170792,0.395299226045609,0.395299702882767,0.594950556755066,0.297475516796112,0.395299434661865,0.395299583673477,0.643863081932068,0.248562976717949,0.3952996134758,0.395299434661865,0.69277560710907,0.199650436639786,0.395299762487411,0.395299255847931,0.741688132286072,0.1507378667593,0.395299881696701,0.395299017429352,0.790600657463074,0.101825274527073,0.395299971103668,0.39529874920845,0.839513123035431,0.0529126673936844,0.395300030708313,0.395298451185226,0.888425648212433,0.00400000018998981,0.395300090312958,0.395298063755035,0.937338054180145,0.594950079917908,0.448212742805481,0.594950079917908,0.497125238180161,0.444212466478348,0.448212713003159,0.444212436676025,0.497125416994095,0.93733674287796,0.696774780750275,0.93733674287796,0.647862315177917,0.39530074596405,0.444211453199387,0.444213330745697,0.44421112537384,
0.493125855922699,0.444210797548294,0.444212347269058,0.546038031578064,0.346388250589371,0.444211721420288,0.444212257862091,0.594950616359711,0.297475695610046,0.444211959838867,0.444212108850479,0.643863201141357,0.248563140630722,0.444212168455124,0.444211959838867,0.692775785923004,0.199650555849075,0.444212317466736,0.44421175122261,0.741688370704651,0.150737941265106,0.444212436676025,0.444211512804031,0.790600895881653,0.101825341582298,0.444212555885315,0.444211214780807,0.839513421058655,0.0529127381742001,0.44421261548996,0.444210916757584,0.888425946235657,0.00400005746632814,0.444212704896927,0.444210529327393,0.937338471412659,0.546037673950195,0.448212742805481,0.546037673950195,0.497125238180161,0.493125230073929,0.448212772607803,0.493125081062317,0.497125506401062,0.986249148845673,0.696774780750275,0.986249148845673,0.647862315177917,0.888425290584564,0.203650057315826,0.937337756156921,0.203650057315826,0.888425290584564,0.252562463283539,0.937337756156921,0.252562433481216,0.986250281333923,0.203650042414665,0.986250281333923,0.252562433481216,0.493124961853027,0.546038091182709,0.839512884616852,0.203650072216988,0.839512884616852,0.252562463283539,0.493124842643738,0.594950735569,0.790600538253784,0.203650072216988,0.790600538253784,0.252562463283539,0.493124693632126,0.643863379955292,0.741688072681427,0.203650072216988,0.741688072681427,0.252562463283539,0.493124514818192,0.692775964736938,0.692775666713715,0.203650072216988,0.692775666713715,0.252562463283539,0.493124306201935,0.741688549518585,0.643863201141357,0.203650072216988,0.643863201141357,0.252562433481216,0.493124037981033,0.790601134300232,0.594950735569,0.203650072216988,0.594950735569,0.252562433481216,0.493123739957809,0.839513659477234,0.546038269996643,0.203650057315826,0.546038269996643,0.252562433481216,0.493123382329941,0.888426303863525,0.497125864028931,0.203650042414665,0.497125834226608,0.252562403678894,0.49312299489975,0.937338829040527,0.497125297784805,0.448212713003159,0.497125267982483,0.497125208377838,0.937337636947632,
0.256562530994415,0.986250102519989,0.256562471389771,0.937337636947632,0.305474936962128,0.986250162124634,0.305474936962128,0.546038568019867,0.101825028657913,0.594951033592224,0.1018251106143,0.93733674287796,0.745687127113342,0.986249148845673,0.745687127113342,0.497126072645187,0.101824939250946,0.888425230979919,0.256562560796738,0.888425230979919,0.305474936962128,0.643863499164581,0.101825155317783,0.839512825012207,0.256562560796738,0.839512825012207,0.305474936962128,0.692775905132294,0.101825185120106,0.790600419044495,0.256562530994415,0.790600419044495,0.305474907159805,0.741688311100006,0.101825185120106,0.741688013076782,0.256562530994415,0.741688013076782,0.305474907159805,0.790600717067719,0.101825155317783,0.69277560710907,0.256562530994415,0.69277560710907,0.305474907159805,0.839513123035431,0.101825103163719,0.643863201141357,0.256562501192093,0.643863201141357,0.305474907159805,0.888425469398499,0.101825013756752,0.594950795173645,0.256562501192093,0.594950735569,0.305474907159805,0.937337875366211,0.101824894547462,0.546038389205933,0.256562471389771,0.546038329601288,0.305474907159805,0.986250281333923,0.101824752986431,0.497125864028931,0.256562471389771,0.497125834226608,0.305474907159805,0.497125238180161,0.546037554740906,0.546037673950195,0.546037554740906,0.937337636947632,0.354387402534485,0.986250102519989,0.354387432336807,0.546038508415222,0.150737509131432,0.594950973987579,0.150737553834915,0.93733674287796,0.79459947347641,0.986249148845673,0.79459947347641,0.497125953435898,0.150737434625626,0.888425171375275,0.354387372732162,0.643863439559937,0.150737583637238,0.839512765407562,0.35438734292984,0.692775905132294,0.150737583637238,0.79060035943985,0.354387313127518,0.741688311100006,0.150737568736076,0.741687953472137,0.354387283325195,0.790600776672363,0.150737538933754,0.692775547504425,0.354387283325195,0.839513182640076,0.150737479329109,0.643863141536713,0.354387283325195,0.888425588607788,0.150737389922142,0.594950735569,0.354387313127518,0.9373379945755,0.150737285614014,
0.546038329601288,0.35438734292984,0.986250400543213,0.150737166404724,0.497125864028931,0.354387402534485,0.497125238180161,0.594949901103973,0.546037673950195,0.594949901103973,0.937337517738342,0.40329983830452,0.986250042915344,0.403299897909164,0.546038448810577,0.199650049209595,0.594950973987579,0.199650034308434,0.93733674287796,0.843511760234833,0.986249148845673,0.843511760234833,0.497125834226608,0.199650019407272,0.88842511177063,0.403299778699875,0.643863439559937,0.199650034308434,0.839512705802917,0.403299748897552,0.692775905132294,0.199650019407272,0.790600299835205,0.403299689292908,0.741688370704651,0.19964998960495,0.741687893867493,0.403299659490585,0.790600776672363,0.199649944901466,0.692775547504425,0.403299659490585,0.83951324224472,0.19964987039566,0.643863141536713,0.403299659490585,0.888425648212433,0.199649780988693,0.594950795173645,0.403299689292908,0.93733811378479,0.199649661779404,0.546038389205933,0.403299748897552,0.986250519752502,0.199649512767792,0.49712598323822,0.403299868106842,0.497125208377838,0.643862247467041,0.546037614345551,0.643862247467041
			} 
			UVIndex: *1836 {
				a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
164,136,136,164,162,137,163,139,139,163,165,140,166,138,138,166,164,139,165,141,141,165,167,142,168,140,140,168,166,141,167,143,143,167,169,144,170,142,142,170,168,143,169,145,145,169,171,146,172,147,147,172,173,148,174,149,149,174,175,150,176,151,151,176,177,152,178,153,153,178,179,153,179,154,154,179,180,149,175,155,155,175,181,156,182,152,152,182,178,155,181,157,157,181,183,158,184,156,156,184,182,157,183,159,159,183,185,160,186,158,158,186,184,159,185,161,161,185,187,162,188,160,160,188,186,161,187,163,163,187,189,164,190,162,162,190,188,163,189,165,165,189,191,166,192,164,164,192,190,165,191,167,167,191,193,168,194,166,166,194,192,167,193,169,169,193,195,170,196,168,168,196,194,169,195,171,171,195,197,172,198,173,173,198,199,174,200,175,175,200,201,176,202,177,177,202,203,178,204,179,179,204,205,179,205,180,180,205,206,175,201,181,181,201,207,182,208,178,178,208,204,181,207,183,183,207,209,184,210,182,182,210,208,183,209,185,185,209,211,186,212,184,184,212,210,185,211,187,187,211,213,188,214,186,186,214,212,187,213,189,189,213,215,190,216,188,188,216,214,189,215,191,191,215,217,192,218,190,190,218,216,191,217,193,193,217,219,194,220,192,192,220,218,193,219,195,195,219,221,196,222,194,194,222,220,195,221,197,197,221,223,198,224,199,199,224,225,200,226,201,201,226,227,202,228,203,203,228,229,204,230,205,205,230,231,205,231,206,206,231,232,201,227,207,207,227,233,208,234,204,204,234,230,207,233,209,209,233,235,210,236,208,208,236,234,209,235,211,211,235,237,212,238,210,210,238,236,211,237,213,213,237,239,214,240,212,212,240,238,213,239,215,215,239,241,216,242,214,214,242,240,215,241,217,217,241,243,218,244,216,216,244,242,217,243,219,219,243,245,220,246,218,218,246,244,219,245,221,221,245,247,222,248,220,220,248,246,221,247,223,223,247,249,224,250,225,225,250,251,226,252,227,227,252,253,228,254,229,229,254,255,230,256,231,231,256,257,231,257,232,232,257,258,227,253,233,233,253,259,234,260,230,230,260,256,233,259,235,235,259,261,236,262,234,234,262,260,235,261,237,237,261,263,238,264,236,236,264,262,237,263,239,239,
263,265,240,266,238,238,266,264,239,265,241,241,265,267,242,268,240,240,268,266,241,267,243,243,267,269,244,270,242,242,270,268,243,269,245,245,269,271,246,272,244,244,272,270,245,271,247,247,271,273,248,274,246,246,274,272,247,273,249,249,273,275,250,276,251,251,276,277,252,278,253,253,278,279,254,280,255,255,280,281,256,282,257,257,282,283,257,283,258,258,283,284,253,279,259,259,279,285,260,286,256,256,286,282,259,285,261,261,285,287,262,288,260,260,288,286,261,287,263,263,287,289,264,290,262,262,290,288,263,289,265,265,289,291,266,292,264,264,292,290,265,291,267,267,291,293,268,294,266,266,294,292,267,293,269,269,293,295,270,296,268,268,296,294,269,295,271,271,295,297,272,298,270,270,298,296,271,297,273,273,297,299,274,300,272,272,300,298,273,299,275,275,299,301,276,302,277,277,302,303,278,304,279,279,304,305,280,306,281,281,306,307,308,310,309,309,310,311,309,311,312,312,311,313,279,305,285,285,305,314,315,316,308,308,316,310,285,314,287,287,314,317,318,319,315,315,319,316,287,317,289,289,317,320,321,322,318,318,322,319,289,320,291,291,320,323,324,325,321,321,325,322,291,323,293,293,323,326,327,328,324,324,328,325,293,326,295,295,326,329,330,331,327,327,331,328,295,329,297,297,329,332,333,334,330,330,334,331,297,332,299,299,332,335,336,337,333,333,337,334,299,335,301,301,335,338,302,339,303,303,339,340,341,343,342,342,343,344,7,345,26,26,345,346,280,347,306,306,347,348,6,349,7,7,349,345,350,351,341,341,351,343,26,346,34,34,346,352,353,354,350,350,354,351,34,352,42,42,352,355,356,357,353,353,357,354,42,355,50,50,355,358,359,360,356,356,360,357,50,358,58,58,358,361,362,363,359,359,363,360,58,361,66,66,361,364,365,366,362,362,366,363,66,364,74,74,364,367,368,369,365,365,369,366,74,367,82,82,367,370,371,372,368,368,372,369,82,370,90,90,370,373,374,375,371,371,375,372,340,376,303,303,376,377,343,378,344,344,378,379,345,380,346,346,380,381,347,382,348,348,382,383,349,384,345,345,384,380,351,385,343,343,385,378,346,381,352,352,381,386,354,387,351,351,387,385,352,386,355,355,386,388,357,389,354,354,389,387,355,388,358,
358,388,390,360,391,357,357,391,389,358,390,361,361,390,392,363,393,360,360,393,391,361,392,364,364,392,394,366,395,363,363,395,393,364,394,367,367,394,396,369,397,366,366,397,395,367,396,370,370,396,398,372,399,369,369,399,397,370,398,373,373,398,400,375,401,372,372,401,399,376,402,377,377,402,403,378,404,379,379,404,405,380,406,381,381,406,407,382,408,383,383,408,409,384,410,380,380,410,406,385,411,378,378,411,404,381,407,386,386,407,412,387,413,385,385,413,411,386,412,388,388,412,414,389,415,387,387,415,413,388,414,390,390,414,416,391,417,389,389,417,415,390,416,392,392,416,418,393,419,391,391,419,417,392,418,394,394,418,420,395,421,393,393,421,419,394,420,396,396,420,422,397,423,395,395,423,421,396,422,398,398,422,424,399,425,397,397,425,423,398,424,400,400,424,426,401,427,399,399,427,425,402,428,403,403,428,429
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementColor"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
		Layer: 1 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 1
			}
		}
	}
	Model: 1146439632, "Model::ProB_Floor_1Wall_10x10", "Mesh" {
		Version: 232
		Properties70:  {
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 1148829296, "Material::Default_Prototype", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "BumpFactor", "double", "Number", "",0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "Opacity", "double", "Number", "",1
		}
	}
	Video: 1148837296, "Video::DiffuseColor_Texture", "Clip" {
		Type: "Clip"
		Properties70:  {
			P: "Path", "KString", "XRefUrl", "", "C:\Users\<USER>\Documents\Unity_Projects\use_cases_cinemachine\Assets\Plugins\ProCore\ProBuilder\Resources\Textures\GridBox_Default.png"
		}
		UseMipMap: 0
		Filename: "C:\Users\<USER>\Documents\Unity_Projects\use_cases_cinemachine\Assets\Plugins\ProCore\ProBuilder\Resources\Textures\GridBox_Default.png"
		RelativeFilename: "..\..\..\Documents\Unity_Projects\use_cases_cinemachine\Assets\Plugins\ProCore\ProBuilder\Resources\Textures\GridBox_Default.png"
	}
	Texture: 294697600, "Texture::DiffuseColor_Texture", "" {
		Type: "TextureVideoClip"
		Version: 202
		TextureName: "Texture::DiffuseColor_Texture"
		Media: "Video::DiffuseColor_Texture"
		FileName: "C:\Users\<USER>\Documents\Unity_Projects\use_cases_cinemachine\Assets\Plugins\ProCore\ProBuilder\Resources\Textures\GridBox_Default.png"
		RelativeFilename: "..\..\..\Documents\Unity_Projects\use_cases_cinemachine\Assets\Plugins\ProCore\ProBuilder\Resources\Textures\GridBox_Default.png"
		ModelUVTranslation: 0,0
		ModelUVScaling: 1,1
		Texture_Alpha_Source: "None"
		Cropping: 0,0,0,0
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::ProB_Floor_1Wall_10x10, Model::RootNode
	C: "OO",1146439632,0
	
	;Material::Default_Prototype, Model::ProB_Floor_1Wall_10x10
	C: "OO",1148829296,1146439632
	
	;Material::Default_Prototype, Model::ProB_Floor_1Wall_10x10
	C: "OO",1148829296,1146439632
	
	;Geometry::Scene, Model::ProB_Floor_1Wall_10x10
	C: "OO",1143716656,1146439632
	
	;Texture::DiffuseColor_Texture, Material::Default_Prototype
	C: "OO",294697600,1148829296
	
	;Texture::DiffuseColor_Texture, Material::Default_Prototype
	C: "OP",294697600,1148829296, "DiffuseColor"
	
	;Video::DiffuseColor_Texture, Texture::DiffuseColor_Texture
	C: "OO",1148837296,294697600
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
