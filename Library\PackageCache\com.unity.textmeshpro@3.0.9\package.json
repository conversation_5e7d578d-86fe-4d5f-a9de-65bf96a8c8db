{"name": "com.unity.textmeshpro", "displayName": "TextMeshPro", "version": "3.0.9", "unity": "2020.1", "unityRelease": "0a10", "description": "TextMeshPro is the ultimate text solution for Unity. It's the perfect replacement for Unity's UI Text and the legacy Text Mesh.\n\nPowerful and easy to use, TextMeshPro (also known as TMP) uses Advanced Text Rendering techniques along with a set of custom shaders; delivering substantial visual quality improvements while giving users incredible flexibility when it comes to text styling and texturing.\n\nTextMeshPro provides Improved Control over text formatting and layout with features like character, word, line and paragraph spacing, kerning, justified text, Links, over 30 Rich Text Tags available, support for Multi Font & Sprites, Custom Styles and more.\n\nGreat performance. Since the geometry created by TextMeshPro uses two triangles per character just like Unity's text components, this improved visual quality and flexibility comes at no additional performance cost.", "keywords": ["TextMeshPro", "TextMesh Pro", "TMP", "Text", "SDF"], "category": "Text Rendering", "dependencies": {"com.unity.ugui": "1.0.0"}, "_upm": {"changelog": "### Fixed\n- Fixed keyboard not appearing in visionOS."}, "upmCi": {"footprint": "9134b20b50857053fe14b076dd3f4d005125c599"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.textmeshpro@3.0/manual/index.html", "repository": {"url": "https://github.cds.internal.unity3d.com/unity/com.unity.textmeshpro.git", "type": "git", "revision": "0af193626b4f76795f7cb6c9b0f55389d0e4b1d6"}}