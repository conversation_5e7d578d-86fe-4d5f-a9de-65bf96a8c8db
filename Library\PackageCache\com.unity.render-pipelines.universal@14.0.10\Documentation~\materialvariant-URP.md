# Material Variants

Many of the materials in a game may be variations on a source&mdash;outfits with a variety of color schemes, damaged and undamaged versions of scenery, shiny and weathered instances of props. To help you manage and maintain these materials, Material Variants address specific shortcomings of copied materials.

To learn more about this functionality, see [Material Variants](https://docs.unity3d.com/2023.1/Documentation/Manual/materialvariant-landingpage.html).
