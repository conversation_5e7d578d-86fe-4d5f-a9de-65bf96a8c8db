#include "Packages/com.unity.render-pipelines.universal/Runtime/VFXGraph/Shaders/VFXLit.hlsl"

#ifndef VFX_SHADERGRAPH

void VFXGetURPLitData(out SurfaceData surfaceData, out InputData inputData, VFX_VARYING_PS_INPUTS i, float3 normalWS, const VFXUVData uvData, uint2 tileIndex)
{
    float3 posRWS = VFXGetPositionRWS(i);
    float4 posSS = i.VFX_VARYING_POSCS;
    PositionInputs posInput = GetPositionInput(posSS.xy, _ScreenSize.zw, posSS.z, posSS.w, posRWS, tileIndex);
    
    float alpha;
    surfaceData = VFXGetSurfaceData(i, normalWS, uvData, alpha);        
    inputData = VFXGetInputData(i, posInput, surfaceData, normalWS);
}

void VFXGetURPLitData(out SurfaceData surfaceData, out InputData inputData, VFX_VARYING_PS_INPUTS i, float3 normalWS, const VFXUVData uvData)
{
    VFXGetURPLitData(surfaceData, inputData, i, normalWS, uvData, uint2(0,0));
}
#endif


#include "Packages/com.unity.render-pipelines.universal/Runtime/VFXGraph/Shaders/VFXLitPixelOutput.hlsl"

