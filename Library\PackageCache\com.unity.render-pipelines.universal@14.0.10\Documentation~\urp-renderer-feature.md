# Pre-built effects (Renderer Features)

A Renderer Feature is an asset that lets you add a built-in effect to a Universal Render Pipeline (URP) Renderer, and configure its behavior.

For examples of how to use Renderer Features, see the [Renderer Features samples in URP Package Samples](package-sample-urp-package-samples.md#renderer-features).

|Page|Description|
|-|-|
|[How to add a Renderer Feature](urp-renderer-feature-how-to-add.md)|Add a Renderer Feature to a Renderer.|
|[Render Objects Renderer Feature](renderer-features/renderer-feature-render-objects-landing.md)|Draw objects on a certain layer, at a certain time, with specific overrides.|
|[Decal Renderer Feature](renderer-feature-decal-landing.md)|Project specific materials (decals) onto other objects in the scene. Decals interact with the scene's lighting and wrap around meshes.|
|[Screen Space Ambient Occlusion (SSAO) Renderer Feature](post-processing-ssao.md)|Darken creases, holes, intersections, and surfaces that are close to each other, in realtime.|
|[Screen Space Shadows Renderer Feature](renderer-feature-screen-space-shadows.md)|Calculate screen-space shadows for opaque objects affected by the main directional light and draw them in the scene.|
|[Full Screen Pass Renderer Feature](renderer-features/renderer-feature-full-screen-pass-landing.md)|Reference for the Full Screen Pass Renderer Feature.|
