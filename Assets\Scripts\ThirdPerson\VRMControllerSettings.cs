using UnityEngine;

namespace VRoidFaceCustomization.ThirdPerson
{
    /// <summary>
    /// VRM控制器配置设置
    /// 用于配置VRM模型的第三人称控制器参数
    /// </summary>
    [CreateAssetMenu(fileName = "VRMControllerSettings", menuName = "VRM/Controller Settings")]
    public class VRMControllerSettings : ScriptableObject
    {
        [Header("CharacterController设置")]
        [Tooltip("角色控制器的高度")]
        public float height = 1.8f;
        
        [Tooltip("角色控制器的半径")]
        public float radius = 0.3f;
        
        [Tooltip("角色控制器的中心点偏移")]
        public Vector3 center = new Vector3(0, 0.9f, 0);
        
        [Header("移动参数")]
        [Tooltip("普通移动速度 (m/s)")]
        public float moveSpeed = 2.0f;
        
        [Tooltip("冲刺速度 (m/s)")]
        public float sprintSpeed = 5.335f;
        
        [Tooltip("跳跃高度")]
        public float jumpHeight = 1.2f;
        
        [Tooltip("旋转平滑时间")]
        [Range(0.0f, 0.3f)]
        public float rotationSmoothTime = 0.12f;
        
        [Tooltip("速度变化率")]
        public float speedChangeRate = 10.0f;
        
        [Header("摄像机设置")]
        [Tooltip("摄像机目标高度")]
        public float cameraTargetHeight = 1.375f;
        
        [Tooltip("开始时是否跟随")]
        public bool followOnStart = true;
        
        [Tooltip("摄像机旋转平滑时间")]
        public float rotationSmoothTime_Camera = 0.12f;
        
        [Header("输入设置")]
        [Tooltip("Input Actions资源路径")]
        public string inputActionsAssetPath = "StarterAssets.inputactions";
        
        [Tooltip("默认动作映射")]
        public string defaultActionMap = "Player";
        
        [Header("地面检测")]
        [Tooltip("地面检测层")]
        public LayerMask groundLayers = 1;
        
        [Tooltip("地面检测偏移")]
        public float groundedOffset = -0.14f;
        
        [Tooltip("地面检测半径")]
        public float groundedRadius = 0.28f;
        
        [Tooltip("地面检测超时")]
        public float groundedTimeout = 0.50f;
        
        [Header("跳跃和重力")]
        [Tooltip("跳跃超时")]
        public float jumpTimeout = 0.50f;
        
        [Tooltip("下落超时")]
        public float fallTimeout = 0.15f;
        
        [Tooltip("重力值")]
        public float gravity = -15.0f;
        
        [Header("音效设置")]
        [Tooltip("脚步声音量")]
        [Range(0, 1)]
        public float footstepAudioVolume = 0.5f;
        
        [Tooltip("着陆音效")]
        public AudioClip landingAudioClip;
        
        [Tooltip("脚步声音效")]
        public AudioClip[] footstepAudioClips;
        
        [Header("预设配置")]
        [Tooltip("角色类型预设")]
        public CharacterType characterType = CharacterType.StandardHumanoid;
        
        /// <summary>
        /// 角色类型枚举
        /// </summary>
        public enum CharacterType
        {
            StandardHumanoid,   // 标准人形
            ChildCharacter,     // 儿童角色
            TallCharacter,      // 高大角色
            CustomSettings      // 自定义设置
        }
        
        /// <summary>
        /// 根据角色类型应用预设配置
        /// </summary>
        public void ApplyPresetForCharacterType()
        {
            switch (characterType)
            {
                case CharacterType.StandardHumanoid:
                    ApplyStandardHumanoidPreset();
                    break;
                    
                case CharacterType.ChildCharacter:
                    ApplyChildCharacterPreset();
                    break;
                    
                case CharacterType.TallCharacter:
                    ApplyTallCharacterPreset();
                    break;
                    
                case CharacterType.CustomSettings:
                    // 保持当前设置不变
                    break;
            }
        }
        
        /// <summary>
        /// 应用标准人形预设
        /// </summary>
        private void ApplyStandardHumanoidPreset()
        {
            // CharacterController设置
            height = 1.8f;
            radius = 0.3f;
            center = new Vector3(0, 0.9f, 0);
            
            // 移动参数
            moveSpeed = 2.0f;
            sprintSpeed = 5.335f;
            jumpHeight = 1.2f;
            
            // 摄像机设置
            cameraTargetHeight = 1.375f;
            
            // 地面检测
            groundedOffset = -0.14f;
            groundedRadius = 0.28f;
        }
        
        /// <summary>
        /// 应用儿童角色预设
        /// </summary>
        private void ApplyChildCharacterPreset()
        {
            // CharacterController设置 (缩小)
            height = 1.2f;
            radius = 0.25f;
            center = new Vector3(0, 0.6f, 0);
            
            // 移动参数 (稍慢)
            moveSpeed = 1.5f;
            sprintSpeed = 4.0f;
            jumpHeight = 1.0f;
            
            // 摄像机设置 (降低)
            cameraTargetHeight = 1.0f;
            
            // 地面检测 (调整)
            groundedOffset = -0.1f;
            groundedRadius = 0.2f;
        }
        
        /// <summary>
        /// 应用高大角色预设
        /// </summary>
        private void ApplyTallCharacterPreset()
        {
            // CharacterController设置 (放大)
            height = 2.2f;
            radius = 0.35f;
            center = new Vector3(0, 1.1f, 0);
            
            // 移动参数 (稍快)
            moveSpeed = 2.5f;
            sprintSpeed = 6.0f;
            jumpHeight = 1.4f;
            
            // 摄像机设置 (提高)
            cameraTargetHeight = 1.7f;
            
            // 地面检测 (调整)
            groundedOffset = -0.18f;
            groundedRadius = 0.32f;
        }
        
        /// <summary>
        /// 创建标准人形配置
        /// </summary>
        public static VRMControllerSettings CreateStandardHumanoid()
        {
            var settings = CreateInstance<VRMControllerSettings>();
            settings.characterType = CharacterType.StandardHumanoid;
            settings.ApplyStandardHumanoidPreset();
            return settings;
        }
        
        /// <summary>
        /// 创建儿童角色配置
        /// </summary>
        public static VRMControllerSettings CreateChildCharacter()
        {
            var settings = CreateInstance<VRMControllerSettings>();
            settings.characterType = CharacterType.ChildCharacter;
            settings.ApplyChildCharacterPreset();
            return settings;
        }
        
        /// <summary>
        /// 创建高大角色配置
        /// </summary>
        public static VRMControllerSettings CreateTallCharacter()
        {
            var settings = CreateInstance<VRMControllerSettings>();
            settings.characterType = CharacterType.TallCharacter;
            settings.ApplyTallCharacterPreset();
            return settings;
        }
        
        /// <summary>
        /// 验证配置参数的有效性
        /// </summary>
        public bool ValidateSettings()
        {
            bool isValid = true;
            
            if (height <= 0)
            {
                Debug.LogWarning("CharacterController高度必须大于0");
                isValid = false;
            }
            
            if (radius <= 0)
            {
                Debug.LogWarning("CharacterController半径必须大于0");
                isValid = false;
            }
            
            if (moveSpeed < 0)
            {
                Debug.LogWarning("移动速度不能为负数");
                isValid = false;
            }
            
            if (sprintSpeed < moveSpeed)
            {
                Debug.LogWarning("冲刺速度应该大于等于移动速度");
                isValid = false;
            }
            
            if (jumpHeight < 0)
            {
                Debug.LogWarning("跳跃高度不能为负数");
                isValid = false;
            }
            
            if (cameraTargetHeight <= 0)
            {
                Debug.LogWarning("摄像机目标高度必须大于0");
                isValid = false;
            }
            
            return isValid;
        }
        
        /// <summary>
        /// 获取配置摘要
        /// </summary>
        public string GetConfigurationSummary()
        {
            return $"VRM控制器配置 ({characterType}):\n" +
                   $"- 高度: {height}m, 半径: {radius}m\n" +
                   $"- 移动速度: {moveSpeed}m/s, 冲刺: {sprintSpeed}m/s\n" +
                   $"- 跳跃高度: {jumpHeight}m\n" +
                   $"- 摄像机目标高度: {cameraTargetHeight}m\n" +
                   $"- 地面检测层: {groundLayers.value}";
        }
        
        /// <summary>
        /// 重置为默认值
        /// </summary>
        [ContextMenu("重置为默认值")]
        public void ResetToDefaults()
        {
            characterType = CharacterType.StandardHumanoid;
            ApplyStandardHumanoidPreset();
            
            // 其他默认设置
            rotationSmoothTime = 0.12f;
            speedChangeRate = 10.0f;
            followOnStart = true;
            rotationSmoothTime_Camera = 0.12f;
            inputActionsAssetPath = "StarterAssets.inputactions";
            defaultActionMap = "Player";
            groundLayers = 1;
            groundedTimeout = 0.50f;
            jumpTimeout = 0.50f;
            fallTimeout = 0.15f;
            gravity = -15.0f;
            footstepAudioVolume = 0.5f;
        }
        
        /// <summary>
        /// 在Inspector中应用预设时调用
        /// </summary>
        void OnValidate()
        {
            // 确保参数在合理范围内
            height = Mathf.Max(0.1f, height);
            radius = Mathf.Max(0.1f, radius);
            moveSpeed = Mathf.Max(0f, moveSpeed);
            sprintSpeed = Mathf.Max(moveSpeed, sprintSpeed);
            jumpHeight = Mathf.Max(0f, jumpHeight);
            cameraTargetHeight = Mathf.Max(0.1f, cameraTargetHeight);
            
            rotationSmoothTime = Mathf.Clamp(rotationSmoothTime, 0f, 0.3f);
            footstepAudioVolume = Mathf.Clamp01(footstepAudioVolume);
        }
    }
}
