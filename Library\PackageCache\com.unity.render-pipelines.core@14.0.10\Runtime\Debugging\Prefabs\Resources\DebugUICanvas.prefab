%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1153602445894428
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 224711363741255626}
  - component: {fileID: 223912878945851142}
  - component: {fileID: 114908889885781782}
  - component: {fileID: 114649910605725082}
  - component: {fileID: 114530362809716058}
  m_Layer: 5
  m_Name: DebugUICanvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &224711363741255626
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1153602445894428}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!223 &223912878945851142
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1153602445894428}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 1
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 0
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 32767
  m_TargetDisplay: 0
--- !u!114 &114908889885781782
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1153602445894428}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!114 &114649910605725082
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1153602445894428}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &114530362809716058
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1153602445894428}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76db615e524a19c4990482d75a475543, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  panelPrefab: {fileID: 224481716535368988, guid: daa46a58178a6ad41ae1ddc2dc7f856d, type: 3}
  prefabs:
  - type: UnityEngine.Rendering.DebugUI+Value, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224720214277421396, guid: dc0f88987826e6e48b1fe9c7c2b53a53, type: 3}
  - type: UnityEngine.Rendering.DebugUI+BoolField, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224131888606727344, guid: ce347ad101f41ee4ab5c3fbc0ea447db, type: 3}
  - type: UnityEngine.Rendering.DebugUI+IntField, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224720214277421396, guid: ae00bb75e0cd5b04b8fe7fb4ab662629, type: 3}
  - type: UnityEngine.Rendering.DebugUI+UIntField, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224720214277421396, guid: f22bcc84a5f4a1944b075a2c4ac71493, type: 3}
  - type: UnityEngine.Rendering.DebugUI+FloatField, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224720214277421396, guid: d8c744701b43c864b88e7f8144e19bc5, type: 3}
  - type: UnityEngine.Rendering.DebugUI+EnumField, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224224135738715566, guid: 988db55689193434fb0b3b89538f978f, type: 3}
  - type: UnityEngine.Rendering.DebugUI+Button, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224438017010656346, guid: f6ce33b91f6ffe54cadacbf4bb112440, type: 3}
  - type: UnityEngine.Rendering.DebugUI+Foldout, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224053494956566916, guid: 1c87ab2ce8b8b304d98fbe9a734b1f74, type: 3}
  - type: UnityEngine.Rendering.DebugUI+ColorField, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224636372931965878, guid: 77c185820dd1a464eac89cae3abccddf, type: 3}
  - type: UnityEngine.Rendering.DebugUI+Vector2Field, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224169904409585018, guid: 326f7c58aed965d41bf7805a782d1e44, type: 3}
  - type: UnityEngine.Rendering.DebugUI+Vector3Field, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224119945032119512, guid: 94afea5f242d72547979595ba963f335, type: 3}
  - type: UnityEngine.Rendering.DebugUI+Vector4Field, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224325631027038092, guid: d47f009476100f545971a81ede14c750, type: 3}
  - type: UnityEngine.Rendering.DebugUI+VBox, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224489511352681190, guid: ca3e294656861a64b8aeeb9f916da0a9, type: 3}
  - type: UnityEngine.Rendering.DebugUI+HBox, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224719784157228276, guid: f7f5e36797cf0c1408561665c67b179b, type: 3}
  - type: UnityEngine.Rendering.DebugUI+Container, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224284813447651300, guid: 38a07789c9e87004dad98c2909f58369, type: 3}
  - type: UnityEngine.Rendering.DebugUI+BitField, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 5833802642077810669, guid: 7c78b588b2e1f7c4a86ca4a985cf6e4a, type: 3}
  - type: UnityEngine.Rendering.DebugUI+HistoryBoolField, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 108402283379224504, guid: 5088d0220f0c4df439cf06c5c270eacb, type: 3}
  - type: UnityEngine.Rendering.DebugUI+HistoryEnumField, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 8535926254376877601, guid: b2da6b27df236b144b3516ed8e7d36ac, type: 3}
  - type: UnityEngine.Rendering.DebugUI+Table, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224284813447651300, guid: 38a07789c9e87004dad98c2909f58369, type: 3}
  - type: UnityEngine.Rendering.DebugUI+Table+Row, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224053494956566916, guid: 2d019437ff89b8d44949727731cd9357, type: 3}
  - type: UnityEngine.Rendering.DebugUI+MessageBox, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224053494956566916, guid: 10a25524b0986f9488b430e2829bbbe8, type: 3}
  - type: UnityEngine.Rendering.DebugUI+ProgressBarValue, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224720214277421396, guid: d3770aaa3bbd8384aabab9ddd383e21e, type: 3}
  - type: UnityEngine.Rendering.DebugUI+ValueTuple, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224720214277421396, guid: a2148203dd960814ca5db0c293ceda35, type: 3}
  - type: UnityEngine.Rendering.DebugUI+ObjectField, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224720214277421396, guid: a87cfaef648f17c41b568e842e068c51, type: 3}
  - type: UnityEngine.Rendering.DebugUI+ObjectListField, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 224224135738715566, guid: ad83bc56407925d44a77a5bd01cd6783, type: 3}
  - type: UnityEngine.Rendering.DebugUI+ObjectPopupField, Unity.RenderPipelines.Core.Runtime,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    prefab: {fileID: 4224455051203994714, guid: 48fb043c850cadc4a883b53785e14c6e, type: 3}
