using UnityEngine;
using System.Collections.Generic;
using System;

namespace VRoidFaceCustomization
{
    /// <summary>
    /// VRM10服装骨骼数据组件
    /// 保存服装的完整骨骼信息，用于正确的骨骼重建
    /// </summary>
    public class VRM10ClothBoneData : MonoBehaviour
    {
        [Header("骨骼数据")]
        [SerializeField] private List<ClothBoneInfo> boneInfos = new List<ClothBoneInfo>();
        [SerializeField] private List<Matrix4x4> originalBindposes = new List<Matrix4x4>();
        [SerializeField] private string rootBoneName;
        
        [Header("元数据")]
        [SerializeField] private string sourceModelName;
        [SerializeField] private string extractionDate;
        [SerializeField] private int totalBoneCount;
        [SerializeField] private int vroidSpecificBoneCount;
        
        /// <summary>
        /// 获取所有骨骼信息
        /// </summary>
        public List<ClothBoneInfo> GetBoneInfos() => new List<ClothBoneInfo>(boneInfos);
        
        /// <summary>
        /// 获取原始绑定姿势
        /// </summary>
        public List<Matrix4x4> GetOriginalBindposes() => new List<Matrix4x4>(originalBindposes);
        
        /// <summary>
        /// 获取根骨骼名称
        /// </summary>
        public string GetRootBoneName() => rootBoneName;
        
        /// <summary>
        /// 设置骨骼数据（由提取工具调用）
        /// </summary>
        public void SetBoneData(List<ClothBoneInfo> bones, List<Matrix4x4> bindposes, string rootBone, string sourceName)
        {
            boneInfos = new List<ClothBoneInfo>(bones);
            originalBindposes = new List<Matrix4x4>(bindposes);
            rootBoneName = rootBone;
            sourceModelName = sourceName;
            extractionDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            totalBoneCount = bones.Count;
            vroidSpecificBoneCount = bones.FindAll(b => b.isVRoidSpecific).Count;
        }
        
        /// <summary>
        /// 根据名称查找骨骼信息
        /// </summary>
        public ClothBoneInfo FindBoneInfo(string boneName)
        {
            return boneInfos.Find(b => b.boneName == boneName);
        }
        
        /// <summary>
        /// 获取所有VRoid专用骨骼
        /// </summary>
        public List<ClothBoneInfo> GetVRoidSpecificBones()
        {
            return boneInfos.FindAll(b => b.isVRoidSpecific);
        }
        
        /// <summary>
        /// 获取所有标准骨骼
        /// </summary>
        public List<ClothBoneInfo> GetStandardBones()
        {
            return boneInfos.FindAll(b => !b.isVRoidSpecific);
        }
        
        /// <summary>
        /// 验证骨骼数据完整性
        /// </summary>
        public bool ValidateBoneData()
        {
            if (boneInfos.Count == 0)
            {
                Debug.LogError("[VRM10ClothBoneData] 骨骼数据为空");
                return false;
            }
            
            if (originalBindposes.Count != boneInfos.Count)
            {
                Debug.LogError($"[VRM10ClothBoneData] 绑定姿势数量不匹配: {originalBindposes.Count} vs {boneInfos.Count}");
                return false;
            }
            
            // 检查层级关系
            foreach (var bone in boneInfos)
            {
                if (!string.IsNullOrEmpty(bone.parentBoneName))
                {
                    var parent = FindBoneInfo(bone.parentBoneName);
                    if (parent == null)
                    {
                        Debug.LogWarning($"[VRM10ClothBoneData] 找不到父骨骼: {bone.parentBoneName} (子骨骼: {bone.boneName})");
                    }
                }
            }
            
            return true;
        }
        
        /// <summary>
        /// 获取骨骼层级深度（用于排序）
        /// </summary>
        public int GetBoneDepth(string boneName)
        {
            var bone = FindBoneInfo(boneName);
            if (bone == null) return -1;
            
            int depth = 0;
            string currentParent = bone.parentBoneName;
            
            while (!string.IsNullOrEmpty(currentParent))
            {
                depth++;
                var parentBone = FindBoneInfo(currentParent);
                if (parentBone == null) break;
                currentParent = parentBone.parentBoneName;
                
                // 防止循环引用
                if (depth > 20)
                {
                    Debug.LogError($"[VRM10ClothBoneData] 检测到骨骼层级循环: {boneName}");
                    break;
                }
            }
            
            return depth;
        }
        
        /// <summary>
        /// 按层级深度排序骨骼（父骨骼在前）
        /// </summary>
        public List<ClothBoneInfo> GetBonesSortedByDepth()
        {
            var sortedBones = new List<ClothBoneInfo>(boneInfos);
            sortedBones.Sort((a, b) => GetBoneDepth(a.boneName).CompareTo(GetBoneDepth(b.boneName)));
            return sortedBones;
        }
        
        /// <summary>
        /// 获取调试信息
        /// </summary>
        public string GetDebugInfo()
        {
            return $@"VRM10ClothBoneData 调试信息:
- 源模型: {sourceModelName}
- 提取时间: {extractionDate}
- 总骨骼数: {totalBoneCount}
- VRoid专用骨骼: {vroidSpecificBoneCount}
- 标准骨骼: {totalBoneCount - vroidSpecificBoneCount}
- 根骨骼: {rootBoneName}
- 绑定姿势数: {originalBindposes.Count}";
        }
    }
    
    /// <summary>
    /// 单个骨骼的完整信息
    /// </summary>
    [Serializable]
    public class ClothBoneInfo
    {
        [Header("基本信息")]
        public string boneName;
        public string parentBoneName;
        public bool isVRoidSpecific;
        
        [Header("Transform数据")]
        public Vector3 localPosition;
        public Quaternion localRotation;
        public Vector3 localScale;
        
        [Header("世界坐标数据")]
        public Vector3 worldPosition;
        public Quaternion worldRotation;
        
        [Header("相对于根骨骼的数据")]
        public Vector3 relativeToRootPosition;
        public Quaternion relativeToRootRotation;
        
        [Header("绑定数据")]
        public Matrix4x4 bindpose;
        public int boneIndex;
        
        /// <summary>
        /// 创建骨骼信息
        /// </summary>
        public static ClothBoneInfo CreateFromTransform(Transform bone, Transform rootBone, int index, bool isVRoid)
        {
            var info = new ClothBoneInfo
            {
                boneName = bone.name,
                parentBoneName = bone.parent != null ? bone.parent.name : "",
                isVRoidSpecific = isVRoid,
                
                localPosition = bone.localPosition,
                localRotation = bone.localRotation,
                localScale = bone.localScale,
                
                worldPosition = bone.position,
                worldRotation = bone.rotation,
                
                boneIndex = index
            };
            
            // 计算相对于根骨骼的位置和旋转
            if (rootBone != null)
            {
                var rootToWorld = rootBone.worldToLocalMatrix;
                var boneWorldMatrix = Matrix4x4.TRS(bone.position, bone.rotation, bone.lossyScale);
                var relativeToBone = rootToWorld * boneWorldMatrix;
                
                info.relativeToRootPosition = relativeToBone.GetColumn(3);
                info.relativeToRootRotation = relativeToBone.rotation;
            }
            
            return info;
        }
        
        /// <summary>
        /// 获取调试字符串
        /// </summary>
        public override string ToString()
        {
            return $"{boneName} (Parent: {parentBoneName}, VRoid: {isVRoidSpecific}, Index: {boneIndex})";
        }
    }
}
