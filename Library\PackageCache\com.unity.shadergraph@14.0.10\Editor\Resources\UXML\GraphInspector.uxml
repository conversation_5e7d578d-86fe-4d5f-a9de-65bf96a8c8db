<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:sg="UnityEditor.ShaderGraph.Drawing">
    <ui:VisualElement name="content" picking-mode="Ignore">
        <ui:VisualElement name="header" picking-mode="Ignore">
            <ui:VisualElement name="labelContainer" picking-mode="Ignore">
                <ui:Label name="titleLabel" text="" />
                <ui:Label name="subTitleLabel" text="" />
            </ui:VisualElement>
        </ui:VisualElement>
        <ui:ScrollView class="unity-scroll-view unity-scroll-view--scroll unity-scroll-view--vertical" name ="scrollView"/>
        <ui:VisualElement name="contentContainer" picking-mode="Ignore" />
            <TabbedView name="GraphInspectorView" >
                <TabButton name="NodeSettingsButton" text="Node Settings" target="NodeSettingsContainer" />
                <TabButton name="GraphSettingsButton" text="Graph Settings" target="GraphSettingsContainer" />
                <ui:VisualElement name="GraphSettingsContainer" picking-mode="Ignore" />
                <ui:VisualElement name="NodeSettingsContainer" picking-mode="Ignore">
                    <ui:Label name="maxItemsMessageLabel" picking-mode="Ignore" text ="Max of 20 visible items reached" />
                </ui:VisualElement>
            </TabbedView>
    </ui:VisualElement>
    <sg:ResizableElement pickingMode="Ignore" resizeRestriction="FlexDirection"/>
</ui:UXML>
