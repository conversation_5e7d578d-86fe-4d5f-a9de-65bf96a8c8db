%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8290671188493191910
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d877ec3e844f2ca46830012e8e79319b, type: 3}
  m_Name: PhysicallyBasedSky
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  rotation:
    m_OverrideState: 0
    m_Value: 0
    min: 0
    max: 360
  skyIntensityMode:
    m_OverrideState: 0
    m_Value: 0
  exposure:
    m_OverrideState: 1
    m_Value: 1
  multiplier:
    m_OverrideState: 0
    m_Value: 1
    min: 0
  upperHemisphereLuxValue:
    m_OverrideState: 0
    m_Value: 1
    min: 0
  upperHemisphereLuxColor:
    m_OverrideState: 0
    m_Value: {x: 0, y: 0, z: 0}
  desiredLuxValue:
    m_OverrideState: 0
    m_Value: 20000
  updateMode:
    m_OverrideState: 0
    m_Value: 0
  updatePeriod:
    m_OverrideState: 0
    m_Value: 0
    min: 0
  includeSunInBaking:
    m_OverrideState: 0
    m_Value: 0
  m_SkyVersion: 1
  m_ObsoleteEarthPreset:
    m_OverrideState: 0
    m_Value: 1
  type:
    m_OverrideState: 0
    m_Value: 1
  sphericalMode:
    m_OverrideState: 1
    m_Value: 1
  seaLevel:
    m_OverrideState: 0
    m_Value: 0
  planetaryRadius:
    m_OverrideState: 0
    m_Value: 6378100
    min: 0
  planetCenterPosition:
    m_OverrideState: 1
    m_Value: {x: 0, y: -6391103, z: 0}
  airDensityR:
    m_OverrideState: 0
    m_Value: 0.04534
    min: 0
    max: 1
  airDensityG:
    m_OverrideState: 0
    m_Value: 0.10237241
    min: 0
    max: 1
  airDensityB:
    m_OverrideState: 0
    m_Value: 0.23264056
    min: 0
    max: 1
  airTint:
    m_OverrideState: 0
    m_Value: {r: 0.9, g: 0.9, b: 1, a: 1}
    hdr: 0
    showAlpha: 0
    showEyeDropper: 1
  airMaximumAltitude:
    m_OverrideState: 0
    m_Value: 55261.973
    min: 0
  aerosolDensity:
    m_OverrideState: 1
    m_Value: 0.228
    min: 0
    max: 1
  aerosolTint:
    m_OverrideState: 1
    m_Value: {r: 0.8584906, g: 0.85140055, b: 0.7181085, a: 1}
    hdr: 0
    showAlpha: 0
    showEyeDropper: 1
  aerosolMaximumAltitude:
    m_OverrideState: 0
    m_Value: 8289.296
    min: 0
  aerosolAnisotropy:
    m_OverrideState: 0
    m_Value: 0.76
    min: -1
    max: 1
  numberOfBounces:
    m_OverrideState: 0
    m_Value: 8
    min: 1
    max: 10
  groundTint:
    m_OverrideState: 1
    m_Value: {r: 0.4319449, g: 0.58538634, b: 0.754717, a: 1}
    hdr: 0
    showAlpha: 0
    showEyeDropper: 0
  groundColorTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  groundEmissionTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  groundEmissionMultiplier:
    m_OverrideState: 0
    m_Value: 1
    min: 0
  planetRotation:
    m_OverrideState: 0
    m_Value: {x: 0, y: 0, z: 0}
  spaceEmissionTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  spaceEmissionMultiplier:
    m_OverrideState: 0
    m_Value: 1
    min: 0
  spaceRotation:
    m_OverrideState: 0
    m_Value: {x: 0, y: 0, z: 0}
  colorSaturation:
    m_OverrideState: 1
    m_Value: 1
    min: 0
    max: 1
  alphaSaturation:
    m_OverrideState: 1
    m_Value: 0
    min: 0
    max: 1
  alphaMultiplier:
    m_OverrideState: 1
    m_Value: 1
    min: 0
    max: 1
  horizonTint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
    hdr: 0
    showAlpha: 0
    showEyeDropper: 0
  zenithTint:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0.78415227, b: 1, a: 1}
    hdr: 0
    showAlpha: 0
    showEyeDropper: 0
  horizonZenithShift:
    m_OverrideState: 0
    m_Value: 0
    min: -1
    max: 1
--- !u!114 &-6507688450006126269
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 953beb541740ddc499d005ee80c9ff29, type: 3}
  m_Name: Fog
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  quality:
    m_OverrideState: 0
    m_Value: 1
  enabled:
    m_OverrideState: 0
    m_Value: 0
  colorMode:
    m_OverrideState: 0
    m_Value: 1
  color:
    m_OverrideState: 0
    m_Value: {r: 0.5, g: 0.5, b: 0.5, a: 1}
    hdr: 1
    showAlpha: 0
    showEyeDropper: 1
  tint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
    hdr: 1
    showAlpha: 0
    showEyeDropper: 1
  maxFogDistance:
    m_OverrideState: 0
    m_Value: 5000
    min: 0
  mipFogMaxMip:
    m_OverrideState: 0
    m_Value: 0.5
    min: 0
    max: 1
  mipFogNear:
    m_OverrideState: 0
    m_Value: 0
    min: 0
  mipFogFar:
    m_OverrideState: 0
    m_Value: 1000
    min: 0
  baseHeight:
    m_OverrideState: 0
    m_Value: 0
  maximumHeight:
    m_OverrideState: 0
    m_Value: 50
  meanFreePath:
    m_OverrideState: 0
    m_Value: 400
    min: 1
  enableVolumetricFog:
    m_OverrideState: 0
    m_Value: 0
  albedo:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
    hdr: 0
    showAlpha: 1
    showEyeDropper: 1
  globalLightProbeDimmer:
    m_OverrideState: 0
    m_Value: 1
    min: 0
    max: 1
  depthExtent:
    m_OverrideState: 0
    m_Value: 64
    min: 0.1
  denoisingMode:
    m_OverrideState: 0
    m_Value: 2
  anisotropy:
    m_OverrideState: 0
    m_Value: 0
    min: -1
    max: 1
  sliceDistributionUniformity:
    m_OverrideState: 0
    m_Value: 0.75
    min: 0
    max: 1
  m_FogControlMode:
    m_OverrideState: 0
    m_Value: 0
  screenResolutionPercentage:
    m_OverrideState: 0
    m_Value: 12.5
    min: 6.25
    max: 50
  volumeSliceCount:
    m_OverrideState: 0
    m_Value: 64
    min: 1
    max: 512
  m_VolumetricFogBudget:
    m_OverrideState: 0
    m_Value: 0.25
    min: 0
    max: 1
  m_ResolutionDepthRatio:
    m_OverrideState: 0
    m_Value: 0.5
    min: 0
    max: 1
  directionalLightsOnly:
    m_OverrideState: 0
    m_Value: 0
  filter:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &-4501967695779405341
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a81bcacc415a1f743bfdf703afc52027, type: 3}
  m_Name: GradientSky
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  rotation:
    m_OverrideState: 0
    m_Value: 0
    min: 0
    max: 360
  skyIntensityMode:
    m_OverrideState: 0
    m_Value: 0
  exposure:
    m_OverrideState: 0
    m_Value: 0
  multiplier:
    m_OverrideState: 0
    m_Value: 1
    min: 0
  upperHemisphereLuxValue:
    m_OverrideState: 0
    m_Value: 1
    min: 0
  upperHemisphereLuxColor:
    m_OverrideState: 0
    m_Value: {x: 0, y: 0, z: 0}
  desiredLuxValue:
    m_OverrideState: 0
    m_Value: 20000
  updateMode:
    m_OverrideState: 0
    m_Value: 0
  updatePeriod:
    m_OverrideState: 0
    m_Value: 0
    min: 0
  includeSunInBaking:
    m_OverrideState: 0
    m_Value: 0
  top:
    m_OverrideState: 0
    m_Value: {r: 0, g: 0, b: 1, a: 1}
    hdr: 1
    showAlpha: 0
    showEyeDropper: 1
  middle:
    m_OverrideState: 0
    m_Value: {r: 0.3, g: 0.7, b: 1, a: 1}
    hdr: 1
    showAlpha: 0
    showEyeDropper: 1
  bottom:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
    hdr: 1
    showAlpha: 0
    showEyeDropper: 1
  gradientDiffusion:
    m_OverrideState: 0
    m_Value: 1
--- !u!114 &-1016694868962581565
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 56b145d2b9ee1ac4f846968484e7485a, type: 3}
  m_Name: ContactShadows
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  quality:
    m_OverrideState: 0
    m_Value: 1
  enable:
    m_OverrideState: 1
    m_Value: 1
  length:
    m_OverrideState: 0
    m_Value: 0.15
    min: 0
    max: 1
  opacity:
    m_OverrideState: 0
    m_Value: 1
    min: 0
    max: 1
  distanceScaleFactor:
    m_OverrideState: 0
    m_Value: 0.5
    min: 0
    max: 1
  maxDistance:
    m_OverrideState: 0
    m_Value: 50
    min: 0
  minDistance:
    m_OverrideState: 0
    m_Value: 0
    min: 0
  fadeDistance:
    m_OverrideState: 0
    m_Value: 5
    min: 0
  fadeInDistance:
    m_OverrideState: 0
    m_Value: 0
    min: 0
  rayBias:
    m_OverrideState: 0
    m_Value: 0.2
    min: 0
    max: 1
  thicknessScale:
    m_OverrideState: 0
    m_Value: 0.15
    min: 0.02
    max: 1
  m_SampleCount:
    m_OverrideState: 0
    m_Value: 10
    min: 4
    max: 64
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: StarterAssetsHDRPVolumeProfile
  m_EditorClassIdentifier: 
  components:
  - {fileID: 7686318427622180703}
  - {fileID: -1016694868962581565}
  - {fileID: 7502528774814404555}
  - {fileID: 7542669330009093999}
  - {fileID: 1501199423866068322}
  - {fileID: 5315503232242033309}
  - {fileID: 1932259527246508038}
  - {fileID: -8290671188493191910}
  - {fileID: 198738852298360104}
--- !u!114 &198738852298360104
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 953beb541740ddc499d005ee80c9ff29, type: 3}
  m_Name: Fog
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  quality:
    m_OverrideState: 0
    m_Value: 1
  enabled:
    m_OverrideState: 1
    m_Value: 1
  colorMode:
    m_OverrideState: 1
    m_Value: 1
  color:
    m_OverrideState: 1
    m_Value: {r: 0.8773585, g: 0.8069347, b: 0.40281233, a: 1}
    hdr: 1
    showAlpha: 0
    showEyeDropper: 1
  tint:
    m_OverrideState: 1
    m_Value: {r: 1.8544642, g: 1.8544642, b: 1.8544642, a: 1}
    hdr: 1
    showAlpha: 0
    showEyeDropper: 1
  maxFogDistance:
    m_OverrideState: 0
    m_Value: 5000
    min: 0
  mipFogMaxMip:
    m_OverrideState: 0
    m_Value: 0.5
    min: 0
    max: 1
  mipFogNear:
    m_OverrideState: 0
    m_Value: 0
    min: 0
  mipFogFar:
    m_OverrideState: 0
    m_Value: 1000
    min: 0
  baseHeight:
    m_OverrideState: 1
    m_Value: 5.99
  maximumHeight:
    m_OverrideState: 0
    m_Value: 124.2
  meanFreePath:
    m_OverrideState: 1
    m_Value: 109.6
    min: 1
  enableVolumetricFog:
    m_OverrideState: 1
    m_Value: 1
  albedo:
    m_OverrideState: 1
    m_Value: {r: 1, g: 0.94751257, b: 0.83647794, a: 1}
    hdr: 0
    showAlpha: 1
    showEyeDropper: 1
  globalLightProbeDimmer:
    m_OverrideState: 1
    m_Value: 1
    min: 0
    max: 1
  depthExtent:
    m_OverrideState: 1
    m_Value: 2.4
    min: 0.1
  denoisingMode:
    m_OverrideState: 0
    m_Value: 2
  anisotropy:
    m_OverrideState: 0
    m_Value: 0
    min: -1
    max: 1
  sliceDistributionUniformity:
    m_OverrideState: 0
    m_Value: 0.75
    min: 0
    max: 1
  m_FogControlMode:
    m_OverrideState: 0
    m_Value: 0
  screenResolutionPercentage:
    m_OverrideState: 0
    m_Value: 12.5
    min: 6.25
    max: 50
  volumeSliceCount:
    m_OverrideState: 0
    m_Value: 64
    min: 1
    max: 512
  m_VolumetricFogBudget:
    m_OverrideState: 0
    m_Value: 0.33
    min: 0
    max: 1
  m_ResolutionDepthRatio:
    m_OverrideState: 0
    m_Value: 0.666
    min: 0
    max: 1
  directionalLightsOnly:
    m_OverrideState: 0
    m_Value: 0
  filter:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &1501199423866068322
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 24f077503be6ae942a1e1245dbd53ea9, type: 3}
  m_Name: Bloom
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  quality:
    m_OverrideState: 1
    m_Value: 1
  threshold:
    m_OverrideState: 0
    m_Value: 0
    min: 0
  intensity:
    m_OverrideState: 1
    m_Value: 0.024
    min: 0
    max: 1
  scatter:
    m_OverrideState: 0
    m_Value: 0.7
    min: 0
    max: 1
  tint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
    hdr: 0
    showAlpha: 0
    showEyeDropper: 1
  dirtTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  dirtIntensity:
    m_OverrideState: 0
    m_Value: 0
    min: 0
  anamorphic:
    m_OverrideState: 0
    m_Value: 1
  m_Resolution:
    m_OverrideState: 1
    m_Value: 2
  m_HighQualityPrefiltering:
    m_OverrideState: 1
    m_Value: 0
  m_HighQualityFiltering:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &1932259527246508038
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0d7593b3a9277ac4696b20006c21dde2, type: 3}
  m_Name: VisualEnvironment
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  skyType:
    m_OverrideState: 1
    m_Value: 4
  skyAmbientMode:
    m_OverrideState: 1
    m_Value: 0
  fogType:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &3531000181233230649
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4b8bcdf71d7fafa419fca1ed162f5fc9, type: 3}
  m_Name: ColorAdjustments
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  postExposure:
    m_OverrideState: 0
    m_Value: 0
  contrast:
    m_OverrideState: 0
    m_Value: 0
    min: -100
    max: 100
  colorFilter:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
    hdr: 1
    showAlpha: 0
    showEyeDropper: 1
  hueShift:
    m_OverrideState: 0
    m_Value: 0
    min: -180
    max: 180
  saturation:
    m_OverrideState: 0
    m_Value: 0
    min: -100
    max: 100
--- !u!114 &5315503232242033309
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2d08ce26990eb1a4a9177b860541e702, type: 3}
  m_Name: Exposure
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  mode:
    m_OverrideState: 1
    m_Value: 1
  meteringMode:
    m_OverrideState: 0
    m_Value: 2
  luminanceSource:
    m_OverrideState: 0
    m_Value: 1
  fixedExposure:
    m_OverrideState: 1
    m_Value: 7.12177
  compensation:
    m_OverrideState: 1
    m_Value: 3.11
  limitMin:
    m_OverrideState: 1
    m_Value: 5.5409927
  limitMax:
    m_OverrideState: 1
    m_Value: 13.578639
  curveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -10
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 20
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  limitMinCurveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -12
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 18
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  limitMaxCurveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -8
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 22
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  adaptationMode:
    m_OverrideState: 0
    m_Value: 1
  adaptationSpeedDarkToLight:
    m_OverrideState: 0
    m_Value: 3
    min: 0.001
  adaptationSpeedLightToDark:
    m_OverrideState: 0
    m_Value: 1
    min: 0.001
  weightTextureMask:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  histogramPercentages:
    m_OverrideState: 0
    m_Value: {x: 40, y: 90}
    min: 0
    max: 100
  histogramUseCurveRemapping:
    m_OverrideState: 0
    m_Value: 0
  targetMidGray:
    m_OverrideState: 0
    m_Value: 0
  centerAroundExposureTarget:
    m_OverrideState: 0
    m_Value: 0
  proceduralCenter:
    m_OverrideState: 0
    m_Value: {x: 0.5, y: 0.5}
  proceduralRadii:
    m_OverrideState: 0
    m_Value: {x: 0.3, y: 0.3}
  maskMinIntensity:
    m_OverrideState: 0
    m_Value: -30
  maskMaxIntensity:
    m_OverrideState: 0
    m_Value: 30
  proceduralSoftness:
    m_OverrideState: 0
    m_Value: 0.5
    min: 0
--- !u!114 &6738618058292305898
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 953beb541740ddc499d005ee80c9ff29, type: 3}
  m_Name: Fog
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  quality:
    m_OverrideState: 0
    m_Value: 1
  enabled:
    m_OverrideState: 0
    m_Value: 0
  colorMode:
    m_OverrideState: 0
    m_Value: 1
  color:
    m_OverrideState: 0
    m_Value: {r: 0.5, g: 0.5, b: 0.5, a: 1}
    hdr: 1
    showAlpha: 0
    showEyeDropper: 1
  tint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
    hdr: 1
    showAlpha: 0
    showEyeDropper: 1
  maxFogDistance:
    m_OverrideState: 0
    m_Value: 5000
    min: 0
  mipFogMaxMip:
    m_OverrideState: 0
    m_Value: 0.5
    min: 0
    max: 1
  mipFogNear:
    m_OverrideState: 0
    m_Value: 0
    min: 0
  mipFogFar:
    m_OverrideState: 0
    m_Value: 1000
    min: 0
  baseHeight:
    m_OverrideState: 0
    m_Value: 0
  maximumHeight:
    m_OverrideState: 0
    m_Value: 50
  meanFreePath:
    m_OverrideState: 0
    m_Value: 400
    min: 1
  enableVolumetricFog:
    m_OverrideState: 0
    m_Value: 0
  albedo:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
    hdr: 0
    showAlpha: 1
    showEyeDropper: 1
  globalLightProbeDimmer:
    m_OverrideState: 0
    m_Value: 1
    min: 0
    max: 1
  depthExtent:
    m_OverrideState: 0
    m_Value: 64
    min: 0.1
  denoisingMode:
    m_OverrideState: 0
    m_Value: 2
  anisotropy:
    m_OverrideState: 0
    m_Value: 0
    min: -1
    max: 1
  sliceDistributionUniformity:
    m_OverrideState: 0
    m_Value: 0.75
    min: 0
    max: 1
  m_FogControlMode:
    m_OverrideState: 0
    m_Value: 0
  screenResolutionPercentage:
    m_OverrideState: 0
    m_Value: 12.5
    min: 6.25
    max: 50
  volumeSliceCount:
    m_OverrideState: 0
    m_Value: 64
    min: 1
    max: 512
  m_VolumetricFogBudget:
    m_OverrideState: 0
    m_Value: 0.25
    min: 0
    max: 1
  m_ResolutionDepthRatio:
    m_OverrideState: 0
    m_Value: 0.5
    min: 0
    max: 1
  directionalLightsOnly:
    m_OverrideState: 0
    m_Value: 0
  filter:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &7502528774814404555
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9008a067f4d626c4d8bc4bc48f04bb89, type: 3}
  m_Name: AmbientOcclusion
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  quality:
    m_OverrideState: 0
    m_Value: 1
  rayTracing:
    m_OverrideState: 0
    m_Value: 0
  intensity:
    m_OverrideState: 1
    m_Value: 0.53
    min: 0
    max: 4
  directLightingStrength:
    m_OverrideState: 0
    m_Value: 0
    min: 0
    max: 1
  radius:
    m_OverrideState: 1
    m_Value: 1.5
    min: 0.25
    max: 5
  spatialBilateralAggressiveness:
    m_OverrideState: 0
    m_Value: 0.15
    min: 0
    max: 1
  temporalAccumulation:
    m_OverrideState: 0
    m_Value: 1
  ghostingReduction:
    m_OverrideState: 0
    m_Value: 0.5
    min: 0
    max: 1
  blurSharpness:
    m_OverrideState: 0
    m_Value: 0.1
    min: 0
    max: 1
  layerMask:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Bits: **********
  m_StepCount:
    m_OverrideState: 0
    m_Value: 6
    min: 2
    max: 32
  m_FullResolution:
    m_OverrideState: 0
    m_Value: 0
  m_MaximumRadiusInPixels:
    m_OverrideState: 0
    m_Value: 40
    min: 16
    max: 256
  m_BilateralUpsample:
    m_OverrideState: 0
    m_Value: 1
  m_DirectionCount:
    m_OverrideState: 0
    m_Value: 2
    min: 1
    max: 6
  m_RayLength:
    m_OverrideState: 0
    m_Value: 3
    min: 0
  m_SampleCount:
    m_OverrideState: 0
    m_Value: 2
    min: 1
    max: 64
  m_Denoise:
    m_OverrideState: 0
    m_Value: 1
  m_DenoiserRadius:
    m_OverrideState: 0
    m_Value: 0.5
    min: 0.001
    max: 1
--- !u!114 &7542669330009093999
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f086a068d4c5889438831b3ae9afc11c, type: 3}
  m_Name: Tonemapping
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  mode:
    m_OverrideState: 1
    m_Value: 2
  toeStrength:
    m_OverrideState: 0
    m_Value: 0
    min: 0
    max: 1
  toeLength:
    m_OverrideState: 0
    m_Value: 0.5
    min: 0
    max: 1
  shoulderStrength:
    m_OverrideState: 0
    m_Value: 0
    min: 0
    max: 1
  shoulderLength:
    m_OverrideState: 0
    m_Value: 0.5
    min: 0
  shoulderAngle:
    m_OverrideState: 0
    m_Value: 0
    min: 0
    max: 1
  gamma:
    m_OverrideState: 0
    m_Value: 1
    min: 0.001
  lutTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  lutContribution:
    m_OverrideState: 0
    m_Value: 1
    min: 0
    max: 1
--- !u!114 &7686318427622180703
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7ddcec8a8eb2d684d833ac8f5d26aebd, type: 3}
  m_Name: HDShadowSettings
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  maxShadowDistance:
    m_OverrideState: 1
    m_Value: 80
    min: 0
  directionalTransmissionMultiplier:
    m_OverrideState: 0
    m_Value: 1
    min: 0
    max: 1
  cascadeShadowSplitCount:
    m_OverrideState: 0
    m_Value: 4
    min: 1
    max: 4
  cascadeShadowSplit0:
    m_OverrideState: 1
    m_Value: 0.05
  cascadeShadowSplit1:
    m_OverrideState: 1
    m_Value: 0.22115377
  cascadeShadowSplit2:
    m_OverrideState: 1
    m_Value: 0.40769255
  cascadeShadowBorder0:
    m_OverrideState: 1
    m_Value: 0.13333334
  cascadeShadowBorder1:
    m_OverrideState: 1
    m_Value: 0.06666666
  cascadeShadowBorder2:
    m_OverrideState: 1
    m_Value: 0
  cascadeShadowBorder3:
    m_OverrideState: 1
    m_Value: 0
