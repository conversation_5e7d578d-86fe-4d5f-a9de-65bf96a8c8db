{"name": "com.unity.mathematics", "displayName": "Mathematics", "version": "1.2.6", "unity": "2018.3", "description": "Unity's C# SIMD math library providing vector types and math functions with a shader like syntax.", "keywords": ["unity"], "repository": {"url": "https://github.com/Unity-Technologies/Unity.Mathematics.git", "type": "git", "revision": "f110c8c230d253654afed153569030a587cc7557"}, "upmCi": {"footprint": "f9e81f42318ed88a56c8bf031a82ee5c30370e88"}}