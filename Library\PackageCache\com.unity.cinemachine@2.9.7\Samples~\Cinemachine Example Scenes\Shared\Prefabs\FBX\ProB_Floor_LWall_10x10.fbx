; FBX 7.4.0 project file
; Copyright (C) 1997-2015 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7400
	CreationTimeStamp:  {
		Version: 1000
		Year: 2017
		Month: 10
		Day: 19
		Hour: 13
		Minute: 22
		Second: 41
		Millisecond: 456
	}
	Creator: "FBX SDK/FBX Plugins version 2017.1"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "exports static meshes with materials and textures"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "export mesh materials textures uvs"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "C:\Users\<USER>\AppData\Local\Temp\tmp7a3b1e5c.tmp"
			P: "SrcDocumentUrl", "KString", "Url", "", "C:\Users\<USER>\AppData\Local\Temp\tmp7a3b1e5c.tmp"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "1.0.0f1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "1.0.0f1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 1147068480, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 7
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 2
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 1
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
	ObjectType: "Video" {
		Count: 1
		PropertyTemplate: "FbxVideo" {
			Properties70:  {
				P: "ImageSequence", "bool", "", "",0
				P: "ImageSequenceOffset", "int", "Integer", "",0
				P: "FrameRate", "double", "Number", "",0
				P: "LastFrame", "int", "Integer", "",0
				P: "Width", "int", "Integer", "",0
				P: "Height", "int", "Integer", "",0
				P: "Path", "KString", "XRefUrl", "", ""
				P: "StartFrame", "int", "Integer", "",0
				P: "StopFrame", "int", "Integer", "",0
				P: "PlaySpeed", "double", "Number", "",0
				P: "Offset", "KTime", "Time", "",0
				P: "InterlaceMode", "enum", "", "",0
				P: "FreeRunning", "bool", "", "",0
				P: "Loop", "bool", "", "",0
				P: "AccessMode", "enum", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 1143691056, "Geometry::Scene", "Mesh" {
		Vertices: *1086 {
			a: -0,0,0,-100,0,0,-0,100,0,-100,100,0,-1000,0,0,-1000,0,-100,-1000,100,0,-1000,100,-100,-100,0,-1000,-0,0,-1000,-100,100,-1000,-0,100,-1000,-0,0,-100,-0,100,-100,-100,100,-100,-100,0,-100,-100,0,-200,-0,0,-200,-1000,100,-200,-1000,0,-200,-0,100,-200,-100,100,-200,-100,0,-300,-0,0,-300,-1000,100,-300,-1000,0,-300,-0,100,-300,-100,100,-300,-100,0,-400,-0,0,-400,-1000,100,-400,-1000,0,-400,-0,100,-400,-100,100,-400,-100,0,-500,-0,0,-500,-1000,100,-500,-1000,0,-500,-0,100,-500,-100,100,-500,-100,0,-600,-0,0,-600,-1000,100,-600,-1000,0,-600,-0,100,-600,-100,100,-600,-100,0,-700,-0,0,-700,-1000,100,-700,-1000,0,-700,-0,100,-700,-100,100,-700,-100,0,-800,-0,0,-800,-1000,100,-800,-1000,0,-800,-0,100,-800,-100,100,-800,-100,0,-900,-0,0,-900,-1000,100,-900,-1000,0,-900,-0,100,-900,-100,100,-900,-1000,100,-1000,-1000,0,-1000,-0,400,-900,-100,400,-900,-0,400,-1000,-100,400,-1000,-200,0,0,-200,0,-100,-200,100,0,-200,100,-200,-200,100,-100,-200,0,-200,-200,100,-300,-200,0,-300,-200,100,-400,-200,0,-400,-200,100,-500,-200,0,-500,-200,100,-600,-200,0,-600,-200,100,-700,-200,0,-700,-200,100,-800,-200,0,-800,-200,100,-900,-200,0,-900,-200,400,-1000,-200,400,-900,-200,0,-1000,-200,100,-1000,-300,0,0,-300,0,-100,-300,100,0,-300,100,-200,-300,100,-100,-300,0,-200,-300,100,-300,-300,0,-300,-300,100,-400,-300,0,-400,-300,100,-500,-300,0,-500,-300,100,-600,-300,0,-600,-300,100,-700,-300,0,-700,-300,100,-800,-300,0,-800,-300,100,-900,-300,0,-900,-300,400,-1000,-300,400,-900,-300,0,-1000,-300,100,-1000,-400,0,0,-400,0,-100,-400,100,0,-400,100,-200,-400,100,-100,-400,0,-200,-400,100,-300,-400,0,-300,-400,100,-400,-400,0,-400,-400,100,-500,-400,0,-500,-400,100,-600,-400,0,-600,-400,100,-700,-400,0,-700,-400,100,-800,-400,0,-800,-400,100,-900,-400,0,-900,-400,400,-1000,-400,400,-900,-400,0,-1000,-400,100,-1000,-500,0,0,-500,0,-100,-500,100,0,-500,100,-200,-500,100,-100,-500,0,-200,-500,100,-300,-500,0,-300,-500,100,-400,-500,0,-400,-500,100,-500,-500,0,-500,-500,100,-600,-500,0,-600,-500,100,-700,-500,0,-700,-500,100,-800,-500,0,-800,
-500,100,-900,-500,0,-900,-500,400,-1000,-500,400,-900,-500,0,-1000,-500,100,-1000,-600,0,0,-600,0,-100,-600,100,0,-600,100,-200,-600,100,-100,-600,0,-200,-600,100,-300,-600,0,-300,-600,100,-400,-600,0,-400,-600,100,-500,-600,0,-500,-600,100,-600,-600,0,-600,-600,100,-700,-600,0,-700,-600,100,-800,-600,0,-800,-600,100,-900,-600,0,-900,-600,400,-1000,-600,400,-900,-600,0,-1000,-600,100,-1000,-700,0,0,-700,0,-100,-700,100,0,-700,100,-200,-700,100,-100,-700,0,-200,-700,100,-300,-700,0,-300,-700,100,-400,-700,0,-400,-700,100,-500,-700,0,-500,-700,100,-600,-700,0,-600,-700,100,-700,-700,0,-700,-700,100,-800,-700,0,-800,-700,100,-900,-700,0,-900,-700,400,-1000,-700,400,-900,-700,0,-1000,-700,100,-1000,-800,0,0,-800,0,-100,-800,100,0,-800,100,-200,-800,100,-100,-800,0,-200,-800,100,-300,-800,0,-300,-800,100,-400,-800,0,-400,-800,100,-500,-800,0,-500,-800,100,-600,-800,0,-600,-800,100,-700,-800,0,-700,-800,100,-800,-800,0,-800,-800,100,-900,-800,0,-900,-800,400,-1000,-800,400,-900,-800,0,-1000,-800,100,-1000,-900,0,0,-900,0,-100,-900,100,0,-900,100,-200,-900,100,-100,-900,0,-200,-900,100,-300,-900,0,-300,-900,100,-400,-900,0,-400,-900,100,-500,-900,0,-500,-900,100,-600,-900,0,-600,-900,100,-700,-900,0,-700,-900,100,-800,-900,0,-800,-900,100,-900,-900,0,-900,-900,400,-1000,-900,400,-900,-900,0,-1000,-900,100,-1000,-900,400,-200,-900,400,-100,-1000,400,-200,-1000,400,-100,-900,400,0,-1000,400,0,-900,400,-300,-1000,400,-300,-900,400,-400,-1000,400,-400,-900,400,-500,-1000,400,-500,-900,400,-600,-1000,400,-600,-900,400,-700,-1000,400,-700,-900,400,-800,-1000,400,-800,-1000,400,-900,-1000,400,-1000,-900,200,-100,-900,200,0,-1000,200,-100,-1000,200,-200,-1000,200,0,-900,200,-200,-1000,200,-300,-900,200,-300,-1000,200,-400,-900,200,-400,-1000,200,-500,-900,200,-500,-1000,200,-600,-900,200,-600,-1000,200,-700,-900,200,-700,-1000,200,-800,-900,200,-800,-1000,200,-900,-900,200,-900,-1000,200,-1000,-100,200,-1000,-0,200,-1000,-900,200,-1000,-0,200,-900,-100,200,-900,-800,200,-1000,-800,200,-900,-700,200,-1000,-700,200,-900,-600,200,-1000,
-600,200,-900,-500,200,-1000,-500,200,-900,-400,200,-1000,-400,200,-900,-300,200,-1000,-300,200,-900,-200,200,-1000,-200,200,-900,-900,300,-100,-900,300,0,-1000,300,-100,-1000,300,-200,-1000,300,0,-900,300,-200,-1000,300,-300,-900,300,-300,-1000,300,-400,-900,300,-400,-1000,300,-500,-900,300,-500,-1000,300,-600,-900,300,-600,-1000,300,-700,-900,300,-700,-1000,300,-800,-900,300,-800,-1000,300,-900,-900,300,-900,-1000,300,-1000,-100,300,-1000,-0,300,-1000,-900,300,-1000,-0,300,-900,-100,300,-900,-800,300,-1000,-800,300,-900,-700,300,-1000,-700,300,-900,-600,300,-1000,-600,300,-900,-500,300,-1000,-500,300,-900,-400,300,-1000,-400,300,-900,-300,300,-1000,-300,300,-900,-200,300,-1000,-200,300,-900
		} 
		PolygonVertexIndex: *2160 {
			a: 0,2,-2,1,2,-4,4,6,-6,5,6,-8,8,10,-10,9,10,-12,12,13,-1,0,13,-3,2,13,-4,3,13,-15,12,0,-16,15,0,-2,15,16,-13,12,16,-18,7,18,-6,5,18,-20,12,17,-14,13,17,-21,13,20,-15,14,20,-22,16,22,-18,17,22,-24,18,24,-20,19,24,-26,17,23,-21,20,23,-27,20,26,-22,21,26,-28,22,28,-24,23,28,-30,24,30,-26,25,30,-32,23,29,-27,26,29,-33,26,32,-28,27,32,-34,28,34,-30,29,34,-36,30,36,-32,31,36,-38,29,35,-33,32,35,-39,32,38,-34,33,38,-40,34,40,-36,35,40,-42,36,42,-38,37,42,-44,35,41,-39,38,41,-45,38,44,-40,39,44,-46,40,46,-42,41,46,-48,42,48,-44,43,48,-50,41,47,-45,44,47,-51,44,50,-46,45,50,-52,46,52,-48,47,52,-54,48,54,-50,49,54,-56,47,53,-51,50,53,-57,50,56,-52,51,56,-58,52,58,-54,53,58,-60,54,60,-56,55,60,-62,53,59,-57,56,59,-63,56,62,-58,57,62,-64,58,8,-60,59,8,-10,60,64,-62,61,64,-66,59,9,-63,62,9,-12,66,68,-68,67,68,-70,1,70,-16,15,70,-72,3,72,-2,1,72,-71,21,73,-15,14,73,-75,14,74,-4,3,74,-73,15,71,-17,16,71,-76,27,76,-22,21,76,-74,16,75,-23,22,75,-78,33,78,-28,27,78,-77,22,77,-29,28,77,-80,39,80,-34,33,80,-79,28,79,-35,34,79,-82,45,82,-40,39,82,-81,34,81,-41,40,81,-84,51,84,-46,45,84,-83,40,83,-47,46,83,-86,57,86,-52,51,86,-85,46,85,-53,52,85,-88,63,88,-58,57,88,-87,52,87,-59,58,87,-90,69,90,-68,67,90,-92,58,89,-9,8,89,-93,8,92,-11,10,92,-94,70,94,-72,71,94,-96,72,96,-71,70,96,-95,73,97,-75,74,97,-99,74,98,-73,72,98,-97,71,95,-76,75,95,-100,76,100,-74,73,100,-98,75,99,-78,77,99,-102,78,102,-77,76,102,-101,77,101,-80,79,101,-104,80,104,-79,78,104,-103,79,103,-82,81,103,-106,82,106,-81,80,106,-105,81,105,-84,83,105,-108,84,108,-83,82,108,-107,83,107,-86,85,107,-110,86,110,-85,84,110,-109,85,109,-88,87,109,-112,88,112,-87,86,112,-111,87,111,-90,89,111,-114,90,114,-92,91,114,-116,89,113,-93,92,113,-117,92,116,-94,93,116,-118,94,118,-96,95,118,-120,96,120,-95,94,120,-119,97,121,-99,98,121,-123,98,122,-97,96,122,-121,95,119,-100,99,119,-124,100,124,-98,97,124,-122,99,123,-102,101,123,-126,102,126,-101,100,126,-125,101,125,-104,103,125,-128,104,128,-103,102,128,-127,103,127,-106,105,127,-130,106,130,-105,104,130,-129,105,129,
-108,107,129,-132,108,132,-107,106,132,-131,107,131,-110,109,131,-134,110,134,-109,108,134,-133,109,133,-112,111,133,-136,112,136,-111,110,136,-135,111,135,-114,113,135,-138,114,138,-116,115,138,-140,113,137,-117,116,137,-141,116,140,-118,117,140,-142,118,142,-120,119,142,-144,120,144,-119,118,144,-143,121,145,-123,122,145,-147,122,146,-121,120,146,-145,119,143,-124,123,143,-148,124,148,-122,121,148,-146,123,147,-126,125,147,-150,126,150,-125,124,150,-149,125,149,-128,127,149,-152,128,152,-127,126,152,-151,127,151,-130,129,151,-154,130,154,-129,128,154,-153,129,153,-132,131,153,-156,132,156,-131,130,156,-155,131,155,-134,133,155,-158,134,158,-133,132,158,-157,133,157,-136,135,157,-160,136,160,-135,134,160,-159,135,159,-138,137,159,-162,138,162,-140,139,162,-164,137,161,-141,140,161,-165,140,164,-142,141,164,-166,142,166,-144,143,166,-168,144,168,-143,142,168,-167,145,169,-147,146,169,-171,146,170,-145,144,170,-169,143,167,-148,147,167,-172,148,172,-146,145,172,-170,147,171,-150,149,171,-174,150,174,-149,148,174,-173,149,173,-152,151,173,-176,152,176,-151,150,176,-175,151,175,-154,153,175,-178,154,178,-153,152,178,-177,153,177,-156,155,177,-180,156,180,-155,154,180,-179,155,179,-158,157,179,-182,158,182,-157,156,182,-181,157,181,-160,159,181,-184,160,184,-159,158,184,-183,159,183,-162,161,183,-186,162,186,-164,163,186,-188,161,185,-165,164,185,-189,164,188,-166,165,188,-190,166,190,-168,167,190,-192,168,192,-167,166,192,-191,169,193,-171,170,193,-195,170,194,-169,168,194,-193,167,191,-172,171,191,-196,172,196,-170,169,196,-194,171,195,-174,173,195,-198,174,198,-173,172,198,-197,173,197,-176,175,197,-200,176,200,-175,174,200,-199,175,199,-178,177,199,-202,178,202,-177,176,202,-201,177,201,-180,179,201,-204,180,204,-179,178,204,-203,179,203,-182,181,203,-206,182,206,-181,180,206,-205,181,205,-184,183,205,-208,184,208,-183,182,208,-207,183,207,-186,185,207,-210,186,210,-188,187,210,-212,185,209,-189,188,209,-213,188,212,-190,189,212,-214,190,214,-192,191,214,-216,192,216,-191,190,216,-215,193,217,-195,194,217,-219,194,
218,-193,192,218,-217,191,215,-196,195,215,-220,196,220,-194,193,220,-218,195,219,-198,197,219,-222,198,222,-197,196,222,-221,197,221,-200,199,221,-224,200,224,-199,198,224,-223,199,223,-202,201,223,-226,202,226,-201,200,226,-225,201,225,-204,203,225,-228,204,228,-203,202,228,-227,203,227,-206,205,227,-230,206,230,-205,204,230,-229,205,229,-208,207,229,-232,208,232,-207,206,232,-231,207,231,-210,209,231,-234,210,234,-212,211,234,-236,209,233,-213,212,233,-237,212,236,-214,213,236,-238,214,238,-216,215,238,-240,216,240,-215,214,240,-239,217,241,-219,218,241,-243,218,242,-217,216,242,-241,215,239,-220,219,239,-244,220,244,-218,217,244,-242,219,243,-222,221,243,-246,222,246,-221,220,246,-245,221,245,-224,223,245,-248,224,248,-223,222,248,-247,223,247,-226,225,247,-250,226,250,-225,224,250,-249,225,249,-228,227,249,-252,228,252,-227,226,252,-251,227,251,-230,229,251,-254,230,254,-229,228,254,-253,229,253,-232,231,253,-256,232,256,-231,230,256,-255,231,255,-234,233,255,-258,234,258,-236,235,258,-260,233,257,-237,236,257,-261,236,260,-238,237,260,-262,238,4,-240,239,4,-6,240,6,-239,238,6,-5,262,264,-264,263,264,-266,263,265,-267,266,265,-268,239,5,-244,243,5,-20,268,269,-263,262,269,-265,243,19,-246,245,19,-26,270,271,-269,268,271,-270,245,25,-248,247,25,-32,272,273,-271,270,273,-272,247,31,-250,249,31,-38,274,275,-273,272,275,-274,249,37,-252,251,37,-44,276,277,-275,274,277,-276,251,43,-254,253,43,-50,278,279,-277,276,279,-278,253,49,-256,255,49,-56,259,280,-279,278,280,-280,255,55,-258,257,55,-62,258,281,-260,259,281,-281,257,61,-261,260,61,-66,260,65,-262,261,65,-65,242,282,-241,240,282,-284,7,284,-19,18,284,-286,240,283,-7,6,283,-287,6,286,-8,7,286,-285,241,287,-243,242,287,-283,18,285,-25,24,285,-289,244,289,-242,241,289,-288,24,288,-31,30,288,-291,246,291,-245,244,291,-290,30,290,-37,36,290,-293,248,293,-247,246,293,-292,36,292,-43,42,292,-295,250,295,-249,248,295,-294,42,294,-49,48,294,-297,252,297,-251,250,297,-296,48,296,-55,54,296,-299,254,299,-253,252,299,-298,54,298,-61,60,298,-301,256,301,-255,254,301,-300,
60,300,-65,64,300,-303,10,303,-12,11,303,-305,64,302,-262,261,302,-306,62,306,-64,63,306,-308,261,305,-238,237,305,-309,232,309,-257,256,309,-302,237,308,-214,213,308,-311,208,311,-233,232,311,-310,213,310,-190,189,310,-313,184,313,-209,208,313,-312,189,312,-166,165,312,-315,160,315,-185,184,315,-314,165,314,-142,141,314,-317,136,317,-161,160,317,-316,141,316,-118,117,316,-319,112,319,-137,136,319,-318,117,318,-94,93,318,-321,88,321,-113,112,321,-320,93,320,-11,10,320,-304,63,307,-89,88,307,-322,11,304,-63,62,304,-307,282,322,-284,283,322,-324,284,324,-286,285,324,-326,283,323,-287,286,323,-327,286,326,-285,284,326,-325,287,327,-283,282,327,-323,285,325,-289,288,325,-329,289,329,-288,287,329,-328,288,328,-291,290,328,-331,291,331,-290,289,331,-330,290,330,-293,292,330,-333,293,333,-292,291,333,-332,292,332,-295,294,332,-335,295,335,-294,293,335,-334,294,334,-297,296,334,-337,297,337,-296,295,337,-336,296,336,-299,298,336,-339,299,339,-298,297,339,-338,298,338,-301,300,338,-341,301,341,-300,299,341,-340,300,340,-303,302,340,-343,303,343,-305,304,343,-345,302,342,-306,305,342,-346,306,346,-308,307,346,-348,305,345,-309,308,345,-349,309,349,-302,301,349,-342,308,348,-311,310,348,-351,311,351,-310,309,351,-350,310,350,-313,312,350,-353,313,353,-312,311,353,-352,312,352,-315,314,352,-355,315,355,-314,313,355,-354,314,354,-317,316,354,-357,317,357,-316,315,357,-356,316,356,-319,318,356,-359,319,359,-318,317,359,-358,318,358,-321,320,358,-361,321,361,-320,319,361,-360,320,360,-304,303,360,-344,307,347,-322,321,347,-362,304,344,-307,306,344,-347,322,263,-324,323,263,-267,324,265,-326,325,265,-265,323,266,-327,326,266,-268,326,267,-325,324,267,-266,327,262,-323,322,262,-264,325,264,-329,328,264,-270,329,268,-328,327,268,-263,328,269,-331,330,269,-272,331,270,-330,329,270,-269,330,271,-333,332,271,-274,333,272,-332,331,272,-271,332,273,-335,334,273,-276,335,274,-334,333,274,-273,334,275,-337,336,275,-278,337,276,-336,335,276,-275,336,277,-339,338,277,-280,339,278,-338,337,278,-277,338,279,-341,340,279,-281,341,259,-340,339,
259,-279,340,280,-343,342,280,-282,343,69,-345,344,69,-69,342,281,-346,345,281,-259,346,66,-348,347,66,-68,345,258,-349,348,258,-235,349,235,-342,341,235,-260,348,234,-351,350,234,-211,351,211,-350,349,211,-236,350,210,-353,352,210,-187,353,187,-352,351,187,-212,352,186,-355,354,186,-163,355,163,-354,353,163,-188,354,162,-357,356,162,-139,357,139,-356,355,139,-164,356,138,-359,358,138,-115,359,115,-358,357,115,-140,358,114,-361,360,114,-91,361,91,-360,359,91,-116,360,90,-344,343,90,-70,347,67,-362,361,67,-92,344,68,-347,346,68,-67
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *6480 {
				a: -0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,
-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,
-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,
-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,
-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,
-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,
-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,
-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0
			} 
			NormalsW: *2160 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *6480 {
				a: 0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,
-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,
0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,
-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,
-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,
0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,
-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,
0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0
			} 
			BinormalsW: *2160 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *6480 {
				a: 1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,
-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,
-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,
-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,
1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,
-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,
-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,
-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1
			} 
			TangentsW: *2160 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementColor: 0 {
			Version: 101
			Name: "VertexColors"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			Colors: *2028 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
			ColorIndex: *2160 {
				a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
141,168,139,139,168,166,140,167,142,142,167,169,143,170,141,141,170,168,142,169,144,144,169,171,145,172,143,143,172,170,144,171,146,146,171,173,147,174,148,148,174,175,146,173,149,149,173,176,150,177,151,151,177,178,152,179,153,153,179,180,154,181,155,155,181,182,156,183,157,157,183,184,157,184,158,158,184,185,153,180,159,159,180,186,160,187,156,156,187,183,159,186,161,161,186,188,162,189,160,160,189,187,161,188,163,163,188,190,164,191,162,162,191,189,163,190,165,165,190,192,166,193,164,164,193,191,165,192,167,167,192,194,168,195,166,166,195,193,167,194,169,169,194,196,170,197,168,168,197,195,169,196,171,171,196,198,172,199,170,170,199,197,171,198,173,173,198,200,174,201,175,175,201,202,173,200,176,176,200,203,177,204,178,178,204,205,179,206,180,180,206,207,181,208,182,182,208,209,183,210,184,184,210,211,184,211,185,185,211,212,180,207,186,186,207,213,187,214,183,183,214,210,186,213,188,188,213,215,189,216,187,187,216,214,188,215,190,190,215,217,191,218,189,189,218,216,190,217,192,192,217,219,193,220,191,191,220,218,192,219,194,194,219,221,195,222,193,193,222,220,194,221,196,196,221,223,197,224,195,195,224,222,196,223,198,198,223,225,199,226,197,197,226,224,198,225,200,200,225,227,201,228,202,202,228,229,200,227,203,203,227,230,204,231,205,205,231,232,206,233,207,207,233,234,208,235,209,209,235,236,210,237,211,211,237,238,211,238,212,212,238,239,207,234,213,213,234,240,214,241,210,210,241,237,213,240,215,215,240,242,216,243,214,214,243,241,215,242,217,217,242,244,218,245,216,216,245,243,217,244,219,219,244,246,220,247,218,218,247,245,219,246,221,221,246,248,222,249,220,220,249,247,221,248,223,223,248,250,224,251,222,222,251,249,223,250,225,225,250,252,226,253,224,224,253,251,225,252,227,227,252,254,228,255,229,229,255,256,227,254,230,230,254,257,231,258,232,232,258,259,233,260,234,234,260,261,235,262,236,236,262,263,237,264,238,238,264,265,238,265,239,239,265,266,234,261,240,240,261,267,241,268,237,237,268,264,240,267,242,242,267,269,243,270,241,241,270,268,242,269,244,244,269,271,245,272,243,243,272,270,244,271,246,
246,271,273,247,274,245,245,274,272,246,273,248,248,273,275,249,276,247,247,276,274,248,275,250,250,275,277,251,278,249,249,278,276,250,277,252,252,277,279,253,280,251,251,280,278,252,279,254,254,279,281,255,282,256,256,282,283,254,281,257,257,281,284,258,285,259,259,285,286,260,287,261,261,287,288,262,289,263,263,289,290,264,291,265,265,291,292,265,292,266,266,292,293,261,288,267,267,288,294,268,295,264,264,295,291,267,294,269,269,294,296,270,297,268,268,297,295,269,296,271,271,296,298,272,299,270,270,299,297,271,298,273,273,298,300,274,301,272,272,301,299,273,300,275,275,300,302,276,303,274,274,303,301,275,302,277,277,302,304,278,305,276,276,305,303,277,304,279,279,304,306,280,307,278,278,307,305,279,306,281,281,306,308,282,309,283,283,309,310,281,308,284,284,308,311,285,312,286,286,312,313,287,314,288,288,314,315,289,316,290,290,316,317,318,320,319,319,320,321,319,321,322,322,321,323,288,315,294,294,315,324,325,326,318,318,326,320,294,324,296,296,324,327,328,329,325,325,329,326,296,327,298,298,327,330,331,332,328,328,332,329,298,330,300,300,330,333,334,335,331,331,335,332,300,333,302,302,333,336,337,338,334,334,338,335,302,336,304,304,336,339,340,341,337,337,341,338,304,339,306,306,339,342,310,343,340,340,343,341,306,342,308,308,342,344,309,345,310,310,345,343,308,344,311,311,344,346,312,347,313,313,347,348,349,351,350,350,351,352,7,353,26,26,353,354,289,355,316,316,355,356,6,357,7,7,357,353,358,359,349,349,359,351,26,354,34,34,354,360,361,362,358,358,362,359,34,360,42,42,360,363,364,365,361,361,365,362,42,363,50,50,363,366,367,368,364,364,368,365,50,366,58,58,366,369,370,371,367,367,371,368,58,369,66,66,369,372,373,374,370,370,374,371,66,372,74,74,372,375,376,377,373,373,377,374,74,375,82,82,375,378,379,380,376,376,380,377,82,378,90,90,378,381,10,382,11,11,382,383,348,384,313,313,384,385,386,388,387,387,388,389,313,385,286,286,385,390,391,393,392,392,393,394,286,390,259,259,390,395,396,397,391,391,397,393,259,395,232,232,395,398,399,400,396,396,400,397,232,398,205,205,398,401,402,403,399,399,403,400,205,401,178,
178,401,404,405,406,402,402,406,403,178,404,151,151,404,407,408,409,405,405,409,406,151,407,124,124,407,410,411,412,408,408,412,409,124,410,10,10,410,382,387,389,411,411,389,412,93,413,85,85,413,414,351,415,352,352,415,416,353,417,354,354,417,418,355,419,356,356,419,420,357,421,353,353,421,417,359,422,351,351,422,415,354,418,360,360,418,423,362,424,359,359,424,422,360,423,363,363,423,425,365,426,362,362,426,424,363,425,366,366,425,427,368,428,365,365,428,426,366,427,369,369,427,429,371,430,368,368,430,428,369,429,372,372,429,431,374,432,371,371,432,430,372,431,375,375,431,433,377,434,374,374,434,432,375,433,378,378,433,435,380,436,377,377,436,434,378,435,381,381,435,437,382,438,383,383,438,439,384,440,385,385,440,441,388,442,389,389,442,443,385,441,390,390,441,444,393,445,394,394,445,446,390,444,395,395,444,447,397,448,393,393,448,445,395,447,398,398,447,449,400,450,397,397,450,448,398,449,401,401,449,451,403,452,400,400,452,450,401,451,404,404,451,453,406,454,403,403,454,452,404,453,407,407,453,455,409,456,406,406,456,454,407,455,410,410,455,457,412,458,409,409,458,456,410,457,382,382,457,438,389,443,412,412,443,458,413,459,414,414,459,460,415,461,416,416,461,462,417,463,418,418,463,464,419,465,420,420,465,466,421,467,417,417,467,463,422,468,415,415,468,461,418,464,423,423,464,469,424,470,422,422,470,468,423,469,425,425,469,471,426,472,424,424,472,470,425,471,427,427,471,473,428,474,426,426,474,472,427,473,429,429,473,475,430,476,428,428,476,474,429,475,431,431,475,477,432,478,430,430,478,476,431,477,433,433,477,479,434,480,432,432,480,478,433,479,435,435,479,481,436,482,434,434,482,480,435,481,437,437,481,483,438,484,439,439,484,485,440,486,441,441,486,487,442,488,443,443,488,489,441,487,444,444,487,490,445,491,446,446,491,492,444,490,447,447,490,493,448,494,445,445,494,491,447,493,449,449,493,495,450,496,448,448,496,494,449,495,451,451,495,497,452,498,450,450,498,496,451,497,453,453,497,499,454,500,452,452,500,498,453,499,455,455,499,501,456,502,454,454,502,500,455,501,457,457,501,503,458,504,456,456,504,502,457,
503,438,438,503,484,443,489,458,458,489,504,459,505,460,460,505,506
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *1014 {
				a: 0,0,-1,0,0,1,-1,1,0,0,-1,0,0,1,-1,1,1,0,0,0,1,1,0,1,1,0,0,0,1,1,0,1,0,0,1,0,0,-1,1,-1,0,-1,-1,-1,0,0,-1,0,-1,-2,0,-2,-2,1,-2,0,2,0,2,1,0,-2,1,-2,-1,-3,0,-3,-3,1,-3,0,3,0,3,1,0,-3,1,-3,-1,-4,0,-4,-4,1,-4,0,4,0,4,1,0,-4,1,-4,-1,-5,0,-5,-5,1,-5,0,5,0,5,1,0,-5,1,-5,-1,-6,0,-6,-6,1,-6,0,6,0,6,1,0,-6,1,-6,-1,-7,0,-7,-7,1,-7,0,7,0,7,1,0,-7,1,-7,-1,-8,0,-8,-8,1,-8,0,8,0,8,1,0,-8,1,-8,-1,-9,0,-9,-9,1,-9,0,9,0,9,1,0,-9,1,-9,-1,-10,0,-10,-10,1,-10,0,10,0,10,1,0,-9,1,-9,0,-10,1,-10,-2,0,-2,-1,-2,1,-2,0,2,-2,2,-1,2,0,-2,-2,2,-3,-2,-3,2,-4,-2,-4,2,-5,-2,-5,2,-6,-2,-6,2,-7,-2,-7,2,-8,-2,-8,2,-9,-2,-9,2,-10,2,-9,-2,-10,2,0,2,1,-3,0,-3,-1,-3,1,-3,0,3,-2,3,-1,3,0,-3,-2,3,-3,-3,-3,3,-4,-3,-4,3,-5,-3,-5,3,-6,-3,-6,3,-7,-3,-7,3,-8,-3,-8,3,-9,-3,-9,3,-10,3,-9,-3,-10,3,0,3,1,-4,0,-4,-1,-4,1,-4,0,4,-2,4,-1,4,0,-4,-2,4,-3,-4,-3,4,-4,-4,-4,4,-5,-4,-5,4,-6,-4,-6,4,-7,-4,-7,4,-8,-4,-8,4,-9,-4,-9,4,-10,4,-9,-4,-10,4,0,4,1,-5,0,-5,-1,-5,1,-5,0,5,-2,5,-1,5,0,-5,-2,5,-3,-5,-3,5,-4,-5,-4,5,-5,-5,-5,5,-6,-5,-6,5,-7,-5,-7,5,-8,-5,-8,5,-9,-5,-9,5,-10,5,-9,-5,-10,5,0,5,1,-6,0,-6,-1,-6,1,-6,0,6,-2,6,-1,6,0,-6,-2,6,-3,-6,-3,6,-4,-6,-4,6,-5,-6,-5,6,-6,-6,-6,6,-7,-6,-7,6,-8,-6,-8,6,-9,-6,-9,6,-10,6,-9,-6,-10,6,0,6,1,-7,0,-7,-1,-7,1,-7,0,7,-2,7,-1,7,0,-7,-2,7,-3,-7,-3,7,-4,-7,-4,7,-5,-7,-5,7,-6,-7,-6,7,-7,-7,-7,7,-8,-7,-8,7,-9,-7,-9,7,-10,7,-9,-7,-10,7,0,7,1,-8,0,-8,-1,-8,1,-8,0,8,-2,8,-1,8,0,-8,-2,8,-3,-8,-3,8,-4,-8,-4,8,-5,-8,-5,8,-6,-8,-6,8,-7,-8,-7,8,-8,-8,-8,8,-9,-8,-9,8,-10,8,-9,-8,-10,8,0,8,1,-9,0,-9,-1,-9,1,-9,0,9,-2,9,-1,9,0,-9,-2,9,-3,-9,-3,9,-4,-9,-4,9,-5,-9,-5,9,-6,-9,-6,9,-7,-9,-7,9,-8,-9,-8,9,-9,-9,-9,9,-10,9,-9,-9,-10,9,0,9,1,-10,0,-10,-1,-10,1,-10,0,9,-2,9,-1,10,-2,10,-1,9,0,10,0,-10,-2,9,-3,10,-3,-10,-3,9,-4,10,-4,-10,-4,9,-5,10,-5,-10,-5,9,-6,10,-6,-10,-6,9,-7,10,-7,-10,-7,9,-8,10,-8,-10,-8,10,-9,-10,-9,10,-10,-10,-10,10,0,10,1,1,1,0,1,1,2,0,2,-1,2,-2,2,-9,2,-10,2,0,2,2,1,2,2,-3,2,3,1,3,2,-4,2,4,1,4,2,-5,2,5,1,5,2,-6,2,6,1,6,2,-7,2,7,1,7,2,-8,2,8,1,8,2,-9,2,9,1,9,2,-10,2,1,2,0,2,10,2,9,2,0,1,-1,1,0,2,-1,2,8,2,-8,1,
-9,1,-8,2,-9,2,7,2,-7,1,-7,2,6,2,-6,1,-6,2,5,2,-5,1,-5,2,4,2,-4,1,-4,2,3,2,-3,1,-3,2,2,2,-2,1,-2,2,10,2,9,2,1,3,0,3,-1,3,-2,3,-9,3,-10,3,0,3,2,3,-3,3,3,3,-4,3,4,3,-5,3,5,3,-6,3,6,3,-7,3,7,3,-8,3,8,3,-9,3,9,3,-10,3,1,3,0,3,10,3,9,3,0,3,-1,3,8,3,-8,3,-9,3,7,3,-7,3,6,3,-6,3,5,3,-5,3,4,3,-4,3,3,3,-3,3,2,3,-2,3,10,3,9,3,1,4,0,4,-1,4,-2,4,-9,4,-10,4,0,4,2,4,-3,4,3,4,-4,4,4,4,-5,4,5,4,-6,4,6,4,-7,4,7,4,-8,4,8,4,-9,4,9,4,-10,4,1,4,0,4,10,4,9,4,0,4,-1,4,8,4,-8,4,-9,4,7,4,-7,4,6,4,-6,4,5,4,-5,4,4,4,-4,4,3,4,-3,4,2,4,-2,4,10,4,9,4
			} 
			UVIndex: *2160 {
				a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
141,168,139,139,168,166,140,167,142,142,167,169,143,170,141,141,170,168,142,169,144,144,169,171,145,172,143,143,172,170,144,171,146,146,171,173,147,174,148,148,174,175,146,173,149,149,173,176,150,177,151,151,177,178,152,179,153,153,179,180,154,181,155,155,181,182,156,183,157,157,183,184,157,184,158,158,184,185,153,180,159,159,180,186,160,187,156,156,187,183,159,186,161,161,186,188,162,189,160,160,189,187,161,188,163,163,188,190,164,191,162,162,191,189,163,190,165,165,190,192,166,193,164,164,193,191,165,192,167,167,192,194,168,195,166,166,195,193,167,194,169,169,194,196,170,197,168,168,197,195,169,196,171,171,196,198,172,199,170,170,199,197,171,198,173,173,198,200,174,201,175,175,201,202,173,200,176,176,200,203,177,204,178,178,204,205,179,206,180,180,206,207,181,208,182,182,208,209,183,210,184,184,210,211,184,211,185,185,211,212,180,207,186,186,207,213,187,214,183,183,214,210,186,213,188,188,213,215,189,216,187,187,216,214,188,215,190,190,215,217,191,218,189,189,218,216,190,217,192,192,217,219,193,220,191,191,220,218,192,219,194,194,219,221,195,222,193,193,222,220,194,221,196,196,221,223,197,224,195,195,224,222,196,223,198,198,223,225,199,226,197,197,226,224,198,225,200,200,225,227,201,228,202,202,228,229,200,227,203,203,227,230,204,231,205,205,231,232,206,233,207,207,233,234,208,235,209,209,235,236,210,237,211,211,237,238,211,238,212,212,238,239,207,234,213,213,234,240,214,241,210,210,241,237,213,240,215,215,240,242,216,243,214,214,243,241,215,242,217,217,242,244,218,245,216,216,245,243,217,244,219,219,244,246,220,247,218,218,247,245,219,246,221,221,246,248,222,249,220,220,249,247,221,248,223,223,248,250,224,251,222,222,251,249,223,250,225,225,250,252,226,253,224,224,253,251,225,252,227,227,252,254,228,255,229,229,255,256,227,254,230,230,254,257,231,258,232,232,258,259,233,260,234,234,260,261,235,262,236,236,262,263,237,264,238,238,264,265,238,265,239,239,265,266,234,261,240,240,261,267,241,268,237,237,268,264,240,267,242,242,267,269,243,270,241,241,270,268,242,269,244,244,269,271,245,272,243,243,272,270,244,271,246,
246,271,273,247,274,245,245,274,272,246,273,248,248,273,275,249,276,247,247,276,274,248,275,250,250,275,277,251,278,249,249,278,276,250,277,252,252,277,279,253,280,251,251,280,278,252,279,254,254,279,281,255,282,256,256,282,283,254,281,257,257,281,284,258,285,259,259,285,286,260,287,261,261,287,288,262,289,263,263,289,290,264,291,265,265,291,292,265,292,266,266,292,293,261,288,267,267,288,294,268,295,264,264,295,291,267,294,269,269,294,296,270,297,268,268,297,295,269,296,271,271,296,298,272,299,270,270,299,297,271,298,273,273,298,300,274,301,272,272,301,299,273,300,275,275,300,302,276,303,274,274,303,301,275,302,277,277,302,304,278,305,276,276,305,303,277,304,279,279,304,306,280,307,278,278,307,305,279,306,281,281,306,308,282,309,283,283,309,310,281,308,284,284,308,311,285,312,286,286,312,313,287,314,288,288,314,315,289,316,290,290,316,317,318,320,319,319,320,321,319,321,322,322,321,323,288,315,294,294,315,324,325,326,318,318,326,320,294,324,296,296,324,327,328,329,325,325,329,326,296,327,298,298,327,330,331,332,328,328,332,329,298,330,300,300,330,333,334,335,331,331,335,332,300,333,302,302,333,336,337,338,334,334,338,335,302,336,304,304,336,339,340,341,337,337,341,338,304,339,306,306,339,342,310,343,340,340,343,341,306,342,308,308,342,344,309,345,310,310,345,343,308,344,311,311,344,346,312,347,313,313,347,348,349,351,350,350,351,352,7,353,26,26,353,354,289,355,316,316,355,356,6,357,7,7,357,353,358,359,349,349,359,351,26,354,34,34,354,360,361,362,358,358,362,359,34,360,42,42,360,363,364,365,361,361,365,362,42,363,50,50,363,366,367,368,364,364,368,365,50,366,58,58,366,369,370,371,367,367,371,368,58,369,66,66,369,372,373,374,370,370,374,371,66,372,74,74,372,375,376,377,373,373,377,374,74,375,82,82,375,378,379,380,376,376,380,377,82,378,90,90,378,381,10,382,11,11,382,383,348,384,313,313,384,385,386,388,387,387,388,389,313,385,286,286,385,390,391,393,392,392,393,394,286,390,259,259,390,395,396,397,391,391,397,393,259,395,232,232,395,398,399,400,396,396,400,397,232,398,205,205,398,401,402,403,399,399,403,400,205,401,178,
178,401,404,405,406,402,402,406,403,178,404,151,151,404,407,408,409,405,405,409,406,151,407,124,124,407,410,411,412,408,408,412,409,124,410,10,10,410,382,387,389,411,411,389,412,93,413,85,85,413,414,351,415,352,352,415,416,353,417,354,354,417,418,355,419,356,356,419,420,357,421,353,353,421,417,359,422,351,351,422,415,354,418,360,360,418,423,362,424,359,359,424,422,360,423,363,363,423,425,365,426,362,362,426,424,363,425,366,366,425,427,368,428,365,365,428,426,366,427,369,369,427,429,371,430,368,368,430,428,369,429,372,372,429,431,374,432,371,371,432,430,372,431,375,375,431,433,377,434,374,374,434,432,375,433,378,378,433,435,380,436,377,377,436,434,378,435,381,381,435,437,382,438,383,383,438,439,384,440,385,385,440,441,388,442,389,389,442,443,385,441,390,390,441,444,393,445,394,394,445,446,390,444,395,395,444,447,397,448,393,393,448,445,395,447,398,398,447,449,400,450,397,397,450,448,398,449,401,401,449,451,403,452,400,400,452,450,401,451,404,404,451,453,406,454,403,403,454,452,404,453,407,407,453,455,409,456,406,406,456,454,407,455,410,410,455,457,412,458,409,409,458,456,410,457,382,382,457,438,389,443,412,412,443,458,413,459,414,414,459,460,415,461,416,416,461,462,417,463,418,418,463,464,419,465,420,420,465,466,421,467,417,417,467,463,422,468,415,415,468,461,418,464,423,423,464,469,424,470,422,422,470,468,423,469,425,425,469,471,426,472,424,424,472,470,425,471,427,427,471,473,428,474,426,426,474,472,427,473,429,429,473,475,430,476,428,428,476,474,429,475,431,431,475,477,432,478,430,430,478,476,431,477,433,433,477,479,434,480,432,432,480,478,433,479,435,435,479,481,436,482,434,434,482,480,435,481,437,437,481,483,438,484,439,439,484,485,440,486,441,441,486,487,442,488,443,443,488,489,441,487,444,444,487,490,445,491,446,446,491,492,444,490,447,447,490,493,448,494,445,445,494,491,447,493,449,449,493,495,450,496,448,448,496,494,449,495,451,451,495,497,452,498,450,450,498,496,451,497,453,453,497,499,454,500,452,452,500,498,453,499,455,455,499,501,456,502,454,454,502,500,455,501,457,457,501,503,458,504,456,456,504,502,457,
503,438,438,503,484,443,489,458,458,489,504,459,505,460,460,505,506
			} 
		}
		LayerElementUV: 1 {
			Version: 101
			Name: "UVSet1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *1014 {
				a: 0.363608628511429,0.363610059022903,0.399169474840164,0.363610059022903,0.363608628511429,0.399170935153961,0.399169474840164,0.399170935153961,0.363609701395035,0.00400000018998981,0.399170637130737,0.00400005048140883,0.36360964179039,0.0395608693361282,0.39917054772377,0.0395609475672245,0.683657228946686,0.150243893265724,0.719218075275421,0.150243788957596,0.683657288551331,0.185804694890976,0.719218134880066,0.185804605484009,0.324047684669495,0.723218619823456,0.359608620405197,0.723218619823456,0.324047684669495,0.758779466152191,0.359608620405197,0.758779466152191,0.683655917644501,0.509853422641754,0.68365603685379,0.5454141497612,0.648095190525055,0.509853541851044,0.648095309734344,0.54541426897049,0.00400007981806993,0.0395612344145775,0.0395609438419342,0.0395611859858036,0.00400000018998981,0.00400033313781023,0.0395609252154827,0.00400031264871359,0.0395609848201275,0.0751220658421516,0.0040001580491662,0.0751221030950546,0.43473145365715,0.0395610108971596,0.434731483459473,0.00400013104081154,0.288486838340759,0.723218619823456,0.288486838340759,0.758779466152191,0.612534463405609,0.509853601455688,0.612534523010254,0.545414328575134,0.0395610220730305,0.110682904720306,0.00400023208931088,0.110682934522629,0.470292299985886,0.0395610667765141,0.470292329788208,0.00400018831714988,0.252925962209702,0.723218619823456,0.252925962209702,0.758779466152191,0.576973736286163,0.509853661060333,0.576973795890808,0.545414388179779,0.039561040699482,0.146243691444397,0.00400028610602021,0.146243706345558,0.505853116512299,0.0395610928535461,0.505853176116943,0.0040002460591495,0.217365175485611,0.723218619823456,0.217365175485611,0.758779466152191,0.541413009166718,0.509853720664978,0.541413009166718,0.545414447784424,0.0395610481500626,0.181804478168488,0.0040003196336329,0.181804463267326,0.541413962841034,0.0395611003041267,0.541413962841034,0.00400026841089129,0.181804373860359,0.723218619823456,0.181804373860359,0.758779466152191,0.505852282047272,0.509853720664978,0.505852282047272,
0.545414447784424,0.0395610257983208,0.217365220189095,0.00400032009929419,0.217365175485611,0.576974809169769,0.0395610891282558,0.576974749565125,0.0040002609603107,0.146243512630463,0.723218619823456,0.146243512630463,0.758779466152191,0.470291495323181,0.509853661060333,0.470291495323181,0.545414447784424,0.0395609810948372,0.252925962209702,0.00400028750300407,0.252925872802734,0.612535655498505,0.0395610518753529,0.612535536289215,0.00400022370740771,0.110682643949986,0.723218619823456,0.110682643949986,0.758779466152191,0.434730678796768,0.509853601455688,0.434730648994446,0.545414388179779,0.0395609103143215,0.288486659526825,0.00400022091343999,0.288486570119858,0.648096442222595,0.0395609922707081,0.64809638261795,0.00400018086656928,0.0751217752695084,0.723218619823456,0.0751217752695084,0.758779466152191,0.399169832468033,0.509853541851044,0.399169772863388,0.545414388179779,0.0395608060061932,0.324047386646271,0.0040001249872148,0.324047297239304,0.683657288551331,0.039560928940773,0.683657228946686,0.00400012219324708,0.0395608991384506,0.723218619823456,0.0395608991384506,0.758779466152191,0.363608926534653,0.509853422641754,0.36360889673233,0.545414328575134,0.0395606979727745,0.359608113765717,0.00400000018998981,0.359607994556427,0.719218134880066,0.0395608432590961,0.719218015670776,0.00400002161040902,0.00400001974776387,0.723218619823456,0.004000015091151,0.758779466152191,0.0395609587430954,0.363610059022903,0.0395609550178051,0.399170964956284,0.00400006398558617,0.363610059022903,0.00400005979463458,0.399170964956284,0.0751217678189278,0.00400026328861713,0.0751217976212502,0.0395611263811588,0.4347303211689,0.399170935153961,0.4347303211689,0.363610059022903,0.612534582614899,0.58097505569458,0.648095369338989,0.580974996089935,0.683656096458435,0.580974876880646,0.0751218199729919,0.0751220211386681,0.576973855495453,0.58097517490387,0.0751218423247337,0.110682874917984,0.541413068771362,0.580975234508514,0.0751218423247337,0.146243676543236,0.505852282047272,0.580975234508514,0.0751218125224113,
0.181804478168488,0.470291465520859,0.580975294113159,0.0751217678189278,0.217365264892578,0.434730619192123,0.580975234508514,0.0751216933131218,0.252926021814346,0.399169713258743,0.580975234508514,0.0751216113567352,0.288486778736115,0.363608837127686,0.58097517490387,0.0751215070486069,0.324047476053238,0.00400005374103785,0.434731781482697,0.0395609550178051,0.434731781482697,0.0751213803887367,0.359608203172684,0.648096442222595,0.150243952870369,0.64809650182724,0.185804754495621,0.110682621598244,0.00400021066889167,0.110682658851147,0.0395610891282558,0.470291197299957,0.399170935153961,0.470291197299957,0.363610059022903,0.612534701824188,0.616535842418671,0.648095488548279,0.616535723209381,0.683656215667725,0.616535604000092,0.110682666301727,0.0751219913363457,0.576973915100098,0.61653596162796,0.110682666301727,0.110682867467403,0.541413128376007,0.616536021232605,0.110682658851147,0.146243691444397,0.505852282047272,0.61653608083725,0.110682606697083,0.18180450797081,0.470291465520859,0.61653608083725,0.110682539641857,0.217365324497223,0.434730559587479,0.61653608083725,0.110682442784309,0.252926111221313,0.399169683456421,0.61653608083725,0.110682338476181,0.288486868143082,0.363608777523041,0.616536021232605,0.110682211816311,0.324047595262527,0.0040000481531024,0.470292627811432,0.0395609512925148,0.470292627811432,0.110682055354118,0.359608322381973,0.61253559589386,0.150243997573853,0.612535655498505,0.185804814100266,0.14624348282814,0.00400014547631145,0.146243512630463,0.0395610630512238,0.505852043628693,0.399170935153961,0.505852043628693,0.363610059022903,0.612534821033478,0.652096629142761,0.648095607757568,0.652096509933472,0.683656394481659,0.652096331119537,0.146243527531624,0.0751219764351845,0.576973974704742,0.652096748352051,0.146243512630463,0.110682882368565,0.541413187980652,0.652096807956696,0.14624348282814,0.146243721246719,0.505852341651917,0.652096927165985,0.146243423223495,0.181804567575455,0.470291465520859,0.652096927165985,0.146243333816528,0.217365399003029,0.434730559587479,
0.65209698677063,0.146243214607239,0.252926200628281,0.399169653654099,0.65209698677063,0.146243095397949,0.288486987352371,0.363608717918396,0.652096927165985,0.146242931485176,0.324047744274139,0.00400004303082824,0.505853474140167,0.0395609512925148,0.505853474140167,0.146242752671242,0.359608501195908,0.576974749565125,0.150244012475014,0.576974809169769,0.185804843902588,0.181804373860359,0.0040000919252634,0.18180438876152,0.039561040699482,0.541412830352783,0.399170935153961,0.541412830352783,0.363610059022903,0.612534940242767,0.687657415866852,0.648095726966858,0.687657237052917,0.683656573295593,0.687657058238983,0.18180438876152,0.0751219764351845,0.576974093914032,0.687657594680786,0.181804373860359,0.110682897269726,0.541413247585297,0.687657654285431,0.181804329156876,0.146243765950203,0.505852341651917,0.68765777349472,0.18180425465107,0.181804627180099,0.470291495323181,0.687657833099365,0.181804150342941,0.217365488409996,0.434730559587479,0.687657833099365,0.181804016232491,0.25292631983757,0.399169623851776,0.687657833099365,0.181803852319717,0.288487136363983,0.363608658313751,0.687657833099365,0.181803673505783,0.324047923088074,0.0040000369772315,0.541414320468903,0.0395609475672245,0.541414320468903,0.181803464889526,0.359608709812164,0.541413962841034,0.150244042277336,0.541413962841034,0.18580487370491,0.217365294694901,0.00400004722177982,0.217365309596062,0.0395610295236111,0.576973736286163,0.399170935153961,0.576973736286163,0.363610059022903,0.612535059452057,0.723218262195587,0.648095905780792,0.723218083381653,0.683656752109528,0.723217844963074,0.217365294694901,0.0751219764351845,0.576974213123322,0.723218441009521,0.217365264892578,0.110682919621468,0.541413307189941,0.723218560218811,0.217365205287933,0.146243825554848,0.505852401256561,0.723218619823456,0.217365100979805,0.181804716587067,0.470291525125504,0.723218679428101,0.217364981770515,0.217365592718124,0.434730589389801,0.723218739032745,0.217364847660065,0.25292643904686,0.399169623851776,0.72321879863739,0.21736466884613,
0.288487315177917,0.363608628511429,0.72321879863739,0.217364460229874,0.32404813170433,0.00400002999231219,0.576975166797638,0.0395609401166439,0.576975166797638,0.217364221811295,0.359608948230743,0.505853176116943,0.150244027376175,0.505853176116943,0.185804858803749,0.252926260232925,0.00400001322850585,0.252926260232925,0.0395610295236111,0.612534582614899,0.399170935153961,0.612534582614899,0.363610088825226,0.612535238265991,0.758779108524323,0.648096144199371,0.758778929710388,0.683656990528107,0.758778691291809,0.252926230430603,0.0751220062375069,0.576974332332611,0.758779287338257,0.252926170825958,0.110682956874371,0.541413426399231,0.758779406547546,0.252926081418991,0.146243885159492,0.505852520465851,0.758779525756836,0.252925992012024,0.181804805994034,0.470291554927826,0.758779644966125,0.252925843000412,0.217365726828575,0.434730619192123,0.75877970457077,0.2529256939888,0.252926617860794,0.399169653654099,0.758779764175415,0.252925485372543,0.288487493991852,0.363608628511429,0.758779764175415,0.252925276756287,0.324048340320587,0.00400002207607031,0.612536013126373,0.0395609326660633,0.612536013126373,0.252925008535385,0.359609186649323,0.47029235959053,0.150243982672691,0.470292299985886,0.185804843902588,0.288487255573273,0.00400000018998981,0.28848722577095,0.039561040699482,0.648095488548279,0.399170964956284,0.648095488548279,0.363610088825226,0.612535417079926,0.794340014457703,0.648096323013306,0.794339835643768,0.683657228946686,0.794339597225189,0.288487195968628,0.0751220285892487,0.576974451541901,0.794340193271637,0.288487106561661,0.110683016479015,0.541413545608521,0.794340372085571,0.288487017154694,0.14624397456646,0.505852580070496,0.794340491294861,0.288486897945404,0.181804925203323,0.470291644334793,0.794340550899506,0.288486748933792,0.217365860939026,0.434730678796768,0.794340670108795,0.288486570119858,0.252926766872406,0.399169683456421,0.79434072971344,0.288486361503601,0.288487702608109,0.363608628511429,0.794340789318085,0.288486123085022,0.324048578739166,0.00400001415982842,
0.648096859455109,0.0395609214901924,0.648096919059753,0.28848585486412,0.359609484672546,0.434731513261795,0.150243908166885,0.43473145365715,0.185804784297943,0.324048280715942,0.00400001322850585,0.324048221111298,0.0395610742270947,0.683656275272369,0.399170964956284,0.683656275272369,0.363610088825226,0.612535536289215,0.829900920391083,0.648096561431885,0.829900741577148,0.683657467365265,0.829900503158569,0.324048161506653,0.0751220807433128,0.576974630355835,0.829901099205017,0.324048072099686,0.110683090984821,0.54141366481781,0.829901278018951,0.324047982692719,0.146244078874588,0.505852699279785,0.829901397228241,0.324047833681107,0.181805044412613,0.47029173374176,0.829901516437531,0.324047684669495,0.217366009950638,0.434730738401413,0.82990163564682,0.324047476053238,0.25292694568634,0.399169743061066,0.829901695251465,0.324047267436981,0.288487911224365,0.363608688116074,0.82990175485611,0.324047029018402,0.324048846960068,0.00400000670924783,0.683657705783844,0.0395609103143215,0.683657765388489,0.324046760797501,0.35960978269577,0.39917066693306,0.150243788957596,0.399170577526093,0.185804709792137,0.359609365463257,0.00400005653500557,0.35960927605629,0.0395611524581909,0.71921718120575,0.399170964956284,0.71921718120575,0.363610059022903,0.288486868143082,0.683657765388489,0.324047744274139,0.683657765388489,0.288486868143082,0.719218552112579,0.324047744274139,0.719218552112579,0.359608620405197,0.683657765388489,0.359608620405197,0.719218552112579,0.359609156847,0.07512217015028,0.252925992012024,0.683657765388489,0.252925992012024,0.719218611717224,0.359609067440033,0.110683187842369,0.217365175485611,0.683657765388489,0.217365175485611,0.719218611717224,0.359608948230743,0.146244183182716,0.181804358959198,0.683657765388489,0.181804358959198,0.719218611717224,0.359608799219131,0.181805193424225,0.146243527531624,0.683657765388489,0.146243527531624,0.719218611717224,0.359608620405197,0.217366173863411,0.11068270355463,0.683657765388489,0.11068269610405,0.719218611717224,0.359608441591263,0.252927154302597,
0.0751218050718307,0.683657765388489,0.0751217976212502,0.719218611717224,0.359608203172684,0.2884880900383,0.0395609028637409,0.719218552112579,0.359607934951782,0.324049115180969,0.00400000018998981,0.719218552112579,0.359607666730881,0.359610080718994,0.363609731197357,0.150243654847145,0.363609671592712,0.185804635286331,0.288486927747726,0.869462013244629,0.324047803878784,0.869461953639984,0.288486957550049,0.905022859573364,0.324047863483429,0.905022799968719,0.399170458316803,0.0751218125224113,0.434731364250183,0.0751218721270561,0.683656275272369,0.434731781482697,0.71921718120575,0.434731781482697,0.3636095225811,0.0751217380166054,0.252926081418991,0.869462013244629,0.252926051616669,0.905022859573364,0.470292240381241,0.0751219093799591,0.217365220189095,0.869462013244629,0.217365205287933,0.905022859573364,0.505853116512299,0.0751219317317009,0.181804373860359,0.869462013244629,0.181804344058037,0.905022859573364,0.541413962841034,0.0751219317317009,0.146243527531624,0.869462013244629,0.146243497729301,0.905022859573364,0.576974809169769,0.0751219093799591,0.110682681202888,0.869462013244629,0.110682658851147,0.905022859573364,0.612535655498505,0.0751218721270561,0.0751218274235725,0.869462013244629,0.0751217976212502,0.905022859573364,0.64809650182724,0.0751218125224113,0.0395609587430954,0.869461953639984,0.0395609177649021,0.905022859573364,0.683657348155975,0.0751217305660248,0.00400001695379615,0.869461953639984,0.00400000018998981,0.905022859573364,0.719218194484711,0.0751216411590576,0.68365740776062,0.221365511417389,0.719218194484711,0.221365422010422,0.363609552383423,0.221365511417389,0.399170488119125,0.221365600824356,0.363608658313751,0.833901762962341,0.399169534444809,0.833901822566986,0.363608628511429,0.869462668895721,0.399169504642487,0.869462668895721,0.434731364250183,0.22136564552784,0.648095548152924,0.833901822566986,0.683656454086304,0.833901762962341,0.648095607757568,0.869462668895721,0.683656454086304,0.869462668895721,0.470292270183563,0.221365690231323,0.612534701824188,
0.833901822566986,0.612534701824188,0.869462668895721,0.505853116512299,0.221365705132484,0.576973855495453,0.833901822566986,0.576973855495453,0.869462668895721,0.541413962841034,0.221365705132484,0.541412949562073,0.833901822566986,0.541412949562073,0.869462668895721,0.576974809169769,0.221365675330162,0.505852103233337,0.833901822566986,0.505852103233337,0.869462668895721,0.612535715103149,0.221365630626678,0.470291286706924,0.833901822566986,0.470291256904602,0.869462668895721,0.648096561431885,0.221365585923195,0.434730410575867,0.833901822566986,0.434730410575867,0.869462668895721,0.00400000903755426,0.794340312480927,0.0395608954131603,0.794340312480927,0.288486927747726,0.940583765506744,0.324047863483429,0.940583765506744,0.399170398712158,0.110682718455791,0.434731304645538,0.110682755708694,0.683656275272369,0.470292627811432,0.71921718120575,0.470292627811432,0.363609433174133,0.110682658851147,0.252926051616669,0.9405837059021,0.470292210578918,0.110682778060436,0.217365190386772,0.9405837059021,0.505853056907654,0.110682785511017,0.181804344058037,0.9405837059021,0.541413962841034,0.110682778060436,0.146243497729301,0.9405837059021,0.576974809169769,0.110682755708694,0.110682643949986,0.9405837059021,0.612535715103149,0.110682711005211,0.0751217901706696,0.9405837059021,0.648096561431885,0.110682658851147,0.0395609177649021,0.9405837059021,0.683657467365265,0.110682591795921,0.00400001695379615,0.940583765506744,0.719218313694,0.110682502388954,0.683657467365265,0.256926327943802,0.719218254089355,0.256926238536835,0.363609433174133,0.256926447153091,0.399170398712158,0.256926506757736,0.363608658313751,0.905023574829102,0.399169504642487,0.905023574829102,0.434731334447861,0.256926536560059,0.648095548152924,0.905023574829102,0.683656454086304,0.905023574829102,0.470292240381241,0.256926536560059,0.612534642219543,0.905023515224457,0.505853116512299,0.256926566362381,0.576973795890808,0.905023515224457,0.541413962841034,0.256926536560059,0.541412949562073,0.905023515224457,0.576974868774414,0.256926506757736,
0.505852103233337,0.905023515224457,0.612535715103149,0.256926476955414,0.470291256904602,0.905023515224457,0.648096561431885,0.256926417350769,0.434730380773544,0.905023515224457,0.0040000039152801,0.829901158809662,0.03956089168787,0.829901158809662,0.288486868143082,0.97614461183548,0.324047803878784,0.976144671440125,0.399170368909836,0.146243616938591,0.434731274843216,0.146243631839752,0.683656275272369,0.505853414535522,0.71921718120575,0.505853414535522,0.363609373569489,0.146243587136269,0.252925992012024,0.976144552230835,0.470292180776596,0.146243661642075,0.217365145683289,0.976144552230835,0.505853056907654,0.146243661642075,0.181804314255714,0.97614449262619,0.541413962841034,0.146243646740913,0.14624348282814,0.97614449262619,0.576974868774414,0.14624360203743,0.110682636499405,0.97614449262619,0.612535715103149,0.146243557333946,0.0751217976212502,0.976144552230835,0.64809662103653,0.146243497729301,0.0395609475672245,0.976144552230835,0.68365752696991,0.146243408322334,0.00400009891018271,0.97614461183548,0.719218373298645,0.146243318915367,0.68365752696991,0.292487174272537,0.719218373298645,0.29248708486557,0.363609373569489,0.292487382888794,0.399170398712158,0.292487412691116,0.363608747720718,0.940584480762482,0.399169564247131,0.940584421157837,0.434731304645538,0.292487412691116,0.648095488548279,0.940584421157837,0.683656454086304,0.940584480762482,0.470292210578918,0.292487442493439,0.612534642219543,0.940584361553192,0.505853116512299,0.292487442493439,0.576973736286163,0.940584361553192,0.541413962841034,0.292487412691116,0.541412949562073,0.940584301948547,0.576974868774414,0.292487382888794,0.505852103233337,0.940584301948547,0.612535774707794,0.292487323284149,0.470291256904602,0.940584301948547,0.64809662103653,0.292487263679504,0.434730410575867,0.940584361553192,0.00400000018998981,0.865461945533752,0.0395608879625797,0.865461945533752
			} 
			UVIndex: *2160 {
				a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
141,168,139,139,168,166,140,167,142,142,167,169,143,170,141,141,170,168,142,169,144,144,169,171,145,172,143,143,172,170,144,171,146,146,171,173,147,174,148,148,174,175,146,173,149,149,173,176,150,177,151,151,177,178,152,179,153,153,179,180,154,181,155,155,181,182,156,183,157,157,183,184,157,184,158,158,184,185,153,180,159,159,180,186,160,187,156,156,187,183,159,186,161,161,186,188,162,189,160,160,189,187,161,188,163,163,188,190,164,191,162,162,191,189,163,190,165,165,190,192,166,193,164,164,193,191,165,192,167,167,192,194,168,195,166,166,195,193,167,194,169,169,194,196,170,197,168,168,197,195,169,196,171,171,196,198,172,199,170,170,199,197,171,198,173,173,198,200,174,201,175,175,201,202,173,200,176,176,200,203,177,204,178,178,204,205,179,206,180,180,206,207,181,208,182,182,208,209,183,210,184,184,210,211,184,211,185,185,211,212,180,207,186,186,207,213,187,214,183,183,214,210,186,213,188,188,213,215,189,216,187,187,216,214,188,215,190,190,215,217,191,218,189,189,218,216,190,217,192,192,217,219,193,220,191,191,220,218,192,219,194,194,219,221,195,222,193,193,222,220,194,221,196,196,221,223,197,224,195,195,224,222,196,223,198,198,223,225,199,226,197,197,226,224,198,225,200,200,225,227,201,228,202,202,228,229,200,227,203,203,227,230,204,231,205,205,231,232,206,233,207,207,233,234,208,235,209,209,235,236,210,237,211,211,237,238,211,238,212,212,238,239,207,234,213,213,234,240,214,241,210,210,241,237,213,240,215,215,240,242,216,243,214,214,243,241,215,242,217,217,242,244,218,245,216,216,245,243,217,244,219,219,244,246,220,247,218,218,247,245,219,246,221,221,246,248,222,249,220,220,249,247,221,248,223,223,248,250,224,251,222,222,251,249,223,250,225,225,250,252,226,253,224,224,253,251,225,252,227,227,252,254,228,255,229,229,255,256,227,254,230,230,254,257,231,258,232,232,258,259,233,260,234,234,260,261,235,262,236,236,262,263,237,264,238,238,264,265,238,265,239,239,265,266,234,261,240,240,261,267,241,268,237,237,268,264,240,267,242,242,267,269,243,270,241,241,270,268,242,269,244,244,269,271,245,272,243,243,272,270,244,271,246,
246,271,273,247,274,245,245,274,272,246,273,248,248,273,275,249,276,247,247,276,274,248,275,250,250,275,277,251,278,249,249,278,276,250,277,252,252,277,279,253,280,251,251,280,278,252,279,254,254,279,281,255,282,256,256,282,283,254,281,257,257,281,284,258,285,259,259,285,286,260,287,261,261,287,288,262,289,263,263,289,290,264,291,265,265,291,292,265,292,266,266,292,293,261,288,267,267,288,294,268,295,264,264,295,291,267,294,269,269,294,296,270,297,268,268,297,295,269,296,271,271,296,298,272,299,270,270,299,297,271,298,273,273,298,300,274,301,272,272,301,299,273,300,275,275,300,302,276,303,274,274,303,301,275,302,277,277,302,304,278,305,276,276,305,303,277,304,279,279,304,306,280,307,278,278,307,305,279,306,281,281,306,308,282,309,283,283,309,310,281,308,284,284,308,311,285,312,286,286,312,313,287,314,288,288,314,315,289,316,290,290,316,317,318,320,319,319,320,321,319,321,322,322,321,323,288,315,294,294,315,324,325,326,318,318,326,320,294,324,296,296,324,327,328,329,325,325,329,326,296,327,298,298,327,330,331,332,328,328,332,329,298,330,300,300,330,333,334,335,331,331,335,332,300,333,302,302,333,336,337,338,334,334,338,335,302,336,304,304,336,339,340,341,337,337,341,338,304,339,306,306,339,342,310,343,340,340,343,341,306,342,308,308,342,344,309,345,310,310,345,343,308,344,311,311,344,346,312,347,313,313,347,348,349,351,350,350,351,352,7,353,26,26,353,354,289,355,316,316,355,356,6,357,7,7,357,353,358,359,349,349,359,351,26,354,34,34,354,360,361,362,358,358,362,359,34,360,42,42,360,363,364,365,361,361,365,362,42,363,50,50,363,366,367,368,364,364,368,365,50,366,58,58,366,369,370,371,367,367,371,368,58,369,66,66,369,372,373,374,370,370,374,371,66,372,74,74,372,375,376,377,373,373,377,374,74,375,82,82,375,378,379,380,376,376,380,377,82,378,90,90,378,381,10,382,11,11,382,383,348,384,313,313,384,385,386,388,387,387,388,389,313,385,286,286,385,390,391,393,392,392,393,394,286,390,259,259,390,395,396,397,391,391,397,393,259,395,232,232,395,398,399,400,396,396,400,397,232,398,205,205,398,401,402,403,399,399,403,400,205,401,178,
178,401,404,405,406,402,402,406,403,178,404,151,151,404,407,408,409,405,405,409,406,151,407,124,124,407,410,411,412,408,408,412,409,124,410,10,10,410,382,387,389,411,411,389,412,93,413,85,85,413,414,351,415,352,352,415,416,353,417,354,354,417,418,355,419,356,356,419,420,357,421,353,353,421,417,359,422,351,351,422,415,354,418,360,360,418,423,362,424,359,359,424,422,360,423,363,363,423,425,365,426,362,362,426,424,363,425,366,366,425,427,368,428,365,365,428,426,366,427,369,369,427,429,371,430,368,368,430,428,369,429,372,372,429,431,374,432,371,371,432,430,372,431,375,375,431,433,377,434,374,374,434,432,375,433,378,378,433,435,380,436,377,377,436,434,378,435,381,381,435,437,382,438,383,383,438,439,384,440,385,385,440,441,388,442,389,389,442,443,385,441,390,390,441,444,393,445,394,394,445,446,390,444,395,395,444,447,397,448,393,393,448,445,395,447,398,398,447,449,400,450,397,397,450,448,398,449,401,401,449,451,403,452,400,400,452,450,401,451,404,404,451,453,406,454,403,403,454,452,404,453,407,407,453,455,409,456,406,406,456,454,407,455,410,410,455,457,412,458,409,409,458,456,410,457,382,382,457,438,389,443,412,412,443,458,413,459,414,414,459,460,415,461,416,416,461,462,417,463,418,418,463,464,419,465,420,420,465,466,421,467,417,417,467,463,422,468,415,415,468,461,418,464,423,423,464,469,424,470,422,422,470,468,423,469,425,425,469,471,426,472,424,424,472,470,425,471,427,427,471,473,428,474,426,426,474,472,427,473,429,429,473,475,430,476,428,428,476,474,429,475,431,431,475,477,432,478,430,430,478,476,431,477,433,433,477,479,434,480,432,432,480,478,433,479,435,435,479,481,436,482,434,434,482,480,435,481,437,437,481,483,438,484,439,439,484,485,440,486,441,441,486,487,442,488,443,443,488,489,441,487,444,444,487,490,445,491,446,446,491,492,444,490,447,447,490,493,448,494,445,445,494,491,447,493,449,449,493,495,450,496,448,448,496,494,449,495,451,451,495,497,452,498,450,450,498,496,451,497,453,453,497,499,454,500,452,452,500,498,453,499,455,455,499,501,456,502,454,454,502,500,455,501,457,457,501,503,458,504,456,456,504,502,457,
503,438,438,503,484,443,489,458,458,489,504,459,505,460,460,505,506
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementColor"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
		Layer: 1 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 1
			}
		}
	}
	Geometry: 1143700784, "Geometry::Scene", "Mesh" {
		Vertices: *1086 {
			a: -0,0,0,-100,0,0,-0,100,0,-100,100,0,-1000,0,0,-1000,0,-100,-1000,100,0,-1000,100,-100,-100,0,-1000,-0,0,-1000,-100,100,-1000,-0,100,-1000,-0,0,-100,-0,100,-100,-100,100,-100,-100,0,-100,-100,0,-200,-0,0,-200,-1000,100,-200,-1000,0,-200,-0,100,-200,-100,100,-200,-100,0,-300,-0,0,-300,-1000,100,-300,-1000,0,-300,-0,100,-300,-100,100,-300,-100,0,-400,-0,0,-400,-1000,100,-400,-1000,0,-400,-0,100,-400,-100,100,-400,-100,0,-500,-0,0,-500,-1000,100,-500,-1000,0,-500,-0,100,-500,-100,100,-500,-100,0,-600,-0,0,-600,-1000,100,-600,-1000,0,-600,-0,100,-600,-100,100,-600,-100,0,-700,-0,0,-700,-1000,100,-700,-1000,0,-700,-0,100,-700,-100,100,-700,-100,0,-800,-0,0,-800,-1000,100,-800,-1000,0,-800,-0,100,-800,-100,100,-800,-100,0,-900,-0,0,-900,-1000,100,-900,-1000,0,-900,-0,100,-900,-100,100,-900,-1000,100,-1000,-1000,0,-1000,-0,400,-900,-100,400,-900,-0,400,-1000,-100,400,-1000,-200,0,0,-200,0,-100,-200,100,0,-200,100,-200,-200,100,-100,-200,0,-200,-200,100,-300,-200,0,-300,-200,100,-400,-200,0,-400,-200,100,-500,-200,0,-500,-200,100,-600,-200,0,-600,-200,100,-700,-200,0,-700,-200,100,-800,-200,0,-800,-200,100,-900,-200,0,-900,-200,400,-1000,-200,400,-900,-200,0,-1000,-200,100,-1000,-300,0,0,-300,0,-100,-300,100,0,-300,100,-200,-300,100,-100,-300,0,-200,-300,100,-300,-300,0,-300,-300,100,-400,-300,0,-400,-300,100,-500,-300,0,-500,-300,100,-600,-300,0,-600,-300,100,-700,-300,0,-700,-300,100,-800,-300,0,-800,-300,100,-900,-300,0,-900,-300,400,-1000,-300,400,-900,-300,0,-1000,-300,100,-1000,-400,0,0,-400,0,-100,-400,100,0,-400,100,-200,-400,100,-100,-400,0,-200,-400,100,-300,-400,0,-300,-400,100,-400,-400,0,-400,-400,100,-500,-400,0,-500,-400,100,-600,-400,0,-600,-400,100,-700,-400,0,-700,-400,100,-800,-400,0,-800,-400,100,-900,-400,0,-900,-400,400,-1000,-400,400,-900,-400,0,-1000,-400,100,-1000,-500,0,0,-500,0,-100,-500,100,0,-500,100,-200,-500,100,-100,-500,0,-200,-500,100,-300,-500,0,-300,-500,100,-400,-500,0,-400,-500,100,-500,-500,0,-500,-500,100,-600,-500,0,-600,-500,100,-700,-500,0,-700,-500,100,-800,-500,0,-800,
-500,100,-900,-500,0,-900,-500,400,-1000,-500,400,-900,-500,0,-1000,-500,100,-1000,-600,0,0,-600,0,-100,-600,100,0,-600,100,-200,-600,100,-100,-600,0,-200,-600,100,-300,-600,0,-300,-600,100,-400,-600,0,-400,-600,100,-500,-600,0,-500,-600,100,-600,-600,0,-600,-600,100,-700,-600,0,-700,-600,100,-800,-600,0,-800,-600,100,-900,-600,0,-900,-600,400,-1000,-600,400,-900,-600,0,-1000,-600,100,-1000,-700,0,0,-700,0,-100,-700,100,0,-700,100,-200,-700,100,-100,-700,0,-200,-700,100,-300,-700,0,-300,-700,100,-400,-700,0,-400,-700,100,-500,-700,0,-500,-700,100,-600,-700,0,-600,-700,100,-700,-700,0,-700,-700,100,-800,-700,0,-800,-700,100,-900,-700,0,-900,-700,400,-1000,-700,400,-900,-700,0,-1000,-700,100,-1000,-800,0,0,-800,0,-100,-800,100,0,-800,100,-200,-800,100,-100,-800,0,-200,-800,100,-300,-800,0,-300,-800,100,-400,-800,0,-400,-800,100,-500,-800,0,-500,-800,100,-600,-800,0,-600,-800,100,-700,-800,0,-700,-800,100,-800,-800,0,-800,-800,100,-900,-800,0,-900,-800,400,-1000,-800,400,-900,-800,0,-1000,-800,100,-1000,-900,0,0,-900,0,-100,-900,100,0,-900,100,-200,-900,100,-100,-900,0,-200,-900,100,-300,-900,0,-300,-900,100,-400,-900,0,-400,-900,100,-500,-900,0,-500,-900,100,-600,-900,0,-600,-900,100,-700,-900,0,-700,-900,100,-800,-900,0,-800,-900,100,-900,-900,0,-900,-900,400,-1000,-900,400,-900,-900,0,-1000,-900,100,-1000,-900,400,-200,-900,400,-100,-1000,400,-200,-1000,400,-100,-900,400,0,-1000,400,0,-900,400,-300,-1000,400,-300,-900,400,-400,-1000,400,-400,-900,400,-500,-1000,400,-500,-900,400,-600,-1000,400,-600,-900,400,-700,-1000,400,-700,-900,400,-800,-1000,400,-800,-1000,400,-900,-1000,400,-1000,-900,200,-100,-900,200,0,-1000,200,-100,-1000,200,-200,-1000,200,0,-900,200,-200,-1000,200,-300,-900,200,-300,-1000,200,-400,-900,200,-400,-1000,200,-500,-900,200,-500,-1000,200,-600,-900,200,-600,-1000,200,-700,-900,200,-700,-1000,200,-800,-900,200,-800,-1000,200,-900,-900,200,-900,-1000,200,-1000,-100,200,-1000,-0,200,-1000,-900,200,-1000,-0,200,-900,-100,200,-900,-800,200,-1000,-800,200,-900,-700,200,-1000,-700,200,-900,-600,200,-1000,
-600,200,-900,-500,200,-1000,-500,200,-900,-400,200,-1000,-400,200,-900,-300,200,-1000,-300,200,-900,-200,200,-1000,-200,200,-900,-900,300,-100,-900,300,0,-1000,300,-100,-1000,300,-200,-1000,300,0,-900,300,-200,-1000,300,-300,-900,300,-300,-1000,300,-400,-900,300,-400,-1000,300,-500,-900,300,-500,-1000,300,-600,-900,300,-600,-1000,300,-700,-900,300,-700,-1000,300,-800,-900,300,-800,-1000,300,-900,-900,300,-900,-1000,300,-1000,-100,300,-1000,-0,300,-1000,-900,300,-1000,-0,300,-900,-100,300,-900,-800,300,-1000,-800,300,-900,-700,300,-1000,-700,300,-900,-600,300,-1000,-600,300,-900,-500,300,-1000,-500,300,-900,-400,300,-1000,-400,300,-900,-300,300,-1000,-300,300,-900,-200,300,-1000,-200,300,-900
		} 
		PolygonVertexIndex: *2160 {
			a: 0,2,-2,1,2,-4,4,6,-6,5,6,-8,8,10,-10,9,10,-12,12,13,-1,0,13,-3,2,13,-4,3,13,-15,12,0,-16,15,0,-2,15,16,-13,12,16,-18,7,18,-6,5,18,-20,12,17,-14,13,17,-21,13,20,-15,14,20,-22,16,22,-18,17,22,-24,18,24,-20,19,24,-26,17,23,-21,20,23,-27,20,26,-22,21,26,-28,22,28,-24,23,28,-30,24,30,-26,25,30,-32,23,29,-27,26,29,-33,26,32,-28,27,32,-34,28,34,-30,29,34,-36,30,36,-32,31,36,-38,29,35,-33,32,35,-39,32,38,-34,33,38,-40,34,40,-36,35,40,-42,36,42,-38,37,42,-44,35,41,-39,38,41,-45,38,44,-40,39,44,-46,40,46,-42,41,46,-48,42,48,-44,43,48,-50,41,47,-45,44,47,-51,44,50,-46,45,50,-52,46,52,-48,47,52,-54,48,54,-50,49,54,-56,47,53,-51,50,53,-57,50,56,-52,51,56,-58,52,58,-54,53,58,-60,54,60,-56,55,60,-62,53,59,-57,56,59,-63,56,62,-58,57,62,-64,58,8,-60,59,8,-10,60,64,-62,61,64,-66,59,9,-63,62,9,-12,66,68,-68,67,68,-70,1,70,-16,15,70,-72,3,72,-2,1,72,-71,21,73,-15,14,73,-75,14,74,-4,3,74,-73,15,71,-17,16,71,-76,27,76,-22,21,76,-74,16,75,-23,22,75,-78,33,78,-28,27,78,-77,22,77,-29,28,77,-80,39,80,-34,33,80,-79,28,79,-35,34,79,-82,45,82,-40,39,82,-81,34,81,-41,40,81,-84,51,84,-46,45,84,-83,40,83,-47,46,83,-86,57,86,-52,51,86,-85,46,85,-53,52,85,-88,63,88,-58,57,88,-87,52,87,-59,58,87,-90,69,90,-68,67,90,-92,58,89,-9,8,89,-93,8,92,-11,10,92,-94,70,94,-72,71,94,-96,72,96,-71,70,96,-95,73,97,-75,74,97,-99,74,98,-73,72,98,-97,71,95,-76,75,95,-100,76,100,-74,73,100,-98,75,99,-78,77,99,-102,78,102,-77,76,102,-101,77,101,-80,79,101,-104,80,104,-79,78,104,-103,79,103,-82,81,103,-106,82,106,-81,80,106,-105,81,105,-84,83,105,-108,84,108,-83,82,108,-107,83,107,-86,85,107,-110,86,110,-85,84,110,-109,85,109,-88,87,109,-112,88,112,-87,86,112,-111,87,111,-90,89,111,-114,90,114,-92,91,114,-116,89,113,-93,92,113,-117,92,116,-94,93,116,-118,94,118,-96,95,118,-120,96,120,-95,94,120,-119,97,121,-99,98,121,-123,98,122,-97,96,122,-121,95,119,-100,99,119,-124,100,124,-98,97,124,-122,99,123,-102,101,123,-126,102,126,-101,100,126,-125,101,125,-104,103,125,-128,104,128,-103,102,128,-127,103,127,-106,105,127,-130,106,130,-105,104,130,-129,105,129,
-108,107,129,-132,108,132,-107,106,132,-131,107,131,-110,109,131,-134,110,134,-109,108,134,-133,109,133,-112,111,133,-136,112,136,-111,110,136,-135,111,135,-114,113,135,-138,114,138,-116,115,138,-140,113,137,-117,116,137,-141,116,140,-118,117,140,-142,118,142,-120,119,142,-144,120,144,-119,118,144,-143,121,145,-123,122,145,-147,122,146,-121,120,146,-145,119,143,-124,123,143,-148,124,148,-122,121,148,-146,123,147,-126,125,147,-150,126,150,-125,124,150,-149,125,149,-128,127,149,-152,128,152,-127,126,152,-151,127,151,-130,129,151,-154,130,154,-129,128,154,-153,129,153,-132,131,153,-156,132,156,-131,130,156,-155,131,155,-134,133,155,-158,134,158,-133,132,158,-157,133,157,-136,135,157,-160,136,160,-135,134,160,-159,135,159,-138,137,159,-162,138,162,-140,139,162,-164,137,161,-141,140,161,-165,140,164,-142,141,164,-166,142,166,-144,143,166,-168,144,168,-143,142,168,-167,145,169,-147,146,169,-171,146,170,-145,144,170,-169,143,167,-148,147,167,-172,148,172,-146,145,172,-170,147,171,-150,149,171,-174,150,174,-149,148,174,-173,149,173,-152,151,173,-176,152,176,-151,150,176,-175,151,175,-154,153,175,-178,154,178,-153,152,178,-177,153,177,-156,155,177,-180,156,180,-155,154,180,-179,155,179,-158,157,179,-182,158,182,-157,156,182,-181,157,181,-160,159,181,-184,160,184,-159,158,184,-183,159,183,-162,161,183,-186,162,186,-164,163,186,-188,161,185,-165,164,185,-189,164,188,-166,165,188,-190,166,190,-168,167,190,-192,168,192,-167,166,192,-191,169,193,-171,170,193,-195,170,194,-169,168,194,-193,167,191,-172,171,191,-196,172,196,-170,169,196,-194,171,195,-174,173,195,-198,174,198,-173,172,198,-197,173,197,-176,175,197,-200,176,200,-175,174,200,-199,175,199,-178,177,199,-202,178,202,-177,176,202,-201,177,201,-180,179,201,-204,180,204,-179,178,204,-203,179,203,-182,181,203,-206,182,206,-181,180,206,-205,181,205,-184,183,205,-208,184,208,-183,182,208,-207,183,207,-186,185,207,-210,186,210,-188,187,210,-212,185,209,-189,188,209,-213,188,212,-190,189,212,-214,190,214,-192,191,214,-216,192,216,-191,190,216,-215,193,217,-195,194,217,-219,194,
218,-193,192,218,-217,191,215,-196,195,215,-220,196,220,-194,193,220,-218,195,219,-198,197,219,-222,198,222,-197,196,222,-221,197,221,-200,199,221,-224,200,224,-199,198,224,-223,199,223,-202,201,223,-226,202,226,-201,200,226,-225,201,225,-204,203,225,-228,204,228,-203,202,228,-227,203,227,-206,205,227,-230,206,230,-205,204,230,-229,205,229,-208,207,229,-232,208,232,-207,206,232,-231,207,231,-210,209,231,-234,210,234,-212,211,234,-236,209,233,-213,212,233,-237,212,236,-214,213,236,-238,214,238,-216,215,238,-240,216,240,-215,214,240,-239,217,241,-219,218,241,-243,218,242,-217,216,242,-241,215,239,-220,219,239,-244,220,244,-218,217,244,-242,219,243,-222,221,243,-246,222,246,-221,220,246,-245,221,245,-224,223,245,-248,224,248,-223,222,248,-247,223,247,-226,225,247,-250,226,250,-225,224,250,-249,225,249,-228,227,249,-252,228,252,-227,226,252,-251,227,251,-230,229,251,-254,230,254,-229,228,254,-253,229,253,-232,231,253,-256,232,256,-231,230,256,-255,231,255,-234,233,255,-258,234,258,-236,235,258,-260,233,257,-237,236,257,-261,236,260,-238,237,260,-262,238,4,-240,239,4,-6,240,6,-239,238,6,-5,262,264,-264,263,264,-266,263,265,-267,266,265,-268,239,5,-244,243,5,-20,268,269,-263,262,269,-265,243,19,-246,245,19,-26,270,271,-269,268,271,-270,245,25,-248,247,25,-32,272,273,-271,270,273,-272,247,31,-250,249,31,-38,274,275,-273,272,275,-274,249,37,-252,251,37,-44,276,277,-275,274,277,-276,251,43,-254,253,43,-50,278,279,-277,276,279,-278,253,49,-256,255,49,-56,259,280,-279,278,280,-280,255,55,-258,257,55,-62,258,281,-260,259,281,-281,257,61,-261,260,61,-66,260,65,-262,261,65,-65,242,282,-241,240,282,-284,7,284,-19,18,284,-286,240,283,-7,6,283,-287,6,286,-8,7,286,-285,241,287,-243,242,287,-283,18,285,-25,24,285,-289,244,289,-242,241,289,-288,24,288,-31,30,288,-291,246,291,-245,244,291,-290,30,290,-37,36,290,-293,248,293,-247,246,293,-292,36,292,-43,42,292,-295,250,295,-249,248,295,-294,42,294,-49,48,294,-297,252,297,-251,250,297,-296,48,296,-55,54,296,-299,254,299,-253,252,299,-298,54,298,-61,60,298,-301,256,301,-255,254,301,-300,
60,300,-65,64,300,-303,10,303,-12,11,303,-305,64,302,-262,261,302,-306,62,306,-64,63,306,-308,261,305,-238,237,305,-309,232,309,-257,256,309,-302,237,308,-214,213,308,-311,208,311,-233,232,311,-310,213,310,-190,189,310,-313,184,313,-209,208,313,-312,189,312,-166,165,312,-315,160,315,-185,184,315,-314,165,314,-142,141,314,-317,136,317,-161,160,317,-316,141,316,-118,117,316,-319,112,319,-137,136,319,-318,117,318,-94,93,318,-321,88,321,-113,112,321,-320,93,320,-11,10,320,-304,63,307,-89,88,307,-322,11,304,-63,62,304,-307,282,322,-284,283,322,-324,284,324,-286,285,324,-326,283,323,-287,286,323,-327,286,326,-285,284,326,-325,287,327,-283,282,327,-323,285,325,-289,288,325,-329,289,329,-288,287,329,-328,288,328,-291,290,328,-331,291,331,-290,289,331,-330,290,330,-293,292,330,-333,293,333,-292,291,333,-332,292,332,-295,294,332,-335,295,335,-294,293,335,-334,294,334,-297,296,334,-337,297,337,-296,295,337,-336,296,336,-299,298,336,-339,299,339,-298,297,339,-338,298,338,-301,300,338,-341,301,341,-300,299,341,-340,300,340,-303,302,340,-343,303,343,-305,304,343,-345,302,342,-306,305,342,-346,306,346,-308,307,346,-348,305,345,-309,308,345,-349,309,349,-302,301,349,-342,308,348,-311,310,348,-351,311,351,-310,309,351,-350,310,350,-313,312,350,-353,313,353,-312,311,353,-352,312,352,-315,314,352,-355,315,355,-314,313,355,-354,314,354,-317,316,354,-357,317,357,-316,315,357,-356,316,356,-319,318,356,-359,319,359,-318,317,359,-358,318,358,-321,320,358,-361,321,361,-320,319,361,-360,320,360,-304,303,360,-344,307,347,-322,321,347,-362,304,344,-307,306,344,-347,322,263,-324,323,263,-267,324,265,-326,325,265,-265,323,266,-327,326,266,-268,326,267,-325,324,267,-266,327,262,-323,322,262,-264,325,264,-329,328,264,-270,329,268,-328,327,268,-263,328,269,-331,330,269,-272,331,270,-330,329,270,-269,330,271,-333,332,271,-274,333,272,-332,331,272,-271,332,273,-335,334,273,-276,335,274,-334,333,274,-273,334,275,-337,336,275,-278,337,276,-336,335,276,-275,336,277,-339,338,277,-280,339,278,-338,337,278,-277,338,279,-341,340,279,-281,341,259,-340,339,
259,-279,340,280,-343,342,280,-282,343,69,-345,344,69,-69,342,281,-346,345,281,-259,346,66,-348,347,66,-68,345,258,-349,348,258,-235,349,235,-342,341,235,-260,348,234,-351,350,234,-211,351,211,-350,349,211,-236,350,210,-353,352,210,-187,353,187,-352,351,187,-212,352,186,-355,354,186,-163,355,163,-354,353,163,-188,354,162,-357,356,162,-139,357,139,-356,355,139,-164,356,138,-359,358,138,-115,359,115,-358,357,115,-140,358,114,-361,360,114,-91,361,91,-360,359,91,-116,360,90,-344,343,90,-70,347,67,-362,361,67,-92,344,68,-347,346,68,-67
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *6480 {
				a: -0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,
-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,
-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,
-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,
-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,
-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,
-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,
-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0
			} 
			NormalsW: *2160 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *6480 {
				a: 0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,
-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,
0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,
-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,
-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,
0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,
-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,
0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0
			} 
			BinormalsW: *2160 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *6480 {
				a: 1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,
-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,
-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,
-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,
1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,
-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,
-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,
-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1
			} 
			TangentsW: *2160 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementColor: 0 {
			Version: 101
			Name: "VertexColors"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			Colors: *2028 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
			ColorIndex: *2160 {
				a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
141,168,139,139,168,166,140,167,142,142,167,169,143,170,141,141,170,168,142,169,144,144,169,171,145,172,143,143,172,170,144,171,146,146,171,173,147,174,148,148,174,175,146,173,149,149,173,176,150,177,151,151,177,178,152,179,153,153,179,180,154,181,155,155,181,182,156,183,157,157,183,184,157,184,158,158,184,185,153,180,159,159,180,186,160,187,156,156,187,183,159,186,161,161,186,188,162,189,160,160,189,187,161,188,163,163,188,190,164,191,162,162,191,189,163,190,165,165,190,192,166,193,164,164,193,191,165,192,167,167,192,194,168,195,166,166,195,193,167,194,169,169,194,196,170,197,168,168,197,195,169,196,171,171,196,198,172,199,170,170,199,197,171,198,173,173,198,200,174,201,175,175,201,202,173,200,176,176,200,203,177,204,178,178,204,205,179,206,180,180,206,207,181,208,182,182,208,209,183,210,184,184,210,211,184,211,185,185,211,212,180,207,186,186,207,213,187,214,183,183,214,210,186,213,188,188,213,215,189,216,187,187,216,214,188,215,190,190,215,217,191,218,189,189,218,216,190,217,192,192,217,219,193,220,191,191,220,218,192,219,194,194,219,221,195,222,193,193,222,220,194,221,196,196,221,223,197,224,195,195,224,222,196,223,198,198,223,225,199,226,197,197,226,224,198,225,200,200,225,227,201,228,202,202,228,229,200,227,203,203,227,230,204,231,205,205,231,232,206,233,207,207,233,234,208,235,209,209,235,236,210,237,211,211,237,238,211,238,212,212,238,239,207,234,213,213,234,240,214,241,210,210,241,237,213,240,215,215,240,242,216,243,214,214,243,241,215,242,217,217,242,244,218,245,216,216,245,243,217,244,219,219,244,246,220,247,218,218,247,245,219,246,221,221,246,248,222,249,220,220,249,247,221,248,223,223,248,250,224,251,222,222,251,249,223,250,225,225,250,252,226,253,224,224,253,251,225,252,227,227,252,254,228,255,229,229,255,256,227,254,230,230,254,257,231,258,232,232,258,259,233,260,234,234,260,261,235,262,236,236,262,263,237,264,238,238,264,265,238,265,239,239,265,266,234,261,240,240,261,267,241,268,237,237,268,264,240,267,242,242,267,269,243,270,241,241,270,268,242,269,244,244,269,271,245,272,243,243,272,270,244,271,246,
246,271,273,247,274,245,245,274,272,246,273,248,248,273,275,249,276,247,247,276,274,248,275,250,250,275,277,251,278,249,249,278,276,250,277,252,252,277,279,253,280,251,251,280,278,252,279,254,254,279,281,255,282,256,256,282,283,254,281,257,257,281,284,258,285,259,259,285,286,260,287,261,261,287,288,262,289,263,263,289,290,264,291,265,265,291,292,265,292,266,266,292,293,261,288,267,267,288,294,268,295,264,264,295,291,267,294,269,269,294,296,270,297,268,268,297,295,269,296,271,271,296,298,272,299,270,270,299,297,271,298,273,273,298,300,274,301,272,272,301,299,273,300,275,275,300,302,276,303,274,274,303,301,275,302,277,277,302,304,278,305,276,276,305,303,277,304,279,279,304,306,280,307,278,278,307,305,279,306,281,281,306,308,282,309,283,283,309,310,281,308,284,284,308,311,285,312,286,286,312,313,287,314,288,288,314,315,289,316,290,290,316,317,318,320,319,319,320,321,319,321,322,322,321,323,288,315,294,294,315,324,325,326,318,318,326,320,294,324,296,296,324,327,328,329,325,325,329,326,296,327,298,298,327,330,331,332,328,328,332,329,298,330,300,300,330,333,334,335,331,331,335,332,300,333,302,302,333,336,337,338,334,334,338,335,302,336,304,304,336,339,340,341,337,337,341,338,304,339,306,306,339,342,310,343,340,340,343,341,306,342,308,308,342,344,309,345,310,310,345,343,308,344,311,311,344,346,312,347,313,313,347,348,349,351,350,350,351,352,7,353,26,26,353,354,289,355,316,316,355,356,6,357,7,7,357,353,358,359,349,349,359,351,26,354,34,34,354,360,361,362,358,358,362,359,34,360,42,42,360,363,364,365,361,361,365,362,42,363,50,50,363,366,367,368,364,364,368,365,50,366,58,58,366,369,370,371,367,367,371,368,58,369,66,66,369,372,373,374,370,370,374,371,66,372,74,74,372,375,376,377,373,373,377,374,74,375,82,82,375,378,379,380,376,376,380,377,82,378,90,90,378,381,10,382,11,11,382,383,348,384,313,313,384,385,386,388,387,387,388,389,313,385,286,286,385,390,391,393,392,392,393,394,286,390,259,259,390,395,396,397,391,391,397,393,259,395,232,232,395,398,399,400,396,396,400,397,232,398,205,205,398,401,402,403,399,399,403,400,205,401,178,
178,401,404,405,406,402,402,406,403,178,404,151,151,404,407,408,409,405,405,409,406,151,407,124,124,407,410,411,412,408,408,412,409,124,410,10,10,410,382,387,389,411,411,389,412,93,413,85,85,413,414,351,415,352,352,415,416,353,417,354,354,417,418,355,419,356,356,419,420,357,421,353,353,421,417,359,422,351,351,422,415,354,418,360,360,418,423,362,424,359,359,424,422,360,423,363,363,423,425,365,426,362,362,426,424,363,425,366,366,425,427,368,428,365,365,428,426,366,427,369,369,427,429,371,430,368,368,430,428,369,429,372,372,429,431,374,432,371,371,432,430,372,431,375,375,431,433,377,434,374,374,434,432,375,433,378,378,433,435,380,436,377,377,436,434,378,435,381,381,435,437,382,438,383,383,438,439,384,440,385,385,440,441,388,442,389,389,442,443,385,441,390,390,441,444,393,445,394,394,445,446,390,444,395,395,444,447,397,448,393,393,448,445,395,447,398,398,447,449,400,450,397,397,450,448,398,449,401,401,449,451,403,452,400,400,452,450,401,451,404,404,451,453,406,454,403,403,454,452,404,453,407,407,453,455,409,456,406,406,456,454,407,455,410,410,455,457,412,458,409,409,458,456,410,457,382,382,457,438,389,443,412,412,443,458,413,459,414,414,459,460,415,461,416,416,461,462,417,463,418,418,463,464,419,465,420,420,465,466,421,467,417,417,467,463,422,468,415,415,468,461,418,464,423,423,464,469,424,470,422,422,470,468,423,469,425,425,469,471,426,472,424,424,472,470,425,471,427,427,471,473,428,474,426,426,474,472,427,473,429,429,473,475,430,476,428,428,476,474,429,475,431,431,475,477,432,478,430,430,478,476,431,477,433,433,477,479,434,480,432,432,480,478,433,479,435,435,479,481,436,482,434,434,482,480,435,481,437,437,481,483,438,484,439,439,484,485,440,486,441,441,486,487,442,488,443,443,488,489,441,487,444,444,487,490,445,491,446,446,491,492,444,490,447,447,490,493,448,494,445,445,494,491,447,493,449,449,493,495,450,496,448,448,496,494,449,495,451,451,495,497,452,498,450,450,498,496,451,497,453,453,497,499,454,500,452,452,500,498,453,499,455,455,499,501,456,502,454,454,502,500,455,501,457,457,501,503,458,504,456,456,504,502,457,
503,438,438,503,484,443,489,458,458,489,504,459,505,460,460,505,506
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *1014 {
				a: 0,0,-1,0,0,1,-1,1,0,0,-1,0,0,1,-1,1,1,0,0,0,1,1,0,1,1,0,0,0,1,1,0,1,0,0,1,0,0,-1,1,-1,0,-1,-1,-1,0,0,-1,0,-1,-2,0,-2,-2,1,-2,0,2,0,2,1,0,-2,1,-2,-1,-3,0,-3,-3,1,-3,0,3,0,3,1,0,-3,1,-3,-1,-4,0,-4,-4,1,-4,0,4,0,4,1,0,-4,1,-4,-1,-5,0,-5,-5,1,-5,0,5,0,5,1,0,-5,1,-5,-1,-6,0,-6,-6,1,-6,0,6,0,6,1,0,-6,1,-6,-1,-7,0,-7,-7,1,-7,0,7,0,7,1,0,-7,1,-7,-1,-8,0,-8,-8,1,-8,0,8,0,8,1,0,-8,1,-8,-1,-9,0,-9,-9,1,-9,0,9,0,9,1,0,-9,1,-9,-1,-10,0,-10,-10,1,-10,0,10,0,10,1,0,-9,1,-9,0,-10,1,-10,-2,0,-2,-1,-2,1,-2,0,2,-2,2,-1,2,0,-2,-2,2,-3,-2,-3,2,-4,-2,-4,2,-5,-2,-5,2,-6,-2,-6,2,-7,-2,-7,2,-8,-2,-8,2,-9,-2,-9,2,-10,2,-9,-2,-10,2,0,2,1,-3,0,-3,-1,-3,1,-3,0,3,-2,3,-1,3,0,-3,-2,3,-3,-3,-3,3,-4,-3,-4,3,-5,-3,-5,3,-6,-3,-6,3,-7,-3,-7,3,-8,-3,-8,3,-9,-3,-9,3,-10,3,-9,-3,-10,3,0,3,1,-4,0,-4,-1,-4,1,-4,0,4,-2,4,-1,4,0,-4,-2,4,-3,-4,-3,4,-4,-4,-4,4,-5,-4,-5,4,-6,-4,-6,4,-7,-4,-7,4,-8,-4,-8,4,-9,-4,-9,4,-10,4,-9,-4,-10,4,0,4,1,-5,0,-5,-1,-5,1,-5,0,5,-2,5,-1,5,0,-5,-2,5,-3,-5,-3,5,-4,-5,-4,5,-5,-5,-5,5,-6,-5,-6,5,-7,-5,-7,5,-8,-5,-8,5,-9,-5,-9,5,-10,5,-9,-5,-10,5,0,5,1,-6,0,-6,-1,-6,1,-6,0,6,-2,6,-1,6,0,-6,-2,6,-3,-6,-3,6,-4,-6,-4,6,-5,-6,-5,6,-6,-6,-6,6,-7,-6,-7,6,-8,-6,-8,6,-9,-6,-9,6,-10,6,-9,-6,-10,6,0,6,1,-7,0,-7,-1,-7,1,-7,0,7,-2,7,-1,7,0,-7,-2,7,-3,-7,-3,7,-4,-7,-4,7,-5,-7,-5,7,-6,-7,-6,7,-7,-7,-7,7,-8,-7,-8,7,-9,-7,-9,7,-10,7,-9,-7,-10,7,0,7,1,-8,0,-8,-1,-8,1,-8,0,8,-2,8,-1,8,0,-8,-2,8,-3,-8,-3,8,-4,-8,-4,8,-5,-8,-5,8,-6,-8,-6,8,-7,-8,-7,8,-8,-8,-8,8,-9,-8,-9,8,-10,8,-9,-8,-10,8,0,8,1,-9,0,-9,-1,-9,1,-9,0,9,-2,9,-1,9,0,-9,-2,9,-3,-9,-3,9,-4,-9,-4,9,-5,-9,-5,9,-6,-9,-6,9,-7,-9,-7,9,-8,-9,-8,9,-9,-9,-9,9,-10,9,-9,-9,-10,9,0,9,1,-10,0,-10,-1,-10,1,-10,0,9,-2,9,-1,10,-2,10,-1,9,0,10,0,-10,-2,9,-3,10,-3,-10,-3,9,-4,10,-4,-10,-4,9,-5,10,-5,-10,-5,9,-6,10,-6,-10,-6,9,-7,10,-7,-10,-7,9,-8,10,-8,-10,-8,10,-9,-10,-9,10,-10,-10,-10,10,0,10,1,1,1,0,1,1,2,0,2,-1,2,-2,2,-9,2,-10,2,0,2,2,1,2,2,-3,2,3,1,3,2,-4,2,4,1,4,2,-5,2,5,1,5,2,-6,2,6,1,6,2,-7,2,7,1,7,2,-8,2,8,1,8,2,-9,2,9,1,9,2,-10,2,1,2,0,2,10,2,9,2,0,1,-1,1,0,2,-1,2,8,2,-8,1,
-9,1,-8,2,-9,2,7,2,-7,1,-7,2,6,2,-6,1,-6,2,5,2,-5,1,-5,2,4,2,-4,1,-4,2,3,2,-3,1,-3,2,2,2,-2,1,-2,2,10,2,9,2,1,3,0,3,-1,3,-2,3,-9,3,-10,3,0,3,2,3,-3,3,3,3,-4,3,4,3,-5,3,5,3,-6,3,6,3,-7,3,7,3,-8,3,8,3,-9,3,9,3,-10,3,1,3,0,3,10,3,9,3,0,3,-1,3,8,3,-8,3,-9,3,7,3,-7,3,6,3,-6,3,5,3,-5,3,4,3,-4,3,3,3,-3,3,2,3,-2,3,10,3,9,3,1,4,0,4,-1,4,-2,4,-9,4,-10,4,0,4,2,4,-3,4,3,4,-4,4,4,4,-5,4,5,4,-6,4,6,4,-7,4,7,4,-8,4,8,4,-9,4,9,4,-10,4,1,4,0,4,10,4,9,4,0,4,-1,4,8,4,-8,4,-9,4,7,4,-7,4,6,4,-6,4,5,4,-5,4,4,4,-4,4,3,4,-3,4,2,4,-2,4,10,4,9,4
			} 
			UVIndex: *2160 {
				a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
141,168,139,139,168,166,140,167,142,142,167,169,143,170,141,141,170,168,142,169,144,144,169,171,145,172,143,143,172,170,144,171,146,146,171,173,147,174,148,148,174,175,146,173,149,149,173,176,150,177,151,151,177,178,152,179,153,153,179,180,154,181,155,155,181,182,156,183,157,157,183,184,157,184,158,158,184,185,153,180,159,159,180,186,160,187,156,156,187,183,159,186,161,161,186,188,162,189,160,160,189,187,161,188,163,163,188,190,164,191,162,162,191,189,163,190,165,165,190,192,166,193,164,164,193,191,165,192,167,167,192,194,168,195,166,166,195,193,167,194,169,169,194,196,170,197,168,168,197,195,169,196,171,171,196,198,172,199,170,170,199,197,171,198,173,173,198,200,174,201,175,175,201,202,173,200,176,176,200,203,177,204,178,178,204,205,179,206,180,180,206,207,181,208,182,182,208,209,183,210,184,184,210,211,184,211,185,185,211,212,180,207,186,186,207,213,187,214,183,183,214,210,186,213,188,188,213,215,189,216,187,187,216,214,188,215,190,190,215,217,191,218,189,189,218,216,190,217,192,192,217,219,193,220,191,191,220,218,192,219,194,194,219,221,195,222,193,193,222,220,194,221,196,196,221,223,197,224,195,195,224,222,196,223,198,198,223,225,199,226,197,197,226,224,198,225,200,200,225,227,201,228,202,202,228,229,200,227,203,203,227,230,204,231,205,205,231,232,206,233,207,207,233,234,208,235,209,209,235,236,210,237,211,211,237,238,211,238,212,212,238,239,207,234,213,213,234,240,214,241,210,210,241,237,213,240,215,215,240,242,216,243,214,214,243,241,215,242,217,217,242,244,218,245,216,216,245,243,217,244,219,219,244,246,220,247,218,218,247,245,219,246,221,221,246,248,222,249,220,220,249,247,221,248,223,223,248,250,224,251,222,222,251,249,223,250,225,225,250,252,226,253,224,224,253,251,225,252,227,227,252,254,228,255,229,229,255,256,227,254,230,230,254,257,231,258,232,232,258,259,233,260,234,234,260,261,235,262,236,236,262,263,237,264,238,238,264,265,238,265,239,239,265,266,234,261,240,240,261,267,241,268,237,237,268,264,240,267,242,242,267,269,243,270,241,241,270,268,242,269,244,244,269,271,245,272,243,243,272,270,244,271,246,
246,271,273,247,274,245,245,274,272,246,273,248,248,273,275,249,276,247,247,276,274,248,275,250,250,275,277,251,278,249,249,278,276,250,277,252,252,277,279,253,280,251,251,280,278,252,279,254,254,279,281,255,282,256,256,282,283,254,281,257,257,281,284,258,285,259,259,285,286,260,287,261,261,287,288,262,289,263,263,289,290,264,291,265,265,291,292,265,292,266,266,292,293,261,288,267,267,288,294,268,295,264,264,295,291,267,294,269,269,294,296,270,297,268,268,297,295,269,296,271,271,296,298,272,299,270,270,299,297,271,298,273,273,298,300,274,301,272,272,301,299,273,300,275,275,300,302,276,303,274,274,303,301,275,302,277,277,302,304,278,305,276,276,305,303,277,304,279,279,304,306,280,307,278,278,307,305,279,306,281,281,306,308,282,309,283,283,309,310,281,308,284,284,308,311,285,312,286,286,312,313,287,314,288,288,314,315,289,316,290,290,316,317,318,320,319,319,320,321,319,321,322,322,321,323,288,315,294,294,315,324,325,326,318,318,326,320,294,324,296,296,324,327,328,329,325,325,329,326,296,327,298,298,327,330,331,332,328,328,332,329,298,330,300,300,330,333,334,335,331,331,335,332,300,333,302,302,333,336,337,338,334,334,338,335,302,336,304,304,336,339,340,341,337,337,341,338,304,339,306,306,339,342,310,343,340,340,343,341,306,342,308,308,342,344,309,345,310,310,345,343,308,344,311,311,344,346,312,347,313,313,347,348,349,351,350,350,351,352,7,353,26,26,353,354,289,355,316,316,355,356,6,357,7,7,357,353,358,359,349,349,359,351,26,354,34,34,354,360,361,362,358,358,362,359,34,360,42,42,360,363,364,365,361,361,365,362,42,363,50,50,363,366,367,368,364,364,368,365,50,366,58,58,366,369,370,371,367,367,371,368,58,369,66,66,369,372,373,374,370,370,374,371,66,372,74,74,372,375,376,377,373,373,377,374,74,375,82,82,375,378,379,380,376,376,380,377,82,378,90,90,378,381,10,382,11,11,382,383,348,384,313,313,384,385,386,388,387,387,388,389,313,385,286,286,385,390,391,393,392,392,393,394,286,390,259,259,390,395,396,397,391,391,397,393,259,395,232,232,395,398,399,400,396,396,400,397,232,398,205,205,398,401,402,403,399,399,403,400,205,401,178,
178,401,404,405,406,402,402,406,403,178,404,151,151,404,407,408,409,405,405,409,406,151,407,124,124,407,410,411,412,408,408,412,409,124,410,10,10,410,382,387,389,411,411,389,412,93,413,85,85,413,414,351,415,352,352,415,416,353,417,354,354,417,418,355,419,356,356,419,420,357,421,353,353,421,417,359,422,351,351,422,415,354,418,360,360,418,423,362,424,359,359,424,422,360,423,363,363,423,425,365,426,362,362,426,424,363,425,366,366,425,427,368,428,365,365,428,426,366,427,369,369,427,429,371,430,368,368,430,428,369,429,372,372,429,431,374,432,371,371,432,430,372,431,375,375,431,433,377,434,374,374,434,432,375,433,378,378,433,435,380,436,377,377,436,434,378,435,381,381,435,437,382,438,383,383,438,439,384,440,385,385,440,441,388,442,389,389,442,443,385,441,390,390,441,444,393,445,394,394,445,446,390,444,395,395,444,447,397,448,393,393,448,445,395,447,398,398,447,449,400,450,397,397,450,448,398,449,401,401,449,451,403,452,400,400,452,450,401,451,404,404,451,453,406,454,403,403,454,452,404,453,407,407,453,455,409,456,406,406,456,454,407,455,410,410,455,457,412,458,409,409,458,456,410,457,382,382,457,438,389,443,412,412,443,458,413,459,414,414,459,460,415,461,416,416,461,462,417,463,418,418,463,464,419,465,420,420,465,466,421,467,417,417,467,463,422,468,415,415,468,461,418,464,423,423,464,469,424,470,422,422,470,468,423,469,425,425,469,471,426,472,424,424,472,470,425,471,427,427,471,473,428,474,426,426,474,472,427,473,429,429,473,475,430,476,428,428,476,474,429,475,431,431,475,477,432,478,430,430,478,476,431,477,433,433,477,479,434,480,432,432,480,478,433,479,435,435,479,481,436,482,434,434,482,480,435,481,437,437,481,483,438,484,439,439,484,485,440,486,441,441,486,487,442,488,443,443,488,489,441,487,444,444,487,490,445,491,446,446,491,492,444,490,447,447,490,493,448,494,445,445,494,491,447,493,449,449,493,495,450,496,448,448,496,494,449,495,451,451,495,497,452,498,450,450,498,496,451,497,453,453,497,499,454,500,452,452,500,498,453,499,455,455,499,501,456,502,454,454,502,500,455,501,457,457,501,503,458,504,456,456,504,502,457,
503,438,438,503,484,443,489,458,458,489,504,459,505,460,460,505,506
			} 
		}
		LayerElementUV: 1 {
			Version: 101
			Name: "UVSet1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *1014 {
				a: 0.363608628511429,0.363610059022903,0.399169474840164,0.363610059022903,0.363608628511429,0.399170935153961,0.399169474840164,0.399170935153961,0.363609701395035,0.00400000018998981,0.399170637130737,0.00400005048140883,0.36360964179039,0.0395608693361282,0.39917054772377,0.0395609475672245,0.683657228946686,0.150243893265724,0.719218075275421,0.150243788957596,0.683657288551331,0.185804694890976,0.719218134880066,0.185804605484009,0.324047684669495,0.723218619823456,0.359608620405197,0.723218619823456,0.324047684669495,0.758779466152191,0.359608620405197,0.758779466152191,0.683655917644501,0.509853422641754,0.68365603685379,0.5454141497612,0.648095190525055,0.509853541851044,0.648095309734344,0.54541426897049,0.00400007981806993,0.0395612344145775,0.0395609438419342,0.0395611859858036,0.00400000018998981,0.00400033313781023,0.0395609252154827,0.00400031264871359,0.0395609848201275,0.0751220658421516,0.0040001580491662,0.0751221030950546,0.43473145365715,0.0395610108971596,0.434731483459473,0.00400013104081154,0.288486838340759,0.723218619823456,0.288486838340759,0.758779466152191,0.612534463405609,0.509853601455688,0.612534523010254,0.545414328575134,0.0395610220730305,0.110682904720306,0.00400023208931088,0.110682934522629,0.470292299985886,0.0395610667765141,0.470292329788208,0.00400018831714988,0.252925962209702,0.723218619823456,0.252925962209702,0.758779466152191,0.576973736286163,0.509853661060333,0.576973795890808,0.545414388179779,0.039561040699482,0.146243691444397,0.00400028610602021,0.146243706345558,0.505853116512299,0.0395610928535461,0.505853176116943,0.0040002460591495,0.217365175485611,0.723218619823456,0.217365175485611,0.758779466152191,0.541413009166718,0.509853720664978,0.541413009166718,0.545414447784424,0.0395610481500626,0.181804478168488,0.0040003196336329,0.181804463267326,0.541413962841034,0.0395611003041267,0.541413962841034,0.00400026841089129,0.181804373860359,0.723218619823456,0.181804373860359,0.758779466152191,0.505852282047272,0.509853720664978,0.505852282047272,
0.545414447784424,0.0395610257983208,0.217365220189095,0.00400032009929419,0.217365175485611,0.576974809169769,0.0395610891282558,0.576974749565125,0.0040002609603107,0.146243512630463,0.723218619823456,0.146243512630463,0.758779466152191,0.470291495323181,0.509853661060333,0.470291495323181,0.545414447784424,0.0395609810948372,0.252925962209702,0.00400028750300407,0.252925872802734,0.612535655498505,0.0395610518753529,0.612535536289215,0.00400022370740771,0.110682643949986,0.723218619823456,0.110682643949986,0.758779466152191,0.434730678796768,0.509853601455688,0.434730648994446,0.545414388179779,0.0395609103143215,0.288486659526825,0.00400022091343999,0.288486570119858,0.648096442222595,0.0395609922707081,0.64809638261795,0.00400018086656928,0.0751217752695084,0.723218619823456,0.0751217752695084,0.758779466152191,0.399169832468033,0.509853541851044,0.399169772863388,0.545414388179779,0.0395608060061932,0.324047386646271,0.0040001249872148,0.324047297239304,0.683657288551331,0.039560928940773,0.683657228946686,0.00400012219324708,0.0395608991384506,0.723218619823456,0.0395608991384506,0.758779466152191,0.363608926534653,0.509853422641754,0.36360889673233,0.545414328575134,0.0395606979727745,0.359608113765717,0.00400000018998981,0.359607994556427,0.719218134880066,0.0395608432590961,0.719218015670776,0.00400002161040902,0.00400001974776387,0.723218619823456,0.004000015091151,0.758779466152191,0.0395609587430954,0.363610059022903,0.0395609550178051,0.399170964956284,0.00400006398558617,0.363610059022903,0.00400005979463458,0.399170964956284,0.0751217678189278,0.00400026328861713,0.0751217976212502,0.0395611263811588,0.4347303211689,0.399170935153961,0.4347303211689,0.363610059022903,0.612534582614899,0.58097505569458,0.648095369338989,0.580974996089935,0.683656096458435,0.580974876880646,0.0751218199729919,0.0751220211386681,0.576973855495453,0.58097517490387,0.0751218423247337,0.110682874917984,0.541413068771362,0.580975234508514,0.0751218423247337,0.146243676543236,0.505852282047272,0.580975234508514,0.0751218125224113,
0.181804478168488,0.470291465520859,0.580975294113159,0.0751217678189278,0.217365264892578,0.434730619192123,0.580975234508514,0.0751216933131218,0.252926021814346,0.399169713258743,0.580975234508514,0.0751216113567352,0.288486778736115,0.363608837127686,0.58097517490387,0.0751215070486069,0.324047476053238,0.00400005374103785,0.434731781482697,0.0395609550178051,0.434731781482697,0.0751213803887367,0.359608203172684,0.648096442222595,0.150243952870369,0.64809650182724,0.185804754495621,0.110682621598244,0.00400021066889167,0.110682658851147,0.0395610891282558,0.470291197299957,0.399170935153961,0.470291197299957,0.363610059022903,0.612534701824188,0.616535842418671,0.648095488548279,0.616535723209381,0.683656215667725,0.616535604000092,0.110682666301727,0.0751219913363457,0.576973915100098,0.61653596162796,0.110682666301727,0.110682867467403,0.541413128376007,0.616536021232605,0.110682658851147,0.146243691444397,0.505852282047272,0.61653608083725,0.110682606697083,0.18180450797081,0.470291465520859,0.61653608083725,0.110682539641857,0.217365324497223,0.434730559587479,0.61653608083725,0.110682442784309,0.252926111221313,0.399169683456421,0.61653608083725,0.110682338476181,0.288486868143082,0.363608777523041,0.616536021232605,0.110682211816311,0.324047595262527,0.0040000481531024,0.470292627811432,0.0395609512925148,0.470292627811432,0.110682055354118,0.359608322381973,0.61253559589386,0.150243997573853,0.612535655498505,0.185804814100266,0.14624348282814,0.00400014547631145,0.146243512630463,0.0395610630512238,0.505852043628693,0.399170935153961,0.505852043628693,0.363610059022903,0.612534821033478,0.652096629142761,0.648095607757568,0.652096509933472,0.683656394481659,0.652096331119537,0.146243527531624,0.0751219764351845,0.576973974704742,0.652096748352051,0.146243512630463,0.110682882368565,0.541413187980652,0.652096807956696,0.14624348282814,0.146243721246719,0.505852341651917,0.652096927165985,0.146243423223495,0.181804567575455,0.470291465520859,0.652096927165985,0.146243333816528,0.217365399003029,0.434730559587479,
0.65209698677063,0.146243214607239,0.252926200628281,0.399169653654099,0.65209698677063,0.146243095397949,0.288486987352371,0.363608717918396,0.652096927165985,0.146242931485176,0.324047744274139,0.00400004303082824,0.505853474140167,0.0395609512925148,0.505853474140167,0.146242752671242,0.359608501195908,0.576974749565125,0.150244012475014,0.576974809169769,0.185804843902588,0.181804373860359,0.0040000919252634,0.18180438876152,0.039561040699482,0.541412830352783,0.399170935153961,0.541412830352783,0.363610059022903,0.612534940242767,0.687657415866852,0.648095726966858,0.687657237052917,0.683656573295593,0.687657058238983,0.18180438876152,0.0751219764351845,0.576974093914032,0.687657594680786,0.181804373860359,0.110682897269726,0.541413247585297,0.687657654285431,0.181804329156876,0.146243765950203,0.505852341651917,0.68765777349472,0.18180425465107,0.181804627180099,0.470291495323181,0.687657833099365,0.181804150342941,0.217365488409996,0.434730559587479,0.687657833099365,0.181804016232491,0.25292631983757,0.399169623851776,0.687657833099365,0.181803852319717,0.288487136363983,0.363608658313751,0.687657833099365,0.181803673505783,0.324047923088074,0.0040000369772315,0.541414320468903,0.0395609475672245,0.541414320468903,0.181803464889526,0.359608709812164,0.541413962841034,0.150244042277336,0.541413962841034,0.18580487370491,0.217365294694901,0.00400004722177982,0.217365309596062,0.0395610295236111,0.576973736286163,0.399170935153961,0.576973736286163,0.363610059022903,0.612535059452057,0.723218262195587,0.648095905780792,0.723218083381653,0.683656752109528,0.723217844963074,0.217365294694901,0.0751219764351845,0.576974213123322,0.723218441009521,0.217365264892578,0.110682919621468,0.541413307189941,0.723218560218811,0.217365205287933,0.146243825554848,0.505852401256561,0.723218619823456,0.217365100979805,0.181804716587067,0.470291525125504,0.723218679428101,0.217364981770515,0.217365592718124,0.434730589389801,0.723218739032745,0.217364847660065,0.25292643904686,0.399169623851776,0.72321879863739,0.21736466884613,
0.288487315177917,0.363608628511429,0.72321879863739,0.217364460229874,0.32404813170433,0.00400002999231219,0.576975166797638,0.0395609401166439,0.576975166797638,0.217364221811295,0.359608948230743,0.505853176116943,0.150244027376175,0.505853176116943,0.185804858803749,0.252926260232925,0.00400001322850585,0.252926260232925,0.0395610295236111,0.612534582614899,0.399170935153961,0.612534582614899,0.363610088825226,0.612535238265991,0.758779108524323,0.648096144199371,0.758778929710388,0.683656990528107,0.758778691291809,0.252926230430603,0.0751220062375069,0.576974332332611,0.758779287338257,0.252926170825958,0.110682956874371,0.541413426399231,0.758779406547546,0.252926081418991,0.146243885159492,0.505852520465851,0.758779525756836,0.252925992012024,0.181804805994034,0.470291554927826,0.758779644966125,0.252925843000412,0.217365726828575,0.434730619192123,0.75877970457077,0.2529256939888,0.252926617860794,0.399169653654099,0.758779764175415,0.252925485372543,0.288487493991852,0.363608628511429,0.758779764175415,0.252925276756287,0.324048340320587,0.00400002207607031,0.612536013126373,0.0395609326660633,0.612536013126373,0.252925008535385,0.359609186649323,0.47029235959053,0.150243982672691,0.470292299985886,0.185804843902588,0.288487255573273,0.00400000018998981,0.28848722577095,0.039561040699482,0.648095488548279,0.399170964956284,0.648095488548279,0.363610088825226,0.612535417079926,0.794340014457703,0.648096323013306,0.794339835643768,0.683657228946686,0.794339597225189,0.288487195968628,0.0751220285892487,0.576974451541901,0.794340193271637,0.288487106561661,0.110683016479015,0.541413545608521,0.794340372085571,0.288487017154694,0.14624397456646,0.505852580070496,0.794340491294861,0.288486897945404,0.181804925203323,0.470291644334793,0.794340550899506,0.288486748933792,0.217365860939026,0.434730678796768,0.794340670108795,0.288486570119858,0.252926766872406,0.399169683456421,0.79434072971344,0.288486361503601,0.288487702608109,0.363608628511429,0.794340789318085,0.288486123085022,0.324048578739166,0.00400001415982842,
0.648096859455109,0.0395609214901924,0.648096919059753,0.28848585486412,0.359609484672546,0.434731513261795,0.150243908166885,0.43473145365715,0.185804784297943,0.324048280715942,0.00400001322850585,0.324048221111298,0.0395610742270947,0.683656275272369,0.399170964956284,0.683656275272369,0.363610088825226,0.612535536289215,0.829900920391083,0.648096561431885,0.829900741577148,0.683657467365265,0.829900503158569,0.324048161506653,0.0751220807433128,0.576974630355835,0.829901099205017,0.324048072099686,0.110683090984821,0.54141366481781,0.829901278018951,0.324047982692719,0.146244078874588,0.505852699279785,0.829901397228241,0.324047833681107,0.181805044412613,0.47029173374176,0.829901516437531,0.324047684669495,0.217366009950638,0.434730738401413,0.82990163564682,0.324047476053238,0.25292694568634,0.399169743061066,0.829901695251465,0.324047267436981,0.288487911224365,0.363608688116074,0.82990175485611,0.324047029018402,0.324048846960068,0.00400000670924783,0.683657705783844,0.0395609103143215,0.683657765388489,0.324046760797501,0.35960978269577,0.39917066693306,0.150243788957596,0.399170577526093,0.185804709792137,0.359609365463257,0.00400005653500557,0.35960927605629,0.0395611524581909,0.71921718120575,0.399170964956284,0.71921718120575,0.363610059022903,0.288486868143082,0.683657765388489,0.324047744274139,0.683657765388489,0.288486868143082,0.719218552112579,0.324047744274139,0.719218552112579,0.359608620405197,0.683657765388489,0.359608620405197,0.719218552112579,0.359609156847,0.07512217015028,0.252925992012024,0.683657765388489,0.252925992012024,0.719218611717224,0.359609067440033,0.110683187842369,0.217365175485611,0.683657765388489,0.217365175485611,0.719218611717224,0.359608948230743,0.146244183182716,0.181804358959198,0.683657765388489,0.181804358959198,0.719218611717224,0.359608799219131,0.181805193424225,0.146243527531624,0.683657765388489,0.146243527531624,0.719218611717224,0.359608620405197,0.217366173863411,0.11068270355463,0.683657765388489,0.11068269610405,0.719218611717224,0.359608441591263,0.252927154302597,
0.0751218050718307,0.683657765388489,0.0751217976212502,0.719218611717224,0.359608203172684,0.2884880900383,0.0395609028637409,0.719218552112579,0.359607934951782,0.324049115180969,0.00400000018998981,0.719218552112579,0.359607666730881,0.359610080718994,0.363609731197357,0.150243654847145,0.363609671592712,0.185804635286331,0.288486927747726,0.869462013244629,0.324047803878784,0.869461953639984,0.288486957550049,0.905022859573364,0.324047863483429,0.905022799968719,0.399170458316803,0.0751218125224113,0.434731364250183,0.0751218721270561,0.683656275272369,0.434731781482697,0.71921718120575,0.434731781482697,0.3636095225811,0.0751217380166054,0.252926081418991,0.869462013244629,0.252926051616669,0.905022859573364,0.470292240381241,0.0751219093799591,0.217365220189095,0.869462013244629,0.217365205287933,0.905022859573364,0.505853116512299,0.0751219317317009,0.181804373860359,0.869462013244629,0.181804344058037,0.905022859573364,0.541413962841034,0.0751219317317009,0.146243527531624,0.869462013244629,0.146243497729301,0.905022859573364,0.576974809169769,0.0751219093799591,0.110682681202888,0.869462013244629,0.110682658851147,0.905022859573364,0.612535655498505,0.0751218721270561,0.0751218274235725,0.869462013244629,0.0751217976212502,0.905022859573364,0.64809650182724,0.0751218125224113,0.0395609587430954,0.869461953639984,0.0395609177649021,0.905022859573364,0.683657348155975,0.0751217305660248,0.00400001695379615,0.869461953639984,0.00400000018998981,0.905022859573364,0.719218194484711,0.0751216411590576,0.68365740776062,0.221365511417389,0.719218194484711,0.221365422010422,0.363609552383423,0.221365511417389,0.399170488119125,0.221365600824356,0.363608658313751,0.833901762962341,0.399169534444809,0.833901822566986,0.363608628511429,0.869462668895721,0.399169504642487,0.869462668895721,0.434731364250183,0.22136564552784,0.648095548152924,0.833901822566986,0.683656454086304,0.833901762962341,0.648095607757568,0.869462668895721,0.683656454086304,0.869462668895721,0.470292270183563,0.221365690231323,0.612534701824188,
0.833901822566986,0.612534701824188,0.869462668895721,0.505853116512299,0.221365705132484,0.576973855495453,0.833901822566986,0.576973855495453,0.869462668895721,0.541413962841034,0.221365705132484,0.541412949562073,0.833901822566986,0.541412949562073,0.869462668895721,0.576974809169769,0.221365675330162,0.505852103233337,0.833901822566986,0.505852103233337,0.869462668895721,0.612535715103149,0.221365630626678,0.470291286706924,0.833901822566986,0.470291256904602,0.869462668895721,0.648096561431885,0.221365585923195,0.434730410575867,0.833901822566986,0.434730410575867,0.869462668895721,0.00400000903755426,0.794340312480927,0.0395608954131603,0.794340312480927,0.288486927747726,0.940583765506744,0.324047863483429,0.940583765506744,0.399170398712158,0.110682718455791,0.434731304645538,0.110682755708694,0.683656275272369,0.470292627811432,0.71921718120575,0.470292627811432,0.363609433174133,0.110682658851147,0.252926051616669,0.9405837059021,0.470292210578918,0.110682778060436,0.217365190386772,0.9405837059021,0.505853056907654,0.110682785511017,0.181804344058037,0.9405837059021,0.541413962841034,0.110682778060436,0.146243497729301,0.9405837059021,0.576974809169769,0.110682755708694,0.110682643949986,0.9405837059021,0.612535715103149,0.110682711005211,0.0751217901706696,0.9405837059021,0.648096561431885,0.110682658851147,0.0395609177649021,0.9405837059021,0.683657467365265,0.110682591795921,0.00400001695379615,0.940583765506744,0.719218313694,0.110682502388954,0.683657467365265,0.256926327943802,0.719218254089355,0.256926238536835,0.363609433174133,0.256926447153091,0.399170398712158,0.256926506757736,0.363608658313751,0.905023574829102,0.399169504642487,0.905023574829102,0.434731334447861,0.256926536560059,0.648095548152924,0.905023574829102,0.683656454086304,0.905023574829102,0.470292240381241,0.256926536560059,0.612534642219543,0.905023515224457,0.505853116512299,0.256926566362381,0.576973795890808,0.905023515224457,0.541413962841034,0.256926536560059,0.541412949562073,0.905023515224457,0.576974868774414,0.256926506757736,
0.505852103233337,0.905023515224457,0.612535715103149,0.256926476955414,0.470291256904602,0.905023515224457,0.648096561431885,0.256926417350769,0.434730380773544,0.905023515224457,0.0040000039152801,0.829901158809662,0.03956089168787,0.829901158809662,0.288486868143082,0.97614461183548,0.324047803878784,0.976144671440125,0.399170368909836,0.146243616938591,0.434731274843216,0.146243631839752,0.683656275272369,0.505853414535522,0.71921718120575,0.505853414535522,0.363609373569489,0.146243587136269,0.252925992012024,0.976144552230835,0.470292180776596,0.146243661642075,0.217365145683289,0.976144552230835,0.505853056907654,0.146243661642075,0.181804314255714,0.97614449262619,0.541413962841034,0.146243646740913,0.14624348282814,0.97614449262619,0.576974868774414,0.14624360203743,0.110682636499405,0.97614449262619,0.612535715103149,0.146243557333946,0.0751217976212502,0.976144552230835,0.64809662103653,0.146243497729301,0.0395609475672245,0.976144552230835,0.68365752696991,0.146243408322334,0.00400009891018271,0.97614461183548,0.719218373298645,0.146243318915367,0.68365752696991,0.292487174272537,0.719218373298645,0.29248708486557,0.363609373569489,0.292487382888794,0.399170398712158,0.292487412691116,0.363608747720718,0.940584480762482,0.399169564247131,0.940584421157837,0.434731304645538,0.292487412691116,0.648095488548279,0.940584421157837,0.683656454086304,0.940584480762482,0.470292210578918,0.292487442493439,0.612534642219543,0.940584361553192,0.505853116512299,0.292487442493439,0.576973736286163,0.940584361553192,0.541413962841034,0.292487412691116,0.541412949562073,0.940584301948547,0.576974868774414,0.292487382888794,0.505852103233337,0.940584301948547,0.612535774707794,0.292487323284149,0.470291256904602,0.940584301948547,0.64809662103653,0.292487263679504,0.434730410575867,0.940584361553192,0.00400000018998981,0.865461945533752,0.0395608879625797,0.865461945533752
			} 
			UVIndex: *2160 {
				a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
141,168,139,139,168,166,140,167,142,142,167,169,143,170,141,141,170,168,142,169,144,144,169,171,145,172,143,143,172,170,144,171,146,146,171,173,147,174,148,148,174,175,146,173,149,149,173,176,150,177,151,151,177,178,152,179,153,153,179,180,154,181,155,155,181,182,156,183,157,157,183,184,157,184,158,158,184,185,153,180,159,159,180,186,160,187,156,156,187,183,159,186,161,161,186,188,162,189,160,160,189,187,161,188,163,163,188,190,164,191,162,162,191,189,163,190,165,165,190,192,166,193,164,164,193,191,165,192,167,167,192,194,168,195,166,166,195,193,167,194,169,169,194,196,170,197,168,168,197,195,169,196,171,171,196,198,172,199,170,170,199,197,171,198,173,173,198,200,174,201,175,175,201,202,173,200,176,176,200,203,177,204,178,178,204,205,179,206,180,180,206,207,181,208,182,182,208,209,183,210,184,184,210,211,184,211,185,185,211,212,180,207,186,186,207,213,187,214,183,183,214,210,186,213,188,188,213,215,189,216,187,187,216,214,188,215,190,190,215,217,191,218,189,189,218,216,190,217,192,192,217,219,193,220,191,191,220,218,192,219,194,194,219,221,195,222,193,193,222,220,194,221,196,196,221,223,197,224,195,195,224,222,196,223,198,198,223,225,199,226,197,197,226,224,198,225,200,200,225,227,201,228,202,202,228,229,200,227,203,203,227,230,204,231,205,205,231,232,206,233,207,207,233,234,208,235,209,209,235,236,210,237,211,211,237,238,211,238,212,212,238,239,207,234,213,213,234,240,214,241,210,210,241,237,213,240,215,215,240,242,216,243,214,214,243,241,215,242,217,217,242,244,218,245,216,216,245,243,217,244,219,219,244,246,220,247,218,218,247,245,219,246,221,221,246,248,222,249,220,220,249,247,221,248,223,223,248,250,224,251,222,222,251,249,223,250,225,225,250,252,226,253,224,224,253,251,225,252,227,227,252,254,228,255,229,229,255,256,227,254,230,230,254,257,231,258,232,232,258,259,233,260,234,234,260,261,235,262,236,236,262,263,237,264,238,238,264,265,238,265,239,239,265,266,234,261,240,240,261,267,241,268,237,237,268,264,240,267,242,242,267,269,243,270,241,241,270,268,242,269,244,244,269,271,245,272,243,243,272,270,244,271,246,
246,271,273,247,274,245,245,274,272,246,273,248,248,273,275,249,276,247,247,276,274,248,275,250,250,275,277,251,278,249,249,278,276,250,277,252,252,277,279,253,280,251,251,280,278,252,279,254,254,279,281,255,282,256,256,282,283,254,281,257,257,281,284,258,285,259,259,285,286,260,287,261,261,287,288,262,289,263,263,289,290,264,291,265,265,291,292,265,292,266,266,292,293,261,288,267,267,288,294,268,295,264,264,295,291,267,294,269,269,294,296,270,297,268,268,297,295,269,296,271,271,296,298,272,299,270,270,299,297,271,298,273,273,298,300,274,301,272,272,301,299,273,300,275,275,300,302,276,303,274,274,303,301,275,302,277,277,302,304,278,305,276,276,305,303,277,304,279,279,304,306,280,307,278,278,307,305,279,306,281,281,306,308,282,309,283,283,309,310,281,308,284,284,308,311,285,312,286,286,312,313,287,314,288,288,314,315,289,316,290,290,316,317,318,320,319,319,320,321,319,321,322,322,321,323,288,315,294,294,315,324,325,326,318,318,326,320,294,324,296,296,324,327,328,329,325,325,329,326,296,327,298,298,327,330,331,332,328,328,332,329,298,330,300,300,330,333,334,335,331,331,335,332,300,333,302,302,333,336,337,338,334,334,338,335,302,336,304,304,336,339,340,341,337,337,341,338,304,339,306,306,339,342,310,343,340,340,343,341,306,342,308,308,342,344,309,345,310,310,345,343,308,344,311,311,344,346,312,347,313,313,347,348,349,351,350,350,351,352,7,353,26,26,353,354,289,355,316,316,355,356,6,357,7,7,357,353,358,359,349,349,359,351,26,354,34,34,354,360,361,362,358,358,362,359,34,360,42,42,360,363,364,365,361,361,365,362,42,363,50,50,363,366,367,368,364,364,368,365,50,366,58,58,366,369,370,371,367,367,371,368,58,369,66,66,369,372,373,374,370,370,374,371,66,372,74,74,372,375,376,377,373,373,377,374,74,375,82,82,375,378,379,380,376,376,380,377,82,378,90,90,378,381,10,382,11,11,382,383,348,384,313,313,384,385,386,388,387,387,388,389,313,385,286,286,385,390,391,393,392,392,393,394,286,390,259,259,390,395,396,397,391,391,397,393,259,395,232,232,395,398,399,400,396,396,400,397,232,398,205,205,398,401,402,403,399,399,403,400,205,401,178,
178,401,404,405,406,402,402,406,403,178,404,151,151,404,407,408,409,405,405,409,406,151,407,124,124,407,410,411,412,408,408,412,409,124,410,10,10,410,382,387,389,411,411,389,412,93,413,85,85,413,414,351,415,352,352,415,416,353,417,354,354,417,418,355,419,356,356,419,420,357,421,353,353,421,417,359,422,351,351,422,415,354,418,360,360,418,423,362,424,359,359,424,422,360,423,363,363,423,425,365,426,362,362,426,424,363,425,366,366,425,427,368,428,365,365,428,426,366,427,369,369,427,429,371,430,368,368,430,428,369,429,372,372,429,431,374,432,371,371,432,430,372,431,375,375,431,433,377,434,374,374,434,432,375,433,378,378,433,435,380,436,377,377,436,434,378,435,381,381,435,437,382,438,383,383,438,439,384,440,385,385,440,441,388,442,389,389,442,443,385,441,390,390,441,444,393,445,394,394,445,446,390,444,395,395,444,447,397,448,393,393,448,445,395,447,398,398,447,449,400,450,397,397,450,448,398,449,401,401,449,451,403,452,400,400,452,450,401,451,404,404,451,453,406,454,403,403,454,452,404,453,407,407,453,455,409,456,406,406,456,454,407,455,410,410,455,457,412,458,409,409,458,456,410,457,382,382,457,438,389,443,412,412,443,458,413,459,414,414,459,460,415,461,416,416,461,462,417,463,418,418,463,464,419,465,420,420,465,466,421,467,417,417,467,463,422,468,415,415,468,461,418,464,423,423,464,469,424,470,422,422,470,468,423,469,425,425,469,471,426,472,424,424,472,470,425,471,427,427,471,473,428,474,426,426,474,472,427,473,429,429,473,475,430,476,428,428,476,474,429,475,431,431,475,477,432,478,430,430,478,476,431,477,433,433,477,479,434,480,432,432,480,478,433,479,435,435,479,481,436,482,434,434,482,480,435,481,437,437,481,483,438,484,439,439,484,485,440,486,441,441,486,487,442,488,443,443,488,489,441,487,444,444,487,490,445,491,446,446,491,492,444,490,447,447,490,493,448,494,445,445,494,491,447,493,449,449,493,495,450,496,448,448,496,494,449,495,451,451,495,497,452,498,450,450,498,496,451,497,453,453,497,499,454,500,452,452,500,498,453,499,455,455,499,501,456,502,454,454,502,500,455,501,457,457,501,503,458,504,456,456,504,502,457,
503,438,438,503,484,443,489,458,458,489,504,459,505,460,460,505,506
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementColor"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
		Layer: 1 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 1
			}
		}
	}
	Model: 1146771008, "Model::ProB_Floor_LWall_10x10", "Mesh" {
		Version: 232
		Properties70:  {
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 1148833296, "Material::Default_Prototype", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "BumpFactor", "double", "Number", "",0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "Opacity", "double", "Number", "",1
		}
	}
	Video: 1148831296, "Video::DiffuseColor_Texture", "Clip" {
		Type: "Clip"
		Properties70:  {
			P: "Path", "KString", "XRefUrl", "", "C:\Users\<USER>\Documents\Unity_Projects\use_cases_cinemachine\Assets\Plugins\ProCore\ProBuilder\Resources\Textures\GridBox_Default.png"
		}
		UseMipMap: 0
		Filename: "C:\Users\<USER>\Documents\Unity_Projects\use_cases_cinemachine\Assets\Plugins\ProCore\ProBuilder\Resources\Textures\GridBox_Default.png"
		RelativeFilename: "..\..\..\Documents\Unity_Projects\use_cases_cinemachine\Assets\Plugins\ProCore\ProBuilder\Resources\Textures\GridBox_Default.png"
	}
	Texture: 294699520, "Texture::DiffuseColor_Texture", "" {
		Type: "TextureVideoClip"
		Version: 202
		TextureName: "Texture::DiffuseColor_Texture"
		Media: "Video::DiffuseColor_Texture"
		FileName: "C:\Users\<USER>\Documents\Unity_Projects\use_cases_cinemachine\Assets\Plugins\ProCore\ProBuilder\Resources\Textures\GridBox_Default.png"
		RelativeFilename: "..\..\..\Documents\Unity_Projects\use_cases_cinemachine\Assets\Plugins\ProCore\ProBuilder\Resources\Textures\GridBox_Default.png"
		ModelUVTranslation: 0,0
		ModelUVScaling: 1,1
		Texture_Alpha_Source: "None"
		Cropping: 0,0,0,0
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::ProB_Floor_LWall_10x10, Model::RootNode
	C: "OO",1146771008,0
	
	;Material::Default_Prototype, Model::ProB_Floor_LWall_10x10
	C: "OO",1148833296,1146771008
	
	;Material::Default_Prototype, Model::ProB_Floor_LWall_10x10
	C: "OO",1148833296,1146771008
	
	;Geometry::Scene, Model::ProB_Floor_LWall_10x10
	C: "OO",1143700784,1146771008
	
	;Texture::DiffuseColor_Texture, Material::Default_Prototype
	C: "OO",294699520,1148833296
	
	;Texture::DiffuseColor_Texture, Material::Default_Prototype
	C: "OP",294699520,1148833296, "DiffuseColor"
	
	;Video::DiffuseColor_Texture, Texture::DiffuseColor_Texture
	C: "OO",1148831296,294699520
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
