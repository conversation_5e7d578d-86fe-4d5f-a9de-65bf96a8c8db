# Upgrading to version 7.4.0 of the Universal Render Pipeline

This page describes how to upgrade from an older version of the Universal Render Pipeline (URP) to version 7.4.0.

## Upgrading from URP 7.2.x and later

1. URP 7.4.0 does not have breaking changes compared with URP 7.2.x and later. To upgrade URP to version 7.4.0, install the new version of the package.

## Upgrading from URP 7.0.x-7.1.x

1. Upgrade to URP 7.2.0 first. Refer to [Upgrading to version 7.2.0 of the Universal Render Pipeline](upgrade-guide-7-2-0.md).

2. Perform the procedure **Upgrading from URP 7.2.x**.
