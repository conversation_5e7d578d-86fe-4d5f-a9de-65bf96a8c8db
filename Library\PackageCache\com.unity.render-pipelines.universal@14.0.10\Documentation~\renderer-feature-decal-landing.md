# Decal Renderer Feature

A Decal Renderer Features projects specific materials (decals) onto other objects in the scene. Decals interact with the scene's lighting and wrap around meshes.

|Page|Description|
|-|-|
|[Decal Renderer Feature](renderer-feature-decal.md)|Use a Decal Renderer Feature in your scene.|
|[Decal Shader Graph](decal-shader.md)|Project a material as a decal if the material uses a Shader Graph with the Decal Material type.|
