# UV Node

## Description

Provides access to the mesh vertex or fragment's **UV** coordinates. The coordinate channel of the output value can be selected with the **Channel** dropdown parameter.

## Ports

| Name        | Direction           | Type  | Binding | Description |
|:------------ |:-------------|:-----|:---|:---|
| Out | Output      |    Vector 4 | None | Mesh's **UV** coordinates. |

## Controls

| Name        | Type           | Options  | Description |
|:------------ |:-------------|:-----|:---|
| Channel | Dropdown | UV0, UV1, UV2, UV3 | Selects coordinate channel of **UV** to output. |
