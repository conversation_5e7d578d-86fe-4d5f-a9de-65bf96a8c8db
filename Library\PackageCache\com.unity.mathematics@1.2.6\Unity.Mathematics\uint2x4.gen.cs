//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using System;
using System.Runtime.CompilerServices;
using Unity.IL2CPP.CompilerServices;

#pragma warning disable 0660, 0661

namespace Unity.Mathematics
{
    /// <summary>A 2x4 matrix of uints.</summary>
    [System.Serializable]
    [Il2CppEagerStaticClassConstruction]
    public partial struct uint2x4 : System.IEquatable<uint2x4>, IFormattable
    {
        /// <summary>Column 0 of the matrix.</summary>
        public uint2 c0;
        /// <summary>Column 1 of the matrix.</summary>
        public uint2 c1;
        /// <summary>Column 2 of the matrix.</summary>
        public uint2 c2;
        /// <summary>Column 3 of the matrix.</summary>
        public uint2 c3;

        /// <summary>uint2x4 zero value.</summary>
        public static readonly uint2x4 zero;

        /// <summary>Constructs a uint2x4 matrix from four uint2 vectors.</summary>
        /// <param name="c0">The matrix column c0 will be set to this value.</param>
        /// <param name="c1">The matrix column c1 will be set to this value.</param>
        /// <param name="c2">The matrix column c2 will be set to this value.</param>
        /// <param name="c3">The matrix column c3 will be set to this value.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public uint2x4(uint2 c0, uint2 c1, uint2 c2, uint2 c3)
        {
            this.c0 = c0;
            this.c1 = c1;
            this.c2 = c2;
            this.c3 = c3;
        }

        /// <summary>Constructs a uint2x4 matrix from 8 uint values given in row-major order.</summary>
        /// <param name="m00">The matrix at row 0, column 0 will be set to this value.</param>
        /// <param name="m01">The matrix at row 0, column 1 will be set to this value.</param>
        /// <param name="m02">The matrix at row 0, column 2 will be set to this value.</param>
        /// <param name="m03">The matrix at row 0, column 3 will be set to this value.</param>
        /// <param name="m10">The matrix at row 1, column 0 will be set to this value.</param>
        /// <param name="m11">The matrix at row 1, column 1 will be set to this value.</param>
        /// <param name="m12">The matrix at row 1, column 2 will be set to this value.</param>
        /// <param name="m13">The matrix at row 1, column 3 will be set to this value.</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public uint2x4(uint m00, uint m01, uint m02, uint m03,
                       uint m10, uint m11, uint m12, uint m13)
        {
            this.c0 = new uint2(m00, m10);
            this.c1 = new uint2(m01, m11);
            this.c2 = new uint2(m02, m12);
            this.c3 = new uint2(m03, m13);
        }

        /// <summary>Constructs a uint2x4 matrix from a single uint value by assigning it to every component.</summary>
        /// <param name="v">uint to convert to uint2x4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public uint2x4(uint v)
        {
            this.c0 = v;
            this.c1 = v;
            this.c2 = v;
            this.c3 = v;
        }

        /// <summary>Constructs a uint2x4 matrix from a single bool value by converting it to uint and assigning it to every component.</summary>
        /// <param name="v">bool to convert to uint2x4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public uint2x4(bool v)
        {
            this.c0 = math.select(new uint2(0u), new uint2(1u), v);
            this.c1 = math.select(new uint2(0u), new uint2(1u), v);
            this.c2 = math.select(new uint2(0u), new uint2(1u), v);
            this.c3 = math.select(new uint2(0u), new uint2(1u), v);
        }

        /// <summary>Constructs a uint2x4 matrix from a bool2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">bool2x4 to convert to uint2x4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public uint2x4(bool2x4 v)
        {
            this.c0 = math.select(new uint2(0u), new uint2(1u), v.c0);
            this.c1 = math.select(new uint2(0u), new uint2(1u), v.c1);
            this.c2 = math.select(new uint2(0u), new uint2(1u), v.c2);
            this.c3 = math.select(new uint2(0u), new uint2(1u), v.c3);
        }

        /// <summary>Constructs a uint2x4 matrix from a single int value by converting it to uint and assigning it to every component.</summary>
        /// <param name="v">int to convert to uint2x4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public uint2x4(int v)
        {
            this.c0 = (uint2)v;
            this.c1 = (uint2)v;
            this.c2 = (uint2)v;
            this.c3 = (uint2)v;
        }

        /// <summary>Constructs a uint2x4 matrix from a int2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">int2x4 to convert to uint2x4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public uint2x4(int2x4 v)
        {
            this.c0 = (uint2)v.c0;
            this.c1 = (uint2)v.c1;
            this.c2 = (uint2)v.c2;
            this.c3 = (uint2)v.c3;
        }

        /// <summary>Constructs a uint2x4 matrix from a single float value by converting it to uint and assigning it to every component.</summary>
        /// <param name="v">float to convert to uint2x4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public uint2x4(float v)
        {
            this.c0 = (uint2)v;
            this.c1 = (uint2)v;
            this.c2 = (uint2)v;
            this.c3 = (uint2)v;
        }

        /// <summary>Constructs a uint2x4 matrix from a float2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">float2x4 to convert to uint2x4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public uint2x4(float2x4 v)
        {
            this.c0 = (uint2)v.c0;
            this.c1 = (uint2)v.c1;
            this.c2 = (uint2)v.c2;
            this.c3 = (uint2)v.c3;
        }

        /// <summary>Constructs a uint2x4 matrix from a single double value by converting it to uint and assigning it to every component.</summary>
        /// <param name="v">double to convert to uint2x4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public uint2x4(double v)
        {
            this.c0 = (uint2)v;
            this.c1 = (uint2)v;
            this.c2 = (uint2)v;
            this.c3 = (uint2)v;
        }

        /// <summary>Constructs a uint2x4 matrix from a double2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">double2x4 to convert to uint2x4</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public uint2x4(double2x4 v)
        {
            this.c0 = (uint2)v.c0;
            this.c1 = (uint2)v.c1;
            this.c2 = (uint2)v.c2;
            this.c3 = (uint2)v.c3;
        }


        /// <summary>Implicitly converts a single uint value to a uint2x4 matrix by assigning it to every component.</summary>
        /// <param name="v">uint to convert to uint2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static implicit operator uint2x4(uint v) { return new uint2x4(v); }

        /// <summary>Explicitly converts a single bool value to a uint2x4 matrix by converting it to uint and assigning it to every component.</summary>
        /// <param name="v">bool to convert to uint2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator uint2x4(bool v) { return new uint2x4(v); }

        /// <summary>Explicitly converts a bool2x4 matrix to a uint2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">bool2x4 to convert to uint2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator uint2x4(bool2x4 v) { return new uint2x4(v); }

        /// <summary>Explicitly converts a single int value to a uint2x4 matrix by converting it to uint and assigning it to every component.</summary>
        /// <param name="v">int to convert to uint2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator uint2x4(int v) { return new uint2x4(v); }

        /// <summary>Explicitly converts a int2x4 matrix to a uint2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">int2x4 to convert to uint2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator uint2x4(int2x4 v) { return new uint2x4(v); }

        /// <summary>Explicitly converts a single float value to a uint2x4 matrix by converting it to uint and assigning it to every component.</summary>
        /// <param name="v">float to convert to uint2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator uint2x4(float v) { return new uint2x4(v); }

        /// <summary>Explicitly converts a float2x4 matrix to a uint2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">float2x4 to convert to uint2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator uint2x4(float2x4 v) { return new uint2x4(v); }

        /// <summary>Explicitly converts a single double value to a uint2x4 matrix by converting it to uint and assigning it to every component.</summary>
        /// <param name="v">double to convert to uint2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator uint2x4(double v) { return new uint2x4(v); }

        /// <summary>Explicitly converts a double2x4 matrix to a uint2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">double2x4 to convert to uint2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static explicit operator uint2x4(double2x4 v) { return new uint2x4(v); }


        /// <summary>Returns the result of a componentwise multiplication operation on two uint2x4 matrices.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise multiplication.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise multiplication.</param>
        /// <returns>uint2x4 result of the componentwise multiplication.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator * (uint2x4 lhs, uint2x4 rhs) { return new uint2x4 (lhs.c0 * rhs.c0, lhs.c1 * rhs.c1, lhs.c2 * rhs.c2, lhs.c3 * rhs.c3); }

        /// <summary>Returns the result of a componentwise multiplication operation on a uint2x4 matrix and a uint value.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise multiplication.</param>
        /// <param name="rhs">Right hand side uint to use to compute componentwise multiplication.</param>
        /// <returns>uint2x4 result of the componentwise multiplication.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator * (uint2x4 lhs, uint rhs) { return new uint2x4 (lhs.c0 * rhs, lhs.c1 * rhs, lhs.c2 * rhs, lhs.c3 * rhs); }

        /// <summary>Returns the result of a componentwise multiplication operation on a uint value and a uint2x4 matrix.</summary>
        /// <param name="lhs">Left hand side uint to use to compute componentwise multiplication.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise multiplication.</param>
        /// <returns>uint2x4 result of the componentwise multiplication.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator * (uint lhs, uint2x4 rhs) { return new uint2x4 (lhs * rhs.c0, lhs * rhs.c1, lhs * rhs.c2, lhs * rhs.c3); }


        /// <summary>Returns the result of a componentwise addition operation on two uint2x4 matrices.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise addition.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise addition.</param>
        /// <returns>uint2x4 result of the componentwise addition.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator + (uint2x4 lhs, uint2x4 rhs) { return new uint2x4 (lhs.c0 + rhs.c0, lhs.c1 + rhs.c1, lhs.c2 + rhs.c2, lhs.c3 + rhs.c3); }

        /// <summary>Returns the result of a componentwise addition operation on a uint2x4 matrix and a uint value.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise addition.</param>
        /// <param name="rhs">Right hand side uint to use to compute componentwise addition.</param>
        /// <returns>uint2x4 result of the componentwise addition.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator + (uint2x4 lhs, uint rhs) { return new uint2x4 (lhs.c0 + rhs, lhs.c1 + rhs, lhs.c2 + rhs, lhs.c3 + rhs); }

        /// <summary>Returns the result of a componentwise addition operation on a uint value and a uint2x4 matrix.</summary>
        /// <param name="lhs">Left hand side uint to use to compute componentwise addition.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise addition.</param>
        /// <returns>uint2x4 result of the componentwise addition.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator + (uint lhs, uint2x4 rhs) { return new uint2x4 (lhs + rhs.c0, lhs + rhs.c1, lhs + rhs.c2, lhs + rhs.c3); }


        /// <summary>Returns the result of a componentwise subtraction operation on two uint2x4 matrices.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise subtraction.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise subtraction.</param>
        /// <returns>uint2x4 result of the componentwise subtraction.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator - (uint2x4 lhs, uint2x4 rhs) { return new uint2x4 (lhs.c0 - rhs.c0, lhs.c1 - rhs.c1, lhs.c2 - rhs.c2, lhs.c3 - rhs.c3); }

        /// <summary>Returns the result of a componentwise subtraction operation on a uint2x4 matrix and a uint value.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise subtraction.</param>
        /// <param name="rhs">Right hand side uint to use to compute componentwise subtraction.</param>
        /// <returns>uint2x4 result of the componentwise subtraction.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator - (uint2x4 lhs, uint rhs) { return new uint2x4 (lhs.c0 - rhs, lhs.c1 - rhs, lhs.c2 - rhs, lhs.c3 - rhs); }

        /// <summary>Returns the result of a componentwise subtraction operation on a uint value and a uint2x4 matrix.</summary>
        /// <param name="lhs">Left hand side uint to use to compute componentwise subtraction.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise subtraction.</param>
        /// <returns>uint2x4 result of the componentwise subtraction.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator - (uint lhs, uint2x4 rhs) { return new uint2x4 (lhs - rhs.c0, lhs - rhs.c1, lhs - rhs.c2, lhs - rhs.c3); }


        /// <summary>Returns the result of a componentwise division operation on two uint2x4 matrices.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise division.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise division.</param>
        /// <returns>uint2x4 result of the componentwise division.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator / (uint2x4 lhs, uint2x4 rhs) { return new uint2x4 (lhs.c0 / rhs.c0, lhs.c1 / rhs.c1, lhs.c2 / rhs.c2, lhs.c3 / rhs.c3); }

        /// <summary>Returns the result of a componentwise division operation on a uint2x4 matrix and a uint value.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise division.</param>
        /// <param name="rhs">Right hand side uint to use to compute componentwise division.</param>
        /// <returns>uint2x4 result of the componentwise division.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator / (uint2x4 lhs, uint rhs) { return new uint2x4 (lhs.c0 / rhs, lhs.c1 / rhs, lhs.c2 / rhs, lhs.c3 / rhs); }

        /// <summary>Returns the result of a componentwise division operation on a uint value and a uint2x4 matrix.</summary>
        /// <param name="lhs">Left hand side uint to use to compute componentwise division.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise division.</param>
        /// <returns>uint2x4 result of the componentwise division.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator / (uint lhs, uint2x4 rhs) { return new uint2x4 (lhs / rhs.c0, lhs / rhs.c1, lhs / rhs.c2, lhs / rhs.c3); }


        /// <summary>Returns the result of a componentwise modulus operation on two uint2x4 matrices.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise modulus.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise modulus.</param>
        /// <returns>uint2x4 result of the componentwise modulus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator % (uint2x4 lhs, uint2x4 rhs) { return new uint2x4 (lhs.c0 % rhs.c0, lhs.c1 % rhs.c1, lhs.c2 % rhs.c2, lhs.c3 % rhs.c3); }

        /// <summary>Returns the result of a componentwise modulus operation on a uint2x4 matrix and a uint value.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise modulus.</param>
        /// <param name="rhs">Right hand side uint to use to compute componentwise modulus.</param>
        /// <returns>uint2x4 result of the componentwise modulus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator % (uint2x4 lhs, uint rhs) { return new uint2x4 (lhs.c0 % rhs, lhs.c1 % rhs, lhs.c2 % rhs, lhs.c3 % rhs); }

        /// <summary>Returns the result of a componentwise modulus operation on a uint value and a uint2x4 matrix.</summary>
        /// <param name="lhs">Left hand side uint to use to compute componentwise modulus.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise modulus.</param>
        /// <returns>uint2x4 result of the componentwise modulus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator % (uint lhs, uint2x4 rhs) { return new uint2x4 (lhs % rhs.c0, lhs % rhs.c1, lhs % rhs.c2, lhs % rhs.c3); }


        /// <summary>Returns the result of a componentwise increment operation on a uint2x4 matrix.</summary>
        /// <param name="val">Value to use when computing the componentwise increment.</param>
        /// <returns>uint2x4 result of the componentwise increment.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator ++ (uint2x4 val) { return new uint2x4 (++val.c0, ++val.c1, ++val.c2, ++val.c3); }


        /// <summary>Returns the result of a componentwise decrement operation on a uint2x4 matrix.</summary>
        /// <param name="val">Value to use when computing the componentwise decrement.</param>
        /// <returns>uint2x4 result of the componentwise decrement.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator -- (uint2x4 val) { return new uint2x4 (--val.c0, --val.c1, --val.c2, --val.c3); }


        /// <summary>Returns the result of a componentwise less than operation on two uint2x4 matrices.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise less than.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise less than.</param>
        /// <returns>bool2x4 result of the componentwise less than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator < (uint2x4 lhs, uint2x4 rhs) { return new bool2x4 (lhs.c0 < rhs.c0, lhs.c1 < rhs.c1, lhs.c2 < rhs.c2, lhs.c3 < rhs.c3); }

        /// <summary>Returns the result of a componentwise less than operation on a uint2x4 matrix and a uint value.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise less than.</param>
        /// <param name="rhs">Right hand side uint to use to compute componentwise less than.</param>
        /// <returns>bool2x4 result of the componentwise less than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator < (uint2x4 lhs, uint rhs) { return new bool2x4 (lhs.c0 < rhs, lhs.c1 < rhs, lhs.c2 < rhs, lhs.c3 < rhs); }

        /// <summary>Returns the result of a componentwise less than operation on a uint value and a uint2x4 matrix.</summary>
        /// <param name="lhs">Left hand side uint to use to compute componentwise less than.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise less than.</param>
        /// <returns>bool2x4 result of the componentwise less than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator < (uint lhs, uint2x4 rhs) { return new bool2x4 (lhs < rhs.c0, lhs < rhs.c1, lhs < rhs.c2, lhs < rhs.c3); }


        /// <summary>Returns the result of a componentwise less or equal operation on two uint2x4 matrices.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise less or equal.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise less or equal.</param>
        /// <returns>bool2x4 result of the componentwise less or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator <= (uint2x4 lhs, uint2x4 rhs) { return new bool2x4 (lhs.c0 <= rhs.c0, lhs.c1 <= rhs.c1, lhs.c2 <= rhs.c2, lhs.c3 <= rhs.c3); }

        /// <summary>Returns the result of a componentwise less or equal operation on a uint2x4 matrix and a uint value.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise less or equal.</param>
        /// <param name="rhs">Right hand side uint to use to compute componentwise less or equal.</param>
        /// <returns>bool2x4 result of the componentwise less or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator <= (uint2x4 lhs, uint rhs) { return new bool2x4 (lhs.c0 <= rhs, lhs.c1 <= rhs, lhs.c2 <= rhs, lhs.c3 <= rhs); }

        /// <summary>Returns the result of a componentwise less or equal operation on a uint value and a uint2x4 matrix.</summary>
        /// <param name="lhs">Left hand side uint to use to compute componentwise less or equal.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise less or equal.</param>
        /// <returns>bool2x4 result of the componentwise less or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator <= (uint lhs, uint2x4 rhs) { return new bool2x4 (lhs <= rhs.c0, lhs <= rhs.c1, lhs <= rhs.c2, lhs <= rhs.c3); }


        /// <summary>Returns the result of a componentwise greater than operation on two uint2x4 matrices.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise greater than.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise greater than.</param>
        /// <returns>bool2x4 result of the componentwise greater than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator > (uint2x4 lhs, uint2x4 rhs) { return new bool2x4 (lhs.c0 > rhs.c0, lhs.c1 > rhs.c1, lhs.c2 > rhs.c2, lhs.c3 > rhs.c3); }

        /// <summary>Returns the result of a componentwise greater than operation on a uint2x4 matrix and a uint value.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise greater than.</param>
        /// <param name="rhs">Right hand side uint to use to compute componentwise greater than.</param>
        /// <returns>bool2x4 result of the componentwise greater than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator > (uint2x4 lhs, uint rhs) { return new bool2x4 (lhs.c0 > rhs, lhs.c1 > rhs, lhs.c2 > rhs, lhs.c3 > rhs); }

        /// <summary>Returns the result of a componentwise greater than operation on a uint value and a uint2x4 matrix.</summary>
        /// <param name="lhs">Left hand side uint to use to compute componentwise greater than.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise greater than.</param>
        /// <returns>bool2x4 result of the componentwise greater than.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator > (uint lhs, uint2x4 rhs) { return new bool2x4 (lhs > rhs.c0, lhs > rhs.c1, lhs > rhs.c2, lhs > rhs.c3); }


        /// <summary>Returns the result of a componentwise greater or equal operation on two uint2x4 matrices.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise greater or equal.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise greater or equal.</param>
        /// <returns>bool2x4 result of the componentwise greater or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator >= (uint2x4 lhs, uint2x4 rhs) { return new bool2x4 (lhs.c0 >= rhs.c0, lhs.c1 >= rhs.c1, lhs.c2 >= rhs.c2, lhs.c3 >= rhs.c3); }

        /// <summary>Returns the result of a componentwise greater or equal operation on a uint2x4 matrix and a uint value.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise greater or equal.</param>
        /// <param name="rhs">Right hand side uint to use to compute componentwise greater or equal.</param>
        /// <returns>bool2x4 result of the componentwise greater or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator >= (uint2x4 lhs, uint rhs) { return new bool2x4 (lhs.c0 >= rhs, lhs.c1 >= rhs, lhs.c2 >= rhs, lhs.c3 >= rhs); }

        /// <summary>Returns the result of a componentwise greater or equal operation on a uint value and a uint2x4 matrix.</summary>
        /// <param name="lhs">Left hand side uint to use to compute componentwise greater or equal.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise greater or equal.</param>
        /// <returns>bool2x4 result of the componentwise greater or equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator >= (uint lhs, uint2x4 rhs) { return new bool2x4 (lhs >= rhs.c0, lhs >= rhs.c1, lhs >= rhs.c2, lhs >= rhs.c3); }


        /// <summary>Returns the result of a componentwise unary minus operation on a uint2x4 matrix.</summary>
        /// <param name="val">Value to use when computing the componentwise unary minus.</param>
        /// <returns>uint2x4 result of the componentwise unary minus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator - (uint2x4 val) { return new uint2x4 (-val.c0, -val.c1, -val.c2, -val.c3); }


        /// <summary>Returns the result of a componentwise unary plus operation on a uint2x4 matrix.</summary>
        /// <param name="val">Value to use when computing the componentwise unary plus.</param>
        /// <returns>uint2x4 result of the componentwise unary plus.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator + (uint2x4 val) { return new uint2x4 (+val.c0, +val.c1, +val.c2, +val.c3); }


        /// <summary>Returns the result of a componentwise left shift operation on a uint2x4 matrix by a number of bits specified by a single int.</summary>
        /// <param name="x">The matrix to left shift.</param>
        /// <param name="n">The number of bits to left shift.</param>
        /// <returns>The result of the componentwise left shift.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator << (uint2x4 x, int n) { return new uint2x4 (x.c0 << n, x.c1 << n, x.c2 << n, x.c3 << n); }

        /// <summary>Returns the result of a componentwise right shift operation on a uint2x4 matrix by a number of bits specified by a single int.</summary>
        /// <param name="x">The matrix to right shift.</param>
        /// <param name="n">The number of bits to right shift.</param>
        /// <returns>The result of the componentwise right shift.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator >> (uint2x4 x, int n) { return new uint2x4 (x.c0 >> n, x.c1 >> n, x.c2 >> n, x.c3 >> n); }

        /// <summary>Returns the result of a componentwise equality operation on two uint2x4 matrices.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise equality.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise equality.</param>
        /// <returns>bool2x4 result of the componentwise equality.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator == (uint2x4 lhs, uint2x4 rhs) { return new bool2x4 (lhs.c0 == rhs.c0, lhs.c1 == rhs.c1, lhs.c2 == rhs.c2, lhs.c3 == rhs.c3); }

        /// <summary>Returns the result of a componentwise equality operation on a uint2x4 matrix and a uint value.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise equality.</param>
        /// <param name="rhs">Right hand side uint to use to compute componentwise equality.</param>
        /// <returns>bool2x4 result of the componentwise equality.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator == (uint2x4 lhs, uint rhs) { return new bool2x4 (lhs.c0 == rhs, lhs.c1 == rhs, lhs.c2 == rhs, lhs.c3 == rhs); }

        /// <summary>Returns the result of a componentwise equality operation on a uint value and a uint2x4 matrix.</summary>
        /// <param name="lhs">Left hand side uint to use to compute componentwise equality.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise equality.</param>
        /// <returns>bool2x4 result of the componentwise equality.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator == (uint lhs, uint2x4 rhs) { return new bool2x4 (lhs == rhs.c0, lhs == rhs.c1, lhs == rhs.c2, lhs == rhs.c3); }


        /// <summary>Returns the result of a componentwise not equal operation on two uint2x4 matrices.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise not equal.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise not equal.</param>
        /// <returns>bool2x4 result of the componentwise not equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator != (uint2x4 lhs, uint2x4 rhs) { return new bool2x4 (lhs.c0 != rhs.c0, lhs.c1 != rhs.c1, lhs.c2 != rhs.c2, lhs.c3 != rhs.c3); }

        /// <summary>Returns the result of a componentwise not equal operation on a uint2x4 matrix and a uint value.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise not equal.</param>
        /// <param name="rhs">Right hand side uint to use to compute componentwise not equal.</param>
        /// <returns>bool2x4 result of the componentwise not equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator != (uint2x4 lhs, uint rhs) { return new bool2x4 (lhs.c0 != rhs, lhs.c1 != rhs, lhs.c2 != rhs, lhs.c3 != rhs); }

        /// <summary>Returns the result of a componentwise not equal operation on a uint value and a uint2x4 matrix.</summary>
        /// <param name="lhs">Left hand side uint to use to compute componentwise not equal.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise not equal.</param>
        /// <returns>bool2x4 result of the componentwise not equal.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool2x4 operator != (uint lhs, uint2x4 rhs) { return new bool2x4 (lhs != rhs.c0, lhs != rhs.c1, lhs != rhs.c2, lhs != rhs.c3); }


        /// <summary>Returns the result of a componentwise bitwise not operation on a uint2x4 matrix.</summary>
        /// <param name="val">Value to use when computing the componentwise bitwise not.</param>
        /// <returns>uint2x4 result of the componentwise bitwise not.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator ~ (uint2x4 val) { return new uint2x4 (~val.c0, ~val.c1, ~val.c2, ~val.c3); }


        /// <summary>Returns the result of a componentwise bitwise and operation on two uint2x4 matrices.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise bitwise and.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise bitwise and.</param>
        /// <returns>uint2x4 result of the componentwise bitwise and.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator & (uint2x4 lhs, uint2x4 rhs) { return new uint2x4 (lhs.c0 & rhs.c0, lhs.c1 & rhs.c1, lhs.c2 & rhs.c2, lhs.c3 & rhs.c3); }

        /// <summary>Returns the result of a componentwise bitwise and operation on a uint2x4 matrix and a uint value.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise bitwise and.</param>
        /// <param name="rhs">Right hand side uint to use to compute componentwise bitwise and.</param>
        /// <returns>uint2x4 result of the componentwise bitwise and.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator & (uint2x4 lhs, uint rhs) { return new uint2x4 (lhs.c0 & rhs, lhs.c1 & rhs, lhs.c2 & rhs, lhs.c3 & rhs); }

        /// <summary>Returns the result of a componentwise bitwise and operation on a uint value and a uint2x4 matrix.</summary>
        /// <param name="lhs">Left hand side uint to use to compute componentwise bitwise and.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise bitwise and.</param>
        /// <returns>uint2x4 result of the componentwise bitwise and.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator & (uint lhs, uint2x4 rhs) { return new uint2x4 (lhs & rhs.c0, lhs & rhs.c1, lhs & rhs.c2, lhs & rhs.c3); }


        /// <summary>Returns the result of a componentwise bitwise or operation on two uint2x4 matrices.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise bitwise or.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise bitwise or.</param>
        /// <returns>uint2x4 result of the componentwise bitwise or.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator | (uint2x4 lhs, uint2x4 rhs) { return new uint2x4 (lhs.c0 | rhs.c0, lhs.c1 | rhs.c1, lhs.c2 | rhs.c2, lhs.c3 | rhs.c3); }

        /// <summary>Returns the result of a componentwise bitwise or operation on a uint2x4 matrix and a uint value.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise bitwise or.</param>
        /// <param name="rhs">Right hand side uint to use to compute componentwise bitwise or.</param>
        /// <returns>uint2x4 result of the componentwise bitwise or.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator | (uint2x4 lhs, uint rhs) { return new uint2x4 (lhs.c0 | rhs, lhs.c1 | rhs, lhs.c2 | rhs, lhs.c3 | rhs); }

        /// <summary>Returns the result of a componentwise bitwise or operation on a uint value and a uint2x4 matrix.</summary>
        /// <param name="lhs">Left hand side uint to use to compute componentwise bitwise or.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise bitwise or.</param>
        /// <returns>uint2x4 result of the componentwise bitwise or.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator | (uint lhs, uint2x4 rhs) { return new uint2x4 (lhs | rhs.c0, lhs | rhs.c1, lhs | rhs.c2, lhs | rhs.c3); }


        /// <summary>Returns the result of a componentwise bitwise exclusive or operation on two uint2x4 matrices.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise bitwise exclusive or.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise bitwise exclusive or.</param>
        /// <returns>uint2x4 result of the componentwise bitwise exclusive or.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator ^ (uint2x4 lhs, uint2x4 rhs) { return new uint2x4 (lhs.c0 ^ rhs.c0, lhs.c1 ^ rhs.c1, lhs.c2 ^ rhs.c2, lhs.c3 ^ rhs.c3); }

        /// <summary>Returns the result of a componentwise bitwise exclusive or operation on a uint2x4 matrix and a uint value.</summary>
        /// <param name="lhs">Left hand side uint2x4 to use to compute componentwise bitwise exclusive or.</param>
        /// <param name="rhs">Right hand side uint to use to compute componentwise bitwise exclusive or.</param>
        /// <returns>uint2x4 result of the componentwise bitwise exclusive or.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator ^ (uint2x4 lhs, uint rhs) { return new uint2x4 (lhs.c0 ^ rhs, lhs.c1 ^ rhs, lhs.c2 ^ rhs, lhs.c3 ^ rhs); }

        /// <summary>Returns the result of a componentwise bitwise exclusive or operation on a uint value and a uint2x4 matrix.</summary>
        /// <param name="lhs">Left hand side uint to use to compute componentwise bitwise exclusive or.</param>
        /// <param name="rhs">Right hand side uint2x4 to use to compute componentwise bitwise exclusive or.</param>
        /// <returns>uint2x4 result of the componentwise bitwise exclusive or.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 operator ^ (uint lhs, uint2x4 rhs) { return new uint2x4 (lhs ^ rhs.c0, lhs ^ rhs.c1, lhs ^ rhs.c2, lhs ^ rhs.c3); }



        /// <summary>Returns the uint2 element at a specified index.</summary>
        unsafe public ref uint2 this[int index]
        {
            get
            {
#if ENABLE_UNITY_COLLECTIONS_CHECKS
                if ((uint)index >= 4)
                    throw new System.ArgumentException("index must be between[0...3]");
#endif
                fixed (uint2x4* array = &this) { return ref ((uint2*)array)[index]; }
            }
        }

        /// <summary>Returns true if the uint2x4 is equal to a given uint2x4, false otherwise.</summary>
        /// <param name="rhs">Right hand side argument to compare equality with.</param>
        /// <returns>The result of the equality comparison.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public bool Equals(uint2x4 rhs) { return c0.Equals(rhs.c0) && c1.Equals(rhs.c1) && c2.Equals(rhs.c2) && c3.Equals(rhs.c3); }

        /// <summary>Returns true if the uint2x4 is equal to a given uint2x4, false otherwise.</summary>
        /// <param name="o">Right hand side argument to compare equality with.</param>
        /// <returns>The result of the equality comparison.</returns>
        public override bool Equals(object o) { return o is uint2x4 converted && Equals(converted); }


        /// <summary>Returns a hash code for the uint2x4.</summary>
        /// <returns>The computed hash code.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override int GetHashCode() { return (int)math.hash(this); }


        /// <summary>Returns a string representation of the uint2x4.</summary>
        /// <returns>String representation of the value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override string ToString()
        {
            return string.Format("uint2x4({0}, {1}, {2}, {3},  {4}, {5}, {6}, {7})", c0.x, c1.x, c2.x, c3.x, c0.y, c1.y, c2.y, c3.y);
        }

        /// <summary>Returns a string representation of the uint2x4 using a specified format and culture-specific format information.</summary>
        /// <param name="format">Format string to use during string formatting.</param>
        /// <param name="formatProvider">Format provider to use during string formatting.</param>
        /// <returns>String representation of the value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public string ToString(string format, IFormatProvider formatProvider)
        {
            return string.Format("uint2x4({0}, {1}, {2}, {3},  {4}, {5}, {6}, {7})", c0.x.ToString(format, formatProvider), c1.x.ToString(format, formatProvider), c2.x.ToString(format, formatProvider), c3.x.ToString(format, formatProvider), c0.y.ToString(format, formatProvider), c1.y.ToString(format, formatProvider), c2.y.ToString(format, formatProvider), c3.y.ToString(format, formatProvider));
        }

    }

    public static partial class math
    {
        /// <summary>Returns a uint2x4 matrix constructed from four uint2 vectors.</summary>
        /// <param name="c0">The matrix column c0 will be set to this value.</param>
        /// <param name="c1">The matrix column c1 will be set to this value.</param>
        /// <param name="c2">The matrix column c2 will be set to this value.</param>
        /// <param name="c3">The matrix column c3 will be set to this value.</param>
        /// <returns>uint2x4 constructed from arguments.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 uint2x4(uint2 c0, uint2 c1, uint2 c2, uint2 c3) { return new uint2x4(c0, c1, c2, c3); }

        /// <summary>Returns a uint2x4 matrix constructed from from 8 uint values given in row-major order.</summary>
        /// <param name="m00">The matrix at row 0, column 0 will be set to this value.</param>
        /// <param name="m01">The matrix at row 0, column 1 will be set to this value.</param>
        /// <param name="m02">The matrix at row 0, column 2 will be set to this value.</param>
        /// <param name="m03">The matrix at row 0, column 3 will be set to this value.</param>
        /// <param name="m10">The matrix at row 1, column 0 will be set to this value.</param>
        /// <param name="m11">The matrix at row 1, column 1 will be set to this value.</param>
        /// <param name="m12">The matrix at row 1, column 2 will be set to this value.</param>
        /// <param name="m13">The matrix at row 1, column 3 will be set to this value.</param>
        /// <returns>uint2x4 constructed from arguments.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 uint2x4(uint m00, uint m01, uint m02, uint m03,
                                      uint m10, uint m11, uint m12, uint m13)
        {
            return new uint2x4(m00, m01, m02, m03,
                               m10, m11, m12, m13);
        }

        /// <summary>Returns a uint2x4 matrix constructed from a single uint value by assigning it to every component.</summary>
        /// <param name="v">uint to convert to uint2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 uint2x4(uint v) { return new uint2x4(v); }

        /// <summary>Returns a uint2x4 matrix constructed from a single bool value by converting it to uint and assigning it to every component.</summary>
        /// <param name="v">bool to convert to uint2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 uint2x4(bool v) { return new uint2x4(v); }

        /// <summary>Return a uint2x4 matrix constructed from a bool2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">bool2x4 to convert to uint2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 uint2x4(bool2x4 v) { return new uint2x4(v); }

        /// <summary>Returns a uint2x4 matrix constructed from a single int value by converting it to uint and assigning it to every component.</summary>
        /// <param name="v">int to convert to uint2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 uint2x4(int v) { return new uint2x4(v); }

        /// <summary>Return a uint2x4 matrix constructed from a int2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">int2x4 to convert to uint2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 uint2x4(int2x4 v) { return new uint2x4(v); }

        /// <summary>Returns a uint2x4 matrix constructed from a single float value by converting it to uint and assigning it to every component.</summary>
        /// <param name="v">float to convert to uint2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 uint2x4(float v) { return new uint2x4(v); }

        /// <summary>Return a uint2x4 matrix constructed from a float2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">float2x4 to convert to uint2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 uint2x4(float2x4 v) { return new uint2x4(v); }

        /// <summary>Returns a uint2x4 matrix constructed from a single double value by converting it to uint and assigning it to every component.</summary>
        /// <param name="v">double to convert to uint2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 uint2x4(double v) { return new uint2x4(v); }

        /// <summary>Return a uint2x4 matrix constructed from a double2x4 matrix by componentwise conversion.</summary>
        /// <param name="v">double2x4 to convert to uint2x4</param>
        /// <returns>Converted value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2x4 uint2x4(double2x4 v) { return new uint2x4(v); }

        /// <summary>Return the uint4x2 transpose of a uint2x4 matrix.</summary>
        /// <param name="v">Value to transpose.</param>
        /// <returns>Transposed value.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint4x2 transpose(uint2x4 v)
        {
            return uint4x2(
                v.c0.x, v.c0.y,
                v.c1.x, v.c1.y,
                v.c2.x, v.c2.y,
                v.c3.x, v.c3.y);
        }

        /// <summary>Returns a uint hash code of a uint2x4 matrix.</summary>
        /// <param name="v">Matrix value to hash.</param>
        /// <returns>uint hash of the argument.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint hash(uint2x4 v)
        {
            return csum(v.c0 * uint2(0x9DF50593u, 0xF18EEB85u) +
                        v.c1 * uint2(0x9E19BFC3u, 0x8196B06Fu) +
                        v.c2 * uint2(0xD24EFA19u, 0x7D8048BBu) +
                        v.c3 * uint2(0x713BD06Fu, 0x753AD6ADu)) + 0xD19764C7u;
        }

        /// <summary>
        /// Returns a uint2 vector hash code of a uint2x4 matrix.
        /// When multiple elements are to be hashes together, it can more efficient to calculate and combine wide hash
        /// that are only reduced to a narrow uint hash at the very end instead of at every step.
        /// </summary>
        /// <param name="v">Matrix value to hash.</param>
        /// <returns>uint2 hash of the argument.</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static uint2 hashwide(uint2x4 v)
        {
            return (v.c0 * uint2(0xB5D0BF63u, 0xF9102C5Fu) +
                    v.c1 * uint2(0x9881FB9Fu, 0x56A1530Du) +
                    v.c2 * uint2(0x804B722Du, 0x738E50E5u) +
                    v.c3 * uint2(0x4FC93C25u, 0xCD0445A5u)) + 0xD2B90D9Bu;
        }

    }
}
