%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1425893226964488728
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8404550706546444740}
  - component: {fileID: 9057694317555835649}
  m_Layer: 0
  m_Name: Scroll
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8404550706546444740
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1425893226964488728}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -955.3655, y: -464.9797, z: 160.45508}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2680667830796240931}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &9057694317555835649
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1425893226964488728}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 50c7363fac4d24ae8a679e3e8f1fa838, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Label: Scroll
  m_HistorySamples: 100
  m_TimeWindow: 3
  m_Rect:
    serializedVersion: 2
    x: 350
    y: 0
    width: 150
    height: 150
  m_Visualization: 1
  m_ControlPath: <Mouse>/scroll
  m_ControlIndex: 0
--- !u!1 &2680667828813364402
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2680667828813364403}
  - component: {fileID: 2680667828813364412}
  m_Layer: 0
  m_Name: MMB
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2680667828813364403
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2680667828813364402}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -955.3655, y: -464.9797, z: 160.45508}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2680667830796240931}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2680667828813364412
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2680667828813364402}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 50c7363fac4d24ae8a679e3e8f1fa838, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Label: MMB
  m_HistorySamples: 100
  m_TimeWindow: 3
  m_Rect:
    serializedVersion: 2
    x: 85
    y: 175
    width: 50
    height: 50
  m_Visualization: 1
  m_ControlPath: <Mouse>/middleButton
  m_ControlIndex: 0
--- !u!1 &2680667828976453752
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2680667828976453753}
  - component: {fileID: 2680667828976453754}
  m_Layer: 0
  m_Name: LMB
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2680667828976453753
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2680667828976453752}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -955.3655, y: -464.9797, z: 160.45508}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2680667830796240931}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2680667828976453754
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2680667828976453752}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 50c7363fac4d24ae8a679e3e8f1fa838, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Label: LMB
  m_HistorySamples: 100
  m_TimeWindow: 3
  m_Rect:
    serializedVersion: 2
    x: 25
    y: 175
    width: 50
    height: 50
  m_Visualization: 1
  m_ControlPath: <Mouse>/leftButton
  m_ControlIndex: 0
--- !u!1 &2680667828982178413
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2680667828982178415}
  - component: {fileID: 2680667828982178414}
  m_Layer: 0
  m_Name: Events
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2680667828982178415
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2680667828982178413}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2680667830796240931}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2680667828982178414
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2680667828982178413}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 50c7363fac4d24ae8a679e3e8f1fa838, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Label: Events
  m_HistorySamples: 500
  m_TimeWindow: 3
  m_Rect:
    serializedVersion: 2
    x: 20
    y: 375
    width: 700
    height: 70
  m_Visualization: 4
  m_ControlPath: <Mouse>
  m_ControlIndex: 0
--- !u!1 &2680667830654736508
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2680667830654736509}
  - component: {fileID: 2680667830654736510}
  m_Layer: 0
  m_Name: Lag
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2680667830654736509
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2680667830654736508}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2680667830796240931}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2680667830654736510
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2680667830654736508}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 50c7363fac4d24ae8a679e3e8f1fa838, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Label: Lag (ms)
  m_HistorySamples: 500
  m_TimeWindow: 3
  m_Rect:
    serializedVersion: 2
    x: 20
    y: 460
    width: 700
    height: 70
  m_Visualization: 6
  m_ControlPath: <Mouse>
  m_ControlIndex: 0
--- !u!1 &2680667830751945958
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2680667830751945952}
  - component: {fileID: 2680667830751945959}
  m_Layer: 0
  m_Name: Position
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2680667830751945952
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2680667830751945958}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -955.3655, y: -464.9797, z: 160.45508}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2680667830796240931}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2680667830751945959
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2680667830751945958}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 50c7363fac4d24ae8a679e3e8f1fa838, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Label: Position
  m_HistorySamples: 100
  m_TimeWindow: 3
  m_Rect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 150
    height: 150
  m_Visualization: 1
  m_ControlPath: <Mouse>/position
  m_ControlIndex: 0
--- !u!1 &2680667830796240929
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2680667830796240931}
  - component: {fileID: 2680667830796240930}
  m_Layer: 0
  m_Name: MouseVis
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2680667830796240931
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2680667830796240929}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 955.3655, y: 464.9797, z: -160.45508}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 2680667830751945952}
  - {fileID: 2680667830847656253}
  - {fileID: 8404550706546444740}
  - {fileID: 2680667828976453753}
  - {fileID: 2680667828813364403}
  - {fileID: 2680667830881149239}
  - {fileID: 2680667828982178415}
  - {fileID: 2680667830654736509}
  - {fileID: 2680667830892693960}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2680667830796240930
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2680667830796240929}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 50c7363fac4d24ae8a679e3e8f1fa838, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Label: 
  m_HistorySamples: 500
  m_TimeWindow: 3
  m_Rect:
    serializedVersion: 2
    x: 15
    y: 10
    width: 300
    height: 30
  m_Visualization: 0
  m_ControlPath: 
  m_ControlIndex: 0
--- !u!1 &2680667830847656252
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2680667830847656253}
  - component: {fileID: 2680667830847656254}
  m_Layer: 0
  m_Name: Delta
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2680667830847656253
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2680667830847656252}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -955.3655, y: -464.9797, z: 160.45508}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2680667830796240931}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2680667830847656254
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2680667830847656252}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 50c7363fac4d24ae8a679e3e8f1fa838, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Label: Delta
  m_HistorySamples: 100
  m_TimeWindow: 3
  m_Rect:
    serializedVersion: 2
    x: 175
    y: 0
    width: 150
    height: 150
  m_Visualization: 1
  m_ControlPath: <Mouse>/delta
  m_ControlIndex: 0
--- !u!1 &2680667830881149238
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2680667830881149239}
  - component: {fileID: 2680667830881149232}
  m_Layer: 0
  m_Name: RMB
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2680667830881149239
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2680667830881149238}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -955.3655, y: -464.9797, z: 160.45508}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2680667830796240931}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2680667830881149232
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2680667830881149238}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 50c7363fac4d24ae8a679e3e8f1fa838, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Label: RMB
  m_HistorySamples: 100
  m_TimeWindow: 3
  m_Rect:
    serializedVersion: 2
    x: 145
    y: 175
    width: 50
    height: 50
  m_Visualization: 1
  m_ControlPath: <Mouse>/rightButton
  m_ControlIndex: 0
--- !u!1 &2680667830892693967
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2680667830892693960}
  - component: {fileID: 2680667830892693961}
  m_Layer: 0
  m_Name: Bytes
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2680667830892693960
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2680667830892693967}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2680667830796240931}
  m_RootOrder: 8
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2680667830892693961
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2680667830892693967}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 50c7363fac4d24ae8a679e3e8f1fa838, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Label: Bytes
  m_HistorySamples: 500
  m_TimeWindow: 3
  m_Rect:
    serializedVersion: 2
    x: 20
    y: 550
    width: 700
    height: 70
  m_Visualization: 7
  m_ControlPath: <Mouse>
  m_ControlIndex: 0
