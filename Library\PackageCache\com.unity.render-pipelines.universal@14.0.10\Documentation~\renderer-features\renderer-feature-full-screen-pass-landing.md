# Full Screen Pass Renderer Feature

A Full Screen Pass Renderer Feature lets you render a full screen effect, such as a vignette or a blur, using a Material.

|Page|Description|
|-|-|
|[How to create a custom post-processing effect](../post-processing/post-processing-custom-effect-low-code.md)|An example of using the Full Screen Pass Renderer Feature to create a grayscale custom post-processing effect..|
|[Full Screen Pass Renderer Feature reference](renderer-feature-full-screen-pass.md)|Reference for the Full Screen Pass Renderer Feature.|
