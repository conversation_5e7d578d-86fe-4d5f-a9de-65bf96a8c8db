%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 337831424, guid: 6a10b2909283487f913b00d94cd3faf5, type: 3}
  m_Name: Timeline trigger cube
  m_EditorClassIdentifier: 
  m_Id: 0
  m_NextId: 6
  m_Tracks:
  - {fileID: 114142723998643226}
  - {fileID: 114293287003459442}
  - {fileID: 114716226793156428}
  m_FixedDuration: 0
  m_EditorSettings:
    fps: 60
  m_UpdateMode: 0
  m_ParameterName: 
  m_DurationMode: 0
--- !u!74 &74859400344553778
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Recorded
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0.3
        value: {x: 0, y: -2.672226, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.75
        value: {x: 0, y: -1.5991572, z: 0}
        inSlope: {x: 0, y: 3.770991, z: 0}
        outSlope: {x: 0, y: 3.770991, z: 0}
        tangentMode: 0
      - serializedVersion: 2
        time: 1.3166667
        value: {x: 0, y: -0.0822258, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      - serializedVersion: 2
        time: 26.2
        value: {x: 0, y: -0.0822258, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      - serializedVersion: 2
        time: 27.133333
        value: {x: 0, y: -2.5322266, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 1
      script: {fileID: 0}
      typeID: 4
      customType: 0
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 0
      attribute: 1
      script: {fileID: 0}
      typeID: 95
      customType: 8
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 0
      attribute: 0
      script: {fileID: 0}
      typeID: 95
      customType: 8
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 0
      attribute: 2
      script: {fileID: 0}
      typeID: 95
      customType: 8
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 27.133333
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0.3
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.75
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 26.2
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 27.133333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.x
    path: 
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0.3
        value: -2.672226
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 1.3166667
        value: -0.0822258
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 26.2
        value: -0.0822258
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 27.133333
        value: -2.5322266
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.y
    path: 
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0.3
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.75
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 26.2
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 27.133333
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.z
    path: 
    classID: 4
    script: {fileID: 0}
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 1
  m_HasMotionFloatCurves: 0
  m_GenerateMotionCurves: 1
  m_Events: []
--- !u!114 &114123743562478488
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fb794a295e73545af71bcdb7375791, type: 3}
  m_Name: CinemachineShot
  m_EditorClassIdentifier: 
  VirtualCamera:
    exposedName: e7b2903ec6b103d41a31066181a3e86e
    defaultValue: {fileID: 0}
--- !u!114 &114142723998643226
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -1544527758, guid: 6a10b2909283487f913b00d94cd3faf5, type: 3}
  m_Name: Track Group
  m_EditorClassIdentifier: 
  m_Locked: 0
  m_Muted: 0
  m_Soloed: 0
  m_Height: 0
  m_CustomPlayableFullTypename: 
  m_AnimClip: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children: []
  m_Clips: []
--- !u!114 &114293287003459442
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 05acc715f855ced458d76ee6f8ac6c61, type: 3}
  m_Name: Cinemachine Track
  m_EditorClassIdentifier: 
  m_Locked: 0
  m_Muted: 0
  m_Soloed: 0
  m_Height: 0
  m_CustomPlayableFullTypename: 
  m_AnimClip: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children: []
  m_Clips:
  - m_Start: 0.016666666666666663
    m_ClipIn: 0
    m_Asset: {fileID: 114680635997628290}
    m_UnderlyingAsset: {fileID: 114680635997628290}
    m_Duration: 0.4333333333333339
    m_ParentID: 0
    m_TimeScale: 1
    m_BlendCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_ParentTrack: {fileID: 114293287003459442}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: 0.3666666666666673
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - serializedVersion: 2
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - serializedVersion: 2
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: CM FreeLook
  - m_Start: 0.08333333333333325
    m_ClipIn: 0
    m_Asset: {fileID: 114123743562478488}
    m_UnderlyingAsset: {fileID: 114123743562478488}
    m_Duration: 29.1
    m_ParentID: 0
    m_TimeScale: 1
    m_BlendCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_ParentTrack: {fileID: 114293287003459442}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 0.3666666666666673
    m_BlendOutDuration: 2.1833333333333336
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - serializedVersion: 2
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - serializedVersion: 2
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: CM FreeLook near
  - m_Start: 27
    m_ClipIn: 0
    m_Asset: {fileID: 114395940253765890}
    m_UnderlyingAsset: {fileID: 114395940253765890}
    m_Duration: 3.2
    m_ParentID: 0
    m_TimeScale: 1
    m_BlendCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_ParentTrack: {fileID: 114293287003459442}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: 2.1833333333333336
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - serializedVersion: 2
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - serializedVersion: 2
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 0
    m_PreExtrapolationMode: 0
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: CM FreeLook
--- !u!114 &114395940253765890
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fb794a295e73545af71bcdb7375791, type: 3}
  m_Name: CinemachineShot
  m_EditorClassIdentifier: 
  VirtualCamera:
    exposedName: a4c7c1a597d7e6647abdb80a72f9d9ac
    defaultValue: {fileID: 0}
--- !u!114 &114680635997628290
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90fb794a295e73545af71bcdb7375791, type: 3}
  m_Name: CinemachineShot
  m_EditorClassIdentifier: 
  VirtualCamera:
    exposedName: 5d984d59c906b564eb255b9fe8688e4d
    defaultValue: {fileID: 0}
--- !u!114 &114716226793156428
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1467732076, guid: 6a10b2909283487f913b00d94cd3faf5, type: 3}
  m_Name: Animation Track
  m_EditorClassIdentifier: 
  m_Locked: 0
  m_Muted: 0
  m_Soloed: 0
  m_Height: 0
  m_CustomPlayableFullTypename: 
  m_AnimClip: {fileID: 74859400344553778}
  m_Parent: {fileID: 11400000}
  m_Children: []
  m_Clips: []
  m_OpenClipPreExtrapolation: 1
  m_OpenClipPostExtrapolation: 1
  m_OpenClipOffsetPosition: {x: -9.99, y: 20.182226, z: -25.06}
  m_OpenClipOffsetRotation: {x: 0, y: 0.7071068, z: 0, w: 0.7071068}
  m_OpenClipTimeOffset: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
