# Scriptable Renderer Features

Scriptable Renderer Features are components you can add to a renderer to alter how URP renders a project.

|Page|Description|
|-|-|
|[Introduction to Scriptable Renderer Features](intro-to-scriptable-renderer-features.md)|What a Scriptable Renderer Feature is, and how a Scriptable Renderer Feature relates to a Scriptable Render Pass.|
|[Inject a custom pass using a Scriptable Renderer Feature](inject-a-pass-using-a-scriptable-renderer-feature.md)|Create a Scriptable Renderer Feature, add it to the Universal Renderer, and enqueue a render pass.|
|[Apply a Scriptable Renderer Feature to a specific camera type](apply-scriptable-feature-to-specific-camera.md)|Control which cameras the effect of a Scriptable Renderer Feature applies to.|
|[Example of a complete Scriptable Renderer Feature](../create-custom-renderer-feature.md)|An example of a complete Scriptable Renderer Feature with a Scriptable Render Pass that creates a blur effect.|
