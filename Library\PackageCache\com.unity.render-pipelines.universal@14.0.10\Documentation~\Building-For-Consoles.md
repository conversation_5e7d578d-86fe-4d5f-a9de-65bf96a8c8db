# Building your Project for Closed platforms

If you have a license to develop games for Closed platforms that require you to meet the confidentiality and legal agreements of the platform provider, then see the relevant developer forums for a link to the console specific render pipeline package.

## Platform package installation

Closed platform packages are not available in the package registry or the Package Manager.

To install a Closed platform package:

1. Download the package from the relevant platform developer forum.
2. Use the Package Manager to install the package locally. For information on how to install packages locally, see [Installing a local package](https://docs.unity3d.com/Manual/upm-ui-local.html).
