# Create a project with URP

![URP 3D Sample](Images/AssetShots/Beauty/Template.png)

The Unity Hub contains the following templates that let you create a pre-configured Universal Render Pipeline (URP) project.

| **Template** | **Description** |
|---|---|
| 2D URP | This is an empty project for 2D applications. URP is pre-configured with 2D renderer. |
| 3D (URP) | This is an empty project for 3D applications. URP is pre-configured with 3D renderer. |
| 3D&#160;Sample&#160;Scenes (URP) | This sample contains four environments that showcase the versatility, scalability, and customizability of URP. The project demonstrates different art styles, rendering paths, and scene complexities. Each scene shows you how to tailor a project to different platforms, from mobile and untethered devices to high-end PCs and consoles. |

To create a new project using a URP template:

1. Open the Unity Hub.

2. Select the **Projects** tab, then select **New project**.

3. Select one of the URP templates.

4. Fill in the **Project settings** fields and select **Create project**. Unity creates a new pre-configured URP project.
