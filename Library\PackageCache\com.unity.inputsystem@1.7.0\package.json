{"name": "com.unity.inputsystem", "displayName": "Input System", "version": "1.7.0", "unity": "2019.4", "description": "A new input system which can be used as a more extensible and customizable alternative to Unity's classic input system in UnityEngine.Input.", "keywords": ["input", "events", "keyboard", "mouse", "gamepad", "touch", "vr", "xr"], "dependencies": {"com.unity.modules.uielements": "1.0.0"}, "_upm": {"changelog": "### Added\n- Preliminary support for visionOS.\n- Show a list of `Derived Bindings` underneath the Binding Path editor to show all controls that matched.\n\n### Changed\n- Changed the `InputAction` constructors so it generates an ID for the action and the optional binding parameter. This is intended to improve the serialization of input actions on behaviors when created through API when the property drawer in the Inspector window does not have a chance to generate an ID.\n\n### Fixed\n- Fixed missing prefab errors in InputDeviceTester project ([case ISXB-420](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-420)).\n- Fixed serialization migration in the Tracked Pose Driver component causing bindings to clear when prefabs are used in some cases ([case ISXB-512](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-512), [case ISXB-521](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-521)).\n- Fixed Tracked Pose Driver to use `Transform.SetLocalPositionAndRotation` when available to improve performance. Based on the user contribution from [DevDunk](https://forum.unity.com/members/devdunk.4432119/) in a [forum post](https://forum.unity.com/threads/more-performant-tracked-pose-driver-solution-included.1462691).\n- Fixed the `Clone` methods of `InputAction` and `InputActionMap` so it copies the Initial State Check flag (`InputAction.wantsInitialStateCheck`) of input actions.\n- Fixed the \"Release tests throws exception in InputSystem\" bug ([case ISXB-581](https://issuetracker.unity3d.com/issues/release-tests-fail-when-input-system-package-is-installed)).\n- Fixed issues with generating Precompiled Layouts for devices which are not defined in a namespace\n- Fixed an issue where some controls like `QuaternionControl` could not be included in a Precompiled Layout because the generated code could not access a setter on child control properties."}, "upmCi": {"footprint": "2884559d44178f24851e34d72f68fad583427ed1"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.inputsystem@1.7/manual/index.html", "repository": {"url": "https://github.com/Unity-Technologies/InputSystem.git", "type": "git", "revision": "e29989176f3a8473ba30854e4bfae953083eba28"}, "samples": [{"displayName": "Custom Binding Composite", "description": "Shows how to implement a custom composite binding.", "path": "Samples~/CustomComposite"}, {"displayName": "Custom Device", "description": "Shows how to implement a custom input device.", "path": "Samples~/CustomDevice"}, {"displayName": "Custom Device Usages", "description": "Shows how to tag devices with custom usage strings that can be used, for example, to distinguish multiple instances of the same type of device (e.g. 'Gamepad') based on how the device is used (e.g. 'Player1' vs 'Player2' or 'LeftHand' vs 'RightHand').", "path": "Samples~/CustomDeviceUsages"}, {"displayName": "Gamepad Mouse Cursor", "description": "An example that shows how to use the gamepad for driving a mouse cursor for use with UIs.", "path": "Samples~/GamepadMouseCursor"}, {"displayName": "In-Game Hints", "description": "Demonstrates how to create in-game hints in the UI which reflect current bindings and active control schemes.", "path": "Samples~/InGameHints"}, {"displayName": "InputDeviceTester", "description": "A scene containing UI to visualize the controls on various supported input devices.", "path": "Samples~/InputDeviceTester"}, {"displayName": "Input Recorder", "description": "Shows how to capture and replay input events. Also useful by itself to debug input event sequences.", "path": "Samples~/InputRecorder"}, {"displayName": "On-Screen Controls", "description": "Demonstrates a simple setup for an on-screen joystick.", "path": "Samples~/OnScreenControls"}, {"displayName": "Rebinding UI", "description": "An example UI component that demonstrates how to create UI for rebinding actions.", "path": "Samples~/RebindingUI"}, {"displayName": "Simple Demo", "description": "A walkthrough of a simple character controller that demonstrates several techniques for working with the input system. See the README.md file in the sample for details.", "path": "Samples~/SimpleDemo"}, {"displayName": "Simple Multiplayer", "description": "Demonstrates how to set up a simple local multiplayer scenario.", "path": "Samples~/SimpleMultiplayer"}, {"displayName": "Touch Samples", "description": "A series of sample scenes for using touch input with the Input System package. This sample is not actually part of the package, but needs to be downloaded.", "path": "Samples~/TouchSamples"}, {"displayName": "UI vs. Game Input", "description": "An example that shows how to deal with ambiguities that may arrise when overlaying interactive UI elements on top of a game scene.", "path": "Samples~/UIvsGameInput"}, {"displayName": "Unity Remote", "description": "An example with a simple scene for trying out the Unity Remote app.", "path": "Samples~/UnityRemote"}, {"displayName": "Visualizers", "description": "Several example visualizations of input controls/devices and input actions.", "path": "Samples~/Visualizers"}]}