{"Exceptions": [{"ValidationTest": "Package Lifecycle Validation", "ExceptionError": "Package com.unity.render-pipelines.universal@14.0.10 depends on package com.unity.burst@1.5.0 which is in an invalid track for release purposes. Release versions can only depend on Release versions.", "PackageVersion": "14.0.10"}, {"ValidationTest": "API Updater Configuration Validation", "PackageVersion": "14.0.10"}]}