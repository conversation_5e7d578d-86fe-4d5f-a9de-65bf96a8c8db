{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "9c15011818df438a8dbc24606e636bd7",
    "m_Properties": [],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "fa9c9d418aec47cdacae32ee8be21bde"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "31163bef56444b2bb2730c87592343a6"
        },
        {
            "m_Id": "8e4aabe5f04143f192130f148fee973c"
        },
        {
            "m_Id": "888067a123ef4271ad3ce1c73bd13f08"
        },
        {
            "m_Id": "b4f20e95e4c2461b967bb07fd4bb3045"
        },
        {
            "m_Id": "22d639475ce34ff49db06e0c903cf2f5"
        },
        {
            "m_Id": "5109f517424145efbb817ceb9f16940e"
        },
        {
            "m_Id": "1d3dc8d919e3440f8ff7b830f44f33a3"
        },
        {
            "m_Id": "d709141fb9a04f0a974e4cc719dffac1"
        },
        {
            "m_Id": "abda54a6d3564acba818758362441b8f"
        },
        {
            "m_Id": "4e52e8adae7f427b970836308002d69c"
        },
        {
            "m_Id": "41bbda71912a4f16b2e578216f04cbeb"
        },
        {
            "m_Id": "b98f0a7a54e14b77808f7e02d549330c"
        },
        {
            "m_Id": "d0dbf532a1504cb6b7f9adb0df870768"
        },
        {
            "m_Id": "9c278368cc094f2c8d08950cbb318f97"
        },
        {
            "m_Id": "7e677b345d7843e2a598c65727114f17"
        },
        {
            "m_Id": "5d5b84a34a5b4583878dfe8ac4c660c8"
        },
        {
            "m_Id": "54ce9f544fc44e9fbed8da20dbf18edc"
        },
        {
            "m_Id": "a4e6f7bb865c4a7ab4a4c7c3b670200f"
        },
        {
            "m_Id": "1f8c6dd4296b42b0b088248ad820bcd6"
        },
        {
            "m_Id": "e4690df9e6b144ce99a7a76217a3ca14"
        },
        {
            "m_Id": "11492f5b290d4fcba5292b59d6e58b2b"
        },
        {
            "m_Id": "b58f7ba36f534794a0f1a136cfa34980"
        },
        {
            "m_Id": "16235f46186041dcafeea88e1ead7b88"
        }
    ],
    "m_GroupDatas": [
        {
            "m_Id": "1fcc60e54a8942ffa01feb61fdaaedf2"
        },
        {
            "m_Id": "5544d5333c044805a12e946fc9ff7eae"
        },
        {
            "m_Id": "55d0399c24694368a093a5739980c3a4"
        }
    ],
    "m_StickyNoteDatas": [
        {
            "m_Id": "e60b3c75ada941319ca666bc9169663a"
        },
        {
            "m_Id": "e07f5a8eb4e543ac8ad48dbe305026ca"
        },
        {
            "m_Id": "6b9afb05b82f47eb8de9e6418d3606d6"
        },
        {
            "m_Id": "cadbbc15f1ec4cdaae79f2d607704ffe"
        },
        {
            "m_Id": "29ae7a3a51b34ca3b7063c07f32c0e8e"
        },
        {
            "m_Id": "6e8f0ec098994a579af28959841408d9"
        },
        {
            "m_Id": "bcbea155d7a240bda3f491040432cd50"
        },
        {
            "m_Id": "8ad0f4153d4c411db8a3c2d06a13f122"
        },
        {
            "m_Id": "50a48e9098e14c618a6141e515eb2e79"
        },
        {
            "m_Id": "facfdedbb7f84a9b90b829b0ba59a835"
        },
        {
            "m_Id": "37eaae17c4164314b861da6d01cd0cc0"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "11492f5b290d4fcba5292b59d6e58b2b"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "e4690df9e6b144ce99a7a76217a3ca14"
                },
                "m_SlotId": 4
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "1d3dc8d919e3440f8ff7b830f44f33a3"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "7e677b345d7843e2a598c65727114f17"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "1f8c6dd4296b42b0b088248ad820bcd6"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "e4690df9e6b144ce99a7a76217a3ca14"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "22d639475ce34ff49db06e0c903cf2f5"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "54ce9f544fc44e9fbed8da20dbf18edc"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "4e52e8adae7f427b970836308002d69c"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "9c278368cc094f2c8d08950cbb318f97"
                },
                "m_SlotId": 4
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "5109f517424145efbb817ceb9f16940e"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1d3dc8d919e3440f8ff7b830f44f33a3"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "54ce9f544fc44e9fbed8da20dbf18edc"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "7e677b345d7843e2a598c65727114f17"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "7e677b345d7843e2a598c65727114f17"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "5d5b84a34a5b4583878dfe8ac4c660c8"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "a4e6f7bb865c4a7ab4a4c7c3b670200f"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1f8c6dd4296b42b0b088248ad820bcd6"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "abda54a6d3564acba818758362441b8f"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "d0dbf532a1504cb6b7f9adb0df870768"
                },
                "m_SlotId": 4
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "d709141fb9a04f0a974e4cc719dffac1"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "b98f0a7a54e14b77808f7e02d549330c"
                },
                "m_SlotId": 4
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": [
            {
                "m_Id": "31163bef56444b2bb2730c87592343a6"
            },
            {
                "m_Id": "8e4aabe5f04143f192130f148fee973c"
            },
            {
                "m_Id": "888067a123ef4271ad3ce1c73bd13f08"
            }
        ]
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 200.0
        },
        "m_Blocks": [
            {
                "m_Id": "b4f20e95e4c2461b967bb07fd4bb3045"
            },
            {
                "m_Id": "b58f7ba36f534794a0f1a136cfa34980"
            },
            {
                "m_Id": "16235f46186041dcafeea88e1ead7b88"
            }
        ]
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Shader Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": ""
    },
    "m_ActiveTargets": [
        {
            "m_Id": "70c304d35f714e15b468c8890084d87e"
        },
        {
            "m_Id": "e516eb92d4e4422b85630d3f1f5af363"
        },
        {
            "m_Id": "83b3695d3f70469f830e8e12680a2e69"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInUnlitSubTarget",
    "m_ObjectId": "033c4333ef1e4c6b90590dc59d085695"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "055aaf8980ed42faaaf27f22c01596ca",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 6.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "059a06bf36f8452b9a42cfdebee82bcc",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "071f2278d2e8412eba543c809ccd7e1e",
    "m_Id": 4,
    "m_DisplayName": "LOD",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "LOD",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "076e00a908064543a6d297b813382f13",
    "m_Id": 2,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "082e992176184b519dd45fb39ee2a97a",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "0c5e559dc9604aae9194c599977c7489"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "0c6b5cda79ee4d23a8d6193daedbb005",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "0e59a378923a4838af0f1ca26cabfe59",
    "m_Id": 2,
    "m_DisplayName": "Dir",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Dir",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1Node",
    "m_ObjectId": "11492f5b290d4fcba5292b59d6e58b2b",
    "m_Group": {
        "m_Id": "55d0399c24694368a093a5739980c3a4"
    },
    "m_Name": "Float",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1068.5001220703125,
            "y": 795.5000610351563,
            "width": 125.5,
            "height": 76.99993896484375
        }
    },
    "m_Slots": [
        {
            "m_Id": "055aaf8980ed42faaaf27f22c01596ca"
        },
        {
            "m_Id": "be3a6d9dba394f9bbb5381999d4811cb"
        }
    ],
    "synonyms": [
        "Vector 1",
        "1",
        "v1",
        "vec1",
        "scalar"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": 0.0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "16235f46186041dcafeea88e1ead7b88",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Alpha",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "dc42996e045144c99844c9616311bb69"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Alpha"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "1c4f0d9be53940d09c0cf8765d6a2bb6",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "1cd88d0e2a8b44f9ad8099586b9ea21a",
    "m_Id": 0,
    "m_DisplayName": "Base Color",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BaseColor",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.5,
        "y": 0.5,
        "z": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.5,
        "g": 0.5,
        "b": 0.5,
        "a": 1.0
    }
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.ShaderGraph.TransformNode",
    "m_ObjectId": "1d3dc8d919e3440f8ff7b830f44f33a3",
    "m_Group": {
        "m_Id": "1fcc60e54a8942ffa01feb61fdaaedf2"
    },
    "m_Name": "Transform",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1326.5001220703125,
            "y": 248.50001525878907,
            "width": 212.5,
            "height": 156.00001525878907
        }
    },
    "m_Slots": [
        {
            "m_Id": "affe2b61660e4be596939694d34e4ee4"
        },
        {
            "m_Id": "72466d3ddaba4681b08bd3fc6e04f6f9"
        }
    ],
    "synonyms": [
        "world",
        "tangent",
        "object",
        "view",
        "screen",
        "convert"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Conversion": {
        "from": 3,
        "to": 2
    },
    "m_ConversionType": 2,
    "m_Normalize": true
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.ShaderGraph.TransformNode",
    "m_ObjectId": "1f8c6dd4296b42b0b088248ad820bcd6",
    "m_Group": {
        "m_Id": "55d0399c24694368a093a5739980c3a4"
    },
    "m_Name": "Transform",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1304.0001220703125,
            "y": 678.0,
            "width": 212.5,
            "height": 156.00006103515626
        }
    },
    "m_Slots": [
        {
            "m_Id": "fb5ac61963f546c7bf839562e619e994"
        },
        {
            "m_Id": "059a06bf36f8452b9a42cfdebee82bcc"
        }
    ],
    "synonyms": [
        "world",
        "tangent",
        "object",
        "view",
        "screen",
        "convert"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Conversion": {
        "from": 3,
        "to": 2
    },
    "m_ConversionType": 2,
    "m_Normalize": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "1fcc60e54a8942ffa01feb61fdaaedf2",
    "m_Title": "Reflections",
    "m_Position": {
        "x": -1669.0001220703125,
        "y": 59.5000114440918
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "20a954ba790e49b7bd107581a7667323",
    "m_Id": 7,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.ViewDirectionNode",
    "m_ObjectId": "22d639475ce34ff49db06e0c903cf2f5",
    "m_Group": {
        "m_Id": "1fcc60e54a8942ffa01feb61fdaaedf2"
    },
    "m_Name": "View Direction",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1426.5001220703125,
            "y": 118.00000762939453,
            "width": 206.0,
            "height": 130.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "0c6b5cda79ee4d23a8d6193daedbb005"
        }
    ],
    "synonyms": [
        "eye direction"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "239e2e6c1017434a885cadf7216207a4",
    "m_Id": 4,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "26910588cdbc48778826b3211199e2ca",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "28e5e6747d4c498d8bb8d1dc31379c38",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "29ae7a3a51b34ca3b7063c07f32c0e8e",
    "m_Title": "",
    "m_Content": "If your cubemap has mip levels, you can use the LOD input to select the mip level and control the sharpness or blurriness of the reflections.  A LOD value of 0 will be sharp and a higher LOD value will be blurry.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -465.0000305175781,
        "y": 118.50001525878906,
        "width": 200.0,
        "height": 125.00001525878906
    },
    "m_Group": {
        "m_Id": "5544d5333c044805a12e946fc9ff7eae"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "2cd0e92320a84407b4f8069d9af78d70",
    "m_Id": 0,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "31163bef56444b2bb2730c87592343a6",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "e1e2846278a246c8a274b3e174bda3c0"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Position"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "37eaae17c4164314b861da6d01cd0cc0",
    "m_Title": "",
    "m_Content": "Using a high LOD level and a world space normal, you can get a result that approximates ambient diffuse lighting.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1184.5001220703125,
        "y": 896.5000610351563,
        "width": 159.0,
        "height": 100.00006103515625
    },
    "m_Group": {
        "m_Id": "55d0399c24694368a093a5739980c3a4"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "38dfcca2aab248c9bc7e259a7c9ab36f",
    "m_Id": 7,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "3a780e6309cd4965a1c594318517ce90",
    "m_Id": 2,
    "m_DisplayName": "Dir",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Dir",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CubemapInputMaterialSlot",
    "m_ObjectId": "3d6dd12c661647028e6d4d5deee923f6",
    "m_Id": 1,
    "m_DisplayName": "Cube",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cube",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Cubemap": {
        "m_SerializedCubemap": "{\"cubemap\":{\"fileID\":8900000,\"guid\":\"cd7fb2f39e535e64d985f05bc8b6061b\",\"type\":3}}",
        "m_Guid": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleRawCubemapNode",
    "m_ObjectId": "41bbda71912a4f16b2e578216f04cbeb",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Sample Cubemap",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1137.5001220703125,
            "y": -146.0,
            "width": 172.00006103515626,
            "height": 166.00001525878907
        }
    },
    "m_Slots": [
        {
            "m_Id": "d5ec046d0d1541f7b7c29252214c6722"
        },
        {
            "m_Id": "e858f77ee20447b9b376b4e584ea6227"
        },
        {
            "m_Id": "b419bb963e574197957daacdea52b87b"
        },
        {
            "m_Id": "797668e2d76749188f60301405f01d57"
        },
        {
            "m_Id": "071f2278d2e8412eba543c809ccd7e1e"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "434cecf8b45a46fc93324c24f9c5278e",
    "m_Id": 2,
    "m_DisplayName": "Dir",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Dir",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "46a574f3c9494995a09683f3aad797ad",
    "m_Id": 4,
    "m_DisplayName": "LOD",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "LOD",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "46dccd3f90c4441ebcfce0d45cbddc5c",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 6.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1Node",
    "m_ObjectId": "4e52e8adae7f427b970836308002d69c",
    "m_Group": {
        "m_Id": "5544d5333c044805a12e946fc9ff7eae"
    },
    "m_Name": "Float",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -606.5000610351563,
            "y": 808.0,
            "width": 125.50003051757813,
            "height": 77.00006103515625
        }
    },
    "m_Slots": [
        {
            "m_Id": "46dccd3f90c4441ebcfce0d45cbddc5c"
        },
        {
            "m_Id": "26910588cdbc48778826b3211199e2ca"
        }
    ],
    "synonyms": [
        "Vector 1",
        "1",
        "v1",
        "vec1",
        "scalar"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": 0.0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "50627f1b982d4f609534584253983330",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "50a48e9098e14c618a6141e515eb2e79",
    "m_Title": "",
    "m_Content": "It's important to understand that the LOD value is the mip level you want to sample.  This means that different resolutions of cube map will have a different number of mip levels.\n\nIf you want consistent results from the LOD input, ensure that all of your cube maps are the same resolution.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -597.5000610351563,
        "y": 1094.0001220703125,
        "width": 336.6590270996094,
        "height": 114.431884765625
    },
    "m_Group": {
        "m_Id": "5544d5333c044805a12e946fc9ff7eae"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleTexture2DNode",
    "m_ObjectId": "5109f517424145efbb817ceb9f16940e",
    "m_Group": {
        "m_Id": "1fcc60e54a8942ffa01feb61fdaaedf2"
    },
    "m_Name": "Sample Texture 2D",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1534.5001220703125,
            "y": 248.50001525878907,
            "width": 208.0,
            "height": 338.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "edf551a3065340e29a163ec12422b4a1"
        },
        {
            "m_Id": "239e2e6c1017434a885cadf7216207a4"
        },
        {
            "m_Id": "64e4ac9f068740c2bbb6631e090a6144"
        },
        {
            "m_Id": "bfdeee15f41144d9b0d60067ee6da29a"
        },
        {
            "m_Id": "20a954ba790e49b7bd107581a7667323"
        },
        {
            "m_Id": "6d62f8fc0cf14d7e98640bfb64adea19"
        },
        {
            "m_Id": "076e00a908064543a6d297b813382f13"
        },
        {
            "m_Id": "6a8c2f5320fc4bf9bc1c8e29d52bee15"
        }
    ],
    "synonyms": [
        "tex2d"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_TextureType": 1,
    "m_NormalMapSpace": 0,
    "m_EnableGlobalMipBias": true,
    "m_MipSamplingMode": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.OneMinusNode",
    "m_ObjectId": "54ce9f544fc44e9fbed8da20dbf18edc",
    "m_Group": {
        "m_Id": "1fcc60e54a8942ffa01feb61fdaaedf2"
    },
    "m_Name": "One Minus",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1220.5001220703125,
            "y": 118.00000762939453,
            "width": 131.5,
            "height": 93.99999237060547
        }
    },
    "m_Slots": [
        {
            "m_Id": "50627f1b982d4f609534584253983330"
        },
        {
            "m_Id": "ccdee7f5de2542bb829e18452d40718b"
        }
    ],
    "synonyms": [
        "complement",
        "invert",
        "opposite"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "5544d5333c044805a12e946fc9ff7eae",
    "m_Title": "LOD Input",
    "m_Position": {
        "x": -631.5001220703125,
        "y": 60.00000762939453
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "55d0399c24694368a093a5739980c3a4",
    "m_Title": "Fake Ambient Lighting",
    "m_Position": {
        "x": -1537.0001220703125,
        "y": 619.4999389648438
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleRawCubemapNode",
    "m_ObjectId": "5d5b84a34a5b4583878dfe8ac4c660c8",
    "m_Group": {
        "m_Id": "1fcc60e54a8942ffa01feb61fdaaedf2"
    },
    "m_Name": "Sample Cubemap",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -915.5000610351563,
            "y": 118.00000762939453,
            "width": 208.0,
            "height": 277.9999694824219
        }
    },
    "m_Slots": [
        {
            "m_Id": "f3a0185ca7984b518cd3aa9db16d1cb0"
        },
        {
            "m_Id": "ba8ffabdb83447349343ad3937a47251"
        },
        {
            "m_Id": "0e59a378923a4838af0f1ca26cabfe59"
        },
        {
            "m_Id": "9e6fe57e492d4b7aa28de4f72dd73458"
        },
        {
            "m_Id": "46a574f3c9494995a09683f3aad797ad"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "64e4ac9f068740c2bbb6631e090a6144",
    "m_Id": 5,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "66a462cda2894370922e6e28f1117fe9",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "6758148b86fa4a01abd1de373084328a",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "6a8c2f5320fc4bf9bc1c8e29d52bee15",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "6b9afb05b82f47eb8de9e6418d3606d6",
    "m_Title": "",
    "m_Content": "Notice that we have to transform the normal map from Tangent space to World space before using it with the Sample Cubemap node.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1320.5001220703125,
        "y": 406.5000305175781,
        "width": 200.0,
        "height": 100.50003051757813
    },
    "m_Group": {
        "m_Id": "1fcc60e54a8942ffa01feb61fdaaedf2"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "6ca74f4679244b4f813d1ebce494f04f",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "6d62f8fc0cf14d7e98640bfb64adea19",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"d7aa8e05f3b6118478e7caf71b9232c2\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 3
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "6dc73457e07d42378eb88884c05b02c4",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "6e8f0ec098994a579af28959841408d9",
    "m_Title": "0",
    "m_Content": "",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -579.5000610351563,
        "y": 340.0000305175781,
        "width": 80.00003051757813,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "5544d5333c044805a12e946fc9ff7eae"
    }
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInTarget",
    "m_ObjectId": "70c304d35f714e15b468c8890084d87e",
    "m_ActiveSubTarget": {
        "m_Id": "033c4333ef1e4c6b90590dc59d085695"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZWriteControl": 0,
    "m_ZTestMode": 4,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CustomEditorGUI": ""
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "72466d3ddaba4681b08bd3fc6e04f6f9",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "75d1deee25714feba9978980c0a99d1c",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"d7aa8e05f3b6118478e7caf71b9232c2\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 3
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "7748214983fa4a5eb36d97742de10667",
    "m_Id": 2,
    "m_DisplayName": "Dir",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Dir",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "797668e2d76749188f60301405f01d57",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "7a5cac2b633e4498a5814163780a3899",
    "m_Id": 4,
    "m_DisplayName": "LOD",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "LOD",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "7b2dfd74805f4723997ce2fe8f67edb6",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ReflectionNode",
    "m_ObjectId": "7e677b345d7843e2a598c65727114f17",
    "m_Group": {
        "m_Id": "1fcc60e54a8942ffa01feb61fdaaedf2"
    },
    "m_Name": "Reflection",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1075.0001220703125,
            "y": 118.00000762939453,
            "width": 159.50006103515626,
            "height": 117.99999237060547
        }
    },
    "m_Slots": [
        {
            "m_Id": "28e5e6747d4c498d8bb8d1dc31379c38"
        },
        {
            "m_Id": "f76e50e6bcbf40ca898ba4963eb6987a"
        },
        {
            "m_Id": "6758148b86fa4a01abd1de373084328a"
        }
    ],
    "synonyms": [
        "mirror"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget",
    "m_ObjectId": "83b3695d3f70469f830e8e12680a2e69",
    "m_Datas": [],
    "m_ActiveSubTarget": {
        "m_Id": "f4f122bcef9e4e08bd27985f324aea9d"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZTestMode": 4,
    "m_ZWriteControl": 0,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CastShadows": true,
    "m_ReceiveShadows": true,
    "m_SupportsLODCrossFade": false,
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "888067a123ef4271ad3ce1c73bd13f08",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Tangent",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "d8b964a1a1ab420297c44c7c9ef98f9c"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Tangent"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "8ad0f4153d4c411db8a3c2d06a13f122",
    "m_Title": "6",
    "m_Content": "",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -568.0000610351563,
        "y": 897.0000610351563,
        "width": 80.00003051757813,
        "height": 100.00006103515625
    },
    "m_Group": {
        "m_Id": "5544d5333c044805a12e946fc9ff7eae"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "8d37bee3c2e440bf8258271056626cc7",
    "m_Id": 6,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "8e4aabe5f04143f192130f148fee973c",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Normal",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "2cd0e92320a84407b4f8069d9af78d70"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Normal"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "9858c7d403334896808e99651bc55962",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CubemapInputMaterialSlot",
    "m_ObjectId": "9b928102e68f46199c29639f4f24aa9e",
    "m_Id": 1,
    "m_DisplayName": "Cube",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cube",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Cubemap": {
        "m_SerializedCubemap": "{\"cubemap\":{\"fileID\":8900000,\"guid\":\"cd7fb2f39e535e64d985f05bc8b6061b\",\"type\":3}}",
        "m_Guid": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleRawCubemapNode",
    "m_ObjectId": "9c278368cc094f2c8d08950cbb318f97",
    "m_Group": {
        "m_Id": "5544d5333c044805a12e946fc9ff7eae"
    },
    "m_Name": "Sample Cubemap",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -465.00006103515627,
            "y": 808.0001220703125,
            "width": 208.0,
            "height": 278.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "dd6fabee61944f68b4e1a656ea482ac5"
        },
        {
            "m_Id": "9b928102e68f46199c29639f4f24aa9e"
        },
        {
            "m_Id": "434cecf8b45a46fc93324c24f9c5278e"
        },
        {
            "m_Id": "082e992176184b519dd45fb39ee2a97a"
        },
        {
            "m_Id": "7a5cac2b633e4498a5814163780a3899"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "9e6fe57e492d4b7aa28de4f72dd73458",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleTexture2DNode",
    "m_ObjectId": "a4e6f7bb865c4a7ab4a4c7c3b670200f",
    "m_Group": {
        "m_Id": "55d0399c24694368a093a5739980c3a4"
    },
    "m_Name": "Sample Texture 2D",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1512.0001220703125,
            "y": 678.0,
            "width": 208.0,
            "height": 338.00006103515627
        }
    },
    "m_Slots": [
        {
            "m_Id": "e23aa898a7144cbcadfa6a356e3282ad"
        },
        {
            "m_Id": "d1ca4c77e989472a95d44b5ca7a29fa7"
        },
        {
            "m_Id": "cbf41e7bae184d28b5e6771bd25582d7"
        },
        {
            "m_Id": "8d37bee3c2e440bf8258271056626cc7"
        },
        {
            "m_Id": "38dfcca2aab248c9bc7e259a7c9ab36f"
        },
        {
            "m_Id": "75d1deee25714feba9978980c0a99d1c"
        },
        {
            "m_Id": "ab602f1ec6114dc1b1c194bd2dc9e63a"
        },
        {
            "m_Id": "a836428dbc724661aa06079e59939689"
        }
    ],
    "synonyms": [
        "tex2d"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_TextureType": 1,
    "m_NormalMapSpace": 0,
    "m_EnableGlobalMipBias": true,
    "m_MipSamplingMode": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "a836428dbc724661aa06079e59939689",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "ab602f1ec6114dc1b1c194bd2dc9e63a",
    "m_Id": 2,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1Node",
    "m_ObjectId": "abda54a6d3564acba818758362441b8f",
    "m_Group": {
        "m_Id": "5544d5333c044805a12e946fc9ff7eae"
    },
    "m_Name": "Float",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -606.5000610351563,
            "y": 530.0,
            "width": 125.50003051757813,
            "height": 77.00006103515625
        }
    },
    "m_Slots": [
        {
            "m_Id": "b688474742df47ce825b19281d38b60a"
        },
        {
            "m_Id": "6dc73457e07d42378eb88884c05b02c4"
        }
    ],
    "synonyms": [
        "Vector 1",
        "1",
        "v1",
        "vec1",
        "scalar"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": 0.0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "affe2b61660e4be596939694d34e4ee4",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "b419bb963e574197957daacdea52b87b",
    "m_Id": 2,
    "m_DisplayName": "Dir",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Dir",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "b4f20e95e4c2461b967bb07fd4bb3045",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.BaseColor",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "1cd88d0e2a8b44f9ad8099586b9ea21a"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.BaseColor"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "b58f7ba36f534794a0f1a136cfa34980",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Emission",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "e11ef23de86f422b84516c55ecb8bbf1"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Emission"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b65f573dc6314144ab47e0d0a8fa5809",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b688474742df47ce825b19281d38b60a",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 4.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CubemapInputMaterialSlot",
    "m_ObjectId": "b6b9a50f7d7744a5a1d61f29fe04a767",
    "m_Id": 1,
    "m_DisplayName": "Cube",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cube",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Cubemap": {
        "m_SerializedCubemap": "{\"cubemap\":{\"fileID\":8900000,\"guid\":\"cd7fb2f39e535e64d985f05bc8b6061b\",\"type\":3}}",
        "m_Guid": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleRawCubemapNode",
    "m_ObjectId": "b98f0a7a54e14b77808f7e02d549330c",
    "m_Group": {
        "m_Id": "5544d5333c044805a12e946fc9ff7eae"
    },
    "m_Name": "Sample Cubemap",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -465.0000305175781,
            "y": 252.0000457763672,
            "width": 208.0,
            "height": 278.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "e1f54f6633d146a1b06048933e6b37a8"
        },
        {
            "m_Id": "3d6dd12c661647028e6d4d5deee923f6"
        },
        {
            "m_Id": "3a780e6309cd4965a1c594318517ce90"
        },
        {
            "m_Id": "d04f641071ea4d409ef6c59d29ca3dcd"
        },
        {
            "m_Id": "e196c7aebb3c4346a25cccab77a1f806"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CubemapInputMaterialSlot",
    "m_ObjectId": "ba8ffabdb83447349343ad3937a47251",
    "m_Id": 1,
    "m_DisplayName": "Cube",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cube",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Cubemap": {
        "m_SerializedCubemap": "{\"cubemap\":{\"fileID\":8900000,\"guid\":\"cd7fb2f39e535e64d985f05bc8b6061b\",\"type\":3}}",
        "m_Guid": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "bcbea155d7a240bda3f491040432cd50",
    "m_Title": "4",
    "m_Content": "",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -577.5000610351563,
        "y": 608.5000610351563,
        "width": 80.00003051757813,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "5544d5333c044805a12e946fc9ff7eae"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "be3a6d9dba394f9bbb5381999d4811cb",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "bf88f50270f94b8b8c3891884cf23b30",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "bfdeee15f41144d9b0d60067ee6da29a",
    "m_Id": 6,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "cadbbc15f1ec4cdaae79f2d607704ffe",
    "m_Title": "",
    "m_Content": "Both the View Direction and Normal inputs need to be in the same space in order for the effect to work correctly.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1644.0001220703125,
        "y": 132.50001525878907,
        "width": 200.0,
        "height": 100.00001525878906
    },
    "m_Group": {
        "m_Id": "1fcc60e54a8942ffa01feb61fdaaedf2"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "cbf41e7bae184d28b5e6771bd25582d7",
    "m_Id": 5,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "ccdee7f5de2542bb829e18452d40718b",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "cf6d3f65220f464fb4cda24ad20c4cf3",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "d04f641071ea4d409ef6c59d29ca3dcd",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleRawCubemapNode",
    "m_ObjectId": "d0dbf532a1504cb6b7f9adb0df870768",
    "m_Group": {
        "m_Id": "5544d5333c044805a12e946fc9ff7eae"
    },
    "m_Name": "Sample Cubemap",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -465.0000305175781,
            "y": 530.0000610351563,
            "width": 208.0,
            "height": 278.00006103515627
        }
    },
    "m_Slots": [
        {
            "m_Id": "1c4f0d9be53940d09c0cf8765d6a2bb6"
        },
        {
            "m_Id": "b6b9a50f7d7744a5a1d61f29fe04a767"
        },
        {
            "m_Id": "7748214983fa4a5eb36d97742de10667"
        },
        {
            "m_Id": "d3012c1e034f4ca9bdfbe4600f891b89"
        },
        {
            "m_Id": "f7e214953be2415fbb1d86a8fbb27c24"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "d1ca4c77e989472a95d44b5ca7a29fa7",
    "m_Id": 4,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "d3012c1e034f4ca9bdfbe4600f891b89",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "d5ec046d0d1541f7b7c29252214c6722",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1Node",
    "m_ObjectId": "d709141fb9a04f0a974e4cc719dffac1",
    "m_Group": {
        "m_Id": "5544d5333c044805a12e946fc9ff7eae"
    },
    "m_Name": "Float",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -606.5000610351563,
            "y": 252.00001525878907,
            "width": 125.50003051757813,
            "height": 76.99998474121094
        }
    },
    "m_Slots": [
        {
            "m_Id": "cf6d3f65220f464fb4cda24ad20c4cf3"
        },
        {
            "m_Id": "b65f573dc6314144ab47e0d0a8fa5809"
        }
    ],
    "synonyms": [
        "Vector 1",
        "1",
        "v1",
        "vec1",
        "scalar"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": 0.0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TangentMaterialSlot",
    "m_ObjectId": "d8b964a1a1ab420297c44c7c9ef98f9c",
    "m_Id": 0,
    "m_DisplayName": "Tangent",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tangent",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CubemapInputMaterialSlot",
    "m_ObjectId": "db3bdc0ebf604972857cdfdfcd092bbf",
    "m_Id": 1,
    "m_DisplayName": "Cube",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cube",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Cubemap": {
        "m_SerializedCubemap": "{\"cubemap\":{\"fileID\":8900000,\"guid\":\"cd7fb2f39e535e64d985f05bc8b6061b\",\"type\":3}}",
        "m_Guid": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "dc42996e045144c99844c9616311bb69",
    "m_Id": 0,
    "m_DisplayName": "Alpha",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Alpha",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "dd6fabee61944f68b4e1a656ea482ac5",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "df50369e81814d5792eab5db4f3b6377",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "e07f5a8eb4e543ac8ad48dbe305026ca",
    "m_Title": "",
    "m_Content": "Passing the World Space View Direction and World Space Normal Vector into the Sample Reflected Cubemap node will give you reflections.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -909.0000610351563,
        "y": 403.5000305175781,
        "width": 200.0,
        "height": 100.00003051757813
    },
    "m_Group": {
        "m_Id": "1fcc60e54a8942ffa01feb61fdaaedf2"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "e11ef23de86f422b84516c55ecb8bbf1",
    "m_Id": 0,
    "m_DisplayName": "Emission",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Emission",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 1,
    "m_DefaultColor": {
        "r": 0.0,
        "g": 0.0,
        "b": 0.0,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e196c7aebb3c4346a25cccab77a1f806",
    "m_Id": 4,
    "m_DisplayName": "LOD",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "LOD",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "e1e2846278a246c8a274b3e174bda3c0",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "e1f54f6633d146a1b06048933e6b37a8",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "e23aa898a7144cbcadfa6a356e3282ad",
    "m_Id": 0,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleRawCubemapNode",
    "m_ObjectId": "e4690df9e6b144ce99a7a76217a3ca14",
    "m_Group": {
        "m_Id": "55d0399c24694368a093a5739980c3a4"
    },
    "m_Name": "Sample Cubemap",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -920.0000610351563,
            "y": 678.0,
            "width": 207.99993896484376,
            "height": 302.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "ebbd025725b340c39b3793bc1b0b7c29"
        },
        {
            "m_Id": "db3bdc0ebf604972857cdfdfcd092bbf"
        },
        {
            "m_Id": "eb644663f1534b34a0ed9b6cf0dc6367"
        },
        {
            "m_Id": "df50369e81814d5792eab5db4f3b6377"
        },
        {
            "m_Id": "e965d30fa45f43ddb0784db588c26a75"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDTarget",
    "m_ObjectId": "e516eb92d4e4422b85630d3f1f5af363",
    "m_ActiveSubTarget": {
        "m_Id": "0c5e559dc9604aae9194c599977c7489"
    },
    "m_Datas": [
        {
            "m_Id": "bf88f50270f94b8b8c3891884cf23b30"
        },
        {
            "m_Id": "6ca74f4679244b4f813d1ebce494f04f"
        },
        {
            "m_Id": "9858c7d403334896808e99651bc55962"
        }
    ],
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false,
    "m_SupportLineRendering": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "e60b3c75ada941319ca666bc9169663a",
    "m_Title": "Sample Cubemap Node",
    "m_Content": "The Sample Cubemap Node samples a Cubemap and returns a Vector 4 color value for use in the shader. It Requires a Direction (Dir) vector input to sample the Cubemap. \n\nYou can achieve a blurring effect by using the LOD input to sample at a different Level of Detail. You can also use the Sampler input to define a custom Sampler State.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -957.0000610351563,
        "y": -146.00001525878907,
        "width": 344.0,
        "height": 165.00001525878907
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CubemapInputMaterialSlot",
    "m_ObjectId": "e858f77ee20447b9b376b4e584ea6227",
    "m_Id": 1,
    "m_DisplayName": "Cube",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cube",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Cubemap": {
        "m_SerializedCubemap": "{\"cubemap\":{\"instanceID\":0}}",
        "m_Guid": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e965d30fa45f43ddb0784db588c26a75",
    "m_Id": 4,
    "m_DisplayName": "LOD",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "LOD",
    "m_StageCapability": 3,
    "m_Value": 6.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "eb644663f1534b34a0ed9b6cf0dc6367",
    "m_Id": 2,
    "m_DisplayName": "Dir",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Dir",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "ebbd025725b340c39b3793bc1b0b7c29",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "edf551a3065340e29a163ec12422b4a1",
    "m_Id": 0,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "f377eaedcfc74d77a38a76d60cbc7f3d"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "f3a0185ca7984b518cd3aa9db16d1cb0",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalUnlitSubTarget",
    "m_ObjectId": "f4f122bcef9e4e08bd27985f324aea9d"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "f76e50e6bcbf40ca898ba4963eb6987a",
    "m_Id": 1,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 1.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "f7e214953be2415fbb1d86a8fbb27c24",
    "m_Id": 4,
    "m_DisplayName": "LOD",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "LOD",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "f989c34afba548a1991c2e4617c7b85e",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "fa9c9d418aec47cdacae32ee8be21bde",
    "m_Name": "",
    "m_ChildObjectList": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "facfdedbb7f84a9b90b829b0ba59a835",
    "m_Title": "",
    "m_Content": "Because we're using the Sample Cubemap node instead of the Sample Reflected Cubemap node, we have to compute the reflection vector manually using the Reflection node.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1069.5001220703125,
        "y": 238.50003051757813,
        "width": 148.50006103515626,
        "height": 133.5
    },
    "m_Group": {
        "m_Id": "1fcc60e54a8942ffa01feb61fdaaedf2"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "fb5ac61963f546c7bf839562e619e994",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

