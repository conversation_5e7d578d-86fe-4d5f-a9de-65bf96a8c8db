//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using NUnit.Framework;
using static Unity.Mathematics.math;
using Burst.Compiler.IL.Tests;

namespace Unity.Mathematics.Tests
{
    [TestFixture]
    public class TestBool2
    {
        [TestCompiler]
        public static void bool2_constructor()
        {
            bool2 a = new bool2(false, true);
            TestUtils.AreEqual(false, a.x);
            TestUtils.AreEqual(true, a.y);
        }

        [TestCompiler]
        public static void bool2_scalar_constructor()
        {
            bool2 a = new bool2(true);
            TestUtils.AreEqual(true, a.x);
            TestUtils.AreEqual(true, a.y);
        }

        [TestCompiler]
        public static void bool2_static_constructor()
        {
            bool2 a = bool2(false, true);
            TestUtils.AreEqual(false, a.x);
            TestUtils.AreEqual(true, a.y);
        }

        [TestCompiler]
        public static void bool2_static_scalar_constructor()
        {
            bool2 a = bool2(true);
            TestUtils.AreEqual(true, a.x);
            TestUtils.AreEqual(true, a.y);
        }

        [TestCompiler]
        public static void bool2_operator_equal_wide_wide()
        {
            bool2 a0 = bool2(true, false);
            bool2 b0 = bool2(true, false);
            bool2 r0 = bool2(true, true);
            TestUtils.AreEqual(r0, a0 == b0);

            bool2 a1 = bool2(true, false);
            bool2 b1 = bool2(false, false);
            bool2 r1 = bool2(false, true);
            TestUtils.AreEqual(r1, a1 == b1);

            bool2 a2 = bool2(false, true);
            bool2 b2 = bool2(true, false);
            bool2 r2 = bool2(false, false);
            TestUtils.AreEqual(r2, a2 == b2);

            bool2 a3 = bool2(false, false);
            bool2 b3 = bool2(false, true);
            bool2 r3 = bool2(true, false);
            TestUtils.AreEqual(r3, a3 == b3);
        }

        [TestCompiler]
        public static void bool2_operator_equal_wide_scalar()
        {
            bool2 a0 = bool2(false, true);
            bool b0 = (true);
            bool2 r0 = bool2(false, true);
            TestUtils.AreEqual(r0, a0 == b0);

            bool2 a1 = bool2(false, false);
            bool b1 = (false);
            bool2 r1 = bool2(true, true);
            TestUtils.AreEqual(r1, a1 == b1);

            bool2 a2 = bool2(false, false);
            bool b2 = (true);
            bool2 r2 = bool2(false, false);
            TestUtils.AreEqual(r2, a2 == b2);

            bool2 a3 = bool2(false, true);
            bool b3 = (false);
            bool2 r3 = bool2(true, false);
            TestUtils.AreEqual(r3, a3 == b3);
        }

        [TestCompiler]
        public static void bool2_operator_equal_scalar_wide()
        {
            bool a0 = (false);
            bool2 b0 = bool2(true, false);
            bool2 r0 = bool2(false, true);
            TestUtils.AreEqual(r0, a0 == b0);

            bool a1 = (true);
            bool2 b1 = bool2(false, false);
            bool2 r1 = bool2(false, false);
            TestUtils.AreEqual(r1, a1 == b1);

            bool a2 = (true);
            bool2 b2 = bool2(false, false);
            bool2 r2 = bool2(false, false);
            TestUtils.AreEqual(r2, a2 == b2);

            bool a3 = (true);
            bool2 b3 = bool2(false, true);
            bool2 r3 = bool2(false, true);
            TestUtils.AreEqual(r3, a3 == b3);
        }

        [TestCompiler]
        public static void bool2_operator_not_equal_wide_wide()
        {
            bool2 a0 = bool2(true, true);
            bool2 b0 = bool2(true, false);
            bool2 r0 = bool2(false, true);
            TestUtils.AreEqual(r0, a0 != b0);

            bool2 a1 = bool2(true, false);
            bool2 b1 = bool2(false, false);
            bool2 r1 = bool2(true, false);
            TestUtils.AreEqual(r1, a1 != b1);

            bool2 a2 = bool2(false, true);
            bool2 b2 = bool2(true, false);
            bool2 r2 = bool2(true, true);
            TestUtils.AreEqual(r2, a2 != b2);

            bool2 a3 = bool2(false, false);
            bool2 b3 = bool2(false, false);
            bool2 r3 = bool2(false, false);
            TestUtils.AreEqual(r3, a3 != b3);
        }

        [TestCompiler]
        public static void bool2_operator_not_equal_wide_scalar()
        {
            bool2 a0 = bool2(false, true);
            bool b0 = (false);
            bool2 r0 = bool2(false, true);
            TestUtils.AreEqual(r0, a0 != b0);

            bool2 a1 = bool2(false, true);
            bool b1 = (true);
            bool2 r1 = bool2(true, false);
            TestUtils.AreEqual(r1, a1 != b1);

            bool2 a2 = bool2(false, false);
            bool b2 = (false);
            bool2 r2 = bool2(false, false);
            TestUtils.AreEqual(r2, a2 != b2);

            bool2 a3 = bool2(false, false);
            bool b3 = (false);
            bool2 r3 = bool2(false, false);
            TestUtils.AreEqual(r3, a3 != b3);
        }

        [TestCompiler]
        public static void bool2_operator_not_equal_scalar_wide()
        {
            bool a0 = (true);
            bool2 b0 = bool2(false, false);
            bool2 r0 = bool2(true, true);
            TestUtils.AreEqual(r0, a0 != b0);

            bool a1 = (true);
            bool2 b1 = bool2(false, false);
            bool2 r1 = bool2(true, true);
            TestUtils.AreEqual(r1, a1 != b1);

            bool a2 = (false);
            bool2 b2 = bool2(true, true);
            bool2 r2 = bool2(true, true);
            TestUtils.AreEqual(r2, a2 != b2);

            bool a3 = (true);
            bool2 b3 = bool2(false, false);
            bool2 r3 = bool2(true, true);
            TestUtils.AreEqual(r3, a3 != b3);
        }

        [TestCompiler]
        public static void bool2_operator_bitwise_and_wide_wide()
        {
            bool2 a0 = bool2(false, false);
            bool2 b0 = bool2(false, false);
            bool2 r0 = bool2(false, false);
            TestUtils.AreEqual(r0, a0 & b0);

            bool2 a1 = bool2(true, true);
            bool2 b1 = bool2(true, false);
            bool2 r1 = bool2(true, false);
            TestUtils.AreEqual(r1, a1 & b1);

            bool2 a2 = bool2(false, false);
            bool2 b2 = bool2(true, true);
            bool2 r2 = bool2(false, false);
            TestUtils.AreEqual(r2, a2 & b2);

            bool2 a3 = bool2(true, true);
            bool2 b3 = bool2(false, false);
            bool2 r3 = bool2(false, false);
            TestUtils.AreEqual(r3, a3 & b3);
        }

        [TestCompiler]
        public static void bool2_operator_bitwise_and_wide_scalar()
        {
            bool2 a0 = bool2(true, false);
            bool b0 = (true);
            bool2 r0 = bool2(true, false);
            TestUtils.AreEqual(r0, a0 & b0);

            bool2 a1 = bool2(false, true);
            bool b1 = (true);
            bool2 r1 = bool2(false, true);
            TestUtils.AreEqual(r1, a1 & b1);

            bool2 a2 = bool2(false, false);
            bool b2 = (false);
            bool2 r2 = bool2(false, false);
            TestUtils.AreEqual(r2, a2 & b2);

            bool2 a3 = bool2(false, true);
            bool b3 = (false);
            bool2 r3 = bool2(false, false);
            TestUtils.AreEqual(r3, a3 & b3);
        }

        [TestCompiler]
        public static void bool2_operator_bitwise_and_scalar_wide()
        {
            bool a0 = (false);
            bool2 b0 = bool2(false, false);
            bool2 r0 = bool2(false, false);
            TestUtils.AreEqual(r0, a0 & b0);

            bool a1 = (true);
            bool2 b1 = bool2(true, true);
            bool2 r1 = bool2(true, true);
            TestUtils.AreEqual(r1, a1 & b1);

            bool a2 = (false);
            bool2 b2 = bool2(true, false);
            bool2 r2 = bool2(false, false);
            TestUtils.AreEqual(r2, a2 & b2);

            bool a3 = (false);
            bool2 b3 = bool2(false, true);
            bool2 r3 = bool2(false, false);
            TestUtils.AreEqual(r3, a3 & b3);
        }

        [TestCompiler]
        public static void bool2_operator_bitwise_or_wide_wide()
        {
            bool2 a0 = bool2(true, true);
            bool2 b0 = bool2(false, false);
            bool2 r0 = bool2(true, true);
            TestUtils.AreEqual(r0, a0 | b0);

            bool2 a1 = bool2(true, false);
            bool2 b1 = bool2(false, false);
            bool2 r1 = bool2(true, false);
            TestUtils.AreEqual(r1, a1 | b1);

            bool2 a2 = bool2(true, false);
            bool2 b2 = bool2(true, true);
            bool2 r2 = bool2(true, true);
            TestUtils.AreEqual(r2, a2 | b2);

            bool2 a3 = bool2(true, true);
            bool2 b3 = bool2(true, false);
            bool2 r3 = bool2(true, true);
            TestUtils.AreEqual(r3, a3 | b3);
        }

        [TestCompiler]
        public static void bool2_operator_bitwise_or_wide_scalar()
        {
            bool2 a0 = bool2(true, true);
            bool b0 = (true);
            bool2 r0 = bool2(true, true);
            TestUtils.AreEqual(r0, a0 | b0);

            bool2 a1 = bool2(false, true);
            bool b1 = (true);
            bool2 r1 = bool2(true, true);
            TestUtils.AreEqual(r1, a1 | b1);

            bool2 a2 = bool2(true, false);
            bool b2 = (true);
            bool2 r2 = bool2(true, true);
            TestUtils.AreEqual(r2, a2 | b2);

            bool2 a3 = bool2(true, false);
            bool b3 = (false);
            bool2 r3 = bool2(true, false);
            TestUtils.AreEqual(r3, a3 | b3);
        }

        [TestCompiler]
        public static void bool2_operator_bitwise_or_scalar_wide()
        {
            bool a0 = (true);
            bool2 b0 = bool2(true, true);
            bool2 r0 = bool2(true, true);
            TestUtils.AreEqual(r0, a0 | b0);

            bool a1 = (false);
            bool2 b1 = bool2(false, true);
            bool2 r1 = bool2(false, true);
            TestUtils.AreEqual(r1, a1 | b1);

            bool a2 = (true);
            bool2 b2 = bool2(true, false);
            bool2 r2 = bool2(true, true);
            TestUtils.AreEqual(r2, a2 | b2);

            bool a3 = (false);
            bool2 b3 = bool2(true, true);
            bool2 r3 = bool2(true, true);
            TestUtils.AreEqual(r3, a3 | b3);
        }

        [TestCompiler]
        public static void bool2_operator_bitwise_xor_wide_wide()
        {
            bool2 a0 = bool2(true, false);
            bool2 b0 = bool2(true, true);
            bool2 r0 = bool2(false, true);
            TestUtils.AreEqual(r0, a0 ^ b0);

            bool2 a1 = bool2(false, true);
            bool2 b1 = bool2(false, true);
            bool2 r1 = bool2(false, false);
            TestUtils.AreEqual(r1, a1 ^ b1);

            bool2 a2 = bool2(false, false);
            bool2 b2 = bool2(false, true);
            bool2 r2 = bool2(false, true);
            TestUtils.AreEqual(r2, a2 ^ b2);

            bool2 a3 = bool2(false, true);
            bool2 b3 = bool2(false, true);
            bool2 r3 = bool2(false, false);
            TestUtils.AreEqual(r3, a3 ^ b3);
        }

        [TestCompiler]
        public static void bool2_operator_bitwise_xor_wide_scalar()
        {
            bool2 a0 = bool2(false, false);
            bool b0 = (false);
            bool2 r0 = bool2(false, false);
            TestUtils.AreEqual(r0, a0 ^ b0);

            bool2 a1 = bool2(true, false);
            bool b1 = (true);
            bool2 r1 = bool2(false, true);
            TestUtils.AreEqual(r1, a1 ^ b1);

            bool2 a2 = bool2(false, false);
            bool b2 = (false);
            bool2 r2 = bool2(false, false);
            TestUtils.AreEqual(r2, a2 ^ b2);

            bool2 a3 = bool2(false, false);
            bool b3 = (false);
            bool2 r3 = bool2(false, false);
            TestUtils.AreEqual(r3, a3 ^ b3);
        }

        [TestCompiler]
        public static void bool2_operator_bitwise_xor_scalar_wide()
        {
            bool a0 = (true);
            bool2 b0 = bool2(true, false);
            bool2 r0 = bool2(false, true);
            TestUtils.AreEqual(r0, a0 ^ b0);

            bool a1 = (true);
            bool2 b1 = bool2(true, false);
            bool2 r1 = bool2(false, true);
            TestUtils.AreEqual(r1, a1 ^ b1);

            bool a2 = (true);
            bool2 b2 = bool2(true, false);
            bool2 r2 = bool2(false, true);
            TestUtils.AreEqual(r2, a2 ^ b2);

            bool a3 = (false);
            bool2 b3 = bool2(true, true);
            bool2 r3 = bool2(true, true);
            TestUtils.AreEqual(r3, a3 ^ b3);
        }

        [TestCompiler]
        public static void bool2_operator_logical_not()
        {
            bool2 a0 = bool2(true, true);
            bool2 r0 = bool2(false, false);
            TestUtils.AreEqual(r0, !a0);

            bool2 a1 = bool2(false, true);
            bool2 r1 = bool2(true, false);
            TestUtils.AreEqual(r1, !a1);

            bool2 a2 = bool2(false, false);
            bool2 r2 = bool2(true, true);
            TestUtils.AreEqual(r2, !a2);

            bool2 a3 = bool2(true, false);
            bool2 r3 = bool2(false, true);
            TestUtils.AreEqual(r3, !a3);
        }

        [TestCase]
        public static void bool2_EqualsObjectOverride()
        {
            TestUtils.IsFalse(new bool2().Equals((object)new int()));
            TestUtils.IsTrue(new bool2().Equals((object)new bool2()));
        }


    }
}
