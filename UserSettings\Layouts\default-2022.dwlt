%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12004, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_PixelRect:
    serializedVersion: 2
    x: 0
    y: 43.2
    width: 2048
    height: 1060.8
  m_ShowMode: 4
  m_Title: Hierarchy
  m_RootView: {fileID: 2}
  m_MinSize: {x: 875, y: 300}
  m_MaxSize: {x: 10000, y: 10000}
  m_Maximized: 1
--- !u!114 &2
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12008, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 3}
  - {fileID: 5}
  - {fileID: 4}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 2048
    height: 1060.8
  m_MinSize: {x: 875, y: 300}
  m_MaxSize: {x: 10000, y: 10000}
  m_UseTopView: 1
  m_TopViewHeight: 30
  m_UseBottomView: 1
  m_BottomViewHeight: 20
--- !u!114 &3
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12011, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 2048
    height: 30
  m_MinSize: {x: 0, y: 0}
  m_MaxSize: {x: 0, y: 0}
  m_LastLoadedLayoutName: 
--- !u!114 &4
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12042, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 1040.8
    width: 2048
    height: 20
  m_MinSize: {x: 0, y: 0}
  m_MaxSize: {x: 0, y: 0}
--- !u!114 &5
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 6}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 30
    width: 2048
    height: 1010.80005
  m_MinSize: {x: 400, y: 100}
  m_MaxSize: {x: 32384, y: 16192}
  vertical: 1
  controlID: 357
--- !u!114 &6
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 7}
  - {fileID: 15}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 2048
    height: 1010.80005
  m_MinSize: {x: 400, y: 100}
  m_MaxSize: {x: 32384, y: 16192}
  vertical: 0
  controlID: 358
--- !u!114 &7
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 8}
  - {fileID: 12}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1572
    height: 1010.80005
  m_MinSize: {x: 300, y: 100}
  m_MaxSize: {x: 24288, y: 16192}
  vertical: 1
  controlID: 359
--- !u!114 &8
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 9}
  - {fileID: 10}
  - {fileID: 11}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1572
    height: 660
  m_MinSize: {x: 300, y: 50}
  m_MaxSize: {x: 24288, y: 8096}
  vertical: 0
  controlID: 360
--- !u!114 &9
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: SceneHierarchyWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 286.4
    height: 660
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_ActualView: {fileID: 17}
  m_Panes:
  - {fileID: 17}
  m_Selected: 0
  m_LastSelected: 1
--- !u!114 &10
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: GameView
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 286.4
    y: 0
    width: 651.19995
    height: 660
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_ActualView: {fileID: 16}
  m_Panes:
  - {fileID: 16}
  - {fileID: 18}
  - {fileID: 19}
  m_Selected: 0
  m_LastSelected: 1
--- !u!114 &11
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: SceneView
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 937.6
    y: 0
    width: 634.4
    height: 660
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_ActualView: {fileID: 20}
  m_Panes:
  - {fileID: 20}
  m_Selected: 0
  m_LastSelected: 0
--- !u!114 &12
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 13}
  - {fileID: 14}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 660
    width: 1572
    height: 350.80005
  m_MinSize: {x: 200, y: 50}
  m_MaxSize: {x: 16192, y: 8096}
  vertical: 0
  controlID: 371
--- !u!114 &13
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: ProjectBrowser
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 542.4
    height: 350.80005
  m_MinSize: {x: 231, y: 271}
  m_MaxSize: {x: 10001, y: 10021}
  m_ActualView: {fileID: 21}
  m_Panes:
  - {fileID: 21}
  m_Selected: 0
  m_LastSelected: 2
--- !u!114 &14
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: ConsoleWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 542.4
    y: 0
    width: 1029.6
    height: 350.80005
  m_MinSize: {x: 102, y: 121}
  m_MaxSize: {x: 4002, y: 4021}
  m_ActualView: {fileID: 22}
  m_Panes:
  - {fileID: 22}
  - {fileID: 23}
  - {fileID: 24}
  m_Selected: 0
  m_LastSelected: 1
--- !u!114 &15
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: InspectorWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 1572
    y: 0
    width: 476
    height: 1010.80005
  m_MinSize: {x: 275, y: 100}
  m_MaxSize: {x: 4000, y: 4000}
  m_ActualView: {fileID: 25}
  m_Panes:
  - {fileID: 25}
  m_Selected: 0
  m_LastSelected: 2
--- !u!114 &16
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12015, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Game
    m_Image: {fileID: 4621777727084837110, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
  m_Pos:
    serializedVersion: 2
    x: 286.4
    y: 73.6
    width: 649.19995
    height: 639
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_OverlaysVisible: 1
  m_SerializedViewNames:
  - UnityEditor.DeviceSimulation.SimulatorWindow
  m_SerializedViewValues:
  - D:\unity\projects\My project (5)\Library\PlayModeViewStates\65049c701248e654180b76b493eb3acb
  m_PlayModeViewName: GameView
  m_ShowGizmos: 0
  m_TargetDisplay: 0
  m_ClearColor: {r: 0, g: 0, b: 0, a: 0}
  m_TargetSize: {x: 3840, y: 2160}
  m_TextureFilterMode: 0
  m_TextureHideFlags: 61
  m_RenderIMGUI: 1
  m_EnterPlayModeBehavior: 0
  m_UseMipMap: 0
  m_VSyncEnabled: 0
  m_Gizmos: 0
  m_Stats: 0
  m_SelectedSizes: 06000000000000000000000000000000000000000000000000000000000000000000000000000000
  m_ZoomArea:
    m_HRangeLocked: 0
    m_VRangeLocked: 0
    hZoomLockedByDefault: 0
    vZoomLockedByDefault: 0
    m_HBaseRangeMin: -1536
    m_HBaseRangeMax: 1536
    m_VBaseRangeMin: -864
    m_VBaseRangeMax: 864
    m_HAllowExceedBaseRangeMin: 1
    m_HAllowExceedBaseRangeMax: 1
    m_VAllowExceedBaseRangeMin: 1
    m_VAllowExceedBaseRangeMax: 1
    m_ScaleWithWindow: 0
    m_HSlider: 0
    m_VSlider: 0
    m_IgnoreScrollWheelUntilClicked: 0
    m_EnableMouseInput: 1
    m_EnableSliderZoomHorizontal: 0
    m_EnableSliderZoomVertical: 0
    m_UniformScale: 1
    m_UpDirection: 1
    m_DrawArea:
      serializedVersion: 2
      x: 0
      y: 21
      width: 649.19995
      height: 618
    m_Scale: {x: 0.2113281, y: 0.2113281}
    m_Translation: {x: 324.59998, y: 309}
    m_MarginLeft: 0
    m_MarginRight: 0
    m_MarginTop: 0
    m_MarginBottom: 0
    m_LastShownAreaInsideMargins:
      serializedVersion: 2
      x: -1536
      y: -1462.1813
      width: 3072
      height: 2924.3625
    m_MinimalGUI: 1
  m_defaultScale: 0.2113281
  m_LastWindowPixelSize: {x: 811.49994, y: 798.75}
  m_ClearInEditMode: 1
  m_NoCameraWarning: 1
  m_LowResolutionForAspectRatios: 00000000000000000000
  m_XRRenderMode: 0
  m_RenderTexture: {fileID: 0}
--- !u!114 &17
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12061, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Hierarchy
    m_Image: {fileID: -3734745235275155857, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 73.6
    width: 285.4
    height: 639
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_OverlaysVisible: 1
  m_SceneHierarchy:
    m_TreeViewState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 52800000
      m_LastClickedID: 32850
      m_ExpandedIDs: 52d0ffff24f3fffff4ffffffce7f0000dc7f000066800000748000007880000090800000be800000c2800000e4cf000016d00000
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: SaveAndHandoffButton
        m_OriginalName: SaveAndHandoffButton
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 32732
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 0
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 9}
      m_SearchString: 
    m_ExpandedScenes: []
    m_CurrenRootInstanceID: 0
    m_LockTracker:
      m_IsLocked: 0
    m_CurrentSortingName: TransformSorting
  m_WindowGUID: 4c969a2b90040154d917609493e03593
--- !u!114 &18
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12043, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 640, y: 580}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Build Settings
    m_Image: {fileID: 0}
    m_Tooltip: 
  m_Pos:
    serializedVersion: 2
    x: 284
    y: 73.6
    width: 690
    height: 687
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_OverlaysVisible: 1
  m_TreeViewState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 06001668
    m_LastClickedID: 1746272262
    m_ExpandedIDs: ffffffff
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
--- !u!114 &19
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12071, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 100, y: 100}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Animation
    m_Image: {fileID: -3237396543322336831, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
  m_Pos:
    serializedVersion: 2
    x: 284
    y: 73.6
    width: 690
    height: 687
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_OverlaysVisible: 1
  m_LockTracker:
    m_IsLocked: 0
  m_LastSelectedObjectID: 32756
--- !u!114 &20
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12013, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Scene
    m_Image: {fileID: 8634526014445323508, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
  m_Pos:
    serializedVersion: 2
    x: 937.60004
    y: 73.6
    width: 632.4
    height: 639
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData:
    - dockPosition: 0
      containerId: overlay-toolbar__top
      floating: 0
      collapsed: 0
      displayed: 1
      snapOffset: {x: 0, y: -26.400024}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 2
      id: Tool Settings
      index: 0
      layout: 1
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__top
      floating: 0
      collapsed: 0
      displayed: 1
      snapOffset: {x: -141, y: 149}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 1
      id: unity-grid-and-snap-toolbar
      index: 1
      layout: 1
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 1
      containerId: overlay-toolbar__top
      floating: 0
      collapsed: 0
      displayed: 1
      snapOffset: {x: 0, y: 24.8}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      id: unity-scene-view-toolbar
      index: 0
      layout: 1
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 1
      containerId: overlay-toolbar__top
      floating: 0
      collapsed: 0
      displayed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 1
      id: unity-search-toolbar
      index: 1
      layout: 1
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 0
      containerId: overlay-container--left
      floating: 0
      collapsed: 0
      displayed: 1
      snapOffset: {x: -43.200012, y: 180.8}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 1
      id: unity-transform-toolbar
      index: 0
      layout: 2
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 0
      containerId: overlay-container--left
      floating: 0
      collapsed: 0
      displayed: 1
      snapOffset: {x: 0, y: 197}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      id: unity-component-tools
      index: 1
      layout: 2
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 0
      containerId: overlay-container--right
      floating: 0
      collapsed: 0
      displayed: 1
      snapOffset: {x: 0.80000305, y: 26.399998}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      id: Orientation
      index: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      floating: 0
      collapsed: 0
      displayed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      id: Scene View/Light Settings
      index: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      floating: 0
      collapsed: 0
      displayed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      id: Scene View/Camera
      index: 1
      layout: 4
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      floating: 0
      collapsed: 0
      displayed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      id: Scene View/Cloth Constraints
      index: 1
      layout: 4
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      floating: 0
      collapsed: 0
      displayed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      id: Scene View/Cloth Collisions
      index: 2
      layout: 4
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      floating: 0
      collapsed: 0
      displayed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      id: Scene View/Navmesh Display
      index: 4
      layout: 4
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      floating: 0
      collapsed: 0
      displayed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      id: Scene View/Agent Display
      index: 5
      layout: 4
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      floating: 0
      collapsed: 0
      displayed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      id: Scene View/Obstacle Display
      index: 6
      layout: 4
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      floating: 0
      collapsed: 0
      displayed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      id: Scene View/Occlusion Culling
      index: 3
      layout: 4
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      floating: 0
      collapsed: 0
      displayed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      id: Scene View/Physics Debugger
      index: 4
      layout: 4
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      floating: 0
      collapsed: 0
      displayed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      id: Scene View/Scene Visibility
      index: 5
      layout: 4
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      floating: 0
      collapsed: 0
      displayed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      id: Scene View/Particles
      index: 6
      layout: 4
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      floating: 0
      collapsed: 0
      displayed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      id: Scene View/Tilemap
      index: 11
      layout: 4
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      floating: 0
      collapsed: 0
      displayed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      id: Scene View/Tilemap Palette Helper
      index: 12
      layout: 4
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      floating: 0
      collapsed: 0
      displayed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      id: Scene View/TrailRenderer
      index: 7
      layout: 4
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      floating: 0
      collapsed: 0
      displayed: 1
      snapOffset: {x: 151.20001, y: 10.399963}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      id: UnityEditor.SceneViewCameraOverlay
      index: 10
      layout: 4
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      floating: 0
      collapsed: 0
      displayed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      id: APV Overlay
      index: 8
      layout: 4
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      floating: 0
      collapsed: 0
      displayed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      id: Cinemachine Tool Settings
      index: 9
      layout: 4
      size: {x: 0, y: 0}
      sizeOverriden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      floating: 0
      collapsed: 0
      displayed: 1
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      id: UnityEditor.SceneViewCameraOverlay (0)
      index: 10
      layout: 4
      size: {x: 0, y: 0}
      sizeOverriden: 0
    m_OverlaysVisible: 1
  m_WindowGUID: cc27987af1a868c49b0894db9c0f5429
  m_Gizmos: 0
  m_OverrideSceneCullingMask: 6917529027641081856
  m_SceneIsLit: 1
  m_SceneLighting: 1
  m_2DMode: 0
  m_isRotationLocked: 0
  m_PlayAudio: 0
  m_AudioPlay: 0
  m_Position:
    m_Target: {x: -0.036124796, y: 2.171425, z: 0.13116506}
    speed: 2
    m_Value: {x: -0.036124796, y: 2.171425, z: 0.13116506}
  m_RenderMode: 0
  m_CameraMode:
    drawMode: 0
    name: Shaded
    section: Shading Mode
  m_ValidateTrueMetals: 0
  m_DoValidateTrueMetals: 0
  m_SceneViewState:
    m_AlwaysRefresh: 0
    showFog: 0
    showSkybox: 0
    showFlares: 0
    showImageEffects: 0
    showParticleSystems: 0
    showVisualEffectGraphs: 1
    m_FxEnabled: 1
  m_Grid:
    xGrid:
      m_Fade:
        m_Target: 0
        speed: 2
        m_Value: 0
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 0, y: 0}
    yGrid:
      m_Fade:
        m_Target: 1
        speed: 2
        m_Value: 1
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 1, y: 1}
    zGrid:
      m_Fade:
        m_Target: 0
        speed: 2
        m_Value: 0
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 1, y: 1}
    m_ShowGrid: 1
    m_GridAxis: 1
    m_gridOpacity: 0.5
  m_Rotation:
    m_Target: {x: -0.040202297, y: 0.006906158, z: -0.00032685028, w: -0.9992629}
    speed: 2
    m_Value: {x: -0.04019847, y: 0.006905501, z: -0.0003268192, w: -0.99916786}
  m_Size:
    m_Target: 0.48762155
    speed: 2
    m_Value: 0.48762155
  m_Ortho:
    m_Target: 0
    speed: 2
    m_Value: 0
  m_CameraSettings:
    m_Speed: 1
    m_SpeedNormalized: 0.5
    m_SpeedMin: 0.001
    m_SpeedMax: 2
    m_EasingEnabled: 1
    m_EasingDuration: 0.4
    m_AccelerationEnabled: 1
    m_FieldOfViewHorizontalOrVertical: 60
    m_NearClip: 0.03
    m_FarClip: 10000
    m_DynamicClip: 1
    m_OcclusionCulling: 0
  m_LastSceneViewRotation: {x: -0.026845926, y: 0.10865876, z: -0.0029466583, w: -0.99374866}
  m_LastSceneViewOrtho: 0
  m_ReplacementShader: {fileID: 0}
  m_ReplacementString: 
  m_SceneVisActive: 0
  m_LastLockedObject: {fileID: 0}
  m_ViewIsLockedToObject: 0
--- !u!114 &21
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12014, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 230, y: 250}
  m_MaxSize: {x: 10000, y: 10000}
  m_TitleContent:
    m_Text: Project
    m_Image: {fileID: -5179483145760003458, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 733.60004
    width: 541.4
    height: 329.80005
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_OverlaysVisible: 1
  m_SearchFilter:
    m_NameFilter: 
    m_ClassNames: []
    m_AssetLabels: []
    m_AssetBundleNames: []
    m_ReferencingInstanceIDs: 
    m_SceneHandles: 
    m_ShowAllHits: 0
    m_SkipHidden: 0
    m_SearchArea: 1
    m_Folders:
    - Assets/Scripts/Data
    m_Globs: []
    m_OriginalText: 
    m_ImportLogFlags: 0
    m_FilterByTypeIntersection: 0
  m_ViewMode: 1
  m_StartGridSize: 16
  m_LastFolders:
  - Assets/Scripts/Data
  m_LastFoldersGridSize: 16
  m_LastProjectPath: "D:\\unity\\projects\\\u634F\u8138\u6362\u88C5\u7CFB\u7EDF"
  m_LockTracker:
    m_IsLocked: 0
  m_FolderTreeState:
    scrollPos: {x: 0, y: 154.19995}
    m_SelectedIDs: 70860000
    m_LastClickedID: 34416
    m_ExpandedIDs: 000000004e86000050860000528600005486000056860000588600005a86000000ca9a3b
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_ClientGUIView: {fileID: 14}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_AssetTreeState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 
    m_LastClickedID: 0
    m_ExpandedIDs: 000000004e86000050860000528600005486000056860000588600005a86000000ca9a3b
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_ListAreaState:
    m_SelectedInstanceIDs: 
    m_LastClickedInstanceID: 0
    m_HadKeyboardFocusLastEvent: 1
    m_ExpandedInstanceIDs: c62300009c980000eea800005c860000428a0000846d0000004601003082000090770000649e010088ab000000000000346f000046910000283bfffffe40ffff
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_ClientGUIView: {fileID: 13}
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
    m_NewAssetIndexInList: -1
    m_ScrollPosition: {x: 0, y: 0}
    m_GridSize: 16
  m_SkipHiddenPackages: 0
  m_DirectoriesAreaWidth: 154.6
--- !u!114 &22
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12003, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 100, y: 100}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Console
    m_Image: {fileID: -4950941429401207979, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
  m_Pos:
    serializedVersion: 2
    x: 542.4
    y: 733.60004
    width: 1027.6
    height: 329.80005
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_OverlaysVisible: 1
--- !u!114 &23
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6629f1bb292b749a18b5fff7994c8b19, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 600, y: 350}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Unity Version Control
    m_Image: {fileID: 0}
    m_Tooltip: 
  m_Pos:
    serializedVersion: 2
    x: 544
    y: 733.60004
    width: 1029.2
    height: 329.80005
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_OverlaysVisible: 1
  mForceToReOpen: 0
--- !u!114 &24
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12070, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 900, y: 216}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Profiler
    m_Image: {fileID: 2169972056302973491, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
  m_Pos:
    serializedVersion: 2
    x: 544
    y: 733.60004
    width: 1029.2
    height: 329.80005
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_OverlaysVisible: 1
  m_Recording: 1
  m_ActiveNativePlatformSupportModuleName: 
  m_AllModules:
  - rid: 8370661371345371136
  - rid: 8370661371345371137
  - rid: 8370661371345371138
  - rid: 8370661371345371139
  - rid: 8370661371345371140
  - rid: 8370661371345371141
  - rid: 8370661371345371142
  - rid: 8370661371345371143
  - rid: 8370661371345371144
  - rid: 8370661371345371145
  - rid: 8370661371345371146
  - rid: 8370661371345371147
  - rid: 8370661371345371148
  - rid: 8370661371345371149
  m_CallstackRecordMode: 1
  m_ClearOnPlay: 0
  references:
    version: 2
    RefIds:
    - rid: 8370661371345371136
      type: {class: CPUProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.CPUProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ViewType: 1
        updateViewLive: 0
        m_CurrentFrameIndex: 268436
        m_HierarchyOverruledThreadFromSelection: 0
        m_ProfilerViewFilteringOptions: 1
        m_FrameDataHierarchyView:
          m_Serialized: 0
          m_TreeViewState:
            scrollPos: {x: 0, y: 0}
            m_SelectedIDs: 
            m_LastClickedID: 0
            m_ExpandedIDs: 
            m_RenameOverlay:
              m_UserAcceptedRename: 0
              m_Name: 
              m_OriginalName: 
              m_EditFieldRect:
                serializedVersion: 2
                x: 0
                y: 0
                width: 0
                height: 0
              m_UserData: 0
              m_IsWaitingForDelay: 0
              m_IsRenaming: 0
              m_OriginalEventType: 11
              m_IsRenamingFilename: 0
              m_ClientGUIView: {fileID: 0}
            m_SearchString: 
          m_MultiColumnHeaderState:
            m_Columns: []
            m_VisibleColumns: 
            m_SortedColumns: 
          m_ThreadIndexInThreadNames: 0
          m_DetailedViewType: 0
          m_DetailedViewSpliterState:
            ID: 0
            splitterInitialOffset: 0
            currentActiveSplitter: 0
            realSizes: []
            relativeSizes: []
            minSizes: []
            maxSizes: []
            lastTotalSize: 0
            splitSize: 0
            xOffset: 0
            m_Version: 1
            oldRealSizes: 
            oldMinSizes: 
            oldMaxSizes: 
            oldSplitSize: 0
          m_DetailedObjectsView:
            m_SelectedID: 0
            m_TreeViewState:
              scrollPos: {x: 0, y: 0}
              m_SelectedIDs: 
              m_LastClickedID: 0
              m_ExpandedIDs: 
              m_RenameOverlay:
                m_UserAcceptedRename: 0
                m_Name: 
                m_OriginalName: 
                m_EditFieldRect:
                  serializedVersion: 2
                  x: 0
                  y: 0
                  width: 0
                  height: 0
                m_UserData: 0
                m_IsWaitingForDelay: 0
                m_IsRenaming: 0
                m_OriginalEventType: 11
                m_IsRenamingFilename: 0
                m_ClientGUIView: {fileID: 0}
              m_SearchString: 
            m_MultiColumnHeaderState:
              m_Columns: []
              m_VisibleColumns: 
              m_SortedColumns: 
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
          m_DetailedCallsView:
            m_SelectedID: -1
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
            m_CalleesTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
            m_CallersTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
          m_FullThreadName: Main Thread
          m_ThreadName: Main Thread
          <threadId>k__BackingField: 0
          <threadIndex>k__BackingField: -1
          m_GroupName: 
    - rid: 8370661371345371137
      type: {class: GPUProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.GPUProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ViewType: 0
        updateViewLive: 0
        m_CurrentFrameIndex: -1
        m_HierarchyOverruledThreadFromSelection: 0
        m_ProfilerViewFilteringOptions: 1
        m_FrameDataHierarchyView:
          m_Serialized: 0
          m_TreeViewState:
            scrollPos: {x: 0, y: 0}
            m_SelectedIDs: 
            m_LastClickedID: 0
            m_ExpandedIDs: 
            m_RenameOverlay:
              m_UserAcceptedRename: 0
              m_Name: 
              m_OriginalName: 
              m_EditFieldRect:
                serializedVersion: 2
                x: 0
                y: 0
                width: 0
                height: 0
              m_UserData: 0
              m_IsWaitingForDelay: 0
              m_IsRenaming: 0
              m_OriginalEventType: 11
              m_IsRenamingFilename: 0
              m_ClientGUIView: {fileID: 0}
            m_SearchString: 
          m_MultiColumnHeaderState:
            m_Columns: []
            m_VisibleColumns: 
            m_SortedColumns: 
          m_ThreadIndexInThreadNames: 0
          m_DetailedViewType: 0
          m_DetailedViewSpliterState:
            ID: 0
            splitterInitialOffset: 0
            currentActiveSplitter: 0
            realSizes: []
            relativeSizes: []
            minSizes: []
            maxSizes: []
            lastTotalSize: 0
            splitSize: 0
            xOffset: 0
            m_Version: 1
            oldRealSizes: 
            oldMinSizes: 
            oldMaxSizes: 
            oldSplitSize: 0
          m_DetailedObjectsView:
            m_SelectedID: 0
            m_TreeViewState:
              scrollPos: {x: 0, y: 0}
              m_SelectedIDs: 
              m_LastClickedID: 0
              m_ExpandedIDs: 
              m_RenameOverlay:
                m_UserAcceptedRename: 0
                m_Name: 
                m_OriginalName: 
                m_EditFieldRect:
                  serializedVersion: 2
                  x: 0
                  y: 0
                  width: 0
                  height: 0
                m_UserData: 0
                m_IsWaitingForDelay: 0
                m_IsRenaming: 0
                m_OriginalEventType: 11
                m_IsRenamingFilename: 0
                m_ClientGUIView: {fileID: 0}
              m_SearchString: 
            m_MultiColumnHeaderState:
              m_Columns: []
              m_VisibleColumns: 
              m_SortedColumns: 
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
          m_DetailedCallsView:
            m_SelectedID: -1
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
            m_CalleesTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
            m_CallersTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
          m_FullThreadName: Main Thread
          m_ThreadName: Main Thread
          <threadId>k__BackingField: 0
          <threadIndex>k__BackingField: -1
          m_GroupName: 
    - rid: 8370661371345371138
      type: {class: RenderingProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.RenderingProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 8370661371345371139
      type: {class: MemoryProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.MemoryProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ViewSplit:
          ID: 0
          splitterInitialOffset: 0
          currentActiveSplitter: -1
          realSizes:
          - 0
          - 0
          relativeSizes:
          - 0.7
          - 0.3
          minSizes:
          - 450
          - 50
          maxSizes:
          - 0
          - 0
          lastTotalSize: 0
          splitSize: 6
          xOffset: 0
          m_Version: 1
          oldRealSizes: 
          oldMinSizes: 
          oldMaxSizes: 
          oldSplitSize: 0
    - rid: 8370661371345371140
      type: {class: AudioProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.AudioProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ShowInactiveDSPChains: 0
        m_HighlightAudibleDSPChains: 1
        m_DSPGraphZoomFactor: 1
        m_DSPGraphHorizontalLayout: 0
    - rid: 8370661371345371141
      type: {class: VideoProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.VideoProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 8370661371345371142
      type: {class: PhysicsProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.PhysicsProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 8370661371345371143
      type: {class: Physics2DProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.Physics2DProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 8370661371345371144
      type: {class: UIProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.UIProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 8370661371345371145
      type: {class: UIDetailsProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.UIDetailsProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 8370661371345371146
      type: {class: GlobalIlluminationProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.GlobalIlluminationProfilerModule,
          UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 8370661371345371147
      type: {class: VirtualTexturingProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.VirtualTexturingProfilerModule,
          UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_VTProfilerView:
          rid: 8370661371345371150
    - rid: 8370661371345371148
      type: {class: FileIOProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.FileIOProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 8370661371345371149
      type: {class: AssetLoadingProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.AssetLoadingProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 8370661371345371150
      type: {class: VirtualTexturingProfilerView, ns: UnityEditor, asm: UnityEditor.CoreModule}
      data:
        m_SortAscending: 0
        m_SortedColumn: -1
--- !u!114 &25
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12019, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 275, y: 100}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Inspector
    m_Image: {fileID: -440750813802333266, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
  m_Pos:
    serializedVersion: 2
    x: 1572
    y: 73.6
    width: 475
    height: 989.80005
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_OverlaysVisible: 1
  m_ObjectsLockedBeforeSerialization: []
  m_InstanceIDsLockedBeforeSerialization: 
  m_PreviewResizer:
    m_CachedPref: -160
    m_ControlHash: 1412526313
    m_PrefName: Preview_InspectorPreview
  m_LastInspectedObjectInstanceID: -1
  m_LastVerticalScrollValue: 0
  m_GlobalObjectId: 
  m_InspectorMode: 0
  m_LockTracker:
    m_IsLocked: 0
  m_PreviewWindow: {fileID: 0}
