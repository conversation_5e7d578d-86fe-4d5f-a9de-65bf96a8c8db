# C# language support

<PERSON><PERSON><PERSON> uses a high performance subset of C# called High Performance C# (HPC#) which has a number of limitations and differences between C#.

|**Topic**|**Description**|
|---|---|
|[HPC# overview](csharp-hpc-overview.md)|Understand how HPC# works with <PERSON>urst.|
|[C#/.NET type support](csharp-type-support.md)| Understand the supported C# features.|
|[C#/.NET System namespace support](csharp-system-support.md)| Understand what's supported in the System namespace.|
|[Static read-only fields and static constructor support](csharp-static-read-only-support.md)|Use static read-only fields and static constructors in Burst code.|
|[String support](csharp-string-support.md)|Use strings in Burst code.|
|[Calling Burst compiled code](csharp-calling-burst-code.md)| Call Burst compiled code from managed code.|
[Function pointers](csharp-function-pointers.md)|Use function pointers to work with dynamic functions.|
|[SharedStatic struct](csharp-shared-static.md)| Use SharedStatic to share static mutable data.|

## Additional resources

* [Burst instrinsics overview](csharp-burst-intrinsics.md)
