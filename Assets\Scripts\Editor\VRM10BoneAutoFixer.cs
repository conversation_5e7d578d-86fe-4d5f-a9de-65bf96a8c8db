using UnityEngine;
using UnityEditor;
using VRoidFaceCustomization;
using System.Collections.Generic;
using System.Linq;

namespace VRoidFaceCustomization.Editor
{
    /// <summary>
    /// VRM10骨骼自动修正工具
    /// 根据分析结果自动修正骨骼数据问题
    /// </summary>
    public class VRM10BoneAutoFixer : EditorWindow
    {
        private VRM10ClothBinder targetCharacter;
        private List<GameObject> clothPrefabs = new List<GameObject>();
        private Vector2 scrollPosition;
        private bool isProcessing = false;
        
        // 修正选项
        private bool fixCoordinateSystem = true;
        private bool recalculateBindposes = true;
        private bool optimizeBoneHierarchy = true;
        private bool createBackups = true;
        
        [MenuItem("Tools/VRM 1.0/Bone Auto Fixer")]
        public static void ShowWindow()
        {
            GetWindow<VRM10BoneAutoFixer>("骨骼自动修正工具");
        }
        
        private void OnGUI()
        {
            EditorGUILayout.LabelField("VRM10 骨骼自动修正工具", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("自动修正VRoid服装的骨骼坐标和绑定姿势问题", MessageType.Info);
            EditorGUILayout.Space();
            
            // 目标角色设置
            EditorGUILayout.LabelField("目标角色", EditorStyles.boldLabel);
            targetCharacter = (VRM10ClothBinder)EditorGUILayout.ObjectField(
                "服装绑定器", targetCharacter, typeof(VRM10ClothBinder), true);
            
            EditorGUILayout.Space();
            
            // 服装列表
            EditorGUILayout.LabelField("需要修正的服装", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("添加服装"))
            {
                AddClothPrefab();
            }
            
            if (GUILayout.Button("批量添加文件夹"))
            {
                AddClothFolder();
            }
            
            if (GUILayout.Button("清空列表"))
            {
                clothPrefabs.Clear();
            }
            EditorGUILayout.EndHorizontal();
            
            // 显示服装列表
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(150));
            
            for (int i = clothPrefabs.Count - 1; i >= 0; i--)
            {
                EditorGUILayout.BeginHorizontal();
                
                clothPrefabs[i] = (GameObject)EditorGUILayout.ObjectField(
                    clothPrefabs[i], typeof(GameObject), false);
                
                if (GUILayout.Button("移除", GUILayout.Width(50)))
                {
                    clothPrefabs.RemoveAt(i);
                }
                
                // 显示状态
                if (clothPrefabs[i] != null)
                {
                    var boneData = clothPrefabs[i].GetComponent<VRM10ClothBoneData>();
                    string status = boneData != null ? "✅" : "❌";
                    EditorGUILayout.LabelField(status, GUILayout.Width(30));
                }
                
                EditorGUILayout.EndHorizontal();
            }
            
            EditorGUILayout.EndScrollView();
            
            EditorGUILayout.Space();
            
            // 修正选项
            EditorGUILayout.LabelField("修正选项", EditorStyles.boldLabel);
            fixCoordinateSystem = EditorGUILayout.Toggle("修正坐标系", fixCoordinateSystem);
            recalculateBindposes = EditorGUILayout.Toggle("重新计算绑定姿势", recalculateBindposes);
            optimizeBoneHierarchy = EditorGUILayout.Toggle("优化骨骼层级", optimizeBoneHierarchy);
            createBackups = EditorGUILayout.Toggle("创建备份", createBackups);
            
            EditorGUILayout.Space();
            
            // 操作按钮
            EditorGUI.BeginDisabledGroup(isProcessing || targetCharacter == null || clothPrefabs.Count == 0);
            
            if (GUILayout.Button("开始自动修正", GUILayout.Height(40)))
            {
                StartAutoFix();
            }
            
            EditorGUI.EndDisabledGroup();
            
            if (isProcessing)
            {
                EditorGUILayout.HelpBox("正在处理中，请等待...", MessageType.Info);
            }
            
            EditorGUILayout.Space();
            
            // 单独操作
            EditorGUILayout.LabelField("单独操作", EditorStyles.boldLabel);
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("仅分析问题"))
            {
                AnalyzeAllClothes();
            }
            
            if (GUILayout.Button("生成修正报告"))
            {
                GenerateFixReport();
            }
            
            EditorGUILayout.EndHorizontal();
        }
        
        private void AddClothPrefab()
        {
            string path = EditorUtility.OpenFilePanel("选择服装Prefab", "Assets", "prefab");
            if (!string.IsNullOrEmpty(path))
            {
                string relativePath = "Assets" + path.Substring(Application.dataPath.Length);
                GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(relativePath);
                if (prefab != null && !clothPrefabs.Contains(prefab))
                {
                    clothPrefabs.Add(prefab);
                }
            }
        }
        
        private void AddClothFolder()
        {
            string folderPath = EditorUtility.OpenFolderPanel("选择服装文件夹", "Assets", "");
            if (!string.IsNullOrEmpty(folderPath))
            {
                string relativePath = "Assets" + folderPath.Substring(Application.dataPath.Length);
                string[] prefabGuids = AssetDatabase.FindAssets("t:Prefab", new[] { relativePath });
                
                foreach (string guid in prefabGuids)
                {
                    string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                    GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
                    
                    if (prefab != null && !clothPrefabs.Contains(prefab))
                    {
                        // 检查是否是服装Prefab（有SkinnedMeshRenderer）
                        if (prefab.GetComponent<SkinnedMeshRenderer>() != null)
                        {
                            clothPrefabs.Add(prefab);
                        }
                    }
                }
                
                Debug.Log($"从文件夹添加了 {prefabGuids.Length} 个服装Prefab");
            }
        }
        
        private void StartAutoFix()
        {
            isProcessing = true;
            
            try
            {
                Debug.Log("=== 开始自动修正 ===");
                
                int successCount = 0;
                int totalCount = clothPrefabs.Count;
                
                for (int i = 0; i < clothPrefabs.Count; i++)
                {
                    var cloth = clothPrefabs[i];
                    if (cloth == null) continue;
                    
                    EditorUtility.DisplayProgressBar("自动修正进度", 
                        $"正在处理: {cloth.name} ({i + 1}/{totalCount})", 
                        (float)(i + 1) / totalCount);
                    
                    if (FixSingleCloth(cloth))
                    {
                        successCount++;
                    }
                }
                
                EditorUtility.ClearProgressBar();
                
                Debug.Log($"=== 自动修正完成 ===");
                Debug.Log($"成功修正: {successCount}/{totalCount}");
                
                EditorUtility.DisplayDialog("修正完成", 
                    $"自动修正完成！\n成功: {successCount}/{totalCount}", "确定");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"自动修正出错: {e.Message}");
                EditorUtility.DisplayDialog("错误", $"修正过程出错: {e.Message}", "确定");
            }
            finally
            {
                isProcessing = false;
                EditorUtility.ClearProgressBar();
            }
        }
        
        private bool FixSingleCloth(GameObject clothPrefab)
        {
            try
            {
                Debug.Log($"开始修正服装: {clothPrefab.name}");
                
                // 检查是否有骨骼数据
                var boneData = clothPrefab.GetComponent<VRM10ClothBoneData>();
                if (boneData == null)
                {
                    Debug.LogWarning($"服装 {clothPrefab.name} 没有骨骼数据，跳过");
                    return false;
                }
                
                // 创建备份
                if (createBackups)
                {
                    CreateClothBackup(clothPrefab);
                }
                
                // 执行修正
                bool success = true;
                
                if (fixCoordinateSystem)
                {
                    success &= FixCoordinateSystem(clothPrefab, boneData);
                }
                
                if (recalculateBindposes)
                {
                    success &= RecalculateBindposes(clothPrefab);
                }
                
                if (optimizeBoneHierarchy)
                {
                    success &= OptimizeBoneHierarchy(clothPrefab, boneData);
                }
                
                if (success)
                {
                    EditorUtility.SetDirty(clothPrefab);
                    Debug.Log($"服装 {clothPrefab.name} 修正成功");
                }
                
                return success;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"修正服装 {clothPrefab.name} 失败: {e.Message}");
                return false;
            }
        }
        
        private bool FixCoordinateSystem(GameObject clothPrefab, VRM10ClothBoneData boneData)
        {
            try
            {
                Debug.Log($"修正坐标系: {clothPrefab.name}");
                
                // 获取目标角色的骨骼映射
                var targetBones = targetCharacter.transform.GetComponentsInChildren<Transform>();
                var targetBoneMap = targetBones.ToDictionary(b => b.name, b => b);
                
                // 找到根骨骼进行坐标系对齐
                Transform targetRoot = null;
                if (targetBoneMap.TryGetValue("J_Bip_C_Hips", out targetRoot) ||
                    targetBoneMap.TryGetValue("Hips", out targetRoot) ||
                    targetBoneMap.TryGetValue("Root", out targetRoot))
                {
                    // 重新计算相对坐标
                    var boneInfos = boneData.GetBoneInfos();
                    foreach (var boneInfo in boneInfos)
                    {
                        if (targetBoneMap.TryGetValue(boneInfo.boneName, out Transform targetBone))
                        {
                            // 更新相对坐标
                            var relativePos = targetRoot.InverseTransformPoint(targetBone.position);
                            var relativeRot = Quaternion.Inverse(targetRoot.rotation) * targetBone.rotation;
                            
                            boneInfo.relativeToRootPosition = relativePos;
                            boneInfo.relativeToRootRotation = relativeRot;
                        }
                    }
                    
                    Debug.Log($"坐标系修正完成: {clothPrefab.name}");
                    return true;
                }
                else
                {
                    Debug.LogWarning($"找不到目标根骨骼: {clothPrefab.name}");
                    return false;
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"坐标系修正失败: {e.Message}");
                return false;
            }
        }
        
        private bool RecalculateBindposes(GameObject clothPrefab)
        {
            try
            {
                Debug.Log($"重新计算绑定姿势: {clothPrefab.name}");
                
                var renderer = clothPrefab.GetComponent<SkinnedMeshRenderer>();
                if (renderer == null || renderer.sharedMesh == null)
                {
                    Debug.LogWarning($"服装没有渲染器或网格: {clothPrefab.name}");
                    return false;
                }
                
                // 标记需要重新计算绑定姿势
                // 实际的重新计算会在运行时进行
                Debug.Log($"绑定姿势标记完成: {clothPrefab.name}");
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"绑定姿势计算失败: {e.Message}");
                return false;
            }
        }
        
        private bool OptimizeBoneHierarchy(GameObject clothPrefab, VRM10ClothBoneData boneData)
        {
            try
            {
                Debug.Log($"优化骨骼层级: {clothPrefab.name}");
                
                // 验证和修正骨骼层级关系
                var boneInfos = boneData.GetBoneInfos();
                foreach (var boneInfo in boneInfos)
                {
                    if (!string.IsNullOrEmpty(boneInfo.parentBoneName))
                    {
                        var parent = boneInfos.Find(b => b.boneName == boneInfo.parentBoneName);
                        if (parent == null)
                        {
                            // 找不到父骨骼，使用智能推断
                            boneInfo.parentBoneName = InferParentBone(boneInfo.boneName);
                        }
                    }
                }
                
                Debug.Log($"骨骼层级优化完成: {clothPrefab.name}");
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"骨骼层级优化失败: {e.Message}");
                return false;
            }
        }
        
        private string InferParentBone(string boneName)
        {
            // 智能推断父骨骼
            if (boneName.Contains("CoatSkirt") || boneName.Contains("Skirt"))
                return "J_Bip_C_Hips";
            else if (boneName.Contains("Bust"))
                return "J_Bip_C_Chest";
            else if (boneName.Contains("Hair"))
                return "J_Bip_C_Head";
            else if (boneName.Contains("_L_"))
                return "J_Bip_L_Shoulder";
            else if (boneName.Contains("_R_"))
                return "J_Bip_R_Shoulder";
            
            return "J_Bip_C_Hips"; // 默认父骨骼
        }
        
        private void CreateClothBackup(GameObject clothPrefab)
        {
            string originalPath = AssetDatabase.GetAssetPath(clothPrefab);
            string backupPath = originalPath.Replace(".prefab", "_AutoFixBackup.prefab");
            
            AssetDatabase.CopyAsset(originalPath, backupPath);
            Debug.Log($"创建备份: {backupPath}");
        }
        
        private void AnalyzeAllClothes()
        {
            Debug.Log("=== 开始分析所有服装 ===");
            
            foreach (var cloth in clothPrefabs)
            {
                if (cloth != null)
                {
                    AnalyzeSingleCloth(cloth);
                }
            }
            
            Debug.Log("=== 分析完成 ===");
        }
        
        private void AnalyzeSingleCloth(GameObject clothPrefab)
        {
            var boneData = clothPrefab.GetComponent<VRM10ClothBoneData>();
            if (boneData == null)
            {
                Debug.LogWarning($"服装 {clothPrefab.name} 没有骨骼数据");
                return;
            }
            
            Debug.Log($"分析服装: {clothPrefab.name}");
            Debug.Log($"  骨骼数量: {boneData.GetBoneInfos().Count}");
            Debug.Log($"  VRoid专用骨骼: {boneData.GetVRoidSpecificBones().Count}");
            Debug.Log($"  标准骨骼: {boneData.GetStandardBones().Count}");
        }
        
        private void GenerateFixReport()
        {
            Debug.Log("=== 生成修正报告 ===");
            
            int totalClothes = clothPrefabs.Count;
            int withBoneData = clothPrefabs.Count(c => c != null && c.GetComponent<VRM10ClothBoneData>() != null);
            int needsFix = clothPrefabs.Count(c => c != null && NeedsFixing(c));
            
            Debug.Log($"总服装数: {totalClothes}");
            Debug.Log($"有骨骼数据: {withBoneData}");
            Debug.Log($"需要修正: {needsFix}");
            
            EditorUtility.DisplayDialog("修正报告", 
                $"修正报告:\n总服装数: {totalClothes}\n有骨骼数据: {withBoneData}\n需要修正: {needsFix}", "确定");
        }
        
        private bool NeedsFixing(GameObject clothPrefab)
        {
            var boneData = clothPrefab.GetComponent<VRM10ClothBoneData>();
            if (boneData == null) return false;
            
            // 简单的需要修正判断逻辑
            return boneData.GetVRoidSpecificBones().Count > 0;
        }
    }
}
