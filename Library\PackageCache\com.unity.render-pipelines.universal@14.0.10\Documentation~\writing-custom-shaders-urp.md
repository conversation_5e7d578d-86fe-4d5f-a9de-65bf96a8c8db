# Writing custom shaders

This section contains guidelines that help you to get started with writing shaders for Universal Render Pipeline (URP).

The section contains the following topics:

* [Creating a sample scene](writing-shaders-urp-basic-prerequisites.md)
* [URP basic unlit shader](writing-shaders-urp-basic-unlit-structure.md)
    * [Basic ShaderLab structure](writing-shaders-urp-basic-unlit-structure.md#basic-shaderlab-structure)
* [URP unlit shader with color input](writing-shaders-urp-unlit-color.md)
* [Drawing a texture](writing-shaders-urp-unlit-texture.md)
* [Visualizing normal vectors](writing-shaders-urp-unlit-normals.md)
* [Reconstruct the world space positions](writing-shaders-urp-reconstruct-world-position.md)

Each example covers some extra information compared to the basic shader example. If you are new to writing shaders using Unity's ShaderLab language, consider going through the sections in the order of appearance on this page.
