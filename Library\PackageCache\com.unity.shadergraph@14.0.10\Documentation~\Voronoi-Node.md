# Voronoi Node

## Description

Generates a Voronoi, or [<PERSON>or<PERSON>](https://en.wikipedia.org/wiki/Worley_noise), noise based on input **UV**. Voronoi noise is generated by calculating distances between a pixel and a lattice of points. By offsetting these points by a pseudo-random number, controlled by input **Angle Offset**, a cluster of cells can be generated. The scale of these cells, and the resulting noise, is controlled by input **Cell Density**. The output **Cells** contains the raw cell data.

You can also choose to use two different hashing methods for calculating the noise. As of Unity version 2021.2, the Voronoi node defaults to the **Deterministic** hash, to ensure consistent results for noise generation across platforms.

## Ports

| Name        | Direction           | Type  | Binding | Description |
|:------------ |:-------------|:-----|:---|:---|
| UV      | Input | Vector 2 | UV | Input UV value |
| Angle Offset      | Input | Float    | None | Offset value for points |
| Cell Density      | Input | Float    | None | Density of cells generated |
| Out | Output      |    Float    | None | Output noise value |
| Cells | Output      |    Float    | None | Raw cell data |

## Controls

| Name        | Type           | Options  | Description |
|:------------ |:-------------|:-----|:---|
| Hash Type      | Dropdown | Deterministic, LegacySine | Selects the hash function used to generate random numbers for noise generation. |


## Generated Code Example

The following example code represents one possible outcome of this node.

```
inline float2 unity_voronoi_noise_randomVector (float2 UV, float offset)
{
    float2x2 m = float2x2(15.27, 47.63, 99.41, 89.98);
    UV = frac(sin(mul(UV, m)) * 46839.32);
    return float2(sin(UV.y*+offset)*0.5+0.5, cos(UV.x*offset)*0.5+0.5);
}

void Unity_Voronoi_float(float2 UV, float AngleOffset, float CellDensity, out float Out, out float Cells)
{
    float2 g = floor(UV * CellDensity);
    float2 f = frac(UV * CellDensity);
    float t = 8.0;
    float3 res = float3(8.0, 0.0, 0.0);

    for(int y=-1; y<=1; y++)
    {
        for(int x=-1; x<=1; x++)
        {
            float2 lattice = float2(x,y);
            float2 offset = unity_voronoi_noise_randomVector(lattice + g, AngleOffset);
            float d = distance(lattice + offset, f);
            if(d < res.x)
            {
                res = float3(d, offset.x, offset.y);
                Out = res.x;
                Cells = res.y;
            }
        }
    }
}
```
