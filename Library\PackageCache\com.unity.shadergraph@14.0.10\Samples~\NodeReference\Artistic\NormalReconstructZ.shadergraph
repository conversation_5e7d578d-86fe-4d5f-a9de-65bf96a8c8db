{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "4fc856c489c047538b430031331e4ae6",
    "m_Properties": [],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "883cf820b2cb480094218595d39b77eb"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "da1439c4438643ed8d4fbc67257a0ee4"
        },
        {
            "m_Id": "6797ac6e8d754708a782814acb8a9a75"
        },
        {
            "m_Id": "b4ba844d8b314c068a54739b0d50f289"
        },
        {
            "m_Id": "3f7a0f0b001745c3b88f27970d1062b3"
        },
        {
            "m_Id": "f1352afad7e84d53b0e9b3ff5cb60b9b"
        },
        {
            "m_Id": "d7b0ff4480f146228ff28b88a4ba9411"
        },
        {
            "m_Id": "57a04f02851d46d988c6e06f7bf55403"
        },
        {
            "m_Id": "451b54fd02c04db8928fee64ee7f652c"
        },
        {
            "m_Id": "df0f0a8f3e0c40158670ca44a6b39dc3"
        },
        {
            "m_Id": "0435aea1cea04891b0af16ae384f8d09"
        },
        {
            "m_Id": "64a8c8377d7347e18e02fc8b15672a44"
        },
        {
            "m_Id": "31ba2ac1c23c4d2eb17179c2590875bf"
        },
        {
            "m_Id": "f3540c81e63b471f94e89ce40b887558"
        },
        {
            "m_Id": "65f038ec0a0c4ac090871eb59a779050"
        },
        {
            "m_Id": "6802b2b823224e709464002ab7d472b7"
        },
        {
            "m_Id": "3b11fba0fb8942129e9bcf0950e71c9a"
        },
        {
            "m_Id": "1f78fe66f7924e04aabde4a478e3b0ca"
        },
        {
            "m_Id": "1d6e742aec414d86b37789221b6e1723"
        },
        {
            "m_Id": "9c312ecff26542dba24b6682e4686b07"
        }
    ],
    "m_GroupDatas": [
        {
            "m_Id": "87067ddff13d42c784d6d00b30d9b8ce"
        },
        {
            "m_Id": "615aba30429f446ebf8ecf63ac15a6e5"
        }
    ],
    "m_StickyNoteDatas": [
        {
            "m_Id": "c88532443058483f92a8e4b0bf2b55ce"
        },
        {
            "m_Id": "9340ceef2ab34edfa58c7f0933200911"
        },
        {
            "m_Id": "e104587ec8694bc1b2280337f9df02f8"
        },
        {
            "m_Id": "eabe1901e13542878b2ba7dd0193ea86"
        },
        {
            "m_Id": "c8342633d91548e4849963769e0a8df7"
        },
        {
            "m_Id": "f7d32794429f475183535d91754dafab"
        },
        {
            "m_Id": "15f488acfbc149a9978b2c5dd757531a"
        },
        {
            "m_Id": "6e481ffcb4304bdca6a9c9db916d5491"
        },
        {
            "m_Id": "297a4725476842f984300b5123dda2f0"
        },
        {
            "m_Id": "36ccde71285944f8a3804690161e507c"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "0435aea1cea04891b0af16ae384f8d09"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "64a8c8377d7347e18e02fc8b15672a44"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "0435aea1cea04891b0af16ae384f8d09"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "64a8c8377d7347e18e02fc8b15672a44"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "0435aea1cea04891b0af16ae384f8d09"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "6802b2b823224e709464002ab7d472b7"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "31ba2ac1c23c4d2eb17179c2590875bf"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "f3540c81e63b471f94e89ce40b887558"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "3b11fba0fb8942129e9bcf0950e71c9a"
                },
                "m_SlotId": 5
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1f78fe66f7924e04aabde4a478e3b0ca"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "57a04f02851d46d988c6e06f7bf55403"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "df0f0a8f3e0c40158670ca44a6b39dc3"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "64a8c8377d7347e18e02fc8b15672a44"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "31ba2ac1c23c4d2eb17179c2590875bf"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "65f038ec0a0c4ac090871eb59a779050"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "3b11fba0fb8942129e9bcf0950e71c9a"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "6802b2b823224e709464002ab7d472b7"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "3b11fba0fb8942129e9bcf0950e71c9a"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "6802b2b823224e709464002ab7d472b7"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "3b11fba0fb8942129e9bcf0950e71c9a"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "d7b0ff4480f146228ff28b88a4ba9411"
                },
                "m_SlotId": 4
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "57a04f02851d46d988c6e06f7bf55403"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "d7b0ff4480f146228ff28b88a4ba9411"
                },
                "m_SlotId": 5
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "57a04f02851d46d988c6e06f7bf55403"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "df0f0a8f3e0c40158670ca44a6b39dc3"
                },
                "m_SlotId": 3
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "451b54fd02c04db8928fee64ee7f652c"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "f3540c81e63b471f94e89ce40b887558"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "65f038ec0a0c4ac090871eb59a779050"
                },
                "m_SlotId": 0
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": [
            {
                "m_Id": "da1439c4438643ed8d4fbc67257a0ee4"
            },
            {
                "m_Id": "6797ac6e8d754708a782814acb8a9a75"
            },
            {
                "m_Id": "b4ba844d8b314c068a54739b0d50f289"
            }
        ]
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 200.0
        },
        "m_Blocks": [
            {
                "m_Id": "3f7a0f0b001745c3b88f27970d1062b3"
            },
            {
                "m_Id": "1d6e742aec414d86b37789221b6e1723"
            },
            {
                "m_Id": "9c312ecff26542dba24b6682e4686b07"
            }
        ]
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Shader Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": ""
    },
    "m_ActiveTargets": [
        {
            "m_Id": "15f99706d93b4ef897ab1da0a1a7a0c0"
        },
        {
            "m_Id": "f86723075c27450f982e6a3b994e4859"
        },
        {
            "m_Id": "1597f52791a14ae49921bb52b0c8f2a4"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "004dc390bc7b4cde8e5cb6f913c7b2b5"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2Node",
    "m_ObjectId": "0435aea1cea04891b0af16ae384f8d09",
    "m_Group": {
        "m_Id": "615aba30429f446ebf8ecf63ac15a6e5"
    },
    "m_Name": "Vector 2",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1166.0001220703125,
            "y": 224.50001525878907,
            "width": 126.9998779296875,
            "height": 100.99992370605469
        }
    },
    "m_Slots": [
        {
            "m_Id": "400aafdb2877422bbe6a626199107bd3"
        },
        {
            "m_Id": "7f4a1a79d85f49b08f3d2d67041465f9"
        },
        {
            "m_Id": "74ac5b2a66324a768b9cc51e8e9365f7"
        }
    ],
    "synonyms": [
        "2",
        "v2",
        "vec2",
        "float2"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "07f7a99bcb4c49ba8c86dd8a2a1c1c29",
    "m_Id": 0,
    "m_DisplayName": "Base Color",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BaseColor",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.5,
        "y": 0.5,
        "z": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.5,
        "g": 0.5,
        "b": 0.5,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "090ea3d1f61a46d29798b1d6259e6a5d",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"35cfe3df8fbb7aa46adb76215627f2e9\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "1001d6f55f7343adbf91af6d511db76c",
    "m_Id": 5,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "10c8e6622fd54b739615934b1748a022",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "152e97736851444db311653adc1998e2",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget",
    "m_ObjectId": "1597f52791a14ae49921bb52b0c8f2a4",
    "m_Datas": [],
    "m_ActiveSubTarget": {
        "m_Id": "d0f2a344521a46b5b103f9ee30df0369"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZTestMode": 4,
    "m_ZWriteControl": 0,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CastShadows": true,
    "m_ReceiveShadows": true,
    "m_SupportsLODCrossFade": false,
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "15f488acfbc149a9978b2c5dd757531a",
    "m_Title": "",
    "m_Content": "This is using the Pythagorean theorem to calculate Z.\n\nX sqaured + Y squared = Z squared",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -832.0000610351563,
        "y": 377.5000305175781,
        "width": 214.0,
        "height": 100.00003051757813
    },
    "m_Group": {
        "m_Id": "615aba30429f446ebf8ecf63ac15a6e5"
    }
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInTarget",
    "m_ObjectId": "15f99706d93b4ef897ab1da0a1a7a0c0",
    "m_ActiveSubTarget": {
        "m_Id": "878b9d93d6f24f25acf6acf8519c921b"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZWriteControl": 0,
    "m_ZTestMode": 4,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CustomEditorGUI": ""
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "1c137a28846242c1a01f8a46273404d2",
    "m_Id": 1,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "1d6e742aec414d86b37789221b6e1723",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Emission",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "2c476972121e41cd9f8f72d032a0f2c9"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Emission"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalizeNode",
    "m_ObjectId": "1f78fe66f7924e04aabde4a478e3b0ca",
    "m_Group": {
        "m_Id": "615aba30429f446ebf8ecf63ac15a6e5"
    },
    "m_Name": "Normalize",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -292.5000305175781,
            "y": 143.00001525878907,
            "width": 131.49996948242188,
            "height": 94.00001525878906
        }
    },
    "m_Slots": [
        {
            "m_Id": "bc31bd8610f9444494fd7f029fba3dfa"
        },
        {
            "m_Id": "47724c5df4ff4a60a6fc6480dff8f13b"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "21e2fe4e3bfb4092ace28575a0f67a9f",
    "m_Id": 4,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "297a4725476842f984300b5123dda2f0",
    "m_Title": "",
    "m_Content": "You could squeeze a tiny bit of performance improvement out of this if you skip this normalize on the end.\n\nAssuming your original X and Y were created from a normalized normal, this normalize is redundant.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -287.5000305175781,
        "y": 239.00003051757813,
        "width": 125.00001525878906,
        "height": 206.45654296875
    },
    "m_Group": {
        "m_Id": "615aba30429f446ebf8ecf63ac15a6e5"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "2be3e2856f1346cabce55de5d296a84c",
    "m_Id": 3,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "2c476972121e41cd9f8f72d032a0f2c9",
    "m_Id": 0,
    "m_DisplayName": "Emission",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Emission",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 1,
    "m_DefaultColor": {
        "r": 0.0,
        "g": 0.0,
        "b": 0.0,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "2fda96b53eac47448b190a22f3a1838a",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SaturateNode",
    "m_ObjectId": "31ba2ac1c23c4d2eb17179c2590875bf",
    "m_Group": {
        "m_Id": "615aba30429f446ebf8ecf63ac15a6e5"
    },
    "m_Name": "Saturate",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -861.0001220703125,
            "y": 275.0000305175781,
            "width": 127.5,
            "height": 93.99990844726563
        }
    },
    "m_Slots": [
        {
            "m_Id": "3ff759be87834f018f3cb9eb6a521a3f"
        },
        {
            "m_Id": "152e97736851444db311653adc1998e2"
        }
    ],
    "synonyms": [
        "clamp"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "36ccde71285944f8a3804690161e507c",
    "m_Title": "",
    "m_Content": "Note that the exact same process is used to unpack regular normal maps - so using this process is not adding extra cost to the shader.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1807.5001220703125,
        "y": 564.5000610351563,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CombineNode",
    "m_ObjectId": "3b11fba0fb8942129e9bcf0950e71c9a",
    "m_Group": {
        "m_Id": "615aba30429f446ebf8ecf63ac15a6e5"
    },
    "m_Name": "Combine",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -424.5000305175781,
            "y": 143.00001525878907,
            "width": 132.0,
            "height": 142.00001525878907
        }
    },
    "m_Slots": [
        {
            "m_Id": "89a2fe9a48b84a7bb45ef17adb41e2b5"
        },
        {
            "m_Id": "5b65694e51b04b45a5c849c978923d8a"
        },
        {
            "m_Id": "3b41ea9f65794809bd9b753840bf7824"
        },
        {
            "m_Id": "3f2bdf96a81a43929dc0bd17f72c97b9"
        },
        {
            "m_Id": "d3d192fee4b349e1bb744af10e655973"
        },
        {
            "m_Id": "83935d9351734c8fb4a975be41d5219f"
        },
        {
            "m_Id": "d25c2ad03f984a76941d1fbd2ead6191"
        }
    ],
    "synonyms": [
        "append"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "3b41ea9f65794809bd9b753840bf7824",
    "m_Id": 2,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "3da4156fca884483ad9b6de50be887ff",
    "m_Id": 0,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "3e805b83329a4f7ab39d22e4c1219ebc",
    "m_Id": 0,
    "m_DisplayName": "Alpha",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Alpha",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "3f2bdf96a81a43929dc0bd17f72c97b9",
    "m_Id": 3,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "3f7a0f0b001745c3b88f27970d1062b3",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.BaseColor",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "07f7a99bcb4c49ba8c86dd8a2a1c1c29"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.BaseColor"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "3ff759be87834f018f3cb9eb6a521a3f",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "400aafdb2877422bbe6a626199107bd3",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "44318a3053ea46f887ce1d77c0dce74a",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalReconstructZNode",
    "m_ObjectId": "451b54fd02c04db8928fee64ee7f652c",
    "m_Group": {
        "m_Id": "87067ddff13d42c784d6d00b30d9b8ce"
    },
    "m_Name": "Normal Reconstruct Z",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1462.5001220703125,
            "y": 133.00003051757813,
            "width": 207.9998779296875,
            "height": 277.99993896484377
        }
    },
    "m_Slots": [
        {
            "m_Id": "ad6bf7b553024bf8894f93e9c857c2d2"
        },
        {
            "m_Id": "b28a36f1ab344521a37827b648f3769a"
        }
    ],
    "synonyms": [
        "derive z"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "47724c5df4ff4a60a6fc6480dff8f13b",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "47b10d85163b424191ca6650786c23d2",
    "m_Id": 0,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "565bd5b8249f4906b69293b92986e56c",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2Node",
    "m_ObjectId": "57a04f02851d46d988c6e06f7bf55403",
    "m_Group": {
        "m_Id": "87067ddff13d42c784d6d00b30d9b8ce"
    },
    "m_Name": "Vector 2",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1969.0001220703125,
            "y": 133.00003051757813,
            "width": 127.0,
            "height": 100.99990844726563
        }
    },
    "m_Slots": [
        {
            "m_Id": "57ac81693ef743bb85ad6c90b061cbc5"
        },
        {
            "m_Id": "a7cdbb3e369147e79a81243adbc0f554"
        },
        {
            "m_Id": "2fda96b53eac47448b190a22f3a1838a"
        }
    ],
    "synonyms": [
        "2",
        "v2",
        "vec2",
        "float2"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "57ac81693ef743bb85ad6c90b061cbc5",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "5b65694e51b04b45a5c849c978923d8a",
    "m_Id": 1,
    "m_DisplayName": "G",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "5b82bfe04f82429984ec395120982d69",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 1.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TangentMaterialSlot",
    "m_ObjectId": "5bf70038267d442281e91cd7102268ef",
    "m_Id": 0,
    "m_DisplayName": "Tangent",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tangent",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "5ef55e1dc1a845b9986df281c15a1dcb",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "615aba30429f446ebf8ecf63ac15a6e5",
    "m_Title": "Under The Hood",
    "m_Position": {
        "x": -1191.0001220703125,
        "y": 74.50004577636719
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DotProductNode",
    "m_ObjectId": "64a8c8377d7347e18e02fc8b15672a44",
    "m_Group": {
        "m_Id": "615aba30429f446ebf8ecf63ac15a6e5"
    },
    "m_Name": "Dot Product",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -988.5000610351563,
            "y": 275.0000305175781,
            "width": 127.49993896484375,
            "height": 117.99990844726563
        }
    },
    "m_Slots": [
        {
            "m_Id": "a35e6079c0ac47158f078ca457901a86"
        },
        {
            "m_Id": "5b82bfe04f82429984ec395120982d69"
        },
        {
            "m_Id": "b1b09f1c4425421887f0be7911a5be71"
        }
    ],
    "synonyms": [
        "scalar product"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SquareRootNode",
    "m_ObjectId": "65f038ec0a0c4ac090871eb59a779050",
    "m_Group": {
        "m_Id": "615aba30429f446ebf8ecf63ac15a6e5"
    },
    "m_Name": "Square Root",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -606.0000610351563,
            "y": 275.0000305175781,
            "width": 127.49984741210938,
            "height": 93.99990844726563
        }
    },
    "m_Slots": [
        {
            "m_Id": "44318a3053ea46f887ce1d77c0dce74a"
        },
        {
            "m_Id": "d57189c0fc2d4d0e9e8a9e671ccc72f9"
        }
    ],
    "synonyms": [
        "sqrt"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "6797ac6e8d754708a782814acb8a9a75",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Normal",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "3da4156fca884483ad9b6de50be887ff"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Normal"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SplitNode",
    "m_ObjectId": "6802b2b823224e709464002ab7d472b7",
    "m_Group": {
        "m_Id": "615aba30429f446ebf8ecf63ac15a6e5"
    },
    "m_Name": "Split",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -988.5000610351563,
            "y": 143.00001525878907,
            "width": 118.49993896484375,
            "height": 101.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "5ef55e1dc1a845b9986df281c15a1dcb"
        },
        {
            "m_Id": "1c137a28846242c1a01f8a46273404d2"
        },
        {
            "m_Id": "915d1a9982f84488a4f2e1b0126d3aa7"
        },
        {
            "m_Id": "2be3e2856f1346cabce55de5d296a84c"
        },
        {
            "m_Id": "21e2fe4e3bfb4092ace28575a0f67a9f"
        }
    ],
    "synonyms": [
        "separate"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "6934aa19e872468a8703d8bd361e9c2e",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "6d08583d50674024acd3f2c08493c20f",
    "m_Id": 1,
    "m_DisplayName": "In Min Max",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "InMinMax",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "6e481ffcb4304bdca6a9c9db916d5491",
    "m_Title": "",
    "m_Content": "This is how the Normal is reconstructed from X and Y",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -766.5000610351563,
        "y": 133.00001525878907,
        "width": 200.0,
        "height": 100.00001525878906
    },
    "m_Group": {
        "m_Id": "615aba30429f446ebf8ecf63ac15a6e5"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "74ac5b2a66324a768b9cc51e8e9365f7",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "762b7c6bb7454e5b89fb761531313fb0",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "7f4a1a79d85f49b08f3d2d67041465f9",
    "m_Id": 2,
    "m_DisplayName": "Y",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Y",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "Y"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "83935d9351734c8fb4a975be41d5219f",
    "m_Id": 5,
    "m_DisplayName": "RGB",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGB",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "87067ddff13d42c784d6d00b30d9b8ce",
    "m_Title": "The Basics",
    "m_Position": {
        "x": -2160.500244140625,
        "y": 74.50004577636719
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInUnlitSubTarget",
    "m_ObjectId": "878b9d93d6f24f25acf6acf8519c921b"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "883cf820b2cb480094218595d39b77eb",
    "m_Name": "",
    "m_ChildObjectList": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "89a2fe9a48b84a7bb45ef17adb41e2b5",
    "m_Id": 0,
    "m_DisplayName": "R",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "915d1a9982f84488a4f2e1b0126d3aa7",
    "m_Id": 2,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "9340ceef2ab34edfa58c7f0933200911",
    "m_Title": "",
    "m_Content": "This texture has the X and Y data from a normal map in the red and green channels.  The Z and alpha channels have other data in them.  We can use the Normal Reconstruct Z Node to create a normal with just the X and Y.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -2132.000244140625,
        "y": 314.5000305175781,
        "width": 144.5001220703125,
        "height": 173.50003051757813
    },
    "m_Group": {
        "m_Id": "87067ddff13d42c784d6d00b30d9b8ce"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "93727a8a03224de1949baa145f0625f9",
    "m_Id": 4,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "9c312ecff26542dba24b6682e4686b07",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Alpha",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "3e805b83329a4f7ab39d22e4c1219ebc"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Alpha"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "9f244797bcdd470287373e90963b0489",
    "m_Id": 2,
    "m_DisplayName": "Out Min Max",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "OutMinMax",
    "m_StageCapability": 3,
    "m_Value": {
        "x": -1.0,
        "y": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "a35e6079c0ac47158f078ca457901a86",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a7cdbb3e369147e79a81243adbc0f554",
    "m_Id": 2,
    "m_DisplayName": "Y",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Y",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "Y"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "ad6bf7b553024bf8894f93e9c857c2d2",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b1b09f1c4425421887f0be7911a5be71",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "b28a36f1ab344521a37827b648f3769a",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "b4ba844d8b314c068a54739b0d50f289",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Tangent",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "5bf70038267d442281e91cd7102268ef"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Tangent"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "b5cd602add3c41fea18f8a4768c28063",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "bc31bd8610f9444494fd7f029fba3dfa",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "bfbf6f3097ac4ae19facd132d2e6fcd1",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "c7c26a9dd01a4fdfa7338f34d9a01532",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "c8342633d91548e4849963769e0a8df7",
    "m_Title": "",
    "m_Content": "Finally, we use the Normal Reconstruct Z Node to create our Normal. We pass in a Vector 2 that contains the expaned data from our red and green channels, and we get a Normal as the result.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1459.5001220703125,
        "y": 416.0000305175781,
        "width": 200.0,
        "height": 107.50003051757813
    },
    "m_Group": {
        "m_Id": "87067ddff13d42c784d6d00b30d9b8ce"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "c88532443058483f92a8e4b0bf2b55ce",
    "m_Title": "Normal Reconstruct Z Node",
    "m_Content": "The Normal Reconstruct Z Node calculates the Z component of a normal vector when given the X and Y components.\n\nIt's useful because in some cases you only want to store the X and Y components of the normal in a texture so that you can use the other texture channels for other data.  Using this node, you can reconstruct the full normal using only the first two components.\n\nThis type of texture packing is a good way to reduce the number of texture samples your shader is using to improve shader performance.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1434.5001220703125,
        "y": -201.50001525878907,
        "width": 284.0,
        "height": 224.00001525878907
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "cbfa4eac89dd48aa97822ca476144a9d",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": -1.0,
        "y": -1.0,
        "z": -1.0,
        "w": -1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalUnlitSubTarget",
    "m_ObjectId": "d0f2a344521a46b5b103f9ee30df0369"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "d25c2ad03f984a76941d1fbd2ead6191",
    "m_Id": 6,
    "m_DisplayName": "RG",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RG",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "d2fb8f1f903d425ca3713a2ca2f3e7b0",
    "m_Id": 7,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "d3d192fee4b349e1bb744af10e655973",
    "m_Id": 4,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "d57189c0fc2d4d0e9e8a9e671ccc72f9",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleTexture2DNode",
    "m_ObjectId": "d7b0ff4480f146228ff28b88a4ba9411",
    "m_Group": {
        "m_Id": "87067ddff13d42c784d6d00b30d9b8ce"
    },
    "m_Name": "Sample Texture 2D",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -2135.500244140625,
            "y": 133.00003051757813,
            "width": 155.0001220703125,
            "height": 177.99993896484376
        }
    },
    "m_Slots": [
        {
            "m_Id": "47b10d85163b424191ca6650786c23d2"
        },
        {
            "m_Id": "93727a8a03224de1949baa145f0625f9"
        },
        {
            "m_Id": "1001d6f55f7343adbf91af6d511db76c"
        },
        {
            "m_Id": "d8a23d610dd843648ab824f0221849d1"
        },
        {
            "m_Id": "d2fb8f1f903d425ca3713a2ca2f3e7b0"
        },
        {
            "m_Id": "090ea3d1f61a46d29798b1d6259e6a5d"
        },
        {
            "m_Id": "ebc5618987df4d01a056eb5c1b7f21a5"
        },
        {
            "m_Id": "e5907eb4d29c4751adda649556fbccf9"
        }
    ],
    "synonyms": [
        "tex2d"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_TextureType": 0,
    "m_NormalMapSpace": 0,
    "m_EnableGlobalMipBias": true,
    "m_MipSamplingMode": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "d8a23d610dd843648ab824f0221849d1",
    "m_Id": 6,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "da1439c4438643ed8d4fbc67257a0ee4",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "bfbf6f3097ac4ae19facd132d2e6fcd1"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Position"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.RemapNode",
    "m_ObjectId": "df0f0a8f3e0c40158670ca44a6b39dc3",
    "m_Group": {
        "m_Id": "87067ddff13d42c784d6d00b30d9b8ce"
    },
    "m_Name": "Remap",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1710.0001220703125,
            "y": 133.00003051757813,
            "width": 187.0,
            "height": 142.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "cbfa4eac89dd48aa97822ca476144a9d"
        },
        {
            "m_Id": "6d08583d50674024acd3f2c08493c20f"
        },
        {
            "m_Id": "9f244797bcdd470287373e90963b0489"
        },
        {
            "m_Id": "efb78ffaad854c1caa31a64e6fff74e9"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "e104587ec8694bc1b2280337f9df02f8",
    "m_Title": "",
    "m_Content": "First we combine the red and green channels together into a Vector 2.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1961.0001220703125,
        "y": 236.00003051757813,
        "width": 108.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "87067ddff13d42c784d6d00b30d9b8ce"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "e508612a670e4dfd9cb63141578daecf",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "e5907eb4d29c4751adda649556fbccf9",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "eabe1901e13542878b2ba7dd0193ea86",
    "m_Title": "",
    "m_Content": "Then we use the Remap node to exand the range of our data from 0 to 1 to the -1 to 1 range. Normally, if our texture is set to be a normal map, this is done automatically, but since our texture is not set to be a normal map, we have to do this manually here.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1734.5001220703125,
        "y": 279.0000305175781,
        "width": 200.0,
        "height": 146.5
    },
    "m_Group": {
        "m_Id": "87067ddff13d42c784d6d00b30d9b8ce"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "ebc5618987df4d01a056eb5c1b7f21a5",
    "m_Id": 2,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "efb78ffaad854c1caa31a64e6fff74e9",
    "m_Id": 3,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalReconstructZNode",
    "m_ObjectId": "f1352afad7e84d53b0e9b3ff5cb60b9b",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Normal Reconstruct Z",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -997.5001220703125,
            "y": -191.00006103515626,
            "width": 169.9998779296875,
            "height": 94.0000228881836
        }
    },
    "m_Slots": [
        {
            "m_Id": "565bd5b8249f4906b69293b92986e56c"
        },
        {
            "m_Id": "10c8e6622fd54b739615934b1748a022"
        }
    ],
    "synonyms": [
        "derive z"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.OneMinusNode",
    "m_ObjectId": "f3540c81e63b471f94e89ce40b887558",
    "m_Group": {
        "m_Id": "615aba30429f446ebf8ecf63ac15a6e5"
    },
    "m_Name": "One Minus",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -733.5001220703125,
            "y": 275.0000305175781,
            "width": 127.50006103515625,
            "height": 93.99990844726563
        }
    },
    "m_Slots": [
        {
            "m_Id": "c7c26a9dd01a4fdfa7338f34d9a01532"
        },
        {
            "m_Id": "6934aa19e872468a8703d8bd361e9c2e"
        }
    ],
    "synonyms": [
        "complement",
        "invert",
        "opposite"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "f7d32794429f475183535d91754dafab",
    "m_Title": "",
    "m_Content": "In",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1156.5001220703125,
        "y": 189.50001525878907,
        "width": 80.0,
        "height": 100.00001525878906
    },
    "m_Group": {
        "m_Id": "615aba30429f446ebf8ecf63ac15a6e5"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDTarget",
    "m_ObjectId": "f86723075c27450f982e6a3b994e4859",
    "m_ActiveSubTarget": {
        "m_Id": "004dc390bc7b4cde8e5cb6f913c7b2b5"
    },
    "m_Datas": [
        {
            "m_Id": "e508612a670e4dfd9cb63141578daecf"
        },
        {
            "m_Id": "b5cd602add3c41fea18f8a4768c28063"
        },
        {
            "m_Id": "762b7c6bb7454e5b89fb761531313fb0"
        }
    ],
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false,
    "m_SupportLineRendering": false
}

