# About Burst 

Burst is a compiler that you can use with Unity's [job system](https://docs.unity3d.com/Manual/JobSystem.html) to create code that enhances and improves your application's performance. It translates your code from IL/.NET bytecode to optimized native CPU code that uses the [LLVM compiler](https://llvm.org/). 

## Installation
To install this package, follow the instructions in the [Package Manager documentation](https://docs.unity3d.com/Manual/upm-ui-actions.html).

If you change the Burst package version (for example, via Update), you need to close and restart the Editor.


## Further resources

### Videos

Conference presentations given by the Burst team:

* [Getting started with Burst - Unite Copenhagen 2019](https://www.youtube.com/watch?v=Tzn-nX9hK1o) ([slides](https://docs.google.com/presentation/d/1id50G18EnRroQaq1apIDU9MrcxhaSihhqPoQrmn1mBg))
* [Supercharging mobile performance with ARM Neon and Unity Burst Compiler](https://www.youtube.com/watch?v=7iEUvlUyr4k)
* [Using Burst Compiler to optimize for Android - Unite Now 2020](https://www.youtube.com/watch?v=WnJV6J-taIM) 
* [Intrinsics: Low-level engine development with Burst - Unite Copenhagen 2019](https://www.youtube.com/watch?v=BpwvXkoFcp8) ([slides](https://www.slideshare.net/unity3d/intrinsics-lowlevel-engine-development-with-burst))
* [Behind the Burst compiler: Converting .NET IL to highly optimized native code - DotNext 2018](https://www.youtube.com/watch?v=LKpyaVrby04)
* [Deep dive into the Burst compiler - Unite LA 2018](https://www.youtube.com/watch?v=QkM6zEGFhDY)
* [C# to machine code: GDC 2018](https://www.youtube.com/watch?v=NF6kcNS6U80)
* [Using the native debugger for Burst compiled code](https://www.youtube.com/watch?v=nou6AIHKJz0)

### Blogs

Blog posts written by members of the Burst team :

* [Raising your game with Burst 1.7](https://blog.unity.com/technology/raising-your-game-with-burst-17)
* [Enhancing mobile performance with the Burst compiler](https://blog.unity.com/technology/enhancing-mobile-performance-with-the-burst-compiler)
* [Enhanced aliasing with Burst](https://blogs.unity3d.com/2020/09/07/enhanced-aliasing-with-burst/)
* [In parameters in Burst](https://blogs.unity3d.com/2020/11/25/in-parameters-in-burst/)

