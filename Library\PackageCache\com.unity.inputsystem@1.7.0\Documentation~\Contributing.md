---
uid: input-system-contributing
---
# Contributing

The [full source code](https://github.com/Unity-Technologies/InputSystem) for the Input System is available on GitHub. This is also where most of the Input System's development happens.

>__Note__: This includes the full source code for the managed/C# part of the system. At this point, the native, platform-specific C++ backends are still closed-source and require a source code license.

## Reporting bugs

To report documentation problems, please use the feedback section at the bottom of the page containing the problem.

To report bugs related to the Input System Please follow Unity's standard [bug reporting guidelines](https://unity3d.com/unity/qa/bug-reporting). Don't forget to submit a Project that the developer who picks up your report can use to reproduce the issue. Be sure to mention that the bug is specific to the Input System package in the description, so it gets forwarded to the correct team at Unity.

## Discussion

To ask questions or discuss the Input System, see the [dedicated section on Unity's forum](https://forum.unity.com/forums/new-input-system.103/). This is also the best place to post feature requests.
