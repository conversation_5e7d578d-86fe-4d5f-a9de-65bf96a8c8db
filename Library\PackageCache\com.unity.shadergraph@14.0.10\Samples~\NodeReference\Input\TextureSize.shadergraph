{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "3de6f69a278d498db54780635b275639",
    "m_Properties": [],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "658d12ecc7304bc7bf5dce82c2d77157"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "81a56b8d6e604be5b40b33276fcd781a"
        },
        {
            "m_Id": "c904cb2ffb6a46658772accca0cd227a"
        },
        {
            "m_Id": "5f38ac0dc2a74ea88a4564e87e928539"
        },
        {
            "m_Id": "60187cfe49a146db9a886f3ef6ca532e"
        },
        {
            "m_Id": "0e8a448d3ed647a18cdddc71d9a5035d"
        },
        {
            "m_Id": "c0b3ce2799dc4d7cafd0cd9c2dbd41a0"
        },
        {
            "m_Id": "d709fc56b15448d58cc8df21580cffb2"
        },
        {
            "m_Id": "70ad95a316934e1ebfa32e2a02cd75db"
        },
        {
            "m_Id": "eb37015b22534791a37fc2ce6e2b2312"
        }
    ],
    "m_GroupDatas": [
        {
            "m_Id": "f97946e512a34aec8ba83459edbe205a"
        }
    ],
    "m_StickyNoteDatas": [
        {
            "m_Id": "cfaf0278f4f54d55acecf488f916631d"
        },
        {
            "m_Id": "7ba144c61a124091a76c446fbe861c46"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "eb37015b22534791a37fc2ce6e2b2312"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "70ad95a316934e1ebfa32e2a02cd75db"
                },
                "m_SlotId": 1
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": [
            {
                "m_Id": "81a56b8d6e604be5b40b33276fcd781a"
            },
            {
                "m_Id": "c904cb2ffb6a46658772accca0cd227a"
            },
            {
                "m_Id": "5f38ac0dc2a74ea88a4564e87e928539"
            }
        ]
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 200.0
        },
        "m_Blocks": [
            {
                "m_Id": "60187cfe49a146db9a886f3ef6ca532e"
            },
            {
                "m_Id": "0e8a448d3ed647a18cdddc71d9a5035d"
            },
            {
                "m_Id": "c0b3ce2799dc4d7cafd0cd9c2dbd41a0"
            }
        ]
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Shader Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": ""
    },
    "m_ActiveTargets": [
        {
            "m_Id": "0a9c60b043ff4287b3ab88c8ca06a3ca"
        },
        {
            "m_Id": "789967c9c2c9452bbcdbfe59e9806445"
        },
        {
            "m_Id": "5d89afac3f7d43c1a3921a27afdaa385"
        }
    ]
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInTarget",
    "m_ObjectId": "0a9c60b043ff4287b3ab88c8ca06a3ca",
    "m_ActiveSubTarget": {
        "m_Id": "98a4ecc6280543c68eac05ab3601ec55"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZWriteControl": 0,
    "m_ZTestMode": 4,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CustomEditorGUI": ""
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "0e1110e38f6f4bf4aacda6c4cc2a14f0",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "0e8a448d3ed647a18cdddc71d9a5035d",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Emission",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "5ad48242c2c24f73a0aee7e1da3a0ec0"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Emission"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0ed4e02e6c7c407ca54d9fc0e5379e18",
    "m_Id": 2,
    "m_DisplayName": "Height",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Height",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TangentMaterialSlot",
    "m_ObjectId": "1587efc28c7b498ba788558b058cbf0c",
    "m_Id": 0,
    "m_DisplayName": "Tangent",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tangent",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "159fe07bfeb546a994ab863f2ab9624b",
    "m_Id": 2,
    "m_DisplayName": "Height",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Height",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "32ab7895d4d94041a59a75dfc6942fc7",
    "m_Id": 0,
    "m_DisplayName": "Alpha",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Alpha",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "4470c56d5b1c4f49a28b2a7ce783d95f",
    "m_Id": 3,
    "m_DisplayName": "Texel Width",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texel Width",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "56cc6838918e46ee86306176bd57b924",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "5ad48242c2c24f73a0aee7e1da3a0ec0",
    "m_Id": 0,
    "m_DisplayName": "Emission",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Emission",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 1,
    "m_DefaultColor": {
        "r": 0.0,
        "g": 0.0,
        "b": 0.0,
        "a": 1.0
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget",
    "m_ObjectId": "5d89afac3f7d43c1a3921a27afdaa385",
    "m_Datas": [],
    "m_ActiveSubTarget": {
        "m_Id": "e39c3db98e2d43a6bde4681d0c7a887e"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZTestMode": 4,
    "m_ZWriteControl": 0,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CastShadows": true,
    "m_ReceiveShadows": true,
    "m_SupportsLODCrossFade": false,
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "5f38ac0dc2a74ea88a4564e87e928539",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Tangent",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "1587efc28c7b498ba788558b058cbf0c"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Tangent"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "60187cfe49a146db9a886f3ef6ca532e",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.BaseColor",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "8422ab8ac9654b18908a7c6354a54f01"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.BaseColor"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "658d12ecc7304bc7bf5dce82c2d77157",
    "m_Name": "",
    "m_ChildObjectList": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DPropertiesNode",
    "m_ObjectId": "70ad95a316934e1ebfa32e2a02cd75db",
    "m_Group": {
        "m_Id": "f97946e512a34aec8ba83459edbe205a"
    },
    "m_Name": "Texture Size",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -486.0,
            "y": 208.50001525878907,
            "width": 211.99993896484376,
            "height": 149.0000457763672
        }
    },
    "m_Slots": [
        {
            "m_Id": "91ecb84b0da245ab93aba9df4df15878"
        },
        {
            "m_Id": "0ed4e02e6c7c407ca54d9fc0e5379e18"
        },
        {
            "m_Id": "f7ee4f619fe44235b0798acbea59583d"
        },
        {
            "m_Id": "f2aed1f0dd594bc1bb7aa1b46e83a9c4"
        },
        {
            "m_Id": "943cc39fde324e95af08d689f9e40f4e"
        }
    ],
    "synonyms": [
        "texel size"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDTarget",
    "m_ObjectId": "789967c9c2c9452bbcdbfe59e9806445",
    "m_ActiveSubTarget": {
        "m_Id": "b7aa968dec8a4f45978aec2c9b830acd"
    },
    "m_Datas": [
        {
            "m_Id": "c4b432566b11497fbf54aeb99530eda1"
        },
        {
            "m_Id": "ea1e3f483e084b43b03c5b113fbbe61f"
        },
        {
            "m_Id": "0e1110e38f6f4bf4aacda6c4cc2a14f0"
        }
    ],
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false,
    "m_SupportLineRendering": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "7ba144c61a124091a76c446fbe861c46",
    "m_Title": "",
    "m_Content": "In this example, the output of each port on the Texture Size node will be as follows:\n\nWidth: 256\nHeight: 256\nTexel Width: 0.00390625\nTexel Height: 0.00390625\n\nThe input texture is 256x256, so the first two output ports give you those values and the second two give you 1/256.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -470.5000305175781,
        "y": 363.5000305175781,
        "width": 200.0,
        "height": 207.00003051757813
    },
    "m_Group": {
        "m_Id": "f97946e512a34aec8ba83459edbe205a"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "81a56b8d6e604be5b40b33276fcd781a",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "56cc6838918e46ee86306176bd57b924"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Position"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "8422ab8ac9654b18908a7c6354a54f01",
    "m_Id": 0,
    "m_DisplayName": "Base Color",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BaseColor",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.5,
        "y": 0.5,
        "z": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.5,
        "g": 0.5,
        "b": 0.5,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "85dca1af0b3f4ee4b2cbda60ff6b72d0",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"instanceID\":0}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DMaterialSlot",
    "m_ObjectId": "8a11c749655e43f38231e13904f2d18b",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "91ecb84b0da245ab93aba9df4df15878",
    "m_Id": 0,
    "m_DisplayName": "Width",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Width",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "943cc39fde324e95af08d689f9e40f4e",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"instanceID\":0}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInUnlitSubTarget",
    "m_ObjectId": "98a4ecc6280543c68eac05ab3601ec55"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "9c7c13db40f6456a8ee1e6e615047625",
    "m_Id": 0,
    "m_DisplayName": "Width",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Width",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "b7aa968dec8a4f45978aec2c9b830acd"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "c0b3ce2799dc4d7cafd0cd9c2dbd41a0",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Alpha",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "32ab7895d4d94041a59a75dfc6942fc7"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Alpha"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "c22007da85de4f1fbcfb041528822d45",
    "m_Id": 4,
    "m_DisplayName": "Texel Height",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texel Height",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "c4b432566b11497fbf54aeb99530eda1",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "c904cb2ffb6a46658772accca0cd227a",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Normal",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "ebd6411c877d4c16b1279a0cb3e9618c"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Normal"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "cfaf0278f4f54d55acecf488f916631d",
    "m_Title": "Texture Size Node",
    "m_Content": "The Texture Size node gives you the number of pixels in a texture's height and width dimensions (512x512, 1024x1024 - for example), as well as the texel size height and width in UV space - which is the reciprocal of the width and height.\n\nThis information is useful when you want to do something in the shader that depends on the resolution of the texture being used.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -481.5000305175781,
        "y": -71.50000762939453,
        "width": 254.00001525878907,
        "height": 182.00001525878907
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DPropertiesNode",
    "m_ObjectId": "d709fc56b15448d58cc8df21580cffb2",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Texture Size",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -756.5000610351563,
            "y": -70.49999237060547,
            "width": 211.99993896484376,
            "height": 149.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "9c7c13db40f6456a8ee1e6e615047625"
        },
        {
            "m_Id": "159fe07bfeb546a994ab863f2ab9624b"
        },
        {
            "m_Id": "4470c56d5b1c4f49a28b2a7ce783d95f"
        },
        {
            "m_Id": "c22007da85de4f1fbcfb041528822d45"
        },
        {
            "m_Id": "85dca1af0b3f4ee4b2cbda60ff6b72d0"
        }
    ],
    "synonyms": [
        "texel size"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalUnlitSubTarget",
    "m_ObjectId": "e39c3db98e2d43a6bde4681d0c7a887e"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "ea1e3f483e084b43b03c5b113fbbe61f",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DAssetNode",
    "m_ObjectId": "eb37015b22534791a37fc2ce6e2b2312",
    "m_Group": {
        "m_Id": "f97946e512a34aec8ba83459edbe205a"
    },
    "m_Name": "Texture 2D Asset",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -718.9999389648438,
            "y": 208.50001525878907,
            "width": 143.5,
            "height": 105.99998474121094
        }
    },
    "m_Slots": [
        {
            "m_Id": "8a11c749655e43f38231e13904f2d18b"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"ca1502b9036d689468a5686c2adfa47e\",\"type\":3}}",
        "m_Guid": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "ebd6411c877d4c16b1279a0cb3e9618c",
    "m_Id": 0,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "f2aed1f0dd594bc1bb7aa1b46e83a9c4",
    "m_Id": 4,
    "m_DisplayName": "Texel Height",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texel Height",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "f7ee4f619fe44235b0798acbea59583d",
    "m_Id": 3,
    "m_DisplayName": "Texel Width",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texel Width",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "f97946e512a34aec8ba83459edbe205a",
    "m_Title": "The Basics",
    "m_Position": {
        "x": -743.9998168945313,
        "y": 150.00006103515626
    }
}

