{"name": "UIvsGameInput", "maps": [{"name": "Player", "id": "c28975c9-e853-4f19-b164-247f85b17891", "actions": [{"name": "Look", "type": "Value", "id": "e910c706-b7ec-4457-a9ba-eb245bc0f7fd", "expectedControlType": "Vector2", "processors": "", "interactions": ""}, {"name": "LookEngage", "type": "<PERSON><PERSON>", "id": "f3fdab0c-adcd-45ae-ba8e-26ee72b7cecb", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "MultiTap"}, {"name": "<PERSON><PERSON>", "type": "<PERSON><PERSON>", "id": "1da914e3-b1b1-4853-a1d3-98e57f0007cf", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": ""}, {"name": "ResetCamera", "type": "<PERSON><PERSON>", "id": "194942d0-922a-40ed-98e7-7a86bfd353d5", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": ""}, {"name": "Fire", "type": "<PERSON><PERSON>", "id": "6d856a00-c92f-4d61-a235-b355727364a7", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "Tap"}, {"name": "UIEngage", "type": "<PERSON><PERSON>", "id": "a760f56f-1b0e-4826-aaa9-f59e75fcf250", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "Press(behavior=2)"}], "bindings": [{"name": "", "id": "c1f7a91b-d0fd-4a62-997e-7fb9b69bf235", "path": "<Gamepad>/rightStick", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "8c8e490b-c610-4785-884f-f04217b23ca4", "path": "<Pointer>/delta", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse;Touch", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "3e5f5442-8668-4b27-a940-df99bad7e831", "path": "<Joystick>/stick", "interactions": "", "processors": "", "groups": "Joystick", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d18be556-a252-46d7-b2f8-df74fab13697", "path": "*/{Back}", "interactions": "", "processors": "", "groups": "Keyboard&Mouse;Gamepad;Joystick;XR", "action": "<PERSON><PERSON>", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "f9de0a3e-9a64-4ef3-9789-b7182e88f415", "path": "*/{PrimaryAction}", "interactions": "MultiTap", "processors": "", "groups": "Keyboard&Mouse;Gamepad;Joystick;XR", "action": "ResetCamera", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "f8dcdb1e-3f93-4ecb-9ec1-2e21c75bc615", "path": "<Touchscreen>/press", "interactions": "MultiTap(tapTime=0.5)", "processors": "", "groups": "Touch", "action": "ResetCamera", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "9310a102-077b-450f-8024-00c290e27c28", "path": "*/{PrimaryAction}", "interactions": "", "processors": "", "groups": "Keyboard&Mouse;XR", "action": "LookEngage", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "318156ec-3299-41de-b385-ef0767aa52ac", "path": "<Keyboard>/leftCtrl", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "LookEngage", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "94b6539b-a8c3-4154-829d-626a9367d8f1", "path": "<Touchscreen>/press", "interactions": "", "processors": "", "groups": "Touch", "action": "LookEngage", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "db87a9de-eaaf-4778-aff1-cea78a2b9c0c", "path": "*/{SecondaryAction}", "interactions": "", "processors": "", "groups": "Keyboard&Mouse;XR", "action": "Fire", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "a2fb8646-ebc5-48fa-af29-2a5c217ffcfb", "path": "<Touchscreen>/touch1/press", "interactions": "", "processors": "", "groups": "Touch", "action": "Fire", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "a8a3616d-3da1-4500-8881-ec15ecf61a3c", "path": "<Gamepad>/rightTrigger", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Fire", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "5bcbb0a0-51f9-4f3c-9b73-b640e3470217", "path": "<Joystick>/trigger", "interactions": "", "processors": "", "groups": "Joystick", "action": "Fire", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "65dc21e5-f8ba-41c8-8798-8707af264863", "path": "<Gamepad>/leftTrigger", "interactions": "", "processors": "", "groups": "Gamepad", "action": "UIEngage", "isComposite": false, "isPartOfComposite": false}]}, {"name": "UI", "id": "aa82b072-01e4-494d-a80c-b8e0a13f2be0", "actions": [{"name": "Navigate", "type": "PassThrough", "id": "4158b10c-58e6-49a9-87b2-b48b1c5a675c", "expectedControlType": "Vector2", "processors": "", "interactions": ""}, {"name": "Submit", "type": "<PERSON><PERSON>", "id": "405732d6-5cc8-4c91-8787-36156f78f661", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": ""}, {"name": "Cancel", "type": "<PERSON><PERSON>", "id": "022be312-3c79-477b-a0d8-87565a78fceb", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": ""}, {"name": "Point", "type": "PassThrough", "id": "29b94aca-5f79-43ab-8638-b4349d10f117", "expectedControlType": "Vector2", "processors": "", "interactions": ""}, {"name": "Click", "type": "PassThrough", "id": "4adfa5b7-2ffc-41b1-ad1f-6b8f84904d5d", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": ""}, {"name": "ScrollWheel", "type": "PassThrough", "id": "338ae756-7358-4e44-aac0-872450414023", "expectedControlType": "Vector2", "processors": "", "interactions": ""}, {"name": "MiddleClick", "type": "PassThrough", "id": "dab6906f-157e-4ee3-a6ca-5f869042cdff", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": ""}, {"name": "RightClick", "type": "PassThrough", "id": "04ac696e-c4a0-465e-9364-81c8b107a4e0", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": ""}, {"name": "TrackedDevicePosition", "type": "PassThrough", "id": "0645c141-f963-4af7-b9f5-736fc58ba11e", "expectedControlType": "Vector3", "processors": "", "interactions": ""}, {"name": "TrackedDeviceOrientation", "type": "PassThrough", "id": "28ac7ccc-eeef-4486-bea3-b148ee578ffc", "expectedControlType": "Quaternion", "processors": "", "interactions": ""}], "bindings": [{"name": "Gamepad", "id": "809f371f-c5e2-4e7a-83a1-d867598f40dd", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "14a5d6e8-4aaf-4119-a9ef-34b8c2c548bf", "path": "<Gamepad>/leftStick/up", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "up", "id": "9144cbe6-05e1-4687-a6d7-24f99d23dd81", "path": "<Gamepad>/rightStick/up", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "2db08d65-c5fb-421b-983f-c71163608d67", "path": "<Gamepad>/leftStick/down", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "58748904-2ea9-4a80-8579-b500e6a76df8", "path": "<Gamepad>/rightStick/down", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "8ba04515-75aa-45de-966d-393d9bbd1c14", "path": "<Gamepad>/leftStick/left", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "712e721c-bdfb-4b23-a86c-a0d9fcfea921", "path": "<Gamepad>/rightStick/left", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "fcd248ae-a788-4676-a12e-f4d81205600b", "path": "<Gamepad>/leftStick/right", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "1f04d9bc-c50b-41a1-bfcc-afb75475ec20", "path": "<Gamepad>/rightStick/right", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "fb8277d4-c5cd-4663-9dc7-ee3f0b506d90", "path": "<Gamepad>/dpad", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": false}, {"name": "Joystick", "id": "e25d9774-381c-4a61-b47c-7b6b299ad9f9", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "3db53b26-6601-41be-9887-63ac74e79d19", "path": "<Joystick>/stick/up", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "0cb3e13e-3d90-4178-8ae6-d9c5501d653f", "path": "<Joystick>/stick/down", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "0392d399-f6dd-4c82-8062-c1e9c0d34835", "path": "<Joystick>/stick/left", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "942a66d9-d42f-43d6-8d70-ecb4ba5363bc", "path": "<Joystick>/stick/right", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "Keyboard", "id": "ff527021-f211-4c02-933e-5976594c46ed", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "563fbfdd-0f09-408d-aa75-8642c4f08ef0", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "up", "id": "eb480147-c587-4a33-85ed-eb0ab9942c43", "path": "<Keyboard>/upArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "2bf42165-60bc-42ca-8072-8c13ab40239b", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "85d264ad-e0a0-4565-b7ff-1a37edde51ac", "path": "<Keyboard>/downArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "74214943-c580-44e4-98eb-ad7eebe17902", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "cea9b045-a000-445b-95b8-0c171af70a3b", "path": "<Keyboard>/leftArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "8607c725-d935-4808-84b1-8354e29bab63", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "4cda81dc-9edd-4e03-9d7c-a71a14345d0b", "path": "<Keyboard>/rightArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "9e92bb26-7e3b-4ec4-b06b-3c8f8e498ddc", "path": "*/{Submit}", "interactions": "", "processors": "", "groups": "", "action": "Submit", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "82627dcc-3b13-4ba9-841d-e4b746d6553e", "path": "*/{Cancel}", "interactions": "", "processors": "", "groups": "", "action": "Cancel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "c52c8e0b-8179-41d3-b8a1-d149033bbe86", "path": "<Mouse>/position", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "e1394cbc-336e-44ce-9ea8-6007ed6193f7", "path": "<Pen>/position", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "5693e57a-238a-46ed-b5ae-e64e6e574302", "path": "<Touchscreen>/touch*/position", "interactions": "", "processors": "", "groups": "Touch", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "4faf7dc9-b979-4210-aa8c-e808e1ef89f5", "path": "<Mouse>/leftButton", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "8d66d5ba-88d7-48e6-b1cd-198bbfef7ace", "path": "<Pen>/tip", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "47c2a644-3ebc-4dae-a106-589b7ca75b59", "path": "<Touchscreen>/touch*/press", "interactions": "", "processors": "", "groups": "Touch", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "bb9e6b34-44bf-4381-ac63-5aa15d19f677", "path": "<XRController>/trigger", "interactions": "", "processors": "", "groups": "XR", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "38c99815-14ea-4617-8627-164d27641299", "path": "<Mouse>/scroll", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "ScrollWheel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "24066f69-da47-44f3-a07e-0015fb02eb2e", "path": "<Mouse>/middleButton", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "MiddleClick", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "4c191405-5738-4d4b-a523-c6a301dbf754", "path": "<Mouse>/rightButton", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "RightClick", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "7236c0d9-6ca3-47cf-a6ee-a97f5b59ea77", "path": "<XRController>/devicePosition", "interactions": "", "processors": "", "groups": "XR", "action": "TrackedDevicePosition", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "23e01e3a-f935-4948-8d8b-9bcac77714fb", "path": "<XRController>/deviceRotation", "interactions": "", "processors": "", "groups": "XR", "action": "TrackedDeviceOrientation", "isComposite": false, "isPartOfComposite": false}]}], "controlSchemes": [{"name": "Keyboard&Mouse", "bindingGroup": "Keyboard&Mouse", "devices": [{"devicePath": "<Keyboard>", "isOptional": false, "isOR": false}, {"devicePath": "<Mouse>", "isOptional": false, "isOR": false}]}, {"name": "Gamepad", "bindingGroup": "Gamepad", "devices": [{"devicePath": "<Gamepad>", "isOptional": false, "isOR": false}]}, {"name": "Touch", "bindingGroup": "Touch", "devices": [{"devicePath": "<Touchscreen>", "isOptional": false, "isOR": false}]}, {"name": "Joystick", "bindingGroup": "Joystick", "devices": [{"devicePath": "<Joystick>", "isOptional": false, "isOR": false}]}, {"name": "XR", "bindingGroup": "XR", "devices": [{"devicePath": "<XRController>", "isOptional": false, "isOR": false}]}]}