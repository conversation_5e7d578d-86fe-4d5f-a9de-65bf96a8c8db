{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "47d7509734dc421ab24464d3819cb248",
    "m_Properties": [],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "b70d5989cf9747d594264eb4a73838f4"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "79b4566eabcb499ba95e8ffeb543b8dd"
        },
        {
            "m_Id": "0c99147adc044856b693089dba85b94b"
        },
        {
            "m_Id": "496e665f5e094c14a5936c70848d6205"
        },
        {
            "m_Id": "fd8acf1378b94b1c956f9ad460db3dd2"
        },
        {
            "m_Id": "e3560219808f4ad4ab217922b6ab9fa3"
        },
        {
            "m_Id": "b40e63015671447cbfc5831659feb0e6"
        },
        {
            "m_Id": "c1797014aced49b890309d0c52bba0c9"
        },
        {
            "m_Id": "d5759e8fc5814ada9c65177859771c47"
        },
        {
            "m_Id": "e1af65bc2f194a4984ed02aa4aff760f"
        },
        {
            "m_Id": "136adadb1d574e6f9be89d4708822939"
        },
        {
            "m_Id": "61c19f9433c4497ca5cd7ee6d7a4db01"
        },
        {
            "m_Id": "f3f7fd9c12ae472c9134f6242a73b337"
        },
        {
            "m_Id": "53093802c3ce4ce0baf5169d8f0e8771"
        },
        {
            "m_Id": "4c9589b035b449aca1da4fec7eb52fa6"
        },
        {
            "m_Id": "ca2a02a13c254f9da4a729961651a94c"
        },
        {
            "m_Id": "fbc978964a224c3a8adaf460b564e5bd"
        },
        {
            "m_Id": "92f804c82e304531a5ec588e1b3ff409"
        },
        {
            "m_Id": "1a285fce312948c3be0deda50bcb559a"
        },
        {
            "m_Id": "2c8751ce7bff4f4dab988a55ed067b6d"
        },
        {
            "m_Id": "e79b4a7dfad5473d887111097f61a771"
        },
        {
            "m_Id": "bf597d1435d7472a90c3819e0cb3a416"
        },
        {
            "m_Id": "7cf05be9e7ba43c1bb307d07e149a9ca"
        },
        {
            "m_Id": "948981b4bf994cb3a2e595d801d13cdf"
        },
        {
            "m_Id": "7e3347c09cc94332b96562d5872b82ad"
        },
        {
            "m_Id": "d27b544d9b484fa38021ded4ba09e2a3"
        },
        {
            "m_Id": "76529467ea8f4fd2a81a82df12f92ed5"
        },
        {
            "m_Id": "59a729532cd54c36b8bf15b100ba16b5"
        },
        {
            "m_Id": "9ea7bc119d554c24b8c27d4c63d41e0c"
        }
    ],
    "m_GroupDatas": [
        {
            "m_Id": "6f2734b7d4914768b52ac344533ab2a9"
        },
        {
            "m_Id": "f8461a60673d4e1eb815aa1af5955cfc"
        },
        {
            "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
        }
    ],
    "m_StickyNoteDatas": [
        {
            "m_Id": "38f3ee317e7b411aa4c45046af7df259"
        },
        {
            "m_Id": "b8818b494cc84e2b942eec04bb6fe41d"
        },
        {
            "m_Id": "4c204cfca5814c6c939a577e900bf778"
        },
        {
            "m_Id": "13a74763cd504f6b8f213a1833a5fbec"
        },
        {
            "m_Id": "8c3c332dba154840bcd2ec8a0b3319e6"
        },
        {
            "m_Id": "ee5e59b25ec944b6a2947e5938320173"
        },
        {
            "m_Id": "028172cb71104e718ae28754d294a613"
        },
        {
            "m_Id": "7f9544108c2d415dab6d4ffd2ca10359"
        },
        {
            "m_Id": "880ca42b5f9e4acd9ed09a09f080fa89"
        },
        {
            "m_Id": "a07547bf814342e59d07f16e5802a988"
        },
        {
            "m_Id": "8e0bd6120484413bb9290b3cb90f5492"
        },
        {
            "m_Id": "e20cc700bd304a99aad996b0c7af7e23"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "136adadb1d574e6f9be89d4708822939"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "53093802c3ce4ce0baf5169d8f0e8771"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "1a285fce312948c3be0deda50bcb559a"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "bf597d1435d7472a90c3819e0cb3a416"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "4c9589b035b449aca1da4fec7eb52fa6"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "ca2a02a13c254f9da4a729961651a94c"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "53093802c3ce4ce0baf5169d8f0e8771"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "61c19f9433c4497ca5cd7ee6d7a4db01"
                },
                "m_SlotId": 3
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "59a729532cd54c36b8bf15b100ba16b5"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "2c8751ce7bff4f4dab988a55ed067b6d"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "61c19f9433c4497ca5cd7ee6d7a4db01"
                },
                "m_SlotId": 4
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "f3f7fd9c12ae472c9134f6242a73b337"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "76529467ea8f4fd2a81a82df12f92ed5"
                },
                "m_SlotId": 6
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "d27b544d9b484fa38021ded4ba09e2a3"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "7cf05be9e7ba43c1bb307d07e149a9ca"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "948981b4bf994cb3a2e595d801d13cdf"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "7e3347c09cc94332b96562d5872b82ad"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "76529467ea8f4fd2a81a82df12f92ed5"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "92f804c82e304531a5ec588e1b3ff409"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1a285fce312948c3be0deda50bcb559a"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "948981b4bf994cb3a2e595d801d13cdf"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "59a729532cd54c36b8bf15b100ba16b5"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "9ea7bc119d554c24b8c27d4c63d41e0c"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "7cf05be9e7ba43c1bb307d07e149a9ca"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "b40e63015671447cbfc5831659feb0e6"
                },
                "m_SlotId": 4
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "d5759e8fc5814ada9c65177859771c47"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "bf597d1435d7472a90c3819e0cb3a416"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "948981b4bf994cb3a2e595d801d13cdf"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "ca2a02a13c254f9da4a729961651a94c"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "76529467ea8f4fd2a81a82df12f92ed5"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "ca2a02a13c254f9da4a729961651a94c"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "7e3347c09cc94332b96562d5872b82ad"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "d27b544d9b484fa38021ded4ba09e2a3"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "bf597d1435d7472a90c3819e0cb3a416"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "e1af65bc2f194a4984ed02aa4aff760f"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "b40e63015671447cbfc5831659feb0e6"
                },
                "m_SlotId": 3
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "e79b4a7dfad5473d887111097f61a771"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "7e3347c09cc94332b96562d5872b82ad"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "fbc978964a224c3a8adaf460b564e5bd"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "9ea7bc119d554c24b8c27d4c63d41e0c"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "fbc978964a224c3a8adaf460b564e5bd"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "e79b4a7dfad5473d887111097f61a771"
                },
                "m_SlotId": 0
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": [
            {
                "m_Id": "79b4566eabcb499ba95e8ffeb543b8dd"
            },
            {
                "m_Id": "0c99147adc044856b693089dba85b94b"
            },
            {
                "m_Id": "496e665f5e094c14a5936c70848d6205"
            }
        ]
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 200.0
        },
        "m_Blocks": [
            {
                "m_Id": "fd8acf1378b94b1c956f9ad460db3dd2"
            }
        ]
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Shader Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": ""
    },
    "m_ActiveTargets": [
        {
            "m_Id": "51210e668f864c68834923766c98c056"
        },
        {
            "m_Id": "c2806d9d6860473ab3acec52bf8fe5f1"
        },
        {
            "m_Id": "0378c575c91f4709a8ece0f02aa33594"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "00ddcb074d644856aa1b30c59e6c701a",
    "m_Id": 1,
    "m_DisplayName": "Width",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Width",
    "m_StageCapability": 3,
    "m_Value": 4.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "028172cb71104e718ae28754d294a613",
    "m_Title": "",
    "m_Content": "We increase the frame rate to full speed - 24 frames per second.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1804.5001220703125,
        "y": 1505.0001220703125,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget",
    "m_ObjectId": "0378c575c91f4709a8ece0f02aa33594",
    "m_Datas": [],
    "m_ActiveSubTarget": {
        "m_Id": "ed4c63b29d6e45a188b07e112ac3258f"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZTestMode": 4,
    "m_ZWriteControl": 0,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CastShadows": true,
    "m_ReceiveShadows": true,
    "m_SupportsLODCrossFade": false,
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "0528cd35e33b405891dcf0d9404f1834",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 2.0,
        "y": 2.0,
        "z": 2.0,
        "w": 2.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "059aa5052c284d5991f8b2d90f3f3897",
    "m_Id": 1,
    "m_DisplayName": "Width",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Width",
    "m_StageCapability": 3,
    "m_Value": 4.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "060846d09e684162ab3d4008dba4a8af",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "08893d1695724a8d8327d8235265a436",
    "m_Id": 4,
    "m_DisplayName": "Smooth Delta",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Smooth Delta",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "0c99147adc044856b693089dba85b94b",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Normal",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "2a18559293ce4d569f945bb2672d000c"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Normal"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "116013b05ba94931b4afc52f678a88e2",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "126e0a424ea54c38b4a19e764cb05246",
    "m_Id": 2,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TimeNode",
    "m_ObjectId": "136adadb1d574e6f9be89d4708822939",
    "m_Group": {
        "m_Id": "f8461a60673d4e1eb815aa1af5955cfc"
    },
    "m_Name": "Time",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -805.0000610351563,
            "y": 489.00006103515627,
            "width": 79.0,
            "height": 77.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "6ba5a1dbfaeb45beb394e00e3dc2c3cd"
        },
        {
            "m_Id": "e4d8fda266d04ddba7224714f5741d7a"
        },
        {
            "m_Id": "3ec7868121db4a9992086f93f7d4fd13"
        },
        {
            "m_Id": "ca950e3dfbe445959cf36cbfff46ea47"
        },
        {
            "m_Id": "ab687450a75c4048bc063690085b7c41"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "13a74763cd504f6b8f213a1833a5fbec",
    "m_Title": "",
    "m_Content": "The Tile input is the frame number to display.  In this case it has a range of 0-15 because there are 4 rows and 4 columns for a total of 16 frames.\n\nIf we plug Time into the Tile input port, we can flip from one frame to the next - creating an animated effect.\n\nUsing the Time node by itself will result in a playback rate of 1 frame per second.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1412.5001220703125,
        "y": 630.5000610351563,
        "width": 277.0,
        "height": 163.60003662109376
    },
    "m_Group": {
        "m_Id": "6f2734b7d4914768b52ac344533ab2a9"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "143e797990834e4bba0d140789a6a59a",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "179f5bd793ef45fab8374ade8ee23868",
    "m_Id": 4,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "17a38d4aae6c47929ad154849773b6a6",
    "m_Id": 4,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "18d6402c90304443bcaf82d3902507e8",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "19bbf079434d48f8a29bfea09a15e9ef",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "19cafc2de78e468780f788b9503f820c",
    "m_Id": 1,
    "m_DisplayName": "Width",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Width",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.FlipNode",
    "m_ObjectId": "1a285fce312948c3be0deda50bcb559a",
    "m_Group": {
        "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
    },
    "m_Name": "Flip",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1070.000244140625,
            "y": 1581.5001220703125,
            "width": 160.00018310546876,
            "height": 207.0001220703125
        }
    },
    "m_Slots": [
        {
            "m_Id": "53e468815ccd46fbba755c2e2c1f8bc2"
        },
        {
            "m_Id": "e4e85a6afe184ea18aec7f58dd13b7e3"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_RedChannel": false,
    "m_GreenChannel": true,
    "m_BlueChannel": false,
    "m_AlphaChannel": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "1e725a322cdb4920b0d1320b608e136c",
    "m_Id": 2,
    "m_DisplayName": "Height",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Height",
    "m_StageCapability": 3,
    "m_Value": 4.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "1e94a872031844e08baafe0dfef46f10",
    "m_Id": 2,
    "m_DisplayName": "Cosine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cosine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "2178676070af474994490b72243b0ebb",
    "m_Id": 4,
    "m_DisplayName": "Smooth Delta",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Smooth Delta",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "250ad69ce87a4c5aa7b7029267765f50",
    "m_Id": 5,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "2850794c48cc485a9b048e8f163042ba"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "29fb1bdb87314ca497b202e31b77cd7f",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 2.0,
        "e01": 2.0,
        "e02": 2.0,
        "e03": 2.0,
        "e10": 2.0,
        "e11": 2.0,
        "e12": 2.0,
        "e13": 2.0,
        "e20": 2.0,
        "e21": 2.0,
        "e22": 2.0,
        "e23": 2.0,
        "e30": 2.0,
        "e31": 2.0,
        "e32": 2.0,
        "e33": 2.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "2a18559293ce4d569f945bb2672d000c",
    "m_Id": 0,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "2b3c598850b1444aae150927f3180951",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"a7ff24a0f3bd4dd4aaf9265ba8da09ba\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleTexture2DNode",
    "m_ObjectId": "2c8751ce7bff4f4dab988a55ed067b6d",
    "m_Group": {
        "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
    },
    "m_Name": "Sample Texture 2D",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -320.0000915527344,
            "y": 1478.5001220703125,
            "width": 208.0,
            "height": 337.0001220703125
        }
    },
    "m_Slots": [
        {
            "m_Id": "617c0140c0db48e7b64c4c8b730c28e9"
        },
        {
            "m_Id": "9279bc8701a64df8b57f11bcb22b5273"
        },
        {
            "m_Id": "250ad69ce87a4c5aa7b7029267765f50"
        },
        {
            "m_Id": "d2774e7daa814c1fac7d7f2fd50ad24e"
        },
        {
            "m_Id": "f7742141292147a89cb8eafdbd7c9480"
        },
        {
            "m_Id": "2b3c598850b1444aae150927f3180951"
        },
        {
            "m_Id": "5b95ad2ecb874bbba2914e373b72aa75"
        },
        {
            "m_Id": "406aed303d974470a05e26f66debb5eb"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_TextureType": 0,
    "m_NormalMapSpace": 0,
    "m_EnableGlobalMipBias": true,
    "m_MipSamplingMode": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "30c9079a48a246159b374a893b68b9c1",
    "m_Id": 4,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "34a5b4bcf11a4808924fd3676e7ab93b",
    "m_Id": 0,
    "m_DisplayName": "Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "34d661d4a04a4facab17a15c4f4e50aa",
    "m_Id": 5,
    "m_DisplayName": "RGB",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGB",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "34ee4abbd99f407f8c052343b22dfe12",
    "m_Id": 0,
    "m_DisplayName": "Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "38f3ee317e7b411aa4c45046af7df259",
    "m_Title": "Flipbook Node",
    "m_Content": "The Flipbook Node generates UV coordinates that are used to sample a texture that's divided into frames laid out in a grid pattern.\n\nIt's most frequently used to playback a series of frames to create an animation.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -876.0000610351563,
        "y": 47.000003814697269,
        "width": 200.0,
        "height": 160.0
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "3c8358d24c5640f6a3f0bfd15fc0c111",
    "m_Id": 4,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "3ec7868121db4a9992086f93f7d4fd13",
    "m_Id": 2,
    "m_DisplayName": "Cosine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cosine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "3f117404ffdc40a1848cacaf1865c2f5",
    "m_Id": 3,
    "m_DisplayName": "Delta Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Delta Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "406aed303d974470a05e26f66debb5eb",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "431ac8c50af040308944711c01553055",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "442de96a37a843698c605ec52eee3d41",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "47155e7eb19d46079969f30fc2d532c6",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "4905eafacb2c4a18b1fb5731790c3a65",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "496e665f5e094c14a5936c70848d6205",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Tangent",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "8709e90bdc9749d791c86a94b156d626"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Tangent"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "4ab4353a98ed436fad9b119236c53dfe",
    "m_Id": 7,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "4c204cfca5814c6c939a577e900bf778",
    "m_Title": "",
    "m_Content": "First we enter the number of rows and columns in the atlas texture in the Width and Height inputs.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1466.5001220703125,
        "y": 385.0000305175781,
        "width": 113.5,
        "height": 100.00003051757813
    },
    "m_Group": {
        "m_Id": "6f2734b7d4914768b52ac344533ab2a9"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TimeNode",
    "m_ObjectId": "4c9589b035b449aca1da4fec7eb52fa6",
    "m_Group": {
        "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
    },
    "m_Name": "Time",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1714.500244140625,
            "y": 1335.5001220703125,
            "width": 79.0001220703125,
            "height": 76.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "34ee4abbd99f407f8c052343b22dfe12"
        },
        {
            "m_Id": "5ed73149a3c641829f9410ef95145aed"
        },
        {
            "m_Id": "1e94a872031844e08baafe0dfef46f10"
        },
        {
            "m_Id": "a493c98271f14233b9526b780374b0b3"
        },
        {
            "m_Id": "2178676070af474994490b72243b0ebb"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "4eb859c11edd462fb1f7af0962609b84",
    "m_Id": 2,
    "m_DisplayName": "Height",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Height",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInTarget",
    "m_ObjectId": "51210e668f864c68834923766c98c056",
    "m_ActiveSubTarget": {
        "m_Id": "f143102fd05744bc95cf204e762736cd"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZWriteControl": 0,
    "m_ZTestMode": 4,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CustomEditorGUI": ""
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MultiplyNode",
    "m_ObjectId": "53093802c3ce4ce0baf5169d8f0e8771",
    "m_Group": {
        "m_Id": "f8461a60673d4e1eb815aa1af5955cfc"
    },
    "m_Name": "Multiply",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -697.0001220703125,
            "y": 527.5000610351563,
            "width": 126.0,
            "height": 118.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "63c0b077696147248ac104ceea85d008"
        },
        {
            "m_Id": "609c5b49d0e94ed4a3185a510de34c86"
        },
        {
            "m_Id": "b6a5f8f96fbb44af90c811beec85c599"
        }
    ],
    "synonyms": [
        "multiplication",
        "times",
        "x"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "53e468815ccd46fbba755c2e2c1f8bc2",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "56cb0ac20022479588b86dd8c1ac986e",
    "m_Id": 5,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.FlipNode",
    "m_ObjectId": "59a729532cd54c36b8bf15b100ba16b5",
    "m_Group": {
        "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
    },
    "m_Name": "Flip",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -571.0001220703125,
            "y": 1478.5001220703125,
            "width": 160.00006103515626,
            "height": 207.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "c3ea2e6b2ab9496f9f0448ebaea31449"
        },
        {
            "m_Id": "a8e9921bed0c4249b631d420f8d28340"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_RedChannel": false,
    "m_GreenChannel": true,
    "m_BlueChannel": false,
    "m_AlphaChannel": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "5b95ad2ecb874bbba2914e373b72aa75",
    "m_Id": 2,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "5cedfb23e5d94ac696f9f0f70b5a8b4d",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "5ed73149a3c641829f9410ef95145aed",
    "m_Id": 1,
    "m_DisplayName": "Sine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "609c5b49d0e94ed4a3185a510de34c86",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 8.0,
        "e01": 2.0,
        "e02": 2.0,
        "e03": 2.0,
        "e10": 2.0,
        "e11": 2.0,
        "e12": 2.0,
        "e13": 2.0,
        "e20": 2.0,
        "e21": 2.0,
        "e22": 2.0,
        "e23": 2.0,
        "e30": 2.0,
        "e31": 2.0,
        "e32": 2.0,
        "e33": 2.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "61404507b8944b45a67d66d8c9c5f676",
    "m_Id": 6,
    "m_DisplayName": "RG",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RG",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "617c0140c0db48e7b64c4c8b730c28e9",
    "m_Id": 0,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.FlipbookNode",
    "m_ObjectId": "61c19f9433c4497ca5cd7ee6d7a4db01",
    "m_Group": {
        "m_Id": "f8461a60673d4e1eb815aa1af5955cfc"
    },
    "m_Name": "Flipbook",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -542.5000610351563,
            "y": 381.0000305175781,
            "width": 160.0,
            "height": 227.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "f843f1cc34a94869a71a1f9a34449490"
        },
        {
            "m_Id": "059aa5052c284d5991f8b2d90f3f3897"
        },
        {
            "m_Id": "1e725a322cdb4920b0d1320b608e136c"
        },
        {
            "m_Id": "cd865d434f324a9f8044d9dee98246cc"
        },
        {
            "m_Id": "30c9079a48a246159b374a893b68b9c1"
        }
    ],
    "synonyms": [
        "atlas",
        "animation"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_InvertX": false,
    "m_InvertY": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "63c0b077696147248ac104ceea85d008",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "66929c3a94314fa5aa4cf8825ddd3978",
    "m_Id": 2,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "684493bd053b46a68449351f06680de5",
    "m_Id": 2,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "6ba5a1dbfaeb45beb394e00e3dc2c3cd",
    "m_Id": 0,
    "m_DisplayName": "Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "6c6ae11167d1467b9f8a3443b89bea84",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "6ece560c9d764954be7043c6b78617d9",
    "m_Id": 2,
    "m_DisplayName": "Y",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Y",
    "m_StageCapability": 3,
    "m_Value": 4.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "Y"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "6f2734b7d4914768b52ac344533ab2a9",
    "m_Title": "Creating an Animated Effect",
    "m_Position": {
        "x": -1748.5001220703125,
        "y": 321.00006103515627
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "71d315fa0071424cb026214375c0c747",
    "m_Id": 5,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "72fcbce7ecb04bb1be49bbc7860ba1d5",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CombineNode",
    "m_ObjectId": "76529467ea8f4fd2a81a82df12f92ed5",
    "m_Group": {
        "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
    },
    "m_Name": "Combine",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1203.0001220703125,
            "y": 1373.5001220703125,
            "width": 124.5,
            "height": 117.0001220703125
        }
    },
    "m_Slots": [
        {
            "m_Id": "76c24f3cc8cd4679ad8a69c150212091"
        },
        {
            "m_Id": "dd0a915b2ed44081a09738b47e8bf18e"
        },
        {
            "m_Id": "684493bd053b46a68449351f06680de5"
        },
        {
            "m_Id": "8a79c56987174673a10b130dc18f3550"
        },
        {
            "m_Id": "a9f46d814b3049aa8b286999be40c887"
        },
        {
            "m_Id": "34d661d4a04a4facab17a15c4f4e50aa"
        },
        {
            "m_Id": "61404507b8944b45a67d66d8c9c5f676"
        }
    ],
    "synonyms": [
        "append"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "76c24f3cc8cd4679ad8a69c150212091",
    "m_Id": 0,
    "m_DisplayName": "R",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "79b4566eabcb499ba95e8ffeb543b8dd",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "4905eafacb2c4a18b1fb5731790c3a65"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Position"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "7b0499e9ed9840658387442be25a9800",
    "m_Id": 3,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "7c1f59d1e0fa46babbedcca658d2eeb0",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ReciprocalNode",
    "m_ObjectId": "7cf05be9e7ba43c1bb307d07e149a9ca",
    "m_Group": {
        "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
    },
    "m_Name": "Reciprocal",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -879.9999389648438,
            "y": 1889.0001220703125,
            "width": 144.9998779296875,
            "height": 128.5001220703125
        }
    },
    "m_Slots": [
        {
            "m_Id": "143e797990834e4bba0d140789a6a59a"
        },
        {
            "m_Id": "ce2d212608114b6d854ba797e423aa98"
        }
    ],
    "synonyms": [
        "rcp"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_ReciprocalMethod": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DivideNode",
    "m_ObjectId": "7e3347c09cc94332b96562d5872b82ad",
    "m_Group": {
        "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
    },
    "m_Name": "Divide",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1385.500244140625,
            "y": 1507.5001220703125,
            "width": 126.0,
            "height": 118.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "ef3bfe0936fe4400b29dccada84d29e2"
        },
        {
            "m_Id": "0528cd35e33b405891dcf0d9404f1834"
        },
        {
            "m_Id": "afba855a582c4e19afd0bfa62a9efc3a"
        }
    ],
    "synonyms": [
        "division",
        "divided by"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "7e33f2fbb05a4cdc86c16c2b6356ae7c",
    "m_Id": 7,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "7f9544108c2d415dab6d4ffd2ca10359",
    "m_Title": "",
    "m_Content": "Multiply the texture coordinates by the reciprocal of the number of rows and columns so we can get them to the size of a single frame instead of the whole texture.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -719.5000610351563,
        "y": 1947.0001220703125,
        "width": 200.0,
        "height": 100.0001220703125
    },
    "m_Group": {
        "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "82949cfa805c430c88026190a654ea9a",
    "m_Id": 4,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "82a47b59110f4b09bb9e36bb59b22665",
    "m_Id": 1,
    "m_DisplayName": "Sine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "849e7e5c87cc4b7f8968d27c71c90294",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TangentMaterialSlot",
    "m_ObjectId": "8709e90bdc9749d791c86a94b156d626",
    "m_Id": 0,
    "m_DisplayName": "Tangent",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tangent",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "880ca42b5f9e4acd9ed09a09f080fa89",
    "m_Title": "",
    "m_Content": "We multiply time by the number of frames per second and then add that to our texture coordinates. ",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1444.0001220703125,
        "y": 1230.5001220703125,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "8a79c56987174673a10b130dc18f3550",
    "m_Id": 3,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "8c3c332dba154840bcd2ec8a0b3319e6",
    "m_Title": "",
    "m_Content": "Multiplying Time by a constant will speed up or slow down the playback rate. The muliplier value determines the frame rate, so here we're playing the frames at 8 frames per second.\n\nIt's not recommended to dynamically adjust this value during playback as the results will look broken while the value is changing.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -780.0000610351563,
        "y": 659.5000610351563,
        "width": 289.2249450683594,
        "height": 140.0
    },
    "m_Group": {
        "m_Id": "f8461a60673d4e1eb815aa1af5955cfc"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "8e0bd6120484413bb9290b3cb90f5492",
    "m_Title": "",
    "m_Content": "We flip our UVs vertically at the beginning and again at the end to fix our ordering problem.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1131.0001220703125,
        "y": 1801.0001220703125,
        "width": 213.00006103515626,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "8f534a62e6b4479cadc7e03caa03b527",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"a7ff24a0f3bd4dd4aaf9265ba8da09ba\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "8f5a4bc6d59a4cad84417f13dabcf9bb",
    "m_Id": 0,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "8fe4368779624a80b6ebee17390cf3ff",
    "m_Id": 4,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "91a5bb9598334950b0c083ed4c0fdbda",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "9279bc8701a64df8b57f11bcb22b5273",
    "m_Id": 4,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVNode",
    "m_ObjectId": "92f804c82e304531a5ec588e1b3ff409",
    "m_Group": {
        "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
    },
    "m_Name": "UV",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1215.000244140625,
            "y": 1581.5001220703125,
            "width": 145.0,
            "height": 128.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "431ac8c50af040308944711c01553055"
        }
    ],
    "synonyms": [
        "texcoords",
        "coords",
        "coordinates"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_OutputChannel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MultiplyNode",
    "m_ObjectId": "948981b4bf994cb3a2e595d801d13cdf",
    "m_Group": {
        "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
    },
    "m_Name": "Multiply",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -711.5000610351563,
            "y": 1478.5001220703125,
            "width": 129.0,
            "height": 118.0001220703125
        }
    },
    "m_Slots": [
        {
            "m_Id": "18d6402c90304443bcaf82d3902507e8"
        },
        {
            "m_Id": "29fb1bdb87314ca497b202e31b77cd7f"
        },
        {
            "m_Id": "72fcbce7ecb04bb1be49bbc7860ba1d5"
        }
    ],
    "synonyms": [
        "multiplication",
        "times",
        "x"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "9dc92a3957d34eb9ab567fecd91d0978",
    "m_Id": 0,
    "m_DisplayName": "Base Color",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BaseColor",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.5,
        "y": 0.5,
        "z": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.5,
        "g": 0.5,
        "b": 0.5,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.RedirectNodeData",
    "m_ObjectId": "9ea7bc119d554c24b8c27d4c63d41e0c",
    "m_Group": {
        "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
    },
    "m_Name": "Redirect Node",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1369.5001220703125,
            "y": 1934.0001220703125,
            "width": 56.0,
            "height": 24.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "f5419f87630345b3b2099881e96a62ab"
        },
        {
            "m_Id": "a87e66589c78453abaac29f41855dc1a"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "9f5065cb62e741069c466280fc3695e1",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "a07547bf814342e59d07f16e5802a988",
    "m_Title": "",
    "m_Content": "We only want the row to advance every time it gets to the end of the row - so we divide it by the number of rows.  Now we advance to the left every frame and down when we get to the end of the row.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1170.0001220703125,
        "y": 1256.0001220703125,
        "width": 213.00006103515626,
        "height": 110.0
    },
    "m_Group": {
        "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a16fd1be18be4d85b5d500bcff422d91",
    "m_Id": 3,
    "m_DisplayName": "Tile",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tile",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a493c98271f14233b9526b780374b0b3",
    "m_Id": 3,
    "m_DisplayName": "Delta Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Delta Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a7380226e6d24feaaae8cd755a220f27",
    "m_Id": 3,
    "m_DisplayName": "Tile",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tile",
    "m_StageCapability": 3,
    "m_Value": 6.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "a87e66589c78453abaac29f41855dc1a",
    "m_Id": 1,
    "m_DisplayName": "",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "a8e9921bed0c4249b631d420f8d28340",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "a9f46d814b3049aa8b286999be40c887",
    "m_Id": 4,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "aa798a7039954524b8c84133160a45a5",
    "m_Id": 6,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ab27b9cb5f264b4fb27e1d652c008756",
    "m_Id": 6,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ab687450a75c4048bc063690085b7c41",
    "m_Id": 4,
    "m_DisplayName": "Smooth Delta",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Smooth Delta",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "af4ae784bc4244c9ad7cc257d6941419",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "afba855a582c4e19afd0bfa62a9efc3a",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "b33a3598caed468e8fac325d539c1f60",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "b38bcf57ee2344b18a5017853914c3fd",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.FlipbookNode",
    "m_ObjectId": "b40e63015671447cbfc5831659feb0e6",
    "m_Group": {
        "m_Id": "6f2734b7d4914768b52ac344533ab2a9"
    },
    "m_Name": "Flipbook",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1254.0001220703125,
            "y": 379.5000305175781,
            "width": 160.0,
            "height": 227.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "47155e7eb19d46079969f30fc2d532c6"
        },
        {
            "m_Id": "00ddcb074d644856aa1b30c59e6c701a"
        },
        {
            "m_Id": "d4bd07c30c4e4834afbeef9e778eea04"
        },
        {
            "m_Id": "a7380226e6d24feaaae8cd755a220f27"
        },
        {
            "m_Id": "8fe4368779624a80b6ebee17390cf3ff"
        }
    ],
    "synonyms": [
        "atlas",
        "animation"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_InvertX": false,
    "m_InvertY": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "b6a5f8f96fbb44af90c811beec85c599",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "b70d5989cf9747d594264eb4a73838f4",
    "m_Name": "",
    "m_ChildObjectList": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "b8818b494cc84e2b942eec04bb6fe41d",
    "m_Title": "",
    "m_Content": "Here we have a texture that's arranged in a grid with 16 frames. This is sometimes called an atlas texture.\n\nYou can render out a complex effect such as an explosion, or animated smoke (that may take hours per frame to render), arrange the frames in a grid, and then play them back using the Flipbook node. Visually, the results are similar to the original complex effect, but the rendering cost is reduced by orders of magnitude.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1720.0001220703125,
        "y": 692.0000610351563,
        "width": 217.25,
        "height": 213.0
    },
    "m_Group": {
        "m_Id": "6f2734b7d4914768b52ac344533ab2a9"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b8e88675c2984c669ed8580823519151",
    "m_Id": 2,
    "m_DisplayName": "Cosine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cosine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "bd0ef51a93d04b26b06da12237977754",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"a7ff24a0f3bd4dd4aaf9265ba8da09ba\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.AddNode",
    "m_ObjectId": "bf597d1435d7472a90c3819e0cb3a416",
    "m_Group": {
        "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
    },
    "m_Name": "Add",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -860.5000610351563,
            "y": 1478.5001220703125,
            "width": 129.0,
            "height": 118.0001220703125
        }
    },
    "m_Slots": [
        {
            "m_Id": "ec2396e902ed41daabd081cc95744aa0"
        },
        {
            "m_Id": "6c6ae11167d1467b9f8a3443b89bea84"
        },
        {
            "m_Id": "e9fbbb1722a84f72ab731066cc81a5f8"
        }
    ],
    "synonyms": [
        "addition",
        "sum",
        "plus"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleTexture2DNode",
    "m_ObjectId": "c1797014aced49b890309d0c52bba0c9",
    "m_Group": {
        "m_Id": "6f2734b7d4914768b52ac344533ab2a9"
    },
    "m_Name": "Sample Texture 2D",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1723.5001220703125,
            "y": 379.5000305175781,
            "width": 208.0,
            "height": 304.9999694824219
        }
    },
    "m_Slots": [
        {
            "m_Id": "c5b061bc1f244c678dcdef7470a15eb1"
        },
        {
            "m_Id": "82949cfa805c430c88026190a654ea9a"
        },
        {
            "m_Id": "dea805c82cab453bb49c72846f479d36"
        },
        {
            "m_Id": "cf5cde274ab04d33bf46ab3abb4ba03c"
        },
        {
            "m_Id": "4ab4353a98ed436fad9b119236c53dfe"
        },
        {
            "m_Id": "8f534a62e6b4479cadc7e03caa03b527"
        },
        {
            "m_Id": "d3e2f52565ae4923b92286b03fe3f3eb"
        },
        {
            "m_Id": "849e7e5c87cc4b7f8968d27c71c90294"
        }
    ],
    "synonyms": [
        "tex2d"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_TextureType": 0,
    "m_NormalMapSpace": 0,
    "m_EnableGlobalMipBias": true,
    "m_MipSamplingMode": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDTarget",
    "m_ObjectId": "c2806d9d6860473ab3acec52bf8fe5f1",
    "m_ActiveSubTarget": {
        "m_Id": "2850794c48cc485a9b048e8f163042ba"
    },
    "m_Datas": [
        {
            "m_Id": "9f5065cb62e741069c466280fc3695e1"
        },
        {
            "m_Id": "7c1f59d1e0fa46babbedcca658d2eeb0"
        },
        {
            "m_Id": "442de96a37a843698c605ec52eee3d41"
        }
    ],
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false,
    "m_SupportLineRendering": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "c3ea2e6b2ab9496f9f0448ebaea31449",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "c5b061bc1f244c678dcdef7470a15eb1",
    "m_Id": 0,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MultiplyNode",
    "m_ObjectId": "ca2a02a13c254f9da4a729961651a94c",
    "m_Group": {
        "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
    },
    "m_Name": "Multiply",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1611.500244140625,
            "y": 1372.5001220703125,
            "width": 126.0,
            "height": 118.0001220703125
        }
    },
    "m_Slots": [
        {
            "m_Id": "5cedfb23e5d94ac696f9f0f70b5a8b4d"
        },
        {
            "m_Id": "f0753451213440d2bb9a7837ce9c6d53"
        },
        {
            "m_Id": "af4ae784bc4244c9ad7cc257d6941419"
        }
    ],
    "synonyms": [
        "multiplication",
        "times",
        "x"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ca950e3dfbe445959cf36cbfff46ea47",
    "m_Id": 3,
    "m_DisplayName": "Delta Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Delta Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "cd865d434f324a9f8044d9dee98246cc",
    "m_Id": 3,
    "m_DisplayName": "Tile",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tile",
    "m_StageCapability": 3,
    "m_Value": 6.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "ce2d212608114b6d854ba797e423aa98",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "cf5cde274ab04d33bf46ab3abb4ba03c",
    "m_Id": 6,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "d2774e7daa814c1fac7d7f2fd50ad24e",
    "m_Id": 6,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.FloorNode",
    "m_ObjectId": "d27b544d9b484fa38021ded4ba09e2a3",
    "m_Group": {
        "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
    },
    "m_Name": "Floor",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1077.5001220703125,
            "y": 1373.5001220703125,
            "width": 130.5,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "91a5bb9598334950b0c083ed4c0fdbda"
        },
        {
            "m_Id": "116013b05ba94931b4afc52f678a88e2"
        }
    ],
    "synonyms": [
        "down"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "d3e2f52565ae4923b92286b03fe3f3eb",
    "m_Id": 2,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "d4bd07c30c4e4834afbeef9e778eea04",
    "m_Id": 2,
    "m_DisplayName": "Height",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Height",
    "m_StageCapability": 3,
    "m_Value": 4.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleTexture2DNode",
    "m_ObjectId": "d5759e8fc5814ada9c65177859771c47",
    "m_Group": {
        "m_Id": "6f2734b7d4914768b52ac344533ab2a9"
    },
    "m_Name": "Sample Texture 2D",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1078.5001220703125,
            "y": 379.5000305175781,
            "width": 208.00006103515626,
            "height": 338.0000305175781
        }
    },
    "m_Slots": [
        {
            "m_Id": "8f5a4bc6d59a4cad84417f13dabcf9bb"
        },
        {
            "m_Id": "17a38d4aae6c47929ad154849773b6a6"
        },
        {
            "m_Id": "71d315fa0071424cb026214375c0c747"
        },
        {
            "m_Id": "aa798a7039954524b8c84133160a45a5"
        },
        {
            "m_Id": "7e33f2fbb05a4cdc86c16c2b6356ae7c"
        },
        {
            "m_Id": "bd0ef51a93d04b26b06da12237977754"
        },
        {
            "m_Id": "66929c3a94314fa5aa4cf8825ddd3978"
        },
        {
            "m_Id": "19bbf079434d48f8a29bfea09a15e9ef"
        }
    ],
    "synonyms": [
        "tex2d"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_TextureType": 0,
    "m_NormalMapSpace": 0,
    "m_EnableGlobalMipBias": true,
    "m_MipSamplingMode": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "d5c2248abfe347bca3dacf2c164e94ba",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "dc50c91a96774dcfaebdc3843faf72ae",
    "m_Id": 0,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "dd0a915b2ed44081a09738b47e8bf18e",
    "m_Id": 1,
    "m_DisplayName": "G",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ddb0f47e94f54845b3604992a77c8317",
    "m_Id": 7,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "dea805c82cab453bb49c72846f479d36",
    "m_Id": 5,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TimeNode",
    "m_ObjectId": "e1af65bc2f194a4984ed02aa4aff760f",
    "m_Group": {
        "m_Id": "6f2734b7d4914768b52ac344533ab2a9"
    },
    "m_Name": "Time",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1436.0001220703125,
            "y": 549.5000610351563,
            "width": 79.0,
            "height": 77.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "34a5b4bcf11a4808924fd3676e7ab93b"
        },
        {
            "m_Id": "82a47b59110f4b09bb9e36bb59b22665"
        },
        {
            "m_Id": "b8e88675c2984c669ed8580823519151"
        },
        {
            "m_Id": "3f117404ffdc40a1848cacaf1865c2f5"
        },
        {
            "m_Id": "08893d1695724a8d8327d8235265a436"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "e20cc700bd304a99aad996b0c7af7e23",
    "m_Title": "",
    "m_Content": "Number of rows and columns.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1742.0001220703125,
        "y": 1794.0001220703125,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.FlipbookNode",
    "m_ObjectId": "e3560219808f4ad4ab217922b6ab9fa3",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Flipbook",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1061.0001220703125,
            "y": 38.000003814697269,
            "width": 160.00006103515626,
            "height": 227.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "b38bcf57ee2344b18a5017853914c3fd"
        },
        {
            "m_Id": "19cafc2de78e468780f788b9503f820c"
        },
        {
            "m_Id": "4eb859c11edd462fb1f7af0962609b84"
        },
        {
            "m_Id": "a16fd1be18be4d85b5d500bcff422d91"
        },
        {
            "m_Id": "fcb21adad2ae4307b90c33b51b1f7aaa"
        }
    ],
    "synonyms": [
        "atlas",
        "animation"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_InvertX": false,
    "m_InvertY": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e4136c28f851402cb61ca073add4f8c3",
    "m_Id": 1,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e4d8fda266d04ddba7224714f5741d7a",
    "m_Id": 1,
    "m_DisplayName": "Sine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "e4e85a6afe184ea18aec7f58dd13b7e3",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "e637eff523cd44e1b74e71aad0f73b6c",
    "m_Title": "Under The Hood",
    "m_Position": {
        "x": -1829.500244140625,
        "y": 1172.0001220703125
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SplitNode",
    "m_ObjectId": "e79b4a7dfad5473d887111097f61a771",
    "m_Group": {
        "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
    },
    "m_Name": "Split",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1523.000244140625,
            "y": 1531.5001220703125,
            "width": 117.5001220703125,
            "height": 76.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "060846d09e684162ab3d4008dba4a8af"
        },
        {
            "m_Id": "e4136c28f851402cb61ca073add4f8c3"
        },
        {
            "m_Id": "f27c19d3778d49aeb7d31958cc3bc730"
        },
        {
            "m_Id": "7b0499e9ed9840658387442be25a9800"
        },
        {
            "m_Id": "3c8358d24c5640f6a3f0bfd15fc0c111"
        }
    ],
    "synonyms": [
        "separate"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "e9fbbb1722a84f72ab731066cc81a5f8",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "ec2396e902ed41daabd081cc95744aa0",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalUnlitSubTarget",
    "m_ObjectId": "ed4c63b29d6e45a188b07e112ac3258f"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "ed9aeef656a441b99424620d35a14554",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"a7ff24a0f3bd4dd4aaf9265ba8da09ba\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "ee5e59b25ec944b6a2947e5938320173",
    "m_Title": "",
    "m_Content": "This page lists a large number of free flipbook assets created by Unity VFX artists:\n\nhttps://blog.unity.com/technology/free-vfx-image-sequences-flipbooks",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1529.5001220703125,
        "y": 933.0000610351563,
        "width": 461.0,
        "height": 100.00006103515625
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "ef3bfe0936fe4400b29dccada84d29e2",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "f0753451213440d2bb9a7837ce9c6d53",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 24.0,
        "e01": 2.0,
        "e02": 2.0,
        "e03": 2.0,
        "e10": 2.0,
        "e11": 2.0,
        "e12": 2.0,
        "e13": 2.0,
        "e20": 2.0,
        "e21": 2.0,
        "e22": 2.0,
        "e23": 2.0,
        "e30": 2.0,
        "e31": 2.0,
        "e32": 2.0,
        "e33": 2.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInUnlitSubTarget",
    "m_ObjectId": "f143102fd05744bc95cf204e762736cd"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "f27c19d3778d49aeb7d31958cc3bc730",
    "m_Id": 2,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleTexture2DNode",
    "m_ObjectId": "f3f7fd9c12ae472c9134f6242a73b337",
    "m_Group": {
        "m_Id": "f8461a60673d4e1eb815aa1af5955cfc"
    },
    "m_Name": "Sample Texture 2D",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -367.0000305175781,
            "y": 381.0000305175781,
            "width": 208.0000762939453,
            "height": 338.0000305175781
        }
    },
    "m_Slots": [
        {
            "m_Id": "dc50c91a96774dcfaebdc3843faf72ae"
        },
        {
            "m_Id": "179f5bd793ef45fab8374ade8ee23868"
        },
        {
            "m_Id": "56cb0ac20022479588b86dd8c1ac986e"
        },
        {
            "m_Id": "ab27b9cb5f264b4fb27e1d652c008756"
        },
        {
            "m_Id": "ddb0f47e94f54845b3604992a77c8317"
        },
        {
            "m_Id": "ed9aeef656a441b99424620d35a14554"
        },
        {
            "m_Id": "126e0a424ea54c38b4a19e764cb05246"
        },
        {
            "m_Id": "d5c2248abfe347bca3dacf2c164e94ba"
        }
    ],
    "synonyms": [
        "tex2d"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_TextureType": 0,
    "m_NormalMapSpace": 0,
    "m_EnableGlobalMipBias": true,
    "m_MipSamplingMode": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "f5419f87630345b3b2099881e96a62ab",
    "m_Id": 0,
    "m_DisplayName": "",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "f7742141292147a89cb8eafdbd7c9480",
    "m_Id": 7,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "f843f1cc34a94869a71a1f9a34449490",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "f8461a60673d4e1eb815aa1af5955cfc",
    "m_Title": "Playback Speed",
    "m_Position": {
        "x": -830.0,
        "y": 322.50006103515627
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2Node",
    "m_ObjectId": "fbc978964a224c3a8adaf460b564e5bd",
    "m_Group": {
        "m_Id": "e637eff523cd44e1b74e71aad0f73b6c"
    },
    "m_Name": "Vector 2",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1699.000244140625,
            "y": 1687.5001220703125,
            "width": 127.0,
            "height": 101.0001220703125
        }
    },
    "m_Slots": [
        {
            "m_Id": "ffe112466a0b4e1c8737a611c12b4624"
        },
        {
            "m_Id": "6ece560c9d764954be7043c6b78617d9"
        },
        {
            "m_Id": "b33a3598caed468e8fac325d539c1f60"
        }
    ],
    "synonyms": [
        "2",
        "v2",
        "vec2",
        "float2"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "fcb21adad2ae4307b90c33b51b1f7aaa",
    "m_Id": 4,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "fd8acf1378b94b1c956f9ad460db3dd2",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.BaseColor",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "9dc92a3957d34eb9ab567fecd91d0978"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.BaseColor"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ffe112466a0b4e1c8737a611c12b4624",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 4.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

