<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../../../../../UIElementsSchema/UIElements.xsd" editor-extension-mode="True">
    <ui:VisualElement>
        <ui:IMGUIContainer name="path-editor-container" />
        <ui:DropdownField label="Composite Part" index="-1" choices="System.Collections.Generic.List`1[System.String]" name="composite-part-dropdown" tooltip="The named part of the composite that the binding is assigned to. Multiple bindings may be assigned the same part. All controls from all bindings that are assigned the same part will collectively feed values into that part of the composite." />
    </ui:VisualElement>
</ui:UXML>
