.unity-label {
    padding: 5px 2px 2px;
    margin: 2px 4px;
}

.GraphSubWindow {
    position:absolute;
    border-left-width: 1px;
    border-top-width: 1px;
    border-right-width: 1px;
    border-bottom-width: 1px;
    border-radius: 5px;
    flex-direction: column;
    background-color: #2b2b2b;
    border-color: #191919;
    min-width: 100px;
    min-height: 100px;
    width: 200px;
    height: 200px;
}

.GraphSubWindow.windowed {
    position: relative;
    padding-top: 0;
    flex: auto;
    border-left-width: 0;
    border-top-width: 0;
    border-right-width: 0;
    border-bottom-width: 0;
    border-radius: 0;
    width: initial;
    height: initial;
}

.GraphSubWindow.windowed > .resizer {
    display: none;
}

.GraphSubWindow > .mainContainer {
    flex-direction: column;
    align-items: stretch;
}

.GraphSubWindow.scrollable > .mainContainer {
    position: absolute;
    top:0;
    left:0;
    right:0;
    bottom:0;
}

.GraphSubWindow > .mainContainer > #content {
    flex-direction: column;
    align-items: stretch;
}

.GraphSubWindow.scrollable > .mainContainer > #content {
    position: absolute;
    top:0;
    left:0;
    right:0;
    bottom:0;
    flex-direction: column;
    align-items: stretch;
}

.GraphSubWindow > .mainContainer > #content > ScrollView {
    flex: 1 0 0;
}

.GraphSubWindow > .mainContainer > #content > #contentContainer {
    min-height: auto;
    padding: 0 0 6px;
    flex-direction: column;
    flex-grow: 1;
    align-items: stretch;
}

.GraphSubWindow > #content > #header {
    font-size: 15px;
}

.GraphSubWindow > .mainContainer > #content > #header {
    overflow: hidden;
    flex-direction: row;
    align-items: stretch;
    background-color: #393939;
    border-bottom-width: 1px;
    border-color: #212121;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
    padding-left: 1px;
    padding-top: 4px;
    padding-bottom: 2px;
}
