{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "cdb39b7e881643a6b5ecd52c53d159e2",
    "m_Properties": [
        {
            "m_Id": "be4f3add32a5407d8ff7b8a8cd0a9ec4"
        }
    ],
    "m_Keywords": [
        {
            "m_Id": "36193a8b450b40f0a9affd0f570ce398"
        }
    ],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "5493b959342f491a9d87153b14feb984"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "f592f6205a1e4973bc5fdf90adf7b5a4"
        },
        {
            "m_Id": "f4b374b9f0d044d4a4bc791dbb671d9f"
        },
        {
            "m_Id": "18c5b21a9c23478c91545e771efdcad5"
        },
        {
            "m_Id": "e55b3521493549c49be0297d50bf14a8"
        },
        {
            "m_Id": "aafba6aaff6a4140a999ed3298ed0193"
        },
        {
            "m_Id": "a8ae05aa845441f8aa4712a2a5283e85"
        },
        {
            "m_Id": "a3f45a5f88b8440686ba06c023dac0a3"
        },
        {
            "m_Id": "2bab60afdf0b48c3b83b70ada520e9d1"
        },
        {
            "m_Id": "8f3892e446464cb79bdfcf6483408fa3"
        },
        {
            "m_Id": "c0a2c39848aa49268f97487493e86fe5"
        },
        {
            "m_Id": "746413c8181c476dbec5e46dc5b7862f"
        },
        {
            "m_Id": "812b48cefab441189844e5cdeb8a77ea"
        },
        {
            "m_Id": "cbd9c75c0860470ab533bc659df3a358"
        },
        {
            "m_Id": "2cce97d70cd1455ea2330bd61be71113"
        },
        {
            "m_Id": "17b71856fb1042569b601c52ffc9ef72"
        },
        {
            "m_Id": "c7812949c2c34ac3ade62d326064368a"
        },
        {
            "m_Id": "67ab247cb50c4124b99148d00c36969b"
        },
        {
            "m_Id": "eb8dad2083ff459a99c258adba4a766b"
        },
        {
            "m_Id": "c73afd03baca4b1eb761edeace3a02c4"
        },
        {
            "m_Id": "fd3da63425f1481ba81c123a90a911ee"
        },
        {
            "m_Id": "883b740a78604c55bc38258186f67821"
        }
    ],
    "m_GroupDatas": [],
    "m_StickyNoteDatas": [
        {
            "m_Id": "fc123e62826b4eba92f149d2f1fd1c37"
        },
        {
            "m_Id": "7b1b24d6c8054f03818b657db2486597"
        },
        {
            "m_Id": "10ea0bf247654b5c94e1f43b839666f0"
        },
        {
            "m_Id": "5c6fa62f2e1f4f0891909f23976ddcb6"
        },
        {
            "m_Id": "195e485de918442faecaae396a9804a1"
        },
        {
            "m_Id": "7a80e969b7e54283b76e2a2e4c9aaf82"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "17b71856fb1042569b601c52ffc9ef72"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "2cce97d70cd1455ea2330bd61be71113"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "2cce97d70cd1455ea2330bd61be71113"
                },
                "m_SlotId": 3
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "2bab60afdf0b48c3b83b70ada520e9d1"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "2cce97d70cd1455ea2330bd61be71113"
                },
                "m_SlotId": 3
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "e55b3521493549c49be0297d50bf14a8"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "67ab247cb50c4124b99148d00c36969b"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "fd3da63425f1481ba81c123a90a911ee"
                },
                "m_SlotId": 3
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "746413c8181c476dbec5e46dc5b7862f"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "17b71856fb1042569b601c52ffc9ef72"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "746413c8181c476dbec5e46dc5b7862f"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "c7812949c2c34ac3ade62d326064368a"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "746413c8181c476dbec5e46dc5b7862f"
                },
                "m_SlotId": 3
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "2cce97d70cd1455ea2330bd61be71113"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "812b48cefab441189844e5cdeb8a77ea"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "746413c8181c476dbec5e46dc5b7862f"
                },
                "m_SlotId": 173151354
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "883b740a78604c55bc38258186f67821"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "fd3da63425f1481ba81c123a90a911ee"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "c0a2c39848aa49268f97487493e86fe5"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "812b48cefab441189844e5cdeb8a77ea"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "c7812949c2c34ac3ade62d326064368a"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "2cce97d70cd1455ea2330bd61be71113"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "cbd9c75c0860470ab533bc659df3a358"
                },
                "m_SlotId": 3
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "746413c8181c476dbec5e46dc5b7862f"
                },
                "m_SlotId": -382048477
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "fd3da63425f1481ba81c123a90a911ee"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "746413c8181c476dbec5e46dc5b7862f"
                },
                "m_SlotId": -717366018
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": [
            {
                "m_Id": "f592f6205a1e4973bc5fdf90adf7b5a4"
            },
            {
                "m_Id": "f4b374b9f0d044d4a4bc791dbb671d9f"
            },
            {
                "m_Id": "18c5b21a9c23478c91545e771efdcad5"
            }
        ]
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 200.0
        },
        "m_Blocks": [
            {
                "m_Id": "e55b3521493549c49be0297d50bf14a8"
            },
            {
                "m_Id": "aafba6aaff6a4140a999ed3298ed0193"
            },
            {
                "m_Id": "a8ae05aa845441f8aa4712a2a5283e85"
            },
            {
                "m_Id": "a3f45a5f88b8440686ba06c023dac0a3"
            },
            {
                "m_Id": "2bab60afdf0b48c3b83b70ada520e9d1"
            },
            {
                "m_Id": "8f3892e446464cb79bdfcf6483408fa3"
            },
            {
                "m_Id": "eb8dad2083ff459a99c258adba4a766b"
            },
            {
                "m_Id": "c73afd03baca4b1eb761edeace3a02c4"
            }
        ]
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"fileID\":10210,\"guid\":\"0000000000000000e000000000000000\",\"type\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Shader Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": ""
    },
    "m_SubDatas": [],
    "m_ActiveTargets": [
        {
            "m_Id": "82542dd33b2c4febb305e11cb78e091b"
        },
        {
            "m_Id": "3e5bff0fe7ab479f9ae1b554e1361743"
        },
        {
            "m_Id": "adb61d6827d6455bb7c9efbc2c575345"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "012a64cbebc94015849edd40ce5e7066",
    "m_Id": 2,
    "m_DisplayName": "Time Only",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "TIME_ONLY",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "06756430716a4e5e8aeac9179706863e",
    "m_Id": 1,
    "m_DisplayName": "Scale",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Scale",
    "m_StageCapability": 3,
    "m_Value": 25.0,
    "m_DefaultValue": 500.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0a2ad4d5ae63451aa55492a1fd6dccbf",
    "m_Id": -717366018,
    "m_DisplayName": "Flow Time",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "_Flow_Time",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0eaf0962500049cca736cf6a5f131d87",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "10ea0bf247654b5c94e1f43b839666f0",
    "m_Title": "Flow Time",
    "m_Content": "Flow time could be a time node or you can use a Flow Map Time subgraph. \nOnly using the Time node is cheaper but there is a noticeable strobing effect where the entire model appears to be pulsing in rhythm. \nUsing the Flow Time subgraph breaks up the phase blending into smooth gradients across the surface so that it’s non-uniform and removes the strobing effect.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1514.0,
        "y": 117.0,
        "width": 200.0,
        "height": 209.0
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInLitSubTarget",
    "m_ObjectId": "114f1318519341b8b864fde78b8adc1b",
    "m_WorkflowMode": 1,
    "m_NormalDropOffSpace": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "13605a1499fd416cabcad0a15ca0c65d",
    "m_Id": 3,
    "m_DisplayName": "Delta Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Delta Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "1558315bf1c246d0b577ba89662068bf",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.NoiseNode",
    "m_ObjectId": "17b71856fb1042569b601c52ffc9ef72",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Simple Noise",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -662.2501220703125,
            "y": 42.7500114440918,
            "width": 145.49993896484376,
            "height": 149.24996948242188
        }
    },
    "m_Slots": [
        {
            "m_Id": "50578f5f60064521aace3ac3e8ebb6ba"
        },
        {
            "m_Id": "44c857f9935a4c85af1e87d141c7b483"
        },
        {
            "m_Id": "a007d9a7fdb5469ca4598f52723f9051"
        }
    ],
    "synonyms": [
        "value noise"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_HashType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "18c5b21a9c23478c91545e771efdcad5",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Tangent",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "4821e90be8d5485b97fbfc61f53e6a12"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Tangent"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "195e485de918442faecaae396a9804a1",
    "m_Title": "",
    "m_Content": "The default value of 0.5 in the Offset input means that each of the phases starts out half stretched in the negative direction, moves to unstretched, and then moves to half stretched in the positive direction.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -978.0,
        "y": 459.0,
        "width": 200.0,
        "height": 113.0
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "1ae4226d12b64c2ea4df5a4fc4dddaf8",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "1cf4ca86bd4f4245a5ab99e1389192cd",
    "m_Id": -64458125,
    "m_DisplayName": "Phase Offset UVs",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "_Phase_Offset_UVs",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 2.0,
        "y": 2.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "2012b0043d1e40af87a917d298139c58",
    "m_Id": 0,
    "m_DisplayName": "Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "2027884b5c4145d39925dca3928ddddb",
    "m_Id": 1,
    "m_DisplayName": "Tiling",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tiling",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 8.0,
        "y": 8.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "233adf4a34094a628e4d31e9c7146ef3",
    "m_Id": 0,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "28b0486a5886410898bf9cae55c56ebf",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_ExcludeFromTUAndAA": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "2b7dbe2696024761a5d596f572fe0e39",
    "m_Id": 0,
    "m_DisplayName": "Normal (Tangent Space)",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "NormalTS",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 3
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "2bab60afdf0b48c3b83b70ada520e9d1",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Emission",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "a25710deea0a45e894e541fd694f18d4"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Emission"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "2c14133fb5df4551a657ffeb4371c681",
    "m_Id": 1,
    "m_DisplayName": "Sine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.LerpNode",
    "m_ObjectId": "2cce97d70cd1455ea2330bd61be71113",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Lerp",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -449.5000305175781,
            "y": 258.0000305175781,
            "width": 207.99998474121095,
            "height": 326.0000305175781
        }
    },
    "m_Slots": [
        {
            "m_Id": "9dc918fc2ae1499db2ddafd2134ce4a0"
        },
        {
            "m_Id": "7dc8a7def62e4da4a4ae4fd3cbdd2a0d"
        },
        {
            "m_Id": "8ba8efcb351743cfb9c7bc5a2e69a42c"
        },
        {
            "m_Id": "76311857e0c147159344325c8ef8e0e0"
        }
    ],
    "synonyms": [
        "mix",
        "blend",
        "linear interpolate"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 1,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.ShaderKeyword",
    "m_ObjectId": "36193a8b450b40f0a9affd0f570ce398",
    "m_Guid": {
        "m_GuidSerialized": "ea9b9c7d-9cba-42c5-8c1f-dd923a2858f8"
    },
    "m_Name": "Temporal Mode",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "Temporal Mode",
    "m_DefaultReferenceName": "_TEMPORAL_MODE",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_KeywordType": 1,
    "m_KeywordDefinition": 0,
    "m_KeywordScope": 0,
    "m_KeywordStages": 63,
    "m_Entries": [
        {
            "id": 2,
            "displayName": "Time Only",
            "referenceName": "TIME_ONLY"
        },
        {
            "id": 3,
            "displayName": "Flow Map Time",
            "referenceName": "FLOW_MAP_TIME"
        }
    ],
    "m_Value": 0,
    "m_IsEditable": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDTarget",
    "m_ObjectId": "3e5bff0fe7ab479f9ae1b554e1361743",
    "m_ActiveSubTarget": {
        "m_Id": "fcb1c8f7d6bc49adbc655d9776cd58da"
    },
    "m_Datas": [
        {
            "m_Id": "28b0486a5886410898bf9cae55c56ebf"
        },
        {
            "m_Id": "908e9c606f5949a9b7abc87affa09529"
        },
        {
            "m_Id": "6502b076c66b43ff957adc87766e8f2f"
        },
        {
            "m_Id": "5646d763f5dc4f67bb87dc55aded9daa"
        }
    ],
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false,
    "m_SupportLineRendering": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "3f3d54af6a5242c79ce6319cf53b0061",
    "m_Id": 1,
    "m_DisplayName": "UV0",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV0",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "44c857f9935a4c85af1e87d141c7b483",
    "m_Id": 1,
    "m_DisplayName": "Scale",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Scale",
    "m_StageCapability": 3,
    "m_Value": 25.0,
    "m_DefaultValue": 500.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "47e50db222874ed2a44dd2e4d28e5b01",
    "m_Id": 0,
    "m_DisplayName": "Base Color",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BaseColor",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.5,
        "y": 0.5,
        "z": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.5,
        "g": 0.5,
        "b": 0.5,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TangentMaterialSlot",
    "m_ObjectId": "4821e90be8d5485b97fbfc61f53e6a12",
    "m_Id": 0,
    "m_DisplayName": "Tangent",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tangent",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "49ecfee5b754441295d8b166b6c432e6",
    "m_Id": 0,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "4e9dee60ba3146d0be3c45d42a670095",
    "m_Id": 3,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "4ec73c180fcf4a60b1d5302d0848e0cc",
    "m_Id": 2,
    "m_DisplayName": "Cosine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cosine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "50578f5f60064521aace3ac3e8ebb6ba",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DMaterialSlot",
    "m_ObjectId": "529b361f843d41d6af4a71619da59a7b",
    "m_Id": 0,
    "m_DisplayName": "Flowmap",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "5378449de2bf4023851567f2aa52d28a",
    "m_Id": 264662620,
    "m_DisplayName": "Offset",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "_Offset",
    "m_StageCapability": 2,
    "m_Value": 0.5,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "5493b959342f491a9d87153b14feb984",
    "m_Name": "",
    "m_ChildObjectList": [
        {
            "m_Id": "be4f3add32a5407d8ff7b8a8cd0a9ec4"
        },
        {
            "m_Id": "36193a8b450b40f0a9affd0f570ce398"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "55863ca70cbc4e36887214a3fe15369e",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.LightingData",
    "m_ObjectId": "5646d763f5dc4f67bb87dc55aded9daa",
    "m_NormalDropOffSpace": 0,
    "m_BlendPreserveSpecular": true,
    "m_ReceiveDecals": true,
    "m_ReceiveSSR": true,
    "m_ReceiveSSRTransparent": false,
    "m_SpecularAA": false,
    "m_SpecularOcclusionMode": 1,
    "m_OverrideBakedGI": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "5c6fa62f2e1f4f0891909f23976ddcb6",
    "m_Title": "",
    "m_Content": "We put the Flow Time options as enum keywords for easy switching.\n\r\n",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1153.5001220703125,
        "y": 206.00001525878907,
        "width": 123.5,
        "height": 100.00001525878906
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "5ddeeda855ef4caaabe8a8731b8d4b13",
    "m_Id": 3,
    "m_DisplayName": "Lerp",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Lerp",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "61ac1d80c4464f758f23846bbb86216e",
    "m_Id": 2,
    "m_DisplayName": "Offset",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Offset",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDLitData",
    "m_ObjectId": "6502b076c66b43ff957adc87766e8f2f",
    "m_RayTracing": false,
    "m_MaterialType": 0,
    "m_MaterialTypeMask": 2,
    "m_RefractionModel": 0,
    "m_SSSTransmission": true,
    "m_EnergyConservingSpecular": true,
    "m_ClearCoat": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphNode",
    "m_ObjectId": "67ab247cb50c4124b99148d00c36969b",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "FlowMapTime",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1489.0001220703125,
            "y": 331.0,
            "width": 265.0,
            "height": 187.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "bc410f02ede845d683ff998b15d5a163"
        },
        {
            "m_Id": "1cf4ca86bd4f4245a5ab99e1389192cd"
        },
        {
            "m_Id": "fed2ab7526844af19630a8747995cb0f"
        },
        {
            "m_Id": "9e4ba347d8154afb9819abb0768d6247"
        },
        {
            "m_Id": "ddc04da06c444b0e9e6973d148f1b023"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 1,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedSubGraph": "{\n    \"subGraph\": {\n        \"fileID\": -5475051401550479605,\n        \"guid\": \"d3a5ed15ae8578b448a8756ea0b97162\",\n        \"type\": 3\n    }\n}",
    "m_PropertyGuids": [
        "5b5d8775-108b-48c9-9c29-d3b637da8e94",
        "5d028f14-b68d-4a92-837d-0d4c6936b5a7",
        "0abec969-3d19-4183-a657-d2bff276b200",
        "9f1f739e-e306-473a-b9a9-9b5c0bc1f05f"
    ],
    "m_PropertyIds": [
        835282005,
        -64458125,
        -195177180,
        -615581654
    ],
    "m_Dropdowns": [
        "_Mask_Channel"
    ],
    "m_DropdownSelectedEntries": [
        "Red"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "71a9a023497340bfa1dec2eb3d2a6f3c",
    "m_Id": 4,
    "m_DisplayName": "Smooth Delta",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Smooth Delta",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphNode",
    "m_ObjectId": "746413c8181c476dbec5e46dc5b7862f",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "UVFlowMap",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -915.0000610351563,
            "y": 258.0000305175781,
            "width": 178.0,
            "height": 190.99996948242188
        }
    },
    "m_Slots": [
        {
            "m_Id": "fe3c644ff3214cedad95844c33641eb6"
        },
        {
            "m_Id": "bf620f62dabd471587ee6036c10c9b92"
        },
        {
            "m_Id": "0a2ad4d5ae63451aa55492a1fd6dccbf"
        },
        {
            "m_Id": "d5107221c74b47df92ea2fef5de68e21"
        },
        {
            "m_Id": "5378449de2bf4023851567f2aa52d28a"
        },
        {
            "m_Id": "3f3d54af6a5242c79ce6319cf53b0061"
        },
        {
            "m_Id": "a92cd07e0bd64b00a1a3e85f3ff4e7c1"
        },
        {
            "m_Id": "5ddeeda855ef4caaabe8a8731b8d4b13"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedSubGraph": "{\n    \"subGraph\": {\n        \"fileID\": -5475051401550479605,\n        \"guid\": \"c76da6dceef150547b7030daa98eeda6\",\n        \"type\": 3\n    }\n}",
    "m_PropertyGuids": [
        "ca27d64d-aa8e-4ff8-ac37-e945155f64ca",
        "8cf4855a-a638-4c2e-8780-d2a560f350d9",
        "b8b51345-aa81-4cf1-911c-2e81ab7957a0",
        "00a5a208-fc68-4628-a10a-f6a6e2334109",
        "9e6a1d4a-da1e-43ed-9656-c2c79eff8315"
    ],
    "m_PropertyIds": [
        173151354,
        -1135062907,
        -717366018,
        -382048477,
        264662620
    ],
    "m_Dropdowns": [],
    "m_DropdownSelectedEntries": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "76311857e0c147159344325c8ef8e0e0",
    "m_Id": 3,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "7a80e969b7e54283b76e2a2e4c9aaf82",
    "m_Title": "Lerp value",
    "m_Content": "The output lerp value is consistent with the flow time so the two noise pattern we are sampling could be blended smoothly.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -731.0,
        "y": 380.0,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "7b1b24d6c8054f03818b657db2486597",
    "m_Title": "UV Flow Map",
    "m_Content": "This node takes in a sampled texture in the Flow Map input which indicates the direction of flow. \nThe Strength input controls the distance that the UVs get warped.\nThe UV input controls how the texture is applied.\nThe Offset value controls the midpoint of the stretching effect.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -922.0,
        "y": 86.0,
        "width": 200.0,
        "height": 170.9491729736328
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "7da43873f2d64dfb8dd4ee4630ffed51",
    "m_Id": 3,
    "m_DisplayName": "Flow Map Time",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "FLOW_MAP_TIME",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "7dc8a7def62e4da4a4ae4fd3cbdd2a0d",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleTexture2DNode",
    "m_ObjectId": "812b48cefab441189844e5cdeb8a77ea",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Sample Texture 2D",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1130.0001220703125,
            "y": 43.000003814697269,
            "width": 179.00006103515626,
            "height": 154.99996948242188
        }
    },
    "m_Slots": [
        {
            "m_Id": "233adf4a34094a628e4d31e9c7146ef3"
        },
        {
            "m_Id": "feefc53273f0421694a89e200681e7c0"
        },
        {
            "m_Id": "f164feece9c243fba9a98b325024a3cf"
        },
        {
            "m_Id": "bfd238da81f6454eb1a6e33ae65f0d97"
        },
        {
            "m_Id": "99562461323e42e3806bff0b524e9601"
        },
        {
            "m_Id": "c157b97109ef4521a9596cc8b1854f1b"
        },
        {
            "m_Id": "f4778b45a32341dc99d7a7ddb31694a8"
        },
        {
            "m_Id": "1558315bf1c246d0b577ba89662068bf"
        }
    ],
    "synonyms": [
        "tex2d"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_TextureType": 1,
    "m_NormalMapSpace": 0,
    "m_EnableGlobalMipBias": true,
    "m_MipSamplingMode": 0
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInTarget",
    "m_ObjectId": "82542dd33b2c4febb305e11cb78e091b",
    "m_ActiveSubTarget": {
        "m_Id": "114f1318519341b8b864fde78b8adc1b"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZWriteControl": 0,
    "m_ZTestMode": 4,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CustomEditorGUI": ""
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TimeNode",
    "m_ObjectId": "883b740a78604c55bc38258186f67821",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Time",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1303.0001220703125,
            "y": 237.00001525878907,
            "width": 79.0,
            "height": 76.99998474121094
        }
    },
    "m_Slots": [
        {
            "m_Id": "2012b0043d1e40af87a917d298139c58"
        },
        {
            "m_Id": "2c14133fb5df4551a657ffeb4371c681"
        },
        {
            "m_Id": "4ec73c180fcf4a60b1d5302d0848e0cc"
        },
        {
            "m_Id": "13605a1499fd416cabcad0a15ca0c65d"
        },
        {
            "m_Id": "71a9a023497340bfa1dec2eb3d2a6f3c"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalLitSubTarget",
    "m_ObjectId": "8a677c5c77b24e8a9d4d697b1e58b569",
    "m_WorkflowMode": 1,
    "m_NormalDropOffSpace": 0,
    "m_ClearCoat": false,
    "m_BlendModePreserveSpecular": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "8ba8efcb351743cfb9c7bc5a2e69a42c",
    "m_Id": 2,
    "m_DisplayName": "T",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "T",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "8f3892e446464cb79bdfcf6483408fa3",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Occlusion",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "fa509d05a487446d8f929ad8b67f4dfc"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Occlusion"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "908e9c606f5949a9b7abc87affa09529",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "99562461323e42e3806bff0b524e9601",
    "m_Id": 7,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "9dc918fc2ae1499db2ddafd2134ce4a0",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "9e4ba347d8154afb9819abb0768d6247",
    "m_Id": -615581654,
    "m_DisplayName": "Speed",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "_Speed",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a007d9a7fdb5469ca4598f52723f9051",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "a25710deea0a45e894e541fd694f18d4",
    "m_Id": 0,
    "m_DisplayName": "Emission",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Emission",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 1,
    "m_DefaultColor": {
        "r": 0.0,
        "g": 0.0,
        "b": 0.0,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "a3f45a5f88b8440686ba06c023dac0a3",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Smoothness",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "af287234c3a540feb2744ce0b47b6035"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Smoothness"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "a8ae05aa845441f8aa4712a2a5283e85",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Metallic",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "f17f454a20814a3cb47d167208bc1f96"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Metallic"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "a92cd07e0bd64b00a1a3e85f3ff4e7c1",
    "m_Id": 2,
    "m_DisplayName": "UV1",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV1",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "aafba6aaff6a4140a999ed3298ed0193",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.NormalTS",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "2b7dbe2696024761a5d596f572fe0e39"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.NormalTS"
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget",
    "m_ObjectId": "adb61d6827d6455bb7c9efbc2c575345",
    "m_Datas": [],
    "m_ActiveSubTarget": {
        "m_Id": "8a677c5c77b24e8a9d4d697b1e58b569"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZTestMode": 4,
    "m_ZWriteControl": 0,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CastShadows": true,
    "m_ReceiveShadows": true,
    "m_SupportsLODCrossFade": false,
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "af287234c3a540feb2744ce0b47b6035",
    "m_Id": 0,
    "m_DisplayName": "Smoothness",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Smoothness",
    "m_StageCapability": 2,
    "m_Value": 0.5,
    "m_DefaultValue": 0.5,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "bc410f02ede845d683ff998b15d5a163",
    "m_Id": 835282005,
    "m_DisplayName": "Phase Offset Mask",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "_Phase_Offset_Mask",
    "m_StageCapability": 2,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"2fe31a8312ab8524ebd8601be367f0c8\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Texture2DShaderProperty",
    "m_ObjectId": "be4f3add32a5407d8ff7b8a8cd0a9ec4",
    "m_Guid": {
        "m_GuidSerialized": "a3444767-1f15-47a4-af57-ef25b6debbcf"
    },
    "m_Name": "Flowmap",
    "m_DefaultRefNameVersion": 1,
    "m_RefNameGeneratedByDisplayName": "Flowmap",
    "m_DefaultReferenceName": "_Flowmap",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_UseCustomSlotLabel": false,
    "m_CustomSlotLabel": "",
    "m_DismissedVersion": 0,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"0f8b659603075564c8b3b82db6ccef13\",\"type\":3}}",
        "m_Guid": ""
    },
    "isMainTexture": false,
    "useTilingAndOffset": false,
    "m_Modifiable": true,
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "bf620f62dabd471587ee6036c10c9b92",
    "m_Id": -1135062907,
    "m_DisplayName": "Strength",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "_Strength",
    "m_StageCapability": 2,
    "m_Value": 2.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "bfd238da81f6454eb1a6e33ae65f0d97",
    "m_Id": 6,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "bfe0fad07fec4d4fafc12ef1657af21e",
    "m_Id": 0,
    "m_DisplayName": "Bent Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BentNormal",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 3
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "c0a2c39848aa49268f97487493e86fe5",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1263.500244140625,
            "y": 83.0,
            "width": 131.0,
            "height": 34.00000762939453
        }
    },
    "m_Slots": [
        {
            "m_Id": "529b361f843d41d6af4a71619da59a7b"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "be4f3add32a5407d8ff7b8a8cd0a9ec4"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "c157b97109ef4521a9596cc8b1854f1b",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"instanceID\":0}}",
        "m_Guid": ""
    },
    "m_DefaultType": 3
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "c1d6f59514fc403a9838f325eb175860",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "c73afd03baca4b1eb761edeace3a02c4",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.BentNormal",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "bfe0fad07fec4d4fafc12ef1657af21e"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.BentNormal"
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.NoiseNode",
    "m_ObjectId": "c7812949c2c34ac3ade62d326064368a",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Simple Noise",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -662.2501220703125,
            "y": 195.75,
            "width": 145.49993896484376,
            "height": 149.25
        }
    },
    "m_Slots": [
        {
            "m_Id": "1ae4226d12b64c2ea4df5a4fc4dddaf8"
        },
        {
            "m_Id": "06756430716a4e5e8aeac9179706863e"
        },
        {
            "m_Id": "0eaf0962500049cca736cf6a5f131d87"
        }
    ],
    "synonyms": [
        "value noise"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_HashType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TilingAndOffsetNode",
    "m_ObjectId": "cbd9c75c0860470ab533bc659df3a358",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Tiling And Offset",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1179.0001220703125,
            "y": 484.50006103515627,
            "width": 153.5,
            "height": 142.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "55863ca70cbc4e36887214a3fe15369e"
        },
        {
            "m_Id": "2027884b5c4145d39925dca3928ddddb"
        },
        {
            "m_Id": "61ac1d80c4464f758f23846bbb86216e"
        },
        {
            "m_Id": "4e9dee60ba3146d0be3c45d42a670095"
        }
    ],
    "synonyms": [
        "pan",
        "scale"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "ccc37656c6ce47eca38da323e8ec8c17",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ce4a801696d546e69fb7bea53f419355",
    "m_Id": 0,
    "m_DisplayName": "Alpha",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Alpha",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "d5107221c74b47df92ea2fef5de68e21",
    "m_Id": -382048477,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "_UV",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ddc04da06c444b0e9e6973d148f1b023",
    "m_Id": 1,
    "m_DisplayName": "FlowTime",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "FlowTime",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "e55b3521493549c49be0297d50bf14a8",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.BaseColor",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "47e50db222874ed2a44dd2e4d28e5b01"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.BaseColor"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "eb8dad2083ff459a99c258adba4a766b",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Alpha",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "ce4a801696d546e69fb7bea53f419355"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Alpha"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "f164feece9c243fba9a98b325024a3cf",
    "m_Id": 5,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "f17f454a20814a3cb47d167208bc1f96",
    "m_Id": 0,
    "m_DisplayName": "Metallic",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Metallic",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "f4778b45a32341dc99d7a7ddb31694a8",
    "m_Id": 2,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "f4b374b9f0d044d4a4bc791dbb671d9f",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Normal",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "49ecfee5b754441295d8b166b6c432e6"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Normal"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "f592f6205a1e4973bc5fdf90adf7b5a4",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "c1d6f59514fc403a9838f325eb175860"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Position"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "fa509d05a487446d8f929ad8b67f4dfc",
    "m_Id": 0,
    "m_DisplayName": "Ambient Occlusion",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Occlusion",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "fc123e62826b4eba92f149d2f1fd1c37",
    "m_Title": "Flow Mapping",
    "m_Content": "Flow Mapping is a technique that creates the illusion of flowing movement in a texture. It’s achieved by warping the texture coordinates along a specific flow direction over time in two separate phases.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1783.0001220703125,
        "y": -143.00001525878907,
        "width": 260.491455078125,
        "height": 107.06996154785156
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDLitSubTarget",
    "m_ObjectId": "fcb1c8f7d6bc49adbc655d9776cd58da"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.KeywordNode",
    "m_ObjectId": "fd3da63425f1481ba81c123a90a911ee",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Temporal Mode",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1194.0001220703125,
            "y": 307.0000305175781,
            "width": 197.0,
            "height": 118.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "ccc37656c6ce47eca38da323e8ec8c17"
        },
        {
            "m_Id": "012a64cbebc94015849edd40ce5e7066"
        },
        {
            "m_Id": "7da43873f2d64dfb8dd4ee4630ffed51"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Keyword": {
        "m_Id": "36193a8b450b40f0a9affd0f570ce398"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "fe3c644ff3214cedad95844c33641eb6",
    "m_Id": 173151354,
    "m_DisplayName": "Flow Map",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "_Flow_Map",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "fed2ab7526844af19630a8747995cb0f",
    "m_Id": -195177180,
    "m_DisplayName": "Phase Offset Strength",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "_Phase_Offset_Strength",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "feefc53273f0421694a89e200681e7c0",
    "m_Id": 4,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

