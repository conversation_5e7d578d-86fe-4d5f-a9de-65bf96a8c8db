using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Text;
using System.IO;

namespace VRoidFaceCustomization.Editor
{
    /// <summary>
    /// VRM10日志提取器
    /// 专门提取和过滤VRM10服装系统的关键调试信息
    /// </summary>
    public class VRM10LogExtractor : EditorWindow
    {
        private List<string> capturedLogs = new List<string>();
        private List<string> filteredLogs = new List<string>();
        private Vector2 scrollPosition;
        private bool isCapturing = false;
        private string currentTestPhase = "";
        
        // 过滤关键词
        private readonly string[] keywordFilters = {
            "[VRM10ClothBinder]",
            "[VRM10ClothDebugger]", 
            "[VRM10BoneExtractor]",
            "VRM10ClothBoneData",
            "骨骼绑定",
            "骨骼创建",
            "骨骼映射",
            "服装",
            "穿戴",
            "绑定失败",
            "绑定成功",
            "动态骨骼",
            "延迟绑定",
            "数据驱动",
            "SkinnedMeshRenderer",
            "bindpose",
            "Transform"
        };
        
        [MenuItem("Tools/VRM 1.0/Log Extractor")]
        public static void ShowWindow()
        {
            GetWindow<VRM10LogExtractor>("日志提取器");
        }
        
        private void OnEnable()
        {
            Application.logMessageReceived += OnLogMessageReceived;
        }
        
        private void OnDisable()
        {
            Application.logMessageReceived -= OnLogMessageReceived;
        }
        
        private void OnGUI()
        {
            EditorGUILayout.LabelField("VRM10 日志提取器", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("自动捕获和过滤VRM10服装系统的关键调试信息", MessageType.Info);
            EditorGUILayout.Space();
            
            // 控制按钮
            EditorGUILayout.BeginHorizontal();
            
            if (!isCapturing)
            {
                if (GUILayout.Button("开始捕获日志", GUILayout.Height(30)))
                {
                    StartCapturing();
                }
            }
            else
            {
                if (GUILayout.Button("停止捕获", GUILayout.Height(30)))
                {
                    StopCapturing();
                }
            }
            
            if (GUILayout.Button("清空日志", GUILayout.Height(30)))
            {
                ClearLogs();
            }
            
            if (GUILayout.Button("导出日志", GUILayout.Height(30)))
            {
                ExportLogs();
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            // 测试阶段标记
            EditorGUILayout.LabelField("测试阶段标记", EditorStyles.boldLabel);
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("标记：第一次穿戴"))
            {
                MarkTestPhase("=== 第一次穿戴测试（预期有问题） ===");
            }
            
            if (GUILayout.Button("标记：穿戴原版"))
            {
                MarkTestPhase("=== 穿戴原版服装测试 ===");
            }
            
            if (GUILayout.Button("标记：第二次穿戴"))
            {
                MarkTestPhase("=== 第二次穿戴测试（预期正常） ===");
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            // 状态显示
            EditorGUILayout.LabelField($"捕获状态: {(isCapturing ? "🔴 正在捕获" : "⚪ 已停止")}", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"原始日志: {capturedLogs.Count} 条");
            EditorGUILayout.LabelField($"过滤后: {filteredLogs.Count} 条");
            
            EditorGUILayout.Space();
            
            // 显示过滤后的日志
            EditorGUILayout.LabelField("关键日志信息", EditorStyles.boldLabel);
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(300));
            
            foreach (string log in filteredLogs)
            {
                // 根据日志类型设置颜色
                var style = EditorStyles.miniLabel;
                if (log.Contains("ERROR") || log.Contains("失败"))
                {
                    var errorStyle = new GUIStyle(EditorStyles.miniLabel);
                    errorStyle.normal.textColor = Color.red;
                    style = errorStyle;
                }
                else if (log.Contains("WARNING") || log.Contains("警告"))
                {
                    var warningStyle = new GUIStyle(EditorStyles.miniLabel);
                    warningStyle.normal.textColor = Color.yellow;
                    style = warningStyle;
                }
                else if (log.Contains("成功") || log.Contains("完成"))
                {
                    var successStyle = new GUIStyle(EditorStyles.miniLabel);
                    successStyle.normal.textColor = Color.green;
                    style = successStyle;
                }
                
                EditorGUILayout.LabelField(log, style);
            }
            
            EditorGUILayout.EndScrollView();
            
            EditorGUILayout.Space();
            
            // 快速分析
            if (filteredLogs.Count > 0)
            {
                EditorGUILayout.LabelField("快速分析", EditorStyles.boldLabel);
                PerformQuickAnalysis();
            }
        }
        
        private void OnLogMessageReceived(string logString, string stackTrace, LogType type)
        {
            if (!isCapturing) return;
            
            // 添加时间戳
            string timestamp = System.DateTime.Now.ToString("HH:mm:ss.fff");
            string fullLog = $"[{timestamp}] [{type}] {logString}";
            
            capturedLogs.Add(fullLog);
            
            // 检查是否包含关键词
            bool isRelevant = false;
            foreach (string keyword in keywordFilters)
            {
                if (logString.Contains(keyword))
                {
                    isRelevant = true;
                    break;
                }
            }
            
            if (isRelevant)
            {
                filteredLogs.Add(fullLog);
                Repaint();
            }
            
            // 限制日志数量
            if (capturedLogs.Count > 1000)
            {
                capturedLogs.RemoveAt(0);
            }
            
            if (filteredLogs.Count > 200)
            {
                filteredLogs.RemoveAt(0);
            }
        }
        
        private void StartCapturing()
        {
            isCapturing = true;
            ClearLogs();
            Debug.Log("[VRM10LogExtractor] 开始捕获日志");
        }
        
        private void StopCapturing()
        {
            isCapturing = false;
            Debug.Log("[VRM10LogExtractor] 停止捕获日志");
        }
        
        private void ClearLogs()
        {
            capturedLogs.Clear();
            filteredLogs.Clear();
            currentTestPhase = "";
        }
        
        private void MarkTestPhase(string phase)
        {
            currentTestPhase = phase;
            string marker = $"\n{phase}\n";
            filteredLogs.Add(marker);
            Debug.Log($"[VRM10LogExtractor] {phase}");
            Repaint();
        }
        
        private void ExportLogs()
        {
            if (filteredLogs.Count == 0)
            {
                EditorUtility.DisplayDialog("提示", "没有日志可以导出", "确定");
                return;
            }
            
            string fileName = $"VRM10_Debug_Log_{System.DateTime.Now:yyyyMMdd_HHmmss}.txt";
            string path = EditorUtility.SaveFilePanel("导出日志", "", fileName, "txt");
            
            if (!string.IsNullOrEmpty(path))
            {
                var sb = new StringBuilder();
                sb.AppendLine("VRM10 服装系统调试日志");
                sb.AppendLine($"导出时间: {System.DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                sb.AppendLine($"总日志数: {capturedLogs.Count}");
                sb.AppendLine($"关键日志数: {filteredLogs.Count}");
                sb.AppendLine(new string('=', 50));
                sb.AppendLine();
                
                foreach (string log in filteredLogs)
                {
                    sb.AppendLine(log);
                }
                
                File.WriteAllText(path, sb.ToString());
                EditorUtility.DisplayDialog("成功", $"日志已导出到: {path}", "确定");
                
                // 自动打开文件
                System.Diagnostics.Process.Start(path);
            }
        }
        
        private void PerformQuickAnalysis()
        {
            int errorCount = 0;
            int warningCount = 0;
            int successCount = 0;
            int boneCreationCount = 0;
            int bindingAttempts = 0;
            
            foreach (string log in filteredLogs)
            {
                if (log.Contains("ERROR") || log.Contains("失败"))
                    errorCount++;
                else if (log.Contains("WARNING") || log.Contains("警告"))
                    warningCount++;
                else if (log.Contains("成功") || log.Contains("完成"))
                    successCount++;
                
                if (log.Contains("创建骨骼") || log.Contains("创建VRoid"))
                    boneCreationCount++;
                
                if (log.Contains("开始绑定") || log.Contains("骨骼绑定"))
                    bindingAttempts++;
            }
            
            EditorGUILayout.LabelField($"错误: {errorCount} | 警告: {warningCount} | 成功: {successCount}");
            EditorGUILayout.LabelField($"骨骼创建: {boneCreationCount} | 绑定尝试: {bindingAttempts}");
            
            // 关键问题检测
            bool hasDelayedBinding = filteredLogs.Exists(log => log.Contains("延迟绑定"));
            bool hasDataDriven = filteredLogs.Exists(log => log.Contains("数据驱动"));
            bool hasBoneCreation = boneCreationCount > 0;
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("关键特征检测:");
            EditorGUILayout.LabelField($"延迟绑定: {(hasDelayedBinding ? "✅" : "❌")}");
            EditorGUILayout.LabelField($"数据驱动: {(hasDataDriven ? "✅" : "❌")}");
            EditorGUILayout.LabelField($"骨骼创建: {(hasBoneCreation ? "✅" : "❌")}");
        }
        
        /// <summary>
        /// 获取简化的日志摘要
        /// </summary>
        public string GetLogSummary()
        {
            var sb = new StringBuilder();
            sb.AppendLine("=== VRM10 调试日志摘要 ===");
            
            // 只包含最关键的信息
            foreach (string log in filteredLogs)
            {
                // 过滤掉过于详细的信息，只保留关键步骤
                if (log.Contains("开始") || log.Contains("完成") || log.Contains("成功") || 
                    log.Contains("失败") || log.Contains("ERROR") || log.Contains("创建") ||
                    log.Contains("===") || log.Contains("绑定结果"))
                {
                    sb.AppendLine(log);
                }
            }
            
            return sb.ToString();
        }
    }
}
