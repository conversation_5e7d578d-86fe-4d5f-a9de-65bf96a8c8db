PortInputView {
    width: 232px;
    height: 22px;
    padding-top: 1px;
    flex-direction: row;
    justify-content: flex-end;
}

PortInputView > #container {
    background-color: rgba(63, 63, 63, 0.8);
    flex-direction: row;
    align-items: center;
    padding-left: 8px;
    margin-right: 12px;
    border-left-width: 1px;
    border-top-width: 1px;
    border-right-width: 1px;
    border-bottom-width: 1px;
    border-color: rgba(25, 25, 25, 0.8);
    border-radius: 2px;
}

PortInputView > #container > #disabledOverlay {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-color: rgba(32, 32, 32, 0.0);
}

PortInputView.disabled > #container > #disabledOverlay {
    background-color: rgba(32, 32, 32, 0.75);
}

PortInputView > #container > #slot {
    width: 8px;
    height: 8px;
    background-color: #2B2B2B;
    border-color: #232323;
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-left-width: 1px;
    border-right-width: 1px;
    border-radius: 4px;
    margin-left: 6px;
    margin-right: 6px;
    align-items: center;
    justify-content: center;
}

PortInputView > #edge {
    position: absolute;
    right: 0px;
    top: 10.5px;
    height: 2px;
    width: 20px;
    background-color: #ff0000;
}

PortInputView > #container > #slot > #dot {
    width: 4px;
    height: 4px;
    background-color: #ff0000;
    border-radius: 4px;
}

PortInputView.typeMatrix4 > #container > #slot > #dot,
PortInputView.typeMatrix3 > #container > #slot > #dot,
PortInputView.typeMatrix2 > #container > #slot > #dot {
    background-color: #8FC1DF;
}

PortInputView.typeMatrix4,
PortInputView.typeMatrix3,
PortInputView.typeMatrix2 {
    --edge-color: #8FC1DF;
}

PortInputView.typeTexture2D > #container > #slot > #dot,
PortInputView.typeTexture2DArray > #container > #slot > #dot,
PortInputView.typeTexture3D > #container > #slot > #dot,
PortInputView.typeCubemap > #container > #slot > #dot {
    background-color: #FF8B8B;
}

PortInputView.typeTexture2D,
PortInputView.typeTexture2DArray,
PortInputView.typeTexture3D,
PortInputView.typeCubemap {
    --edge-color: #FF8B8B;
}

PortInputView.typeVector4 > #container > #slot > #dot {
    background-color: #FBCBF4;
}

PortInputView.typeVector4 {
    --edge-color: #FBCBF4;
}

PortInputView.typeVector3 > #container > #slot > #dot {
    background-color: #F6FF9A;
}

PortInputView.typeVector3 {
    --edge-color: #F6FF9A;
}

PortInputView.typeVector2 > #container > #slot > #dot {
    background-color: #9AEF92;
}

PortInputView.typeVector2 {
    --edge-color: #9AEF92;
}

PortInputView.typeVector1 > #container > #slot > #dot {
    background-color: #84E4E7;
}

PortInputView.typeVector1 {
    --edge-color: #84E4E7;
}

PortInputView.typeBoolean > #container > #slot > #dot {
    background-color: #9481E6;
}

PortInputView.typeBoolean {
    --edge-color: #9481E6;
}
