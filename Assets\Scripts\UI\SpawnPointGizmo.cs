using UnityEngine;

namespace VRoidFaceCustomization
{
    /// <summary>
    /// 生成点可视化组件
    /// 在Scene视图中显示生成点的Gizmo标记
    /// </summary>
    public class SpawnPointGizmo : MonoBehaviour
    {
        [Header("Gizmo设置")]
        [SerializeField] private Color gizmoColor = Color.green;
        [SerializeField] private float sphereRadius = 0.5f;
        [SerializeField] private float arrowHeight = 2f;
        [SerializeField] private bool showArrow = true;
        [SerializeField] private bool showSphere = true;
        
        /// <summary>
        /// 在Scene视图中绘制Gizmo
        /// </summary>
        private void OnDrawGizmos()
        {
            Gizmos.color = gizmoColor;
            
            if (showSphere)
            {
                Gizmos.DrawWireSphere(transform.position, sphereRadius);
            }
            
            if (showArrow)
            {
                // 绘制向上的箭头
                Vector3 arrowBase = transform.position;
                Vector3 arrowTop = arrowBase + Vector3.up * arrowHeight;
                
                Gizmos.DrawLine(arrowBase, arrowTop);
                
                // 绘制箭头头部
                Gizmos.DrawLine(arrowTop, arrowTop + Vector3.left * 0.2f + Vector3.down * 0.2f);
                Gizmos.DrawLine(arrowTop, arrowTop + Vector3.right * 0.2f + Vector3.down * 0.2f);
                Gizmos.DrawLine(arrowTop, arrowTop + Vector3.forward * 0.2f + Vector3.down * 0.2f);
                Gizmos.DrawLine(arrowTop, arrowTop + Vector3.back * 0.2f + Vector3.down * 0.2f);
            }
        }
        
        /// <summary>
        /// 当对象被选中时绘制的Gizmo
        /// </summary>
        private void OnDrawGizmosSelected()
        {
            Gizmos.color = Color.yellow;
            
            if (showSphere)
            {
                Gizmos.DrawWireSphere(transform.position, sphereRadius + 0.1f);
            }
            
            // 绘制坐标轴
            Gizmos.color = Color.red;
            Gizmos.DrawLine(transform.position, transform.position + Vector3.right * 1f);
            
            Gizmos.color = Color.green;
            Gizmos.DrawLine(transform.position, transform.position + Vector3.up * 1f);
            
            Gizmos.color = Color.blue;
            Gizmos.DrawLine(transform.position, transform.position + Vector3.forward * 1f);
        }
        
        /// <summary>
        /// 设置Gizmo颜色
        /// </summary>
        public void SetGizmoColor(Color color)
        {
            gizmoColor = color;
        }
        
        /// <summary>
        /// 设置球体半径
        /// </summary>
        public void SetSphereRadius(float radius)
        {
            sphereRadius = radius;
        }
        
        /// <summary>
        /// 设置箭头高度
        /// </summary>
        public void SetArrowHeight(float height)
        {
            arrowHeight = height;
        }
    }
}
