using UnityEngine;
using UnityEditor;
using VRoidFaceCustomization;
using System.Collections.Generic;
using System.Text;

namespace VRoidFaceCustomization.Editor
{
    /// <summary>
    /// VRM10骨骼坐标分析器
    /// 专门分析骨骼坐标系和绑定姿势问题
    /// </summary>
    public class VRM10BoneCoordinateAnalyzer : EditorWindow
    {
        private VRM10ClothBinder clothBinder;
        private GameObject clothPrefab;
        private VRM10ClothBoneData boneData;
        private Vector2 scrollPosition;
        
        // 分析结果
        private List<BoneCoordinateInfo> coordinateAnalysis = new List<BoneCoordinateInfo>();
        private bool hasAnalyzed = false;
        
        [System.Serializable]
        public class BoneCoordinateInfo
        {
            public string boneName;
            public bool existsInTarget;
            public Vector3 savedLocalPos;
            public Vector3 savedWorldPos;
            public Vector3 targetLocalPos;
            public Vector3 targetWorldPos;
            public Vector3 positionDelta;
            public float distanceError;
            public bool hasCoordinateIssue;
        }
        
        [MenuItem("Tools/VRM 1.0/Bone Coordinate Analyzer")]
        public static void ShowWindow()
        {
            GetWindow<VRM10BoneCoordinateAnalyzer>("骨骼坐标分析器");
        }
        
        private void OnGUI()
        {
            EditorGUILayout.LabelField("VRM10 骨骼坐标分析器", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("分析骨骼坐标系和绑定姿势问题", MessageType.Info);
            EditorGUILayout.Space();
            
            // 输入设置
            clothBinder = (VRM10ClothBinder)EditorGUILayout.ObjectField(
                "服装绑定器", clothBinder, typeof(VRM10ClothBinder), true);
            
            clothPrefab = (GameObject)EditorGUILayout.ObjectField(
                "服装Prefab", clothPrefab, typeof(GameObject), false);
            
            if (clothPrefab != null)
            {
                boneData = clothPrefab.GetComponent<VRM10ClothBoneData>();
                if (boneData != null)
                {
                    EditorGUILayout.LabelField($"骨骼数据: ✅ ({boneData.GetBoneInfos().Count} 个骨骼)");
                }
                else
                {
                    EditorGUILayout.LabelField("骨骼数据: ❌");
                }
            }
            
            EditorGUILayout.Space();
            
            // 分析按钮
            EditorGUI.BeginDisabledGroup(clothBinder == null || boneData == null);
            
            if (GUILayout.Button("分析骨骼坐标", GUILayout.Height(30)))
            {
                AnalyzeBoneCoordinates();
            }
            
            if (GUILayout.Button("生成修正建议", GUILayout.Height(30)))
            {
                GenerateFixSuggestions();
            }
            
            EditorGUI.EndDisabledGroup();
            
            if (GUILayout.Button("导出分析报告"))
            {
                ExportAnalysisReport();
            }

            if (GUILayout.Button("重置分析结果"))
            {
                ResetAnalysis();
            }
            
            EditorGUILayout.Space();
            
            // 显示分析结果
            if (hasAnalyzed && coordinateAnalysis.Count > 0)
            {
                DrawAnalysisResults();
            }
        }
        
        private void AnalyzeBoneCoordinates()
        {
            coordinateAnalysis.Clear();
            
            Debug.Log("=== 开始骨骼坐标分析 ===");
            
            var boneInfos = boneData.GetBoneInfos();
            var targetBones = clothBinder.transform.GetComponentsInChildren<Transform>();
            var targetBoneMap = new Dictionary<string, Transform>();
            
            foreach (var bone in targetBones)
            {
                if (!targetBoneMap.ContainsKey(bone.name))
                {
                    targetBoneMap[bone.name] = bone;
                }
            }
            
            foreach (var boneInfo in boneInfos)
            {
                var analysis = new BoneCoordinateInfo
                {
                    boneName = boneInfo.boneName,
                    savedLocalPos = boneInfo.localPosition,
                    savedWorldPos = boneInfo.worldPosition,
                    existsInTarget = targetBoneMap.ContainsKey(boneInfo.boneName)
                };
                
                if (analysis.existsInTarget)
                {
                    var targetBone = targetBoneMap[boneInfo.boneName];
                    analysis.targetLocalPos = targetBone.localPosition;
                    analysis.targetWorldPos = targetBone.position;
                    
                    // 计算坐标差异
                    analysis.positionDelta = analysis.savedWorldPos - analysis.targetWorldPos;
                    analysis.distanceError = analysis.positionDelta.magnitude;
                    
                    // 判断是否有坐标问题（距离差异大于0.1单位）
                    analysis.hasCoordinateIssue = analysis.distanceError > 0.1f;
                }
                else
                {
                    // VRoid专用骨骼，需要检查父骨骼坐标
                    analysis.hasCoordinateIssue = true; // 默认认为需要检查
                }
                
                coordinateAnalysis.Add(analysis);
            }
            
            hasAnalyzed = true;
            
            // 统计结果
            int coordinateIssues = coordinateAnalysis.FindAll(a => a.hasCoordinateIssue).Count;
            int missingBones = coordinateAnalysis.FindAll(a => !a.existsInTarget).Count;
            
            Debug.Log($"分析完成: {coordinateIssues} 个坐标问题, {missingBones} 个缺失骨骼");
        }
        
        private void DrawAnalysisResults()
        {
            EditorGUILayout.LabelField("坐标分析结果", EditorStyles.boldLabel);
            
            // 统计信息
            int issueCount = coordinateAnalysis.FindAll(a => a.hasCoordinateIssue).Count;
            int missingCount = coordinateAnalysis.FindAll(a => !a.existsInTarget).Count;
            
            EditorGUILayout.LabelField($"总骨骼: {coordinateAnalysis.Count}");
            EditorGUILayout.LabelField($"坐标问题: {issueCount}");
            EditorGUILayout.LabelField($"缺失骨骼: {missingCount}");
            
            EditorGUILayout.Space();
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(300));
            
            foreach (var analysis in coordinateAnalysis)
            {
                EditorGUILayout.BeginVertical("box");
                
                // 骨骼名称和状态
                EditorGUILayout.BeginHorizontal();
                
                string statusIcon = analysis.existsInTarget ? 
                    (analysis.hasCoordinateIssue ? "⚠️" : "✅") : "❌";
                
                EditorGUILayout.LabelField(statusIcon, GUILayout.Width(30));
                EditorGUILayout.LabelField(analysis.boneName, EditorStyles.boldLabel);
                
                EditorGUILayout.EndHorizontal();
                
                if (analysis.existsInTarget)
                {
                    // 坐标信息
                    EditorGUILayout.LabelField($"保存的世界坐标: {analysis.savedWorldPos}");
                    EditorGUILayout.LabelField($"目标世界坐标: {analysis.targetWorldPos}");
                    EditorGUILayout.LabelField($"坐标差异: {analysis.positionDelta}");
                    EditorGUILayout.LabelField($"距离误差: {analysis.distanceError:F3}");
                    
                    if (analysis.hasCoordinateIssue)
                    {
                        EditorGUILayout.HelpBox($"坐标差异过大: {analysis.distanceError:F3} 单位", MessageType.Warning);
                    }
                }
                else
                {
                    EditorGUILayout.LabelField("状态: 缺失骨骼（需要动态创建）");
                    EditorGUILayout.LabelField($"保存的局部坐标: {analysis.savedLocalPos}");
                }
                
                EditorGUILayout.EndVertical();
                EditorGUILayout.Space();
            }
            
            EditorGUILayout.EndScrollView();
        }
        
        private void GenerateFixSuggestions()
        {
            if (!hasAnalyzed)
            {
                EditorUtility.DisplayDialog("提示", "请先进行坐标分析", "确定");
                return;
            }
            
            var sb = new StringBuilder();
            sb.AppendLine("=== 骨骼坐标修正建议 ===");
            sb.AppendLine();
            
            // 分析坐标问题
            var coordinateIssues = coordinateAnalysis.FindAll(a => a.hasCoordinateIssue && a.existsInTarget);
            if (coordinateIssues.Count > 0)
            {
                sb.AppendLine("🔧 坐标差异问题:");
                foreach (var issue in coordinateIssues)
                {
                    sb.AppendLine($"  - {issue.boneName}: 误差 {issue.distanceError:F3} 单位");
                }
                sb.AppendLine();
                sb.AppendLine("建议解决方案:");
                sb.AppendLine("1. 重新提取骨骼数据时使用相同的角色模型");
                sb.AppendLine("2. 在骨骼创建时进行坐标系转换");
                sb.AppendLine("3. 使用相对坐标而非绝对坐标");
                sb.AppendLine();
            }
            
            // 分析缺失骨骼
            var missingBones = coordinateAnalysis.FindAll(a => !a.existsInTarget);
            if (missingBones.Count > 0)
            {
                sb.AppendLine("🔧 缺失骨骼问题:");
                foreach (var missing in missingBones)
                {
                    sb.AppendLine($"  - {missing.boneName}");
                }
                sb.AppendLine();
                sb.AppendLine("建议解决方案:");
                sb.AppendLine("1. 确保动态骨骼创建逻辑正确");
                sb.AppendLine("2. 检查父骨骼关系是否正确");
                sb.AppendLine("3. 验证骨骼命名规范");
                sb.AppendLine();
            }
            
            // 绑定姿势建议
            sb.AppendLine("🔧 绑定姿势建议:");
            sb.AppendLine("1. 不要直接使用保存的绑定姿势");
            sb.AppendLine("2. 在目标角色上重新计算绑定姿势");
            sb.AppendLine("3. 确保绑定姿势与当前骨骼状态匹配");
            
            Debug.Log(sb.ToString());
            
            EditorUtility.DisplayDialog("修正建议", 
                "修正建议已输出到控制台，请查看详细信息。\n\n主要问题可能是坐标系不匹配，建议重新计算绑定姿势。", "确定");
        }
        
        private void ExportAnalysisReport()
        {
            if (!hasAnalyzed)
            {
                EditorUtility.DisplayDialog("提示", "请先进行坐标分析", "确定");
                return;
            }
            
            string fileName = $"BoneCoordinate_Analysis_{System.DateTime.Now:yyyyMMdd_HHmmss}.txt";
            string path = EditorUtility.SaveFilePanel("导出分析报告", "", fileName, "txt");
            
            if (!string.IsNullOrEmpty(path))
            {
                var sb = new StringBuilder();
                sb.AppendLine("VRM10 骨骼坐标分析报告");
                sb.AppendLine($"分析时间: {System.DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                sb.AppendLine($"服装: {clothPrefab.name}");
                sb.AppendLine($"角色: {clothBinder.name}");
                sb.AppendLine(new string('=', 50));
                sb.AppendLine();
                
                foreach (var analysis in coordinateAnalysis)
                {
                    sb.AppendLine($"骨骼: {analysis.boneName}");
                    sb.AppendLine($"存在于目标: {analysis.existsInTarget}");
                    
                    if (analysis.existsInTarget)
                    {
                        sb.AppendLine($"保存坐标: {analysis.savedWorldPos}");
                        sb.AppendLine($"目标坐标: {analysis.targetWorldPos}");
                        sb.AppendLine($"坐标差异: {analysis.positionDelta}");
                        sb.AppendLine($"距离误差: {analysis.distanceError:F3}");
                        sb.AppendLine($"有坐标问题: {analysis.hasCoordinateIssue}");
                    }
                    else
                    {
                        sb.AppendLine($"保存局部坐标: {analysis.savedLocalPos}");
                        sb.AppendLine("状态: 需要动态创建");
                    }
                    
                    sb.AppendLine();
                }
                
                System.IO.File.WriteAllText(path, sb.ToString());
                EditorUtility.DisplayDialog("成功", $"分析报告已导出到: {path}", "确定");
            }
        }

        private void ResetAnalysis()
        {
            coordinateAnalysis.Clear();
            hasAnalyzed = false;
            Debug.Log("[VRM10BoneCoordinateAnalyzer] 分析结果已重置");
        }
    }
}
