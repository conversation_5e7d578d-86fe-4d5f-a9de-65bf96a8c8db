{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "eef0ccbc354843f6a995b37ff8927bdb",
    "m_Properties": [],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "eafb4924590146afa1d825d23af6c437"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "0738e5de97e3460f9ae266c569b85a20"
        },
        {
            "m_Id": "d63004029c8147c394b53f74a317db8c"
        },
        {
            "m_Id": "5b0636399eeb4acab7055a5ca0f7c4e6"
        },
        {
            "m_Id": "7bbbfd92b26745a583e18bb6b681997d"
        },
        {
            "m_Id": "fecdd64c9d754218bcbe31ab1f75cbe5"
        },
        {
            "m_Id": "a83502ac232c46bfa577cd6a2c543f3c"
        },
        {
            "m_Id": "878a65badeb74f5ab392213aee161c7c"
        },
        {
            "m_Id": "ea900ff1de5e453f8ee5cdbd14c77f60"
        },
        {
            "m_Id": "abfa026550f34723adf298702dc02157"
        },
        {
            "m_Id": "a58cecd5e37041739d1cfbcdc6fdca12"
        },
        {
            "m_Id": "02fd5c70df2e4d21844f6388f6267011"
        },
        {
            "m_Id": "77115f0c3e3049a3bd05f1c95df77605"
        },
        {
            "m_Id": "4e82c92c690244f185ece3b56968b7f1"
        },
        {
            "m_Id": "c2aa8908a856494f9e7b800a3a762736"
        },
        {
            "m_Id": "87fc2a449d5346c48f2ca5be38d54b42"
        },
        {
            "m_Id": "436c35b379304ac28a37d8e4b01d203e"
        },
        {
            "m_Id": "3290fa1dccc34e8f91c6b8217d654579"
        },
        {
            "m_Id": "e02a8abcf5de4b1897732e02a22b4e6a"
        },
        {
            "m_Id": "1af22b9a365642d3b70106676ba7955d"
        }
    ],
    "m_GroupDatas": [
        {
            "m_Id": "05704b9df9cc4e8abb123439c5a7a324"
        },
        {
            "m_Id": "c2c1492a556d4698a8228bda3fef5735"
        }
    ],
    "m_StickyNoteDatas": [
        {
            "m_Id": "600ad88a21c842aeb65d30833a3c1d86"
        },
        {
            "m_Id": "85a7362b1b6c4ce2959d8d09c19e17a5"
        },
        {
            "m_Id": "3ba6b92355f642048fe1e1008c494135"
        },
        {
            "m_Id": "d36eb0fc1ccb49eeacc68c39c194762d"
        },
        {
            "m_Id": "0a5fc1490fe6465eae911e937ef3e101"
        },
        {
            "m_Id": "d57dea5ba42b429f864d8029ddd69c5a"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "02fd5c70df2e4d21844f6388f6267011"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "77115f0c3e3049a3bd05f1c95df77605"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "1af22b9a365642d3b70106676ba7955d"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "87fc2a449d5346c48f2ca5be38d54b42"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "3290fa1dccc34e8f91c6b8217d654579"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "e02a8abcf5de4b1897732e02a22b4e6a"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "436c35b379304ac28a37d8e4b01d203e"
                },
                "m_SlotId": 3
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "3290fa1dccc34e8f91c6b8217d654579"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "4e82c92c690244f185ece3b56968b7f1"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1af22b9a365642d3b70106676ba7955d"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "4e82c92c690244f185ece3b56968b7f1"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "c2aa8908a856494f9e7b800a3a762736"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "77115f0c3e3049a3bd05f1c95df77605"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "abfa026550f34723adf298702dc02157"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "878a65badeb74f5ab392213aee161c7c"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "ea900ff1de5e453f8ee5cdbd14c77f60"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "a58cecd5e37041739d1cfbcdc6fdca12"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "77115f0c3e3049a3bd05f1c95df77605"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "a83502ac232c46bfa577cd6a2c543f3c"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "878a65badeb74f5ab392213aee161c7c"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "abfa026550f34723adf298702dc02157"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "4e82c92c690244f185ece3b56968b7f1"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "c2aa8908a856494f9e7b800a3a762736"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "87fc2a449d5346c48f2ca5be38d54b42"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "ea900ff1de5e453f8ee5cdbd14c77f60"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "abfa026550f34723adf298702dc02157"
                },
                "m_SlotId": 0
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": [
            {
                "m_Id": "0738e5de97e3460f9ae266c569b85a20"
            },
            {
                "m_Id": "d63004029c8147c394b53f74a317db8c"
            },
            {
                "m_Id": "5b0636399eeb4acab7055a5ca0f7c4e6"
            }
        ]
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 200.0
        },
        "m_Blocks": [
            {
                "m_Id": "7bbbfd92b26745a583e18bb6b681997d"
            }
        ]
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Shader Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": ""
    },
    "m_ActiveTargets": [
        {
            "m_Id": "c11a8c79aaed4dc1bbbddd571d26856e"
        },
        {
            "m_Id": "0a8ed575e4274499826c696c2bd28841"
        },
        {
            "m_Id": "86abab6870974d7499e4acf3d11ee645"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1Node",
    "m_ObjectId": "02fd5c70df2e4d21844f6388f6267011",
    "m_Group": {
        "m_Id": "05704b9df9cc4e8abb123439c5a7a324"
    },
    "m_Name": "Float",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1781.4998779296875,
            "y": 545.5,
            "width": 125.5,
            "height": 77.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "1cd96fd8da00451c90c889f67e85db58"
        },
        {
            "m_Id": "3edfd7db886a49139e2a8b667b8db801"
        }
    ],
    "synonyms": [
        "Vector 1",
        "1",
        "v1",
        "vec1",
        "scalar"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": 0.0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0318c0af67b8419ea07f3f0a0dbf27b7",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "0455b19765b543fa8681e7f03242eb8b",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "05704b9df9cc4e8abb123439c5a7a324",
    "m_Title": "Under The Hood",
    "m_Position": {
        "x": -1934.0001220703125,
        "y": 140.00003051757813
    }
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalUnlitSubTarget",
    "m_ObjectId": "069071006ad44bb48b4150d1cb137994"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "0738e5de97e3460f9ae266c569b85a20",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "c45960b230194e06b7af683314cc1486"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Position"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0801d64a621243da9a326e5b93d531be",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "0a5fc1490fe6465eae911e937ef3e101",
    "m_Title": "",
    "m_Content": "This is the math that the Ellipse node performs to generate a shape.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -919.0000610351563,
        "y": 217.50003051757813,
        "width": 102.5,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "05704b9df9cc4e8abb123439c5a7a324"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDTarget",
    "m_ObjectId": "0a8ed575e4274499826c696c2bd28841",
    "m_ActiveSubTarget": {
        "m_Id": "6267921e85664a029d15f0bad93549b2"
    },
    "m_Datas": [
        {
            "m_Id": "7e907c9ec23e4e22ada9172fc0d3f1e4"
        },
        {
            "m_Id": "ce6065fcdcc945acb683932dcdcf4cf3"
        },
        {
            "m_Id": "6ebb6f1e17064ec98fb1eef1a591bb6e"
        }
    ],
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false,
    "m_SupportLineRendering": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "10c809cc76d0439bb1b5090223bf4e65",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "1397e435215c43cc96544c923e8d6c81",
    "m_Id": 4,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DDXYNode",
    "m_ObjectId": "1af22b9a365642d3b70106676ba7955d",
    "m_Group": {
        "m_Id": "05704b9df9cc4e8abb123439c5a7a324"
    },
    "m_Name": "DDXY",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1152.0001220703125,
            "y": 451.50006103515627,
            "width": 127.5001220703125,
            "height": 94.00006103515625
        }
    },
    "m_Slots": [
        {
            "m_Id": "4f50a41c0f774a80ac2828ba115d44aa"
        },
        {
            "m_Id": "51c296a75a1146119f8e8fcac14e65ed"
        }
    ],
    "synonyms": [
        "derivative"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "1cd96fd8da00451c90c889f67e85db58",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 0.5,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "225fa9fada5e4c259c48cb9e0b6d8c4e",
    "m_Id": 3,
    "m_DisplayName": "Height",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Height",
    "m_StageCapability": 3,
    "m_Value": 0.5,
    "m_DefaultValue": 0.5,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "24e60e0de3ed406dac0ef268a90974eb",
    "m_Id": 3,
    "m_DisplayName": "Height",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Height",
    "m_StageCapability": 3,
    "m_Value": 0.5,
    "m_DefaultValue": 0.5,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.FractionNode",
    "m_ObjectId": "3290fa1dccc34e8f91c6b8217d654579",
    "m_Group": {
        "m_Id": "c2c1492a556d4698a8228bda3fef5735"
    },
    "m_Name": "Fraction",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -530.9999389648438,
            "y": 204.50001525878907,
            "width": 130.5,
            "height": 94.00001525878906
        }
    },
    "m_Slots": [
        {
            "m_Id": "efd1bedf19a347939787b95219d7163d"
        },
        {
            "m_Id": "a1db1b6805cc44beb921a8b8b54bff13"
        }
    ],
    "synonyms": [
        "remainder"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "386c3755430240d48dbb4ca05262df63",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "3ba6b92355f642048fe1e1008c494135",
    "m_Title": "",
    "m_Content": "Height",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1757.0001220703125,
        "y": 509.00006103515627,
        "width": 80.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "05704b9df9cc4e8abb123439c5a7a324"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "3e7b1bf2497c4c70a3a3fe35eb8d8b6c",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "3edfd7db886a49139e2a8b667b8db801",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "3f027084fe4a47d598199af88c7b6bac",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 2.0,
        "e01": 2.0,
        "e02": 2.0,
        "e03": 2.0,
        "e10": 2.0,
        "e11": 2.0,
        "e12": 2.0,
        "e13": 2.0,
        "e20": 2.0,
        "e21": 2.0,
        "e22": 2.0,
        "e23": 2.0,
        "e30": 2.0,
        "e31": 2.0,
        "e32": 2.0,
        "e33": 2.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "4038e614e9d84eca93104d26bd66778b",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TilingAndOffsetNode",
    "m_ObjectId": "436c35b379304ac28a37d8e4b01d203e",
    "m_Group": {
        "m_Id": "c2c1492a556d4698a8228bda3fef5735"
    },
    "m_Name": "Tiling And Offset",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -687.4999389648438,
            "y": 204.50001525878907,
            "width": 141.50006103515626,
            "height": 94.00001525878906
        }
    },
    "m_Slots": [
        {
            "m_Id": "10c809cc76d0439bb1b5090223bf4e65"
        },
        {
            "m_Id": "8155e703fc464463966cdda24dc036f7"
        },
        {
            "m_Id": "5a2e0d19cb0d44059078f061265dcee2"
        },
        {
            "m_Id": "8b787864afe3455c881de43e9ae2e13c"
        }
    ],
    "synonyms": [
        "pan",
        "scale"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "43bd89b7769a4d348f03fbcac9fbcc0c",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "4e46a42b99b84f4b9ff0cb42e8ed9b78",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.LengthNode",
    "m_ObjectId": "4e82c92c690244f185ece3b56968b7f1",
    "m_Group": {
        "m_Id": "05704b9df9cc4e8abb123439c5a7a324"
    },
    "m_Name": "Length",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1339.0,
            "y": 347.49993896484377,
            "width": 129.0001220703125,
            "height": 94.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "7b84695673394fc38f47dde664dc154e"
        },
        {
            "m_Id": "0318c0af67b8419ea07f3f0a0dbf27b7"
        }
    ],
    "synonyms": [
        "measure"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "4f50a41c0f774a80ac2828ba115d44aa",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "51c296a75a1146119f8e8fcac14e65ed",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInUnlitSubTarget",
    "m_ObjectId": "543f9367105347e5bd3c0fd725e9ea6c"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "566b9fac2a7a4f3ea672d0ebab885c37",
    "m_Id": 4,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "5a2e0d19cb0d44059078f061265dcee2",
    "m_Id": 2,
    "m_DisplayName": "Offset",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Offset",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "5a723d9dfac445a9b2abe76283dd39cb",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 2.0,
        "y": 2.0,
        "z": 2.0,
        "w": 2.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "5b0636399eeb4acab7055a5ca0f7c4e6",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Tangent",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "9a264677320746a9848ce865937cb94f"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Tangent"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "5e344584c6a941218847ae3cc06c01e8",
    "m_Id": 0,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "600ad88a21c842aeb65d30833a3c1d86",
    "m_Title": "Ellipse Node",
    "m_Content": "The Ellipse Node generates a 2d circle or ellipse shape using the given UV coordinates and Width and Height parameters.\n\nYou can create a circle if Width and Height use the same value.  If the values are different, the result is an ellipse.\n\nThe generated shape can be offset or tiled by connecting a Tiling And Offset Node. Note that in order to preserve the ability to offset the shape within the UV space the shape will not automatically repeat if tiled. To achieve a repeating polygon effect, first connect your input through a Fraction Node as seen below.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -906.0000610351563,
        "y": -215.00001525878907,
        "width": 185.0,
        "height": 317.5000305175781
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "6267921e85664a029d15f0bad93549b2"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "669c99b808c94d35be39f930bee7f655",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "6ebb6f1e17064ec98fb1eef1a591bb6e",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2Node",
    "m_ObjectId": "77115f0c3e3049a3bd05f1c95df77605",
    "m_Group": {
        "m_Id": "05704b9df9cc4e8abb123439c5a7a324"
    },
    "m_Name": "Vector 2",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1631.9998779296875,
            "y": 465.4999694824219,
            "width": 126.9998779296875,
            "height": 101.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "94bdbb19466d48bb8c8a71bdded4ce7e"
        },
        {
            "m_Id": "7e2b369c4c8d4b579d1f382b6a66ecb1"
        },
        {
            "m_Id": "8c761947d3b7468e9e567ecabad3f9e9"
        }
    ],
    "synonyms": [
        "2",
        "v2",
        "vec2",
        "float2"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "7b84695673394fc38f47dde664dc154e",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "7bbbfd92b26745a583e18bb6b681997d",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.BaseColor",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "d07318e31a2c4268834ec9c666739528"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.BaseColor"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "7e2b369c4c8d4b579d1f382b6a66ecb1",
    "m_Id": 2,
    "m_DisplayName": "Y",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Y",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "Y"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "7e907c9ec23e4e22ada9172fc0d3f1e4",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "7ec838f5477847fda3a0cbacb0d3ef75",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "7ed0035f286b4daaa8b00fea5788dc6b",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 0.5,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "8155e703fc464463966cdda24dc036f7",
    "m_Id": 1,
    "m_DisplayName": "Tiling",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tiling",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 6.0,
        "y": 6.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "85a7362b1b6c4ce2959d8d09c19e17a5",
    "m_Title": "",
    "m_Content": "Width",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1749.5001220703125,
        "y": 368.0000305175781,
        "width": 80.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "05704b9df9cc4e8abb123439c5a7a324"
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget",
    "m_ObjectId": "86abab6870974d7499e4acf3d11ee645",
    "m_Datas": [],
    "m_ActiveSubTarget": {
        "m_Id": "069071006ad44bb48b4150d1cb137994"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZTestMode": 4,
    "m_ZWriteControl": 0,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CastShadows": true,
    "m_ReceiveShadows": true,
    "m_SupportsLODCrossFade": false,
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MultiplyNode",
    "m_ObjectId": "878a65badeb74f5ab392213aee161c7c",
    "m_Group": {
        "m_Id": "05704b9df9cc4e8abb123439c5a7a324"
    },
    "m_Name": "Multiply",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1763.9998779296875,
            "y": 232.99998474121095,
            "width": 129.5,
            "height": 94.00001525878906
        }
    },
    "m_Slots": [
        {
            "m_Id": "0455b19765b543fa8681e7f03242eb8b"
        },
        {
            "m_Id": "3f027084fe4a47d598199af88c7b6bac"
        },
        {
            "m_Id": "8b21f4fc755b459fa070ae3de946a76d"
        }
    ],
    "synonyms": [
        "multiplication",
        "times",
        "x"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DivideNode",
    "m_ObjectId": "87fc2a449d5346c48f2ca5be38d54b42",
    "m_Group": {
        "m_Id": "05704b9df9cc4e8abb123439c5a7a324"
    },
    "m_Name": "Divide",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -968.4998779296875,
            "y": 347.49993896484377,
            "width": 208.00006103515626,
            "height": 302.00006103515627
        }
    },
    "m_Slots": [
        {
            "m_Id": "386c3755430240d48dbb4ca05262df63"
        },
        {
            "m_Id": "5a723d9dfac445a9b2abe76283dd39cb"
        },
        {
            "m_Id": "89a434c410fc4b439a16ba27691f51cf"
        }
    ],
    "synonyms": [
        "division",
        "divided by"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "89a434c410fc4b439a16ba27691f51cf",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "8b21f4fc755b459fa070ae3de946a76d",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "8b787864afe3455c881de43e9ae2e13c",
    "m_Id": 3,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "8c761947d3b7468e9e567ecabad3f9e9",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "8c8e2289a4c44deb8c1ec273f93137c7",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "924a2e743faa458bb5bd3c3908faaa3a",
    "m_Id": 2,
    "m_DisplayName": "Width",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Width",
    "m_StageCapability": 3,
    "m_Value": 0.5,
    "m_DefaultValue": 0.5,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "939bc0e656314eeabb223d41250c821f",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 2.0,
        "y": 2.0,
        "z": 2.0,
        "w": 2.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "94bdbb19466d48bb8c8a71bdded4ce7e",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TangentMaterialSlot",
    "m_ObjectId": "9a264677320746a9848ce865937cb94f",
    "m_Id": 0,
    "m_DisplayName": "Tangent",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tangent",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "a1db1b6805cc44beb921a8b8b54bff13",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1Node",
    "m_ObjectId": "a58cecd5e37041739d1cfbcdc6fdca12",
    "m_Group": {
        "m_Id": "05704b9df9cc4e8abb123439c5a7a324"
    },
    "m_Name": "Float",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1781.4998779296875,
            "y": 412.5000305175781,
            "width": 125.5,
            "height": 77.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "7ed0035f286b4daaa8b00fea5788dc6b"
        },
        {
            "m_Id": "0801d64a621243da9a326e5b93d531be"
        }
    ],
    "synonyms": [
        "Vector 1",
        "1",
        "v1",
        "vec1",
        "scalar"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": 0.0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "a79641e0c3624fa484fcaa3b1eab1cce",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVNode",
    "m_ObjectId": "a83502ac232c46bfa577cd6a2c543f3c",
    "m_Group": {
        "m_Id": "05704b9df9cc4e8abb123439c5a7a324"
    },
    "m_Name": "UV",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1908.9998779296875,
            "y": 232.99998474121095,
            "width": 145.0,
            "height": 128.49998474121095
        }
    },
    "m_Slots": [
        {
            "m_Id": "b379ee563b02438c9c7bcc63a623d50d"
        }
    ],
    "synonyms": [
        "texcoords",
        "coords",
        "coordinates"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_OutputChannel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DivideNode",
    "m_ObjectId": "abfa026550f34723adf298702dc02157",
    "m_Group": {
        "m_Id": "05704b9df9cc4e8abb123439c5a7a324"
    },
    "m_Name": "Divide",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1480.4998779296875,
            "y": 347.49993896484377,
            "width": 128.9998779296875,
            "height": 118.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "f85fbf5cdb404ce4934c1285e511e017"
        },
        {
            "m_Id": "939bc0e656314eeabb223d41250c821f"
        },
        {
            "m_Id": "4038e614e9d84eca93104d26bd66778b"
        }
    ],
    "synonyms": [
        "division",
        "divided by"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "b0e102249fb946bf958ff17b1e404b27",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "b1b9152ddaed4394912f9d32a4d19f14"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "b379ee563b02438c9c7bcc63a623d50d",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInTarget",
    "m_ObjectId": "c11a8c79aaed4dc1bbbddd571d26856e",
    "m_ActiveSubTarget": {
        "m_Id": "543f9367105347e5bd3c0fd725e9ea6c"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZWriteControl": 0,
    "m_ZTestMode": 4,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CustomEditorGUI": ""
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.OneMinusNode",
    "m_ObjectId": "c2aa8908a856494f9e7b800a3a762736",
    "m_Group": {
        "m_Id": "05704b9df9cc4e8abb123439c5a7a324"
    },
    "m_Name": "One Minus",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1122.4998779296875,
            "y": 347.49993896484377,
            "width": 127.5,
            "height": 94.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "4e46a42b99b84f4b9ff0cb42e8ed9b78"
        },
        {
            "m_Id": "b0e102249fb946bf958ff17b1e404b27"
        }
    ],
    "synonyms": [
        "complement",
        "invert",
        "opposite"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "c2c1492a556d4698a8228bda3fef5735",
    "m_Title": "Tiling",
    "m_Position": {
        "x": -712.4998168945313,
        "y": 146.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "c45960b230194e06b7af683314cc1486",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "c7eeb44871fd4349af7b0b69cc8333aa",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "ce6065fcdcc945acb683932dcdcf4cf3",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "d07318e31a2c4268834ec9c666739528",
    "m_Id": 0,
    "m_DisplayName": "Base Color",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BaseColor",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.5,
        "y": 0.5,
        "z": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.5,
        "g": 0.5,
        "b": 0.5,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "d36eb0fc1ccb49eeacc68c39c194762d",
    "m_Title": "",
    "m_Content": "UV",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1876.5001220703125,
        "y": 198.50001525878907,
        "width": 80.0,
        "height": 100.00001525878906
    },
    "m_Group": {
        "m_Id": "05704b9df9cc4e8abb123439c5a7a324"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "d57dea5ba42b429f864d8029ddd69c5a",
    "m_Title": "",
    "m_Content": "If you want to create a repeating pattern, be sure to use a Fraction node in between your UV coordinates and the Ellipse node.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -605.5000610351563,
        "y": 339.0000305175781,
        "width": 138.00003051757813,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "c2c1492a556d4698a8228bda3fef5735"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "d63004029c8147c394b53f74a317db8c",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Normal",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "5e344584c6a941218847ae3cc06c01e8"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Normal"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.EllipseNode",
    "m_ObjectId": "e02a8abcf5de4b1897732e02a22b4e6a",
    "m_Group": {
        "m_Id": "c2c1492a556d4698a8228bda3fef5735"
    },
    "m_Name": "Ellipse",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -387.9999084472656,
            "y": 204.50001525878907,
            "width": 208.00001525878907,
            "height": 278.00006103515627
        }
    },
    "m_Slots": [
        {
            "m_Id": "3e7b1bf2497c4c70a3a3fe35eb8d8b6c"
        },
        {
            "m_Id": "fc2470565b1e420e81cfa7ba60bc2131"
        },
        {
            "m_Id": "225fa9fada5e4c259c48cb9e0b6d8c4e"
        },
        {
            "m_Id": "1397e435215c43cc96544c923e8d6c81"
        }
    ],
    "synonyms": [
        "circle"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "e69cefeb260c459fb1128fc51ed5ee89",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubtractNode",
    "m_ObjectId": "ea900ff1de5e453f8ee5cdbd14c77f60",
    "m_Group": {
        "m_Id": "05704b9df9cc4e8abb123439c5a7a324"
    },
    "m_Name": "Subtract",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1634.4998779296875,
            "y": 232.99998474121095,
            "width": 129.4998779296875,
            "height": 94.00001525878906
        }
    },
    "m_Slots": [
        {
            "m_Id": "8c8e2289a4c44deb8c1ec273f93137c7"
        },
        {
            "m_Id": "7ec838f5477847fda3a0cbacb0d3ef75"
        },
        {
            "m_Id": "43bd89b7769a4d348f03fbcac9fbcc0c"
        }
    ],
    "synonyms": [
        "subtraction",
        "remove",
        "minus",
        "take away"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "eafb4924590146afa1d825d23af6c437",
    "m_Name": "",
    "m_ChildObjectList": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "efd1bedf19a347939787b95219d7163d",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "f85fbf5cdb404ce4934c1285e511e017",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "fc2470565b1e420e81cfa7ba60bc2131",
    "m_Id": 2,
    "m_DisplayName": "Width",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Width",
    "m_StageCapability": 3,
    "m_Value": 0.5,
    "m_DefaultValue": 0.5,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.EllipseNode",
    "m_ObjectId": "fecdd64c9d754218bcbe31ab1f75cbe5",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Ellipse",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1118.4998779296875,
            "y": -218.50003051757813,
            "width": 208.0,
            "height": 326.0000305175781
        }
    },
    "m_Slots": [
        {
            "m_Id": "c7eeb44871fd4349af7b0b69cc8333aa"
        },
        {
            "m_Id": "924a2e743faa458bb5bd3c3908faaa3a"
        },
        {
            "m_Id": "24e60e0de3ed406dac0ef268a90974eb"
        },
        {
            "m_Id": "566b9fac2a7a4f3ea672d0ebab885c37"
        }
    ],
    "synonyms": [
        "circle"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

