# SpeedTree Shaders

The Universal Render Pipeline uses the SpeedTree system for tree Shaders.  To read more about that, [read the SpeedTree documentation in the Unity main manual](https://docs.unity3d.com/Manual/SpeedTree.html).

When you use SpeedTree Shaders in URP, keep the following in mind:

* There is no Global Illumination on trees in URP.

* Trees cannot receive shadows in URP.
* In URP, you can configure whether lights should be per vertex of per pixel in the [URP Asset](universalrp-asset.md).
