fileFormatVersion: 2
guid: 13caa80bbe9b34403bda844d1694d394
ModelImporter:
  serializedVersion: 21
  fileIDToRecycleName:
    100000: //RootNode
    100002: Chest
    100004: Head
    100006: Hips
    100008: Jaw
    100010: <PERSON><PERSON><PERSON>D
    100012: LeftArm
    100014: <PERSON><PERSON><PERSON>k
    100016: <PERSON><PERSON>ye
    100018: LeftEyelidLower
    100020: LeftEyelidUpper
    100022: LeftFoot
    100024: LeftForeArm
    100026: LeftHand
    100028: LeftHandIndex1
    100030: LeftHandIndex2
    100032: LeftHandIndex3
    100034: LeftHandMiddle1
    100036: LeftHandMiddle2
    100038: LeftHandMiddle3
    100040: LeftHandPinky1
    100042: LeftHandPinky2
    100044: LeftHandPinky3
    100046: LeftHandRing1
    100048: LeftHandRing2
    100050: LeftHandRing3
    100052: LeftHandThumb1
    100054: LeftHandThumb2
    100056: LeftHandThumb3
    100058: LeftInnerBrow
    100060: LeftIOuterBrow
    100062: LeftLeg
    100064: <PERSON><PERSON>ip<PERSON>orner
    100066: LeftLipLower
    100068: <PERSON><PERSON><PERSON><PERSON><PERSON>
    100070: <PERSON>Nostril
    100072: LeftShoulder
    100074: LeftT<PERSON>
    100076: LeftUpLeg
    100078: Neck
    100080: Reference
    100082: RightArm
    100084: RightCheek
    100086: RightEye
    100088: RightEyelidLower
    100090: RightEyelidUpper
    100092: RightFoot
    100094: RightForeArm
    100096: RightHand
    100098: RightHandIndex1
    100100: RightHandIndex2
    100102: RightHandIndex3
    100104: RightHandMiddle1
    100106: RightHandMiddle2
    100108: RightHandMiddle3
    100110: RightHandPinky1
    100112: RightHandPinky2
    100114: RightHandPinky3
    100116: RightHandRing1
    100118: RightHandRing2
    100120: RightHandRing3
    100122: RightHandThumb1
    100124: RightHandThumb2
    100126: RightHandThumb3
    100128: RightInnerBrow
    100130: RightIOuterBrow
    100132: RightLeg
    100134: RightLipCorner
    100136: RightLipLower
    100138: RightLipUpper
    100140: RightNostril
    100142: RightShoulder
    100144: RightToes
    100146: RightUpLeg
    100148: Spine
    100150: TongueBack
    100152: TongueTip
    400000: //RootNode
    400002: Chest
    400004: Head
    400006: Hips
    400008: Jaw
    400010: JawEND
    400012: LeftArm
    400014: LeftCheek
    400016: LeftEye
    400018: LeftEyelidLower
    400020: LeftEyelidUpper
    400022: LeftFoot
    400024: LeftForeArm
    400026: LeftHand
    400028: LeftHandIndex1
    400030: LeftHandIndex2
    400032: LeftHandIndex3
    400034: LeftHandMiddle1
    400036: LeftHandMiddle2
    400038: LeftHandMiddle3
    400040: LeftHandPinky1
    400042: LeftHandPinky2
    400044: LeftHandPinky3
    400046: LeftHandRing1
    400048: LeftHandRing2
    400050: LeftHandRing3
    400052: LeftHandThumb1
    400054: LeftHandThumb2
    400056: LeftHandThumb3
    400058: LeftInnerBrow
    400060: LeftIOuterBrow
    400062: LeftLeg
    400064: LeftLipCorner
    400066: LeftLipLower
    400068: LeftLipUpper
    400070: LeftNostril
    400072: LeftShoulder
    400074: LeftToes
    400076: LeftUpLeg
    400078: Neck
    400080: Reference
    400082: RightArm
    400084: RightCheek
    400086: RightEye
    400088: RightEyelidLower
    400090: RightEyelidUpper
    400092: RightFoot
    400094: RightForeArm
    400096: RightHand
    400098: RightHandIndex1
    400100: RightHandIndex2
    400102: RightHandIndex3
    400104: RightHandMiddle1
    400106: RightHandMiddle2
    400108: RightHandMiddle3
    400110: RightHandPinky1
    400112: RightHandPinky2
    400114: RightHandPinky3
    400116: RightHandRing1
    400118: RightHandRing2
    400120: RightHandRing3
    400122: RightHandThumb1
    400124: RightHandThumb2
    400126: RightHandThumb3
    400128: RightInnerBrow
    400130: RightIOuterBrow
    400132: RightLeg
    400134: RightLipCorner
    400136: RightLipLower
    400138: RightLipUpper
    400140: RightNostril
    400142: RightShoulder
    400144: RightToes
    400146: RightUpLeg
    400148: Spine
    400150: TongueBack
    400152: TongueTip
    7400000: Walk
    9500000: //RootNode
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    animationCompression: 0
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: Walk
      takeName: _1_Edit1_WalkFWD
      firstFrame: 29.6
      lastFrame: 59.5
      wrapMode: 0
      orientationOffsetY: 1.46
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 0.01
    meshCompression: 0
    addColliders: 0
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 4
    normalCalculationMode: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftUpLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightUpLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Chest
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftForeArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightForeArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftToes
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightToes
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftEye
      humanName: LeftEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightEye
      humanName: RightEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Jaw
      humanName: Jaw
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb1
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb2
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb3
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex1
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex2
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex3
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle1
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle2
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle3
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing1
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing2
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing3
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky1
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky2
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky3
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb1
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb2
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb3
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex1
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex2
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex3
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle1
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle2
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle3
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing1
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing2
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing3
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky1
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky2
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky3
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: SmallStep(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Reference
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Hips
      parentName: 
      position: {x: 0.0005513057, y: 0.9604539, z: 0.14825001}
      rotation: {x: -0.025175245, y: -0.0012315813, z: -0.0060078264, w: 0.99966425}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftUpLeg
      parentName: 
      position: {x: -0.0754495, y: -0.04566402, z: -1.7763568e-17}
      rotation: {x: 0.05640076, y: 0.027300434, z: 0.004994643, w: 0.9980224}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftLeg
      parentName: 
      position: {x: -0.020550499, y: -0.40912998, z: -0.00071864796}
      rotation: {x: -0.017367318, y: -0.1786951, z: -0.019570697, w: 0.98355657}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftFoot
      parentName: 
      position: {x: -0.0051529994, y: -0.4231559, z: -0.027648851}
      rotation: {x: -0.0002880121, y: 0.1051386, z: 0.027920393, w: 0.9940655}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftToes
      parentName: 
      position: {x: -0.007487, y: -0.0731673, z: 0.14542712}
      rotation: {x: -0.015560327, y: 0.00043698392, z: -0.00075559097, w: 0.9998786}
      scale: {x: 1, y: 1, z: 1}
    - name: RightUpLeg
      parentName: 
      position: {x: 0.075449534, y: -0.04566399, z: 1.7763568e-17}
      rotation: {x: 0.030658921, y: 0.099532634, z: 0.015428916, w: 0.9944422}
      scale: {x: 1, y: 1, z: 1}
    - name: RightLeg
      parentName: 
      position: {x: 0.020550467, y: -0.40913, z: -0.00071864796}
      rotation: {x: 0.029253662, y: 0.13521753, z: 0.025294185, w: 0.990061}
      scale: {x: 1, y: 1, z: 1}
    - name: RightFoot
      parentName: 
      position: {x: 0.0051529994, y: -0.4231559, z: -0.027648851}
      rotation: {x: -0.017446658, y: -0.07470319, z: -0.032314803, w: 0.99652946}
      scale: {x: 1, y: 1, z: 1}
    - name: RightToes
      parentName: 
      position: {x: 0.007487, y: -0.0731673, z: 0.1454275}
      rotation: {x: 0.0011941958, y: -0.0056453915, z: 0.001852583, w: 0.99998164}
      scale: {x: 1, y: 1, z: 1}
    - name: Spine
      parentName: 
      position: {x: 1.7763568e-17, y: 0.092263184, z: 0.015771331}
      rotation: {x: 0.051771346, y: 0.0028054859, z: -0.011270309, w: 0.9985914}
      scale: {x: 1, y: 1, z: 1}
    - name: Chest
      parentName: 
      position: {x: 5.3290704e-17, y: 0.16254029, z: -0.0016560555}
      rotation: {x: 0.0065125604, y: 0.009296001, z: 0.034053225, w: 0.99935555}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftShoulder
      parentName: 
      position: {x: -0.038285997, y: 0.2216225, z: -0.017063085}
      rotation: {x: -0.021960836, y: -0.04180141, z: 0.13912761, w: 0.9891481}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftArm
      parentName: 
      position: {x: -0.10050205, y: 0.0000000011250826, z: -1.9039077e-10}
      rotation: {x: 0.091082454, y: 0.022056738, z: -0.1430188, w: 0.9852731}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftForeArm
      parentName: 
      position: {x: -0.2540493, y: -1.0556242e-10, z: 1.09213825e-10}
      rotation: {x: 0.12484056, y: 0.031214235, z: 0.002818492, w: 0.9916817}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHand
      parentName: 
      position: {x: -0.24638927, y: -1.1484463e-10, z: 1.3598456e-11}
      rotation: {x: -0.022898793, y: -0.0335501, z: -0.043298353, w: 0.9982361}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandIndex1
      parentName: 
      position: {x: -0.0751258, y: -0.0078414045, z: 0.032652643}
      rotation: {x: 0.0060999673, y: -0.016749125, z: 0.05687823, w: 0.99822205}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandIndex2
      parentName: 
      position: {x: -0.03979728, y: 0.000049808412, z: 0.0011857506}
      rotation: {x: -0.067607544, y: 0.015224932, z: 0.032717455, w: 0.99705917}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandIndex3
      parentName: 
      position: {x: -0.027968477, y: -0.0000000064163745, z: -0.00000005143485}
      rotation: {x: -0.06627773, y: -0.0075497297, z: 0.17120177, w: 0.9829752}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandMiddle1
      parentName: 
      position: {x: -0.076023825, y: -0.0018851344, z: 0.010141229}
      rotation: {x: -0.0037917863, y: 0.04483006, z: 0.08820312, w: 0.99508595}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandMiddle2
      parentName: 
      position: {x: -0.044280436, y: 0.00000479887, z: -0.00042540033}
      rotation: {x: -0.012599031, y: -0.007551547, z: 0.03147826, w: 0.9993965}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandMiddle3
      parentName: 
      position: {x: -0.033964828, y: -0.000000012184229, z: 0.0000000037526937}
      rotation: {x: -0.007228648, y: 0.00081385655, z: 0.29040176, w: 0.9568772}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandPinky1
      parentName: 
      position: {x: -0.06565995, y: -0.007825106, z: -0.032251246}
      rotation: {x: -0.066111416, y: 0.08169103, z: 0.0931265, w: 0.99009264}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandPinky2
      parentName: 
      position: {x: -0.030805448, y: -0.000030874577, z: -0.0014480774}
      rotation: {x: 0.046947356, y: -0.02116922, z: 0.037690047, w: 0.9979616}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandPinky3
      parentName: 
      position: {x: -0.023064027, y: -0.0000064026085, z: 0.000000017908482}
      rotation: {x: 0.046291146, y: -0.00056925963, z: 0.24186175, w: 0.96920574}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandRing1
      parentName: 
      position: {x: -0.07030211, y: -0.0037453093, z: -0.011411792}
      rotation: {x: -0.020245466, y: 0.07228851, z: 0.09005896, w: 0.9931032}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandRing2
      parentName: 
      position: {x: -0.043135457, y: -0.000020882293, z: -0.0022351781}
      rotation: {x: 0.018288167, y: -0.025606154, z: 0.033972062, w: 0.9989273}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandRing3
      parentName: 
      position: {x: -0.030835565, y: 1.581948e-10, z: -0.000000016455365}
      rotation: {x: 0.01850981, y: -0.0023600208, z: 0.24554445, w: 0.96920574}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandThumb1
      parentName: 
      position: {x: -0.014231241, y: -0.012377825, z: 0.025531668}
      rotation: {x: -0.11075436, y: -0.053521357, z: -0.1105846, w: 0.9862252}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandThumb2
      parentName: 
      position: {x: -0.016374, y: -0.00529, z: 0.023491409}
      rotation: {x: -0.03011053, y: 0.09966989, z: 0.0063956263, w: 0.9945443}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandThumb3
      parentName: 
      position: {x: -0.02546, y: -0.00764, z: 0.020833}
      rotation: {x: 0.04826967, y: -0.21970885, z: -0.019951671, w: 0.97416633}
      scale: {x: 1, y: 1, z: 1}
    - name: Neck
      parentName: 
      position: {x: 7.105427e-17, y: 0.2590093, z: -0.032413255}
      rotation: {x: 0.026307272, y: 0.007300207, z: -0.019885676, w: 0.99942946}
      scale: {x: 1, y: 1, z: 1}
    - name: Head
      parentName: 
      position: {x: 3.5527136e-17, y: 0.08307038, z: 0.0113267815}
      rotation: {x: -0.054225, y: 0.0050988905, z: -0.0018822433, w: 0.99851394}
      scale: {x: 1, y: 1, z: 1}
    - name: Jaw
      parentName: 
      position: {x: 1.7347234e-20, y: 0.0111267585, z: 0.010327543}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: JawEND
      parentName: 
      position: {x: -1.7347234e-20, y: -0.04828876, z: 0.07185171}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftLipCorner
      parentName: 
      position: {x: -0.032843262, y: -0.01657876, z: 0.066121764}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftLipLower
      parentName: 
      position: {x: -0.014250817, y: -0.02168876, z: 0.08224063}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightLipCorner
      parentName: 
      position: {x: 0.03284, y: -0.01657876, z: 0.066118784}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightLipLower
      parentName: 
      position: {x: 0.014250817, y: -0.02168876, z: 0.082238786}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: TongueBack
      parentName: 
      position: {x: -1.7347234e-20, y: -0.022869369, z: 0.010095409}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: TongueTip
      parentName: 
      position: {x: -1.7347234e-20, y: -0.023278812, z: 0.03832271}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftCheek
      parentName: 
      position: {x: -0.054244027, y: 0.03370195, z: 0.0594304}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftEye
      parentName: 
      position: {x: -0.020848233, y: 0.0825027, z: 0.055427432}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftEyelidLower
      parentName: 
      position: {x: -0.035618957, y: 0.06507366, z: 0.07623474}
      rotation: {x: -0.034899496, y: -8.865092e-18, z: 5.331712e-18, w: 0.99939084}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftEyelidUpper
      parentName: 
      position: {x: -0.034406897, y: 0.10060814, z: 0.08020531}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftInnerBrow
      parentName: 
      position: {x: -0.012062691, y: 0.118765265, z: 0.093466826}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftIOuterBrow
      parentName: 
      position: {x: -0.05503987, y: 0.11482529, z: 0.061777398}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftLipUpper
      parentName: 
      position: {x: -0.014501322, y: -0.005111811, z: 0.09461884}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftNostril
      parentName: 
      position: {x: -0.0179, y: 0.026312828, z: 0.0908674}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightCheek
      parentName: 
      position: {x: 0.054239996, y: 0.033702828, z: 0.0594274}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightEye
      parentName: 
      position: {x: 0.020849999, y: 0.08250283, z: 0.0554274}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightEyelidLower
      parentName: 
      position: {x: 0.03562, y: 0.06507283, z: 0.0762374}
      rotation: {x: -0.034899496, y: -8.865092e-18, z: 5.331712e-18, w: 0.99939084}
      scale: {x: 1, y: 1, z: 1}
    - name: RightEyelidUpper
      parentName: 
      position: {x: 0.03441, y: 0.10061283, z: 0.08020739}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightInnerBrow
      parentName: 
      position: {x: 0.012062687, y: 0.118765265, z: 0.093466826}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightIOuterBrow
      parentName: 
      position: {x: 0.055040002, y: 0.11482283, z: 0.061777398}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightLipUpper
      parentName: 
      position: {x: 0.014501322, y: -0.0051071714, z: 0.094617404}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightNostril
      parentName: 
      position: {x: 0.0179, y: 0.026308905, z: 0.09087062}
      rotation: {x: 6.938894e-18, y: -8.673618e-18, z: 5.6378513e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightShoulder
      parentName: 
      position: {x: 0.038286015, y: 0.22162114, z: -0.017063085}
      rotation: {x: 0.17314862, y: 0.9845339, z: -0.015271222, w: -0.021894779}
      scale: {x: 1, y: 1, z: 1}
    - name: RightArm
      parentName: 
      position: {x: -0.100501455, y: -0.0000024966455, z: -0.00000005228366}
      rotation: {x: 0.12911002, y: 0.98841476, z: -0.056917176, w: 0.05592361}
      scale: {x: 1, y: 1, z: 1}
    - name: RightForeArm
      parentName: 
      position: {x: 0.25342825, y: 0.006011353, z: -0.016704524}
      rotation: {x: 0.17294659, y: 0.018318847, z: -0.026436511, w: 0.9844059}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHand
      parentName: 
      position: {x: 0.2453737, y: 0.021641772, z: 0.005550465}
      rotation: {x: -0.07456149, y: 0.04626841, z: 0.0050307373, w: 0.9961298}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandIndex1
      parentName: 
      position: {x: 0.0747695, y: -0.0012430536, z: 0.034344498}
      rotation: {x: -0.00423194, y: 0.16211921, z: -0.04068246, w: 0.9859231}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandIndex2
      parentName: 
      position: {x: 0.0370584, y: 0.00072612107, z: 0.014538894}
      rotation: {x: -0.07758397, y: 0.022350721, z: 0.040922016, w: 0.9958949}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandIndex3
      parentName: 
      position: {x: 0.025225038, y: -0.0049664653, z: 0.011012146}
      rotation: {x: 0.0045231823, y: 0.013293446, z: -0.08829058, w: 0.9959958}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandMiddle1
      parentName: 
      position: {x: 0.075647645, y: 0.0047914027, z: 0.011853182}
      rotation: {x: -0.0017741377, y: 0.0143494075, z: -0.047806773, w: 0.998752}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandMiddle2
      parentName: 
      position: {x: 0.043809064, y: 0.00019418815, z: 0.006454936}
      rotation: {x: -0.018872144, y: -0.04411144, z: 0.08295074, w: 0.99539804}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandMiddle3
      parentName: 
      position: {x: 0.03307247, y: -0.007547537, z: 0.0016898462}
      rotation: {x: 0.0049401834, y: -0.0005552567, z: -0.19793308, w: 0.980203}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandPinky1
      parentName: 
      position: {x: 0.06680334, y: -0.0019941085, z: -0.030756146}
      rotation: {x: -0.06202933, y: -0.25861248, z: -0.016703352, w: 0.9638428}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandPinky2
      parentName: 
      position: {x: 0.028530842, y: -0.001397143, z: -0.011623796}
      rotation: {x: 0.02985772, y: 0.00079828594, z: -0.061665237, w: 0.9976499}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandPinky3
      parentName: 
      position: {x: 0.02142686, y: -0.00055350893, z: -0.008516608}
      rotation: {x: -0.02910623, y: 0.00039729525, z: -0.15225372, w: 0.9879127}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandRing1
      parentName: 
      position: {x: 0.070598476, y: 0.0024570967, z: -0.009821458}
      rotation: {x: -0.0147603545, y: -0.11599676, z: -0.029716086, w: 0.99269533}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandRing2
      parentName: 
      position: {x: 0.042887185, y: -0.0013753821, z: -0.0049458584}
      rotation: {x: 0.020764904, y: -0.021556703, z: 0.075588815, w: 0.9966898}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandRing3
      parentName: 
      position: {x: 0.029500604, y: -0.0076929354, z: -0.004622256}
      rotation: {x: -0.011665739, y: 0.0013560067, z: -0.15477994, w: 0.98787916}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandThumb1
      parentName: 
      position: {x: 0.014684916, y: -0.011104942, z: 0.025858095}
      rotation: {x: -0.09714596, y: 0.028526563, z: 0.12988137, w: 0.9863467}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandThumb2
      parentName: 
      position: {x: 0.016374, y: -0.00529, z: 0.02349136}
      rotation: {x: -0.030910255, y: -0.101721525, z: -0.00582567, w: 0.9943155}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandThumb3
      parentName: 
      position: {x: 0.02546, y: -0.00764, z: 0.020833}
      rotation: {x: 0.04827294, y: 0.21970814, z: 0.01993708, w: 0.97416663}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: f115f4efdf5ca9f4aab6aa6c789cf0c6,
    type: 3}
  animationType: 3
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
