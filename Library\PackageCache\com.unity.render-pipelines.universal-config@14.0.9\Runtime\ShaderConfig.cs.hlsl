//
// This file was automatically generated. Please don't edit by hand. Execute Editor command [ Edit > Rendering > Generate Shader Includes ] instead
//

#ifndef SHADERCONFIG_CS_HLSL
#define SHADERCONFIG_CS_HLSL
//
// UnityEngine.Rendering.Universal.ShaderOptions:  static fields
//
#define MAX_VISIBLE_LIGHT_COUNT_LOW_END_MOBILE (16)
#define MAX_VISIBLE_LIGHT_COUNT_MOBILE (32)
#define MAX_VISIBLE_LIGHT_COUNT_DESKTOP (256)


#endif
