# Render Objects Renderer Feature

Use a Render Objects Renderer Feature to draw objects on a certain layer, at a certain time, with specific overrides.

|Page|Description|
|-|-|
|[Example: How to create a custom rendering effect using the Render Objects Renderer Feature](how-to-custom-effect-render-objects.md) |An example that draws a silhouette when a character goes behind other objects.|
|[Render Objects Renderer Feature reference](renderer-feature-render-objects.md)|Properties that configure the behaviour of a Render Objects Renderer Feature.|
