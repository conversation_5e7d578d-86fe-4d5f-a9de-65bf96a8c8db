<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:sg="UnityEditor.ShaderGraph.Drawing">
    <ui:VisualElement name="content" picking-mode="Ignore">
        <ui:VisualElement name="header" picking-mode="Ignore">
            <ui:VisualElement name="labelContainer" picking-mode="Ignore">
                <ui:Label name="titleLabel" text="" />
                <ui:Label name="subTitleLabel" text="" />
            </ui:VisualElement>
        </ui:VisualElement>
        <ui:VisualElement name="contentContainer" picking-mode="Ignore" />
    </ui:VisualElement>
    <sg:ResizableElement pickingMode="Ignore" resizeRestriction="FlexDirection"/>
</ui:UXML>
