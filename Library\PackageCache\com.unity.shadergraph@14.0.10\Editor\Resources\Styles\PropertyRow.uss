PropertyRow EnumField,
PropertyRow PopupField,
PropertyRow TextField,
PropertyRow Label,
PropertyRow Toggle,
PropertyRow IntegerField,
PropertyRow ColorField,
PropertyRow DoubleField {
    margin-left: 0;
    margin-right: 0;
    -unity-text-align : middle-left;
}

PropertyRow > #container{
    flex-grow: 1;
    padding-left: 8px;
    padding-right: 16px;
    flex-direction: row;
}

PropertyRow > #container > #label {
    flex-grow: 2;
    flex-basis: 0;
    min-width: 92px;
    width: 92px;
    font-size: 12px;
    margin-right: 4px;
    justify-content: center;
}

PropertyRow > #container > #label > Label {
    margin-bottom: 3px;
    flex-wrap: wrap;
    text-overflow: ellipsis; /* Possible values: clip | ellipsis */
    -unity-text-overflow-position: end; /* Possible values: start | middle | end */
    /* Conditions */
    overflow: hidden;
    white-space: wrap;
}

PropertyRow > #container > #label > Label.modified {
    -unity-font-style: bold;
}

PropertyRow > #container > #content{
    flex-grow: 10;
    flex-basis: 0;
    height: auto;
    -unity-font-style: bold;
    width: 100px;
    justify-content: center;
}
