using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

#if !UNITY_DOTSPLAYER
using System.Reflection;

// General Information about an assembly is controlled through the following
// set of attributes. Change these attribute values to modify the information
// associated with an assembly.

[assembly: Assembly<PERSON><PERSON>le("Unity.Mathematics")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("Unity.Mathematics")]
[assembly: AssemblyCopyright("Copyright ©  2017")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

// Setting ComVisible to false makes the types in this assembly not visible
// to COM components.  If you need to access a type in this assembly from
// COM, set the ComVisible attribute to true on that type.

[assembly: ComVisible(false)]

// The following GUID is for the ID of the typelib if this project is exposed to COM

[assembly: Guid("19810344-7387-4155-935F-BDD5CC61F0BF")]

// Version information for an assembly consists of the following four values:
//
//      Major Version
//      Minor Version
//      Build Number
//      Revision
//
// You can specify all the values or you can default the Build and Revision Numbers
// by using the '*' as shown below:
// [assembly: AssemblyVersion("1.0.*")]

[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
#endif

[assembly: InternalsVisibleTo("Unity.Mathematics.Tests")]
[assembly: InternalsVisibleTo("Unity.Mathematics.PerformanceTests")]
[assembly: InternalsVisibleTo("btests")]