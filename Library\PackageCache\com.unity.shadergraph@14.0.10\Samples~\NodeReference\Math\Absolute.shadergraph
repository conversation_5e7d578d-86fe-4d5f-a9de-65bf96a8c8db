{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "dcb45ac26bfd483e9bdcba274fd29f50",
    "m_Properties": [],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "68f032db7db046d5b3c170627b03606a"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "27132f0286934b86a6de4d8e0c51f486"
        },
        {
            "m_Id": "7ceba854560b4751b4a058d727c220ff"
        },
        {
            "m_Id": "4872e29cf8334a65adeddcebdcb60be2"
        },
        {
            "m_Id": "961cf1284f6f4cdb9578857ae3ca70ec"
        },
        {
            "m_Id": "30a47125cb2a44db853d7f7571e920a2"
        },
        {
            "m_Id": "70417f4e71194afabdc4bd6e9b06e822"
        },
        {
            "m_Id": "ee684125d72345988b8fe7896a23092c"
        },
        {
            "m_Id": "0e39c8910e9647bc9085911e320f06c3"
        },
        {
            "m_Id": "2edfebac9023416c8a4a8ed8ccc59456"
        },
        {
            "m_Id": "e02866e1cf0a4249bce499152394935d"
        },
        {
            "m_Id": "1c721bc17e9b4c9f86c4eef2dab5998d"
        },
        {
            "m_Id": "93ff6cb5e8e1462d92d2cdf1534c1a60"
        },
        {
            "m_Id": "5f6f1412f3e142078ab44c896da19e9f"
        },
        {
            "m_Id": "5f09dc75c5c54238a7e637cd2a1fa20b"
        }
    ],
    "m_GroupDatas": [
        {
            "m_Id": "75ba5105445e4889b8e61d33dfaf696e"
        },
        {
            "m_Id": "694a2eb050ef4d14bfe9cbd148f50155"
        }
    ],
    "m_StickyNoteDatas": [
        {
            "m_Id": "45371dd7b0d748c8a9539bd33d8aca03"
        },
        {
            "m_Id": "e21eec8e25454aa393aad0d2f4c19ff3"
        },
        {
            "m_Id": "658be2710e7a4740a75a7dd6431e8586"
        },
        {
            "m_Id": "a6d7bf8af2b640519805dfb5f45907cf"
        },
        {
            "m_Id": "038c067f9b8c40c791d956a85d633fc3"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "0e39c8910e9647bc9085911e320f06c3"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "ee684125d72345988b8fe7896a23092c"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "1c721bc17e9b4c9f86c4eef2dab5998d"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "93ff6cb5e8e1462d92d2cdf1534c1a60"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "70417f4e71194afabdc4bd6e9b06e822"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "ee684125d72345988b8fe7896a23092c"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "e02866e1cf0a4249bce499152394935d"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1c721bc17e9b4c9f86c4eef2dab5998d"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "ee684125d72345988b8fe7896a23092c"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "2edfebac9023416c8a4a8ed8ccc59456"
                },
                "m_SlotId": 0
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": [
            {
                "m_Id": "27132f0286934b86a6de4d8e0c51f486"
            },
            {
                "m_Id": "7ceba854560b4751b4a058d727c220ff"
            },
            {
                "m_Id": "4872e29cf8334a65adeddcebdcb60be2"
            }
        ]
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 200.0
        },
        "m_Blocks": [
            {
                "m_Id": "961cf1284f6f4cdb9578857ae3ca70ec"
            },
            {
                "m_Id": "5f6f1412f3e142078ab44c896da19e9f"
            },
            {
                "m_Id": "5f09dc75c5c54238a7e637cd2a1fa20b"
            }
        ]
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Shader Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": ""
    },
    "m_ActiveTargets": [
        {
            "m_Id": "b9430ceb950842f08198fce17a640911"
        },
        {
            "m_Id": "6622b4daaa264cb28fa11e263c50efc1"
        },
        {
            "m_Id": "5a37afd5df834a2dbfaade536d58233e"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "01df67e1f6dc47e290b4dcf44015bdb8",
    "m_Id": 3,
    "m_DisplayName": "Delta Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Delta Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "030ab8f178254100ab9af0b5ceacae35",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "038c067f9b8c40c791d956a85d633fc3",
    "m_Title": "",
    "m_Content": "Using the Absolute node, now our data goes from 1 to 0 and back to 1, so it's more like a bounce than a wave.\n\nThe same thing could also be done with vertex position to actually make your model bounce.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -376.0000305175781,
        "y": 341.5000305175781,
        "width": 200.00001525878907,
        "height": 131.00003051757813
    },
    "m_Group": {
        "m_Id": "694a2eb050ef4d14bfe9cbd148f50155"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInUnlitSubTarget",
    "m_ObjectId": "0b16dce6e60646beb1a5a7d329e21e82"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalVectorNode",
    "m_ObjectId": "0e39c8910e9647bc9085911e320f06c3",
    "m_Group": {
        "m_Id": "75ba5105445e4889b8e61d33dfaf696e"
    },
    "m_Name": "Normal Vector",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1449.0,
            "y": 132.00001525878907,
            "width": 206.0,
            "height": 130.50001525878907
        }
    },
    "m_Slots": [
        {
            "m_Id": "2a90ee006d0440abbbdc5b5af7af23d0"
        }
    ],
    "synonyms": [
        "surface direction"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "1c69d8e6d43443278739b843ebce2026",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SineNode",
    "m_ObjectId": "1c721bc17e9b4c9f86c4eef2dab5998d",
    "m_Group": {
        "m_Id": "694a2eb050ef4d14bfe9cbd148f50155"
    },
    "m_Name": "Sine",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -617.0000610351563,
            "y": 56.500038146972659,
            "width": 208.00009155273438,
            "height": 278.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "3d274c7cc3124841ae6a486e571ae688"
        },
        {
            "m_Id": "030ab8f178254100ab9af0b5ceacae35"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "27132f0286934b86a6de4d8e0c51f486",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "6b261cc7b6d8499d814dfbc5129438ba"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Position"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "291e1fa75c304d03a7f2be53c85787bb",
    "m_Id": 4,
    "m_DisplayName": "Smooth Delta",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Smooth Delta",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "2a90ee006d0440abbbdc5b5af7af23d0",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "2e131bbf0e9f4aa1a5624b60959aa3dd",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.AbsoluteNode",
    "m_ObjectId": "2edfebac9023416c8a4a8ed8ccc59456",
    "m_Group": {
        "m_Id": "75ba5105445e4889b8e61d33dfaf696e"
    },
    "m_Name": "Absolute",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -988.5000610351563,
            "y": 82.50000762939453,
            "width": 208.00006103515626,
            "height": 278.00006103515627
        }
    },
    "m_Slots": [
        {
            "m_Id": "5448b39fba244c5aad8986bb432cb97f"
        },
        {
            "m_Id": "ceada34864b342c89345efa3201feb7a"
        }
    ],
    "synonyms": [
        "positive"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.AbsoluteNode",
    "m_ObjectId": "30a47125cb2a44db853d7f7571e920a2",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Absolute",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -935.0,
            "y": -220.49998474121095,
            "width": 127.49993896484375,
            "height": 94.00000762939453
        }
    },
    "m_Slots": [
        {
            "m_Id": "1c69d8e6d43443278739b843ebce2026"
        },
        {
            "m_Id": "c61da8023a8d480e8bc99ba2abe0a993"
        }
    ],
    "synonyms": [
        "positive"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "3d274c7cc3124841ae6a486e571ae688",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "424ee881ff814f5d9a68a559a517ca65",
    "m_Id": 0,
    "m_DisplayName": "Base Color",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BaseColor",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.5,
        "y": 0.5,
        "z": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.5,
        "g": 0.5,
        "b": 0.5,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "45371dd7b0d748c8a9539bd33d8aca03",
    "m_Title": "Absolute Node",
    "m_Content": "The Absolute Node will makes all inputs positive, regardless of if they're positive or negative.\n\nIt's important to know that in most cases the shader compiler can perform the absolute operation without any shader instructions - so usually, you get an absolute operation for free.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -798.0000610351563,
        "y": -214.50001525878907,
        "width": 200.0,
        "height": 164.50001525878907
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "4872e29cf8334a65adeddcebdcb60be2",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Tangent",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "f8303fd091b74bcc8e6dc818b5e48fb1"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Tangent"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "4a3a5c4f1f494af6815a1694c001d179",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 1.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "4fd89a6f55e44ea78644ba50d4919084",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "52a7c8cd6245443ebe6cbbf48b07a124",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "5448b39fba244c5aad8986bb432cb97f",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget",
    "m_ObjectId": "5a37afd5df834a2dbfaade536d58233e",
    "m_Datas": [],
    "m_ActiveSubTarget": {
        "m_Id": "ba7bc4c281d946d39891ce9ddd6bde8a"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZTestMode": 4,
    "m_ZWriteControl": 0,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CastShadows": true,
    "m_ReceiveShadows": true,
    "m_SupportsLODCrossFade": false,
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "5f09dc75c5c54238a7e637cd2a1fa20b",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Alpha",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "d74d5e1e691a4ef6b68160b68abf4df3"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Alpha"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "5f6f1412f3e142078ab44c896da19e9f",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Emission",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "a44f6a505fd54f02802805782d5f21c6"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Emission"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "642d17bdcde248f4a65bedd1be4c6c15",
    "m_Id": 0,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "64923652c7004846877ac42b87b2cf39",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "658be2710e7a4740a75a7dd6431e8586",
    "m_Title": "",
    "m_Content": "If you want the result to be positive on both ends instead, you can use the Absolute node to remove the negative sign from the negative values. Now we get 1 to 0 and back to 1 again.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -983.5000610351563,
        "y": 363.5000305175781,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "75ba5105445e4889b8e61d33dfaf696e"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDTarget",
    "m_ObjectId": "6622b4daaa264cb28fa11e263c50efc1",
    "m_ActiveSubTarget": {
        "m_Id": "b6b6a366edcb46fd83582b1e74da368c"
    },
    "m_Datas": [
        {
            "m_Id": "afb28065d6e841dfa39ebde7c7916239"
        },
        {
            "m_Id": "52a7c8cd6245443ebe6cbbf48b07a124"
        },
        {
            "m_Id": "b6721192227d4a898d0bd581c5ed4616"
        }
    ],
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false,
    "m_SupportLineRendering": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "68d0588aefe745f981a85ed954397cc8",
    "m_Id": 0,
    "m_DisplayName": "Direction",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Direction",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "68f032db7db046d5b3c170627b03606a",
    "m_Name": "",
    "m_ChildObjectList": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "694a2eb050ef4d14bfe9cbd148f50155",
    "m_Title": "Bouncing",
    "m_Position": {
        "x": -743.5000610351563,
        "y": -1.9999656677246094
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "6b261cc7b6d8499d814dfbc5129438ba",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MainLightDirectionNode",
    "m_ObjectId": "70417f4e71194afabdc4bd6e9b06e822",
    "m_Group": {
        "m_Id": "75ba5105445e4889b8e61d33dfaf696e"
    },
    "m_Name": "Main Light Direction",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1402.5001220703125,
            "y": 55.00002670288086,
            "width": 159.5001220703125,
            "height": 76.99998474121094
        }
    },
    "m_Slots": [
        {
            "m_Id": "68d0588aefe745f981a85ed954397cc8"
        }
    ],
    "synonyms": [
        "sun"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "75ba5105445e4889b8e61d33dfaf696e",
    "m_Title": "Turn Negatives Positive",
    "m_Position": {
        "x": -1474.0,
        "y": -3.5000228881835939
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "7ceba854560b4751b4a058d727c220ff",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Normal",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "642d17bdcde248f4a65bedd1be4c6c15"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Normal"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "904fb48e427a4649aec48d12a5be1d8f",
    "m_Id": 1,
    "m_DisplayName": "Sine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.AbsoluteNode",
    "m_ObjectId": "93ff6cb5e8e1462d92d2cdf1534c1a60",
    "m_Group": {
        "m_Id": "694a2eb050ef4d14bfe9cbd148f50155"
    },
    "m_Name": "Absolute",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -381.49993896484377,
            "y": 56.500038146972659,
            "width": 207.99993896484376,
            "height": 278.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "2e131bbf0e9f4aa1a5624b60959aa3dd"
        },
        {
            "m_Id": "ea6873c64bae491abd856680a6b88240"
        }
    ],
    "synonyms": [
        "positive"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "961cf1284f6f4cdb9578857ae3ca70ec",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.BaseColor",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "424ee881ff814f5d9a68a559a517ca65"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.BaseColor"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a19b000549624fbbb063b6f29d5311b8",
    "m_Id": 0,
    "m_DisplayName": "Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "a44f6a505fd54f02802805782d5f21c6",
    "m_Id": 0,
    "m_DisplayName": "Emission",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Emission",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 1,
    "m_DefaultColor": {
        "r": 0.0,
        "g": 0.0,
        "b": 0.0,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "a6d7bf8af2b640519805dfb5f45907cf",
    "m_Title": "",
    "m_Content": "In this example, the Sine of time gives us a value that oscilates back and forth between 1 and -1.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -610.5000610351563,
        "y": 335.5000305175781,
        "width": 200.00003051757813,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "694a2eb050ef4d14bfe9cbd148f50155"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "aaee8e7380ca49f9a0a1f503d3cea304",
    "m_Id": 2,
    "m_DisplayName": "Cosine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cosine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "afb28065d6e841dfa39ebde7c7916239",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "b6721192227d4a898d0bd581c5ed4616",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "b6b6a366edcb46fd83582b1e74da368c"
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInTarget",
    "m_ObjectId": "b9430ceb950842f08198fce17a640911",
    "m_ActiveSubTarget": {
        "m_Id": "0b16dce6e60646beb1a5a7d329e21e82"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZWriteControl": 0,
    "m_ZTestMode": 4,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CustomEditorGUI": ""
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalUnlitSubTarget",
    "m_ObjectId": "ba7bc4c281d946d39891ce9ddd6bde8a"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "c61da8023a8d480e8bc99ba2abe0a993",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "ceada34864b342c89345efa3201feb7a",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "d74d5e1e691a4ef6b68160b68abf4df3",
    "m_Id": 0,
    "m_DisplayName": "Alpha",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Alpha",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TimeNode",
    "m_ObjectId": "e02866e1cf0a4249bce499152394935d",
    "m_Group": {
        "m_Id": "694a2eb050ef4d14bfe9cbd148f50155"
    },
    "m_Name": "Time",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -718.5001220703125,
            "y": 56.500038146972659,
            "width": 79.00006103515625,
            "height": 76.99999237060547
        }
    },
    "m_Slots": [
        {
            "m_Id": "a19b000549624fbbb063b6f29d5311b8"
        },
        {
            "m_Id": "904fb48e427a4649aec48d12a5be1d8f"
        },
        {
            "m_Id": "aaee8e7380ca49f9a0a1f503d3cea304"
        },
        {
            "m_Id": "01df67e1f6dc47e290b4dcf44015bdb8"
        },
        {
            "m_Id": "291e1fa75c304d03a7f2be53c85787bb"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "e21eec8e25454aa393aad0d2f4c19ff3",
    "m_Title": "",
    "m_Content": "The dot product between the light direction and the normal gives us 1 when the two vectors are pointed the same direction, but -1 when they're pointed in opposite directions.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1204.5001220703125,
        "y": 387.5000305175781,
        "width": 200.00006103515626,
        "height": 102.00003051757813
    },
    "m_Group": {
        "m_Id": "75ba5105445e4889b8e61d33dfaf696e"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "ea6873c64bae491abd856680a6b88240",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DotProductNode",
    "m_ObjectId": "ee684125d72345988b8fe7896a23092c",
    "m_Group": {
        "m_Id": "75ba5105445e4889b8e61d33dfaf696e"
    },
    "m_Name": "Dot Product",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1209.5001220703125,
            "y": 82.50000762939453,
            "width": 208.00006103515626,
            "height": 302.00006103515627
        }
    },
    "m_Slots": [
        {
            "m_Id": "64923652c7004846877ac42b87b2cf39"
        },
        {
            "m_Id": "4a3a5c4f1f494af6815a1694c001d179"
        },
        {
            "m_Id": "4fd89a6f55e44ea78644ba50d4919084"
        }
    ],
    "synonyms": [
        "scalar product"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TangentMaterialSlot",
    "m_ObjectId": "f8303fd091b74bcc8e6dc818b5e48fb1",
    "m_Id": 0,
    "m_DisplayName": "Tangent",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tangent",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

